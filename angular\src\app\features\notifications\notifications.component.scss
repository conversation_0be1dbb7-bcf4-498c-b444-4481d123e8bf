.container {
  padding: 0 1rem;
}

.no-data {
  text-align: center;
  font-size: 1.5rem;
  margin: 0;
}

.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1rem;
}

mat-card.not-seen {
  background: var(--app-secondary-container-color);
  cursor: pointer;
}

[mat-card-avatar] {
  display: grid;
  place-items: center;
  background-color: var(--app-primary-color);

  mat-icon {
    color: var(--app-on-primary-color);
  }
}

mat-card-actions {
  text-transform: lowercase;
  opacity: 0.7;
  font-size: 0.8rem;
}
