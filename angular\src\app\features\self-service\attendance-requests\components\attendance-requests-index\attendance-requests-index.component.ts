import {Component, DestroyRef, inject, signal} from '@angular/core';
import { AttendanceRequestService, AttendanceRequestState } from '@proxy/self-service/attendance-requests';
import {
  extraGridFilter,
  GridFilterOperation,
  LOADING,
  pagedMap,
  takeOptions,
  TtwrGridComponent
} from '@ttwr-framework/ngx-main-visuals';
import { attendanceRequests } from '../../attendance-requests.model';
import { DateRangeCustomInputComponent, extractTwoDates, requireAllOperator } from '@shared';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, Subject } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {AuthService, SessionStateService} from '@abp/ng.core';
import {jwtDecode} from 'jwt-decode';
import {UserService} from '@shared/services/user.service';


@Component({
  selector: 'app-attendance-requests-index',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `<ttwr-grid [config]="config" />`,
})
export class AttendanceRequestsIndexComponent {
  private attendanceRequest = inject(AttendanceRequestService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private userService = inject(UserService);
  private loading = inject(LOADING);

  constructor() {

  }
  private refreshSubject = new Subject<void>();

  protected config = attendanceRequests.exclude({
    attendanceId: true,
  }).grid({
    title: '::AttendanceRequests',
    refreshSubject: this.refreshSubject,
    dataFunc: (pagination, _, filters) => {
      const duration = filters.find(f => f.attribute === 'duration')?.value;
      const state = filters.find(f => f.attribute === 'state')?.value;

      const { start, end } = duration ? extractTwoDates(duration) : { start: '', end: '' };

      return this.attendanceRequest.getSelfRequests({
        skipCount: pagination.pageSize * pagination.pageIndex,
        maxResultCount: pagination.pageSize,
      }, start, end, state ? [state] : []).pipe(
        requireAllOperator(),
        pagedMap(item => ({
          ...item,
          creationTime: item.creationTime as any,
        }))
      )
    },
    extraFilters: [
      extraGridFilter({
        name: 'duration',
        type: 'text',
        customInputComponent: DateRangeCustomInputComponent,
        customInputComponentExtraInputs: {
          disableDateFilter: true,
        },
        searchBarOperation: GridFilterOperation.In,
      }),
      extraGridFilter({
        name: 'state',
        type: 'select',
        options: takeOptions(AttendanceRequestState),
        multiple: false,
        searchBarOperation: GridFilterOperation.Equal,
      })
    ],
    actions: [
      {
        tooltip: 'Add',
        matIcon: 'add',
        delegateFunc: () => this.router.navigate(['create'], { relativeTo: this.route }),
      }
    ],
    fields: {
      checkIn: {
        columnName: '::GoldenOwl:CheckIn'
      },
      checkOut: {
        columnName: '::GoldenOwl:CheckOut'
      },
      creationTime: {
        columnName: '::IssuedOn',
      },
      state: {
        columnName: '::State',
      },
    },
    fieldActions: [
      {
        label: '::Cancel',
        showFunc: obj => obj.state === AttendanceRequestState.PendingApproval,
        confirmation: {
          title: '::Warning',
          message: '::AreYouSureYouWantToCancelAttendanceRequest?',
        },
        delegateFunc: obj => {
          this.loading.set(true)
          this.attendanceRequest.cancelSelfRequest(obj.id).pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          ).subscribe(() => {
            this.refreshSubject.next();
          })
        }
      }
    ],
  });
}
