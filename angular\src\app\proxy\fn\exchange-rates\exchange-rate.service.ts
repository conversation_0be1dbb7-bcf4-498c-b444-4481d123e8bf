import type { CreateOrUpdateExchangeRateDto, ExchangeRateDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ExchangeRateService {
  apiName = 'Default';
  

  create = (exchangeRate: CreateOrUpdateExchangeRateDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/exchange-rate',
      body: exchangeRate,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/exchange-rate/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ExchangeRateDto>({
      method: 'GET',
      url: `/api/app/exchange-rate/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, currencyCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<ExchangeRateDto>>({
      method: 'GET',
      url: '/api/app/exchange-rate',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount, currencyCode },
    },
    { apiName: this.apiName,...config });
  

  update = (exchangeRate: CreateOrUpdateExchangeRateDto, id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: `/api/app/exchange-rate/${id}`,
      body: exchangeRate,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
