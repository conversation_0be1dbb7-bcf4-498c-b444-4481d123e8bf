import { Component, DestroyRef, inject, input, signal } from '@angular/core';
import { arrayMap, LanguagePipe, LOADING, pagedMap, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { AttachmentService } from '@proxy/attachments';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { filter, map, switchMap } from 'rxjs';
import { AttachmentResponseDto, SaveTempDto } from '@proxy/attachments/dto';
import { attachment } from '@shared/components/attachment-grid-form/attachment.model';
import { EntityType } from '@proxy/attachment-types';
import { bytesToFormattedSize, requireAllOperator } from '@shared';
import { MatDialog } from '@angular/material/dialog';
import {
  AttachmentCreateDialogComponent
} from '@shared/components/attachment-grid-form/attachment-create-dialog/attachment-create-dialog.component';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-attachment-grid-form',
  standalone: true,
  imports: [
    LanguagePipe,
    TtwrGridComponent
  ],
  templateUrl: './attachment-grid-form.component.html',
  styleUrl: './attachment-grid-form.component.scss'
})
export class AttachmentGridFormComponent {
  private attachment = inject(AttachmentService);
  private dialog = inject(MatDialog);
  private destroyRef = inject(DestroyRef);
  private loading = inject(LOADING);

  public entityType = input.required<EntityType>();
  public entityId = input<string>();

  private entityType$ = toObservable(this.entityType);

  private _attachments = signal<(AttachmentResponseDto & { fromServer: boolean })[]>([]);
  public attachments = this._attachments.asReadonly();

  private attachments$ = toObservable(this.attachments);

  constructor() {
    toObservable(this.entityId).pipe(
      filter(Boolean),
      switchMap(id => this.attachment.getList(id)),
      arrayMap(attachment => ({
        ...attachment,
        fromServer: true,
      })),
      takeUntilDestroyed(),
    ).subscribe(attachments => this._attachments.set(attachments));
  }

  protected config = attachment(this.entityType$).grid({
    hiddenPagination: true,
    elevationClass: 'mat-elevation-z0',
    dataFunc: () => this.attachments$.pipe(
      map(attachments => ({
        items: attachments,
        totalCount: attachments.length,
      })),
      pagedMap(attachment => ({
        ...attachment,
        file: this.getAttachmentUrl(attachment.id!),
      })),
      requireAllOperator(),
    ),
    fields: {
      fileName: {
        columnName: '::FileName',
      },
      file: {
        columnName: '::File',
      },
      attachmentTypeId: {
        columnName: '::Type',
      },
      fileSize: {
        columnName: '::Size',
        displayCell: cell => {
          const { size, unit } = bytesToFormattedSize(cell);

          return `${size.toFixed(2)} ${unit}`;
        }
      },
      creationTime: {
        columnName: '::CreatedAt',
      },
      expiryDate: {
        columnName: '::ExpiryDate',
      },
    },
    actions: [
      {
        matIcon: 'add',
        tooltip: 'Add',
        delegateFunc: () => {
          const ref = this.dialog.open(AttachmentCreateDialogComponent, {
            width: '500px',
            data: this.entityType(),
          });

          ref.afterClosed().pipe(
            takeUntilDestroyed(this.destroyRef),
          ).subscribe(res => {
            if (res) {
              this._attachments.update(pre => [...pre, {
                ...res,
                fromServer: false,
              }]);
            }
          })
        },
      }
    ],
    fieldActions: [
      {
        matIcon: 'delete',
        tooltip: 'Delete',
        color: 'warn',
        confirmation: {
          title: '::Warning',
          message: '::AreYouSureYouWantToDelete?',
        },
        delegateFunc: ({ id }) => {
          const fromServer = this._attachments().find(a => a.id === id)?.fromServer;

          if (fromServer) {
            this.loading.set(true);
            this.attachment.delete(id).subscribe(() => {
              this._attachments.update(pre => pre.filter(attachment => attachment.id !== id));
              this.loading.set(false);
            });
          } else {
            this._attachments.update(pre => pre.filter(attachment => attachment.id !== id));
          }
        }
      }
    ],
  });

  attachmentGridValue(bindEntityId: string): SaveTempDto[] {
    return this.attachments().filter(attachment => !attachment.fromServer).map(attachment => ({
      attachmentId: attachment.id,
      bindEntityId,
    }))
  }

  private getAttachmentUrl(id: string) {
    return `${environment.apis.default.url}/api/app/attachment/${id}/attachment-file`;
  }
}
