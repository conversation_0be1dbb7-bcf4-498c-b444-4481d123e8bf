import type { AccountBalanceDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class AccountBalanceService {
  apiName = 'Default';
  

  getBalance = (accountId: string, currencyCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AccountBalanceDto>({
      method: 'GET',
      url: `/api/app/account-balance/balance/${accountId}`,
      params: { currencyCode },
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, accountId: string, financialPeriodId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<AccountBalanceDto>>({
      method: 'GET',
      url: '/api/app/account-balance',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount, accountId, financialPeriodId },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
