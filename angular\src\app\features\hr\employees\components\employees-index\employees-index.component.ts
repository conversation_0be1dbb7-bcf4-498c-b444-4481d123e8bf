import { Component, inject } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { pagedMap, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { employees } from '../../employees.model';
import { EmployeeService } from '@proxy/hr/employees';
import { requireAllOperator } from '@shared';

@Component({
  selector: 'app-employees-index',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `<ttwr-grid [config]="config" />`,
  styles: `
    :host ::ng-deep {
      .mat-column-jobTitleId {
        display: none;
      }
    }
  `,
})
export class EmployeesIndexComponent {
  private employees = inject(EmployeeService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);

  protected config = employees().select({
    id: true,
    name: true,
    surname: true,
    nationalNumber: true,
    employeeStatus: true,
    jobTitleName: true,
    jobTitleId: true,
  }).grid({
    title: '::Employee',
    dataFunc: (pagination, _, filters) => {
      const jobTitleId = filters.find(f => f.attribute === 'jobTitleId')?.value ?? '00000000-0000-0000-0000-000000000000';
      const status = filters.find(f => f.attribute === 'employeeStatus')?.value;

      return this.employees.getList({
          skipCount: pagination.pageSize * pagination.pageIndex,
          maxResultCount: pagination.pageSize,
        }, jobTitleId, status
      ).pipe(
        requireAllOperator(),
        pagedMap(employee => ({
          ...employee,
          employeeStatus: employee.employeeStatue,
          jobTitleId: null as any,
        }))
      );
    },
    actions: [
      {
        tooltip: 'Add',
        matIcon: 'add',
        delegateFunc: () =>
          this.router.navigate(['create'], { relativeTo: this.route.parent }),
      },
    ],
    fieldActions: [
      {
        matIcon: 'edit',
        tooltip: 'Edit',
        color: 'primary',
        delegateFunc: (obj) =>
          this.router.navigate(['update', obj.id], {
            relativeTo: this.route.parent,
          }),
      },
    ],
    fields: {
      employeeStatus: {
        nonSearchable: false,
        columnName: '::GoldenOwl:Status',
      },
      jobTitleId: {
        nonSearchable: false,
      },
      nationalNumber: {
        columnName: '::GoldenOwl:NationalNumber',
      },
      jobTitleName: {
        columnName: '::GoldenOwl:JobTitle',
      },
    },
  });
}
