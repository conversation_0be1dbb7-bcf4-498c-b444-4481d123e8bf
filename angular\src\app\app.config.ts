import { APP_INITIALIZER, ApplicationConfig, inject } from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { registerLocale } from '@abp/ng.core/locale';
import { provideNgxMainVisualsWithABP } from './abp-overrides';
import { routes } from './app.routes';
import { environment } from '../environments/environment';
import { appInitializer } from './app.initializer';
import { MAT_FORM_FIELD_DEFAULT_OPTIONS } from '@angular/material/form-field';
import { MAT_DATE_LOCALE } from '@angular/material/core';
import { LocalizationService } from '@abp/ng.core';
import { registerLocaleData } from '@angular/common';
import ar from '@angular/common/locales/ar';
import { interceptors } from './interceptors';

registerLocaleData(ar);

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes),
    provideHttpClient(
      withInterceptors(interceptors)
    ),
    provideAnimationsAsync(),
    provideNgxMainVisualsWithABP({
      abpOptions: {
        environment,
        registerLocaleFn: registerLocale(),
      },
      isGridSearchableColumnDefault: false,
      isGridSortableColumnDefault: false,
      isGridSearchInSelectFetchDefault: true,
      defaultGridActions: {
        pdf: false,
        excel: false,
        print: false,
        toggleAll: false,
        filters: false,
        sort: false,
      },
    }),
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializer,
      multi: true,
    },
    {
      provide: MAT_FORM_FIELD_DEFAULT_OPTIONS,
      useValue: {
        appearance: 'outline',
      },
    },
    {
      provide: MAT_DATE_LOCALE,
      useFactory: () => inject(LocalizationService).currentLang,
    },
  ]
};

