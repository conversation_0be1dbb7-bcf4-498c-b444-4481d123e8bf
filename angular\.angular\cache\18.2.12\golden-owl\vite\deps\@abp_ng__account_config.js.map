{"version": 3, "sources": ["../../../../../../node_modules/@abp/ng.account/fesm2022/abp-ng.account-config.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { APP_INITIALIZER, makeEnvironmentProviders, Injector, NgModule } from '@angular/core';\nimport { RoutesService, NAVIGATE_TO_MANAGE_PROFILE } from '@abp/ng.core';\nimport { Router } from '@angular/router';\nconst ACCOUNT_ROUTE_PROVIDERS = [{\n  provide: APP_INITIALIZER,\n  useFactory: configureRoutes,\n  deps: [RoutesService],\n  multi: true\n}];\nfunction configureRoutes(routes) {\n  return () => {\n    routes.add([{\n      path: undefined,\n      name: \"AbpAccount::Menu:Account\" /* eAccountRouteNames.Account */,\n      invisible: true,\n      layout: \"account\" /* eLayoutType.account */,\n      order: 1\n    }, {\n      path: '/account/login',\n      name: \"AbpAccount::Login\" /* eAccountRouteNames.Login */,\n      parentName: \"AbpAccount::Menu:Account\" /* eAccountRouteNames.Account */,\n      order: 1\n    }, {\n      path: '/account/register',\n      name: \"AbpAccount::Register\" /* eAccountRouteNames.Register */,\n      parentName: \"AbpAccount::Menu:Account\" /* eAccountRouteNames.Account */,\n      order: 2\n    }, {\n      path: '/account/manage',\n      name: \"AbpAccount::MyAccount\" /* eAccountRouteNames.ManageProfile */,\n      parentName: \"AbpAccount::Menu:Account\" /* eAccountRouteNames.Account */,\n      layout: \"application\" /* eLayoutType.application */,\n      order: 3\n    }, {\n      path: '/account/forgot-password',\n      parentName: \"AbpAccount::Menu:Account\" /* eAccountRouteNames.Account */,\n      name: \"AbpAccount::ForgotPassword\" /* eAccountRouteNames.ForgotPassword */,\n      invisible: true\n    }, {\n      path: '/account/reset-password',\n      parentName: \"AbpAccount::Menu:Account\" /* eAccountRouteNames.Account */,\n      name: \"AbpAccount::ResetPassword\" /* eAccountRouteNames.ResetPassword */,\n      invisible: true\n    }]);\n  };\n}\nfunction navigateToManageProfileFactory(injector) {\n  return () => {\n    const router = injector.get(Router);\n    const routes = injector.get(RoutesService);\n    const {\n      path\n    } = routes.find(item => item.name === \"AbpAccount::MyAccount\" /* eAccountRouteNames.ManageProfile */);\n    router.navigateByUrl(path);\n  };\n}\nfunction provideAccountConfig() {\n  return makeEnvironmentProviders([ACCOUNT_ROUTE_PROVIDERS, {\n    provide: NAVIGATE_TO_MANAGE_PROFILE,\n    useFactory: navigateToManageProfileFactory,\n    deps: [Injector]\n  }]);\n}\n\n/**\n * @deprecated AccountConfigModule is deprecated use `provideAccountConfig` *function* instead.\n */\nclass AccountConfigModule {\n  static forRoot() {\n    return {\n      ngModule: AccountConfigModule,\n      providers: [provideAccountConfig()]\n    };\n  }\n  static {\n    this.ɵfac = function AccountConfigModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AccountConfigModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: AccountConfigModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AccountConfigModule, [{\n    type: NgModule\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ACCOUNT_ROUTE_PROVIDERS, AccountConfigModule, configureRoutes, navigateToManageProfileFactory, provideAccountConfig };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAM,0BAA0B,CAAC;AAAA,EAC/B,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM,CAAC,aAAa;AAAA,EACpB,OAAO;AACT,CAAC;AACD,SAAS,gBAAgB,QAAQ;AAC/B,SAAO,MAAM;AACX,WAAO,IAAI,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,OAAO;AAAA,IACT,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,OAAO;AAAA,IACT,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,OAAO;AAAA,IACT,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,OAAO;AAAA,IACT,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,WAAW;AAAA,IACb,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,WAAW;AAAA,IACb,CAAC,CAAC;AAAA,EACJ;AACF;AACA,SAAS,+BAA+B,UAAU;AAChD,SAAO,MAAM;AACX,UAAM,SAAS,SAAS,IAAI,MAAM;AAClC,UAAM,SAAS,SAAS,IAAI,aAAa;AACzC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,OAAO;AAAA,MAAK,UAAQ,KAAK,SAAS;AAAA;AAAA,IAA8D;AACpG,WAAO,cAAc,IAAI;AAAA,EAC3B;AACF;AACA,SAAS,uBAAuB;AAC9B,SAAO,yBAAyB,CAAC,yBAAyB;AAAA,IACxD,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,MAAM,CAAC,QAAQ;AAAA,EACjB,CAAC,CAAC;AACJ;AAKA,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,OAAO,UAAU;AACf,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,qBAAqB,CAAC;AAAA,IACpC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAqB;AAAA,IACxD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}