import { Component, DestroyRef, inject } from '@angular/core';
import {
  AlertService,
  LanguagePipe,
  LOADING,
  requiredValidator,
  TtwrFormComponent,
} from '@ttwr-framework/ngx-main-visuals';
import { Mat<PERSON>ard, MatCardContent } from '@angular/material/card';
import { rewards } from '../../rewards.model';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { clearDate } from '@shared';
import { RewardService } from '@proxy/hr/rewards';

@Component({
  selector: 'app-rewards-update',
  standalone: true,
  imports: [LanguagePipe, MatCard, MatCardContent, TtwrFormComponent],
  template: `
    <h1>
      {{ '::EditReward' | i18n }}
    </h1>
    <mat-card>
      <mat-card-content>
        <ttwr-form [config]="config" />
      </mat-card-content>
    </mat-card>
  `,
  styles: `
    :host {
      --ttwr-form-gap: 1rem;
      --ttwr-form-grid-template-columns: 1fr 1fr;
    }
  `,
})
export class RewardsUpdateComponent {
  private rewardService = inject(RewardService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);

  protected config = rewards()
    .exclude({
      rewardTypeName: true,
    })
    .form({
      initialRequired: true,
      submitAction: {
        onSubmit: (body) => {
          this.loading.set(true);

          this.rewardService
            .update(this.route.snapshot.params['id'], {
              ...body,
              declarationDate: clearDate(body.declarationDate),
              implementationDate: clearDate(body.implementationDate),
              expirationDate: clearDate(body.expirationDate),
            })
            .pipe(
              finalize(() => this.loading.set(false)),
              takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(() => {
              this.alert.success('::UpdatedSuccessfully');
              this.router.navigate(['.'], { relativeTo: this.route.parent });
            });
        },
      },
      viewFunc: () => this.rewardService.get(this.route.snapshot.params['id']),
      fields: {
        summary: {
          label: '::RewardType:Summary',
          textInputType: 'textarea',
          inputSize: 'span 2',
        },
        employeeId: {
          label: '::GoldenOwl:Employee',
          search: true,
        },
        rewardTypeId: {
          label: '::Reward:RewardType',
          search: true,
        },
        declarationDate: {
          label: '::RewardType:DeclarationDate',
          onChange: () => {
            this.config.fields.implementationDate.control.updateValueAndValidity(
              {
                emitEvent: false,
              }
            );

            this.config.fields.expirationDate.control.updateValueAndValidity({
              emitEvent: false,
            });
          },
          validators: [
            requiredValidator,
            {
              name: 'invalidDeclarationDate',
              message: '::InvalidDeclarationDate',
              validator: (control) => {
                if (!control.value) return null;

                const implementationDateValue = (
                  control.parent?.controls as any
                )['implementationDateValue']?.value;

                if (!implementationDateValue) return null;

                if (implementationDateValue < control.value)
                  return {
                    invalidDeclarationDate: true,
                  };

                return null;
              },
            },
          ],
        },
        implementationDate: {
          label: '::RewardType:ImplementationDate',
          onChange: () => {
            this.config.fields.declarationDate.control.updateValueAndValidity({
              emitEvent: false,
            });

            this.config.fields.expirationDate.control.updateValueAndValidity({
              emitEvent: false,
            });
          },
          validators: [
            requiredValidator,
            {
              name: 'invalidImplementationDate',
              message: '::InvalidImplementationDate',
              validator: (control) => {
                if (!control.value) return null;

                const expirationDateValue = (control.parent?.controls as any)[
                  'expirationDate'
                ]?.value;

                if (!expirationDateValue) return null;

                if (expirationDateValue < control.value)
                  return {
                    invalidImplementationDate: true,
                  };

                return null;
              },
            },
          ],
        },
        expirationDate: {
          label: '::Reward:ExpirationDate',
          onChange: () => {
            this.config.fields.declarationDate.control.updateValueAndValidity({
              emitEvent: false,
            });

            this.config.fields.implementationDate.control.updateValueAndValidity(
              {
                emitEvent: false,
              }
            );
          },
          validators: [
            requiredValidator,
            {
              name: 'invalidExpirationDate',
              message: '::InvalidExpirationDate',
              validator: (control) => {
                if (!control.value) return null;

                const implementationDateValue = (
                  control.parent?.controls as any
                )['implementationDateValue']?.value;

                if (!implementationDateValue) return null;

                if (control.value < implementationDateValue)
                  return {
                    invalidExpirationDate: true,
                  };

                return null;
              },
            },
          ],
        },
      },
    });
}
