import { fields, model, takeOptions } from '@ttwr-framework/ngx-main-visuals';
// import { KindOfTimeOff, LeaveValidationType, RequestUnit } from '@proxy/hr/leave-types';
import { KindOfTimeOff, RequestUnit } from '@proxy/hr/leave-types';

export const leaveTypes = model({
  id: fields.text(),
  name: fields.text(),
  code: fields.text(),
  requestUnit: fields.select('single', takeOptions(RequestUnit)),
  kindOfTimeOff: fields.select('single', takeOptions(KindOfTimeOff)),
  requiresAllocation: fields.boolean(),
})


