import { Component, DestroyRef, inject, signal } from '@angular/core';
import { AlertService, LanguagePipe, LOADING, TtwrFormComponent } from '@ttwr-framework/ngx-main-visuals';
import { finalize } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatCard, MatCardContent } from '@angular/material/card';
import { ActivatedRoute, Router } from '@angular/router';
import { LeaveAllocationService } from '@proxy/hr/leave-allocations';
import { leaveAllocations } from '../../leave-allocations.model';
import { RequestUnit } from '@proxy/hr/leave-types';
import { DateRangeCustomInputComponent, extractTwoDates } from '@shared';

@Component({
  selector: 'app-leave-allocations-create',
  standalone: true,
  templateUrl: './leave-allocations-create.component.html',
  styleUrl: './leave-allocations-create.component.scss',
  imports: [Mat<PERSON><PERSON>, MatCardContent, TtwrFormComponent, LanguagePipe],
})
export class LeaveAllocationsCreateComponent {
  private leaveAllocation = inject(LeaveAllocationService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private loading = inject(LOADING);

  private leaveTypeIdToRequestUnitMap = new Map<string, RequestUnit>();

  private numberOfDaysHiddenSignal = signal(false);
  private numberOfHoursHiddenSignal = signal(false);

  protected config = leaveAllocations(leaveTypes => {
    leaveTypes.forEach(leaveType => {
      this.leaveTypeIdToRequestUnitMap.set(leaveType.id, leaveType.requestUnit);
    })
  }).exclude({
    employeeName: true,
    leaveTypeName: true,
  }).form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        const { start, end } = extractTwoDates(body.validityDateRange);

        this.leaveAllocation.create({
          ...body,
          validityFrom: start,
          validityTo: end,
          numberOfDays: this.numberOfDaysHiddenSignal() ? 0 : body.numberOfDays,
          numberOfHours: this.numberOfHoursHiddenSignal() ? 0 : body.numberOfHours,
        })
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::CreatedSuccessfully');
            this.router.navigate(['.'], { relativeTo: this.route.parent });
          })
      },
    },
    fields: {
      employeeId: {
        search: true,
        label: '::GoldenOwl:Employee',
      },
      leaveTypeId: {
        search: true,
        label: '::GoldenOwl:LeaveType',
        onChange: value => {
          if (!value) return;

          const requestUnit = this.leaveTypeIdToRequestUnitMap.get(value);

          if (requestUnit === undefined) return;

          this.numberOfDaysHiddenSignal.set(requestUnit === RequestUnit.Hours);
          this.numberOfHoursHiddenSignal.set(requestUnit === RequestUnit.Days);
        },
      },
      numberOfDays: {
        label: '::Days',
        hiddenSignal: this.numberOfDaysHiddenSignal,
        defaultValue: 0,
      },
      numberOfHours: {
        label: '::Hours',
        hiddenSignal: this.numberOfHoursHiddenSignal,
        defaultValue: 0,
      },
      validityDateRange: {
        customInputComponent: DateRangeCustomInputComponent,
      },
    },
  })
}
