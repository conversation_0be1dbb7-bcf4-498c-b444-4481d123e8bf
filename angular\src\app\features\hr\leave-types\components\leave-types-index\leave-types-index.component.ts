import { Component, DestroyRef, inject } from '@angular/core';
import { AlertService, LOADING, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { LeaveTypeService } from '@proxy/leave-types';
import { leaveTypes } from '../../leave-types.model';
import { requireAllOperator } from '@shared';
import { ActivatedRoute, Router } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-leave-types-index',
  standalone: true,
  imports: [
    TtwrGridComponent
  ],
  template: `<ttwr-grid [config]="config" class="actions-end" />`,
})
export class LeaveTypesIndexComponent {
  private leaveType = inject(LeaveTypeService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);

  private refreshSubject = new Subject<void>();

  protected config = leaveTypes.select({
    id: true,
    name: true,
  }).grid({
    title: '::GoldenOwl:LeaveTypes',
    refreshSubject: this.refreshSubject,
    dataFunc: (pagination) => this.leaveType.getList({
      skipCount: pagination.pageSize * pagination.pageIndex,
      maxResultCount: pagination.pageSize,
    }).pipe(requireAllOperator()),
    actions: [
      {
        tooltip: 'Add',
        matIcon: 'add',
        delegateFunc: () =>
          this.router.navigate(['create'], { relativeTo: this.route.parent }),
      },
    ],
    fieldActions: [
      {
        matIcon: 'edit',
        tooltip: 'Edit',
        delegateFunc: (obj) =>
          this.router.navigate(['update', obj.id], {
            relativeTo: this.route.parent,
          }),
      },
      {
        matIcon: 'delete',
        tooltip: 'Delete',
        color: 'warn',
        confirmation: {
          title: '::Warning',
          message: '::AreYouSureYouWantToDelete?',
        },
        delegateFunc: (obj) => {
          this.loading.set(true);

          this.leaveType.delete(obj.id).pipe(
            takeUntilDestroyed(this.destroyRef)
          ).subscribe(() => {
            this.refreshSubject.next();
            this.alert.success('::SuccessDelete');
          })
        }
      }
    ],
  })
}
