import { Component, Inject } from '@angular/core';
import { MatButton } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { YES, NO } from '@shared';

@Component({
    selector: 'app-common-actions-confirm',
    standalone: true,
    templateUrl: './common-actions-confirm.component.html',
    styleUrls: ['./common-actions-confirm.component.scss'],
    imports: [MatDialogContent, MatDialogTitle, MatButton, LanguagePipe]
})
export class CommonActionsConfirmComponent {
  public yesLabel: string = YES;
  public noLabel: string = NO;

  constructor(
    public dialogRef: MatDialogRef<CommonActionsConfirmComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { title: string; message: string }
  ) {
     this.dialogRef.updateSize('500px');
    }


  onConfirm(): void {
    this.dialogRef.close(true);
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
