import {
  EmailSettingGroupComponent,
  EmailSettingsService,
  SETTING_MANAGEMENT_FEATURES,
  SETTING_MANAGEMENT_FEATURES_PROVIDERS,
  SETTING_MANAGEMENT_HAS_SETTING,
  SETTING_MANAGEMENT_ROUTE_PROVIDERS,
  SETTING_MANAGEMENT_ROUTE_VISIBILITY,
  SETTING_MANAGEMENT_SETTING_TAB_PROVIDERS,
  SETTING_MANAGEMENT_VISIBLE_PROVIDERS,
  SettingManagementConfigModule,
  SettingTabsService,
  configureRoutes,
  configureSettingTabs,
  provideSettingManagementConfig,
  setSettingManagementVisibility
} from "./chunk-H6KB75UQ.js";
import "./chunk-YSVG55IP.js";
import "./chunk-HL4RP4FA.js";
import "./chunk-VITQ7ATO.js";
import "./chunk-JS23NXQZ.js";
import "./chunk-F7YCCNPX.js";
import "./chunk-AABMUNXW.js";
import "./chunk-K46JBGQH.js";
import "./chunk-B4FFJ7GE.js";
import "./chunk-VTW5CIPD.js";
import "./chunk-7YC2NMXI.js";
import "./chunk-ILOBLWYU.js";
import "./chunk-ZTELYOIP.js";
export {
  EmailSettingGroupComponent,
  EmailSettingsService,
  SETTING_MANAGEMENT_FEATURES,
  SETTING_MANAGEMENT_FEATURES_PROVIDERS,
  SETTING_MANAGEMENT_HAS_SETTING,
  SETTING_MANAGEMENT_ROUTE_PROVIDERS,
  SETTING_MANAGEMENT_ROUTE_VISIBILITY,
  SETTING_MANAGEMENT_SETTING_TAB_PROVIDERS,
  SETTING_MANAGEMENT_VISIBLE_PROVIDERS,
  SettingManagementConfigModule,
  SettingTabsService,
  configureRoutes,
  configureSettingTabs,
  provideSettingManagementConfig,
  setSettingManagementVisibility
};
//# sourceMappingURL=@abp_ng__setting-management_config.js.map
