import type { AttachmentRequestDto, AttachmentResponseDto, SaveTempDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class AttachmentService {
  apiName = 'Default';
  

  create = (dto: AttachmentRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AttachmentResponseDto>({
      method: 'POST',
      url: '/api/app/attachment',
      body: dto,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/attachment/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getAttachmentFile = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, Blob>({
      method: 'GET',
      responseType: 'blob',
      url: `/api/app/attachment/${id}/attachment-file`,
    },
    { apiName: this.apiName,...config });
  

  getList = (bindEntityId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AttachmentResponseDto[]>({
      method: 'GET',
      url: '/api/app/attachment',
      params: { bindEntityId },
    },
    { apiName: this.apiName,...config });
  

  saveTemp = (tempDtos: SaveTempDto[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/attachment/save-temp',
      body: tempDtos,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
