import { ChangeDetectionStrategy, Component, DestroyRef, inject } from '@angular/core';
import {
  AlertService,
  LanguagePipe,
  LOADING,
  TtwrGridComponent,
  TtwrViewComponent
} from '@ttwr-framework/ngx-main-visuals';
import { currency, exchangeRate } from '../../currency.model';
import { ActivatedRoute } from '@angular/router';
import { CurrencyService } from '@proxy/fn/currencies';
import { requireAllOperator } from '@shared';
import { ExchangeRateService } from '@proxy/fn/exchange-rates';
import { MatCard, MatCardContent } from '@angular/material/card';
import { MatDivider } from '@angular/material/divider';
import { MatDialog } from '@angular/material/dialog';
import { Subject, tap } from 'rxjs';
import { ExchangeRateCreateDialogComponent } from './exchange-rate-create-dialog.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ExchangeRateUpdateDialogComponent } from './exchange-rate-update-dialog.component';

@Component({
  selector: 'app-currency-view',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    TtwrViewComponent,
    TtwrGridComponent,
    MatCard,
    MatCardContent,
    LanguagePipe,
    MatDivider
  ],
  template: `
    <h1>
      {{ '::GoldenOwl:ViewCurrencyDetails' | i18n }}
    </h1>
    <mat-card>
      <mat-card-content>
        <ttwr-view [config]="viewConfig"/>
        <mat-divider />
        <ttwr-grid class="actions-end" [config]="gridConfig" />
      </mat-card-content>
    </mat-card>
  `,
  styles: `
    :host {
      --ttwr-view-field-label-width: 130px;
      --ttwr-view-grid-template-columns: 1fr 1fr;
      --ttwr-grid-title-font-size: 1.5rem;
      --ttwr-grid-title-margin: 1rem 0 0;
      --mat-table-background-color: var(--mdc-elevated-card-container-color);
      --mat-paginator-container-background-color: var(--mdc-elevated-card-container-color);
    }

    mat-divider {
      margin-top: 2rem;
      opacity: 0.6;
    }
  `,
})
export class CurrencyViewComponent {
  private currency = inject(CurrencyService);
  private exchangeRate = inject(ExchangeRateService);
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private dialog = inject(MatDialog);
  private alert = inject(AlertService);
  private loading = inject(LOADING);

  private _currencyCode = '';

  protected viewConfig = currency.view({
    viewFunc: () => this.currency.getView(this.route.snapshot.params['code']).pipe(
      requireAllOperator(),
      tap(({ code }) => {
        this._currencyCode = code;
      })
    ),
    fields: {
      fullName: {
        label: '::Currency:FullName',
      },
      isActive: {
        label: '::Currency:IsActive',
      },
      code: {
        label: '::Currency:Code',
      },
      symbol: {
        label: '::Currency:Symbol',
      },
      unitLabel: {
        label: '::Currency:UnitLabel',
      },
      subUnitLabel: {
        label: '::Currency:SubUnitLabel',
      },
      decimalPlaces: {
        label: '::Currency:DecimalPlaces',
      },
    },
  });

  private refreshSubject = new Subject<void>();

  protected gridConfig = exchangeRate.grid({
    title: '::ExchangeRates',
    elevationClass: 'mat-elevation-z0',
    refreshSubject: this.refreshSubject,
    dataFunc: pagination => this.exchangeRate.getList({
      maxResultCount: pagination.pageSize,
      skipCount: pagination.pageSize * pagination.pageIndex,
    }, this.route.snapshot.params['code']).pipe(
      requireAllOperator(),
    ),
    actions: [
      {
        matIcon: 'add',
        tooltip: 'Add',
        delegateFunc: () => {
          const ref = this.dialog.open(ExchangeRateCreateDialogComponent, {
            width: '500px',
            data: {
              currencyCode: this._currencyCode,
            },
          });

          ref.afterClosed().pipe(
            takeUntilDestroyed(this.destroyRef),
          ).subscribe(res => {
            if (res) {
              this.refreshSubject.next();
            }
          })
        }
      },
    ],
    fieldActions: [
      {
        matIcon: 'edit',
        tooltip: 'Edit',
        delegateFunc: (obj) => {
          const ref = this.dialog.open(ExchangeRateUpdateDialogComponent, {
            width: '500px',
            data: {
              ...obj,
              currencyCode: this._currencyCode,
            },
          });

          ref.afterClosed().pipe(
            takeUntilDestroyed(this.destroyRef),
          ).subscribe(res => {
            if (res) {
              this.refreshSubject.next();
            }
          })
        },
      },
      {
        matIcon: 'delete',
        tooltip: 'Delete',
        color: 'warn',
        confirmation: {
          title: '::Warning',
          message: '::AreYouSureYouWantToDelete?',
        },
        delegateFunc: (obj) => {
          this.loading.set(true);

          this.exchangeRate.delete(obj.id).pipe(
            takeUntilDestroyed(this.destroyRef)
          ).subscribe(() => {
            this.refreshSubject.next();
            this.alert.success('::SuccessDelete');
          })
        }
      }
    ],
    fields: {
      date: {
        columnName: '::ExchangeRate:Date'
      },
      rate: {
        columnName: '::ExchangeRate:Rate'
      },
    },
  })
}
