import { Component, DestroyRef, inject } from '@angular/core';
import {
  AlertService,
  LanguagePipe,
  LOADING,
  requiredValidator,
  TtwrFormComponent
} from '@ttwr-framework/ngx-main-visuals';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { penaltiesTypes } from '../penalties-types.model';
import { finalize, of } from 'rxjs';
import { PenaltyTypeService, SalaryEffectValueType } from '@proxy/hr/penalty-types';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-penalties-types-update-dialog',
  standalone: true,
  imports: [TtwrFormComponent, MatDialogTitle, MatDialogContent, LanguagePipe],
  template: `
    <h1 mat-dialog-title>{{ '::EditPenaltyType' | i18n }}</h1>
    <mat-dialog-content>
      <ttwr-form [config]="config"/>
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-grid-template-columns: 1fr 1fr;
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class PenaltiesTypesUpdateDialogComponent {
  private penaltyType = inject(PenaltyTypeService);
  private alert = inject(AlertService);
  private dialogRef = inject(MatDialogRef);
  private destroyRef = inject(DestroyRef);
  private data = inject<DialogData>(MAT_DIALOG_DATA);
  private loading = inject(LOADING);

  protected config = penaltiesTypes.form({
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        this.penaltyType
          .putUpdatePenaltyTypeByIdAndDto(this.data.id, body)
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::UpdatedSuccessfully');
            this.dialogRef.close(true);
          });
      },
    },
    viewFunc: () => of(this.data),
    fields: {
      name: {
        validators: [requiredValidator],
      },
      code: {
        label: '::PenaltyType:Code',
        validators: [requiredValidator],
      },
      isTerminateContract: {
        label: '::PenaltyType:IsTerminateContract',
      },
      salaryEffectValue: {
        label: '::PenaltyType:SalaryEffectValue',
      },
      salaryEffectValueType: {
        label: '::PenaltyType:SalaryEffectValueType',
      },
    },
  });
}

interface DialogData {
  id: string;
  name: string;
  code: string;
  salaryEffectValueType: SalaryEffectValueType;
  salaryEffectValue: number;
  isTerminateContract: boolean;
}
