import {Component, inject, signal} from '@angular/core';
import { MatRadi<PERSON>Button, MatRadioGroup } from '@angular/material/radio';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { ReactiveFormsModule } from '@angular/forms';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/material/form-field';
import { provideNativeDateAdapter } from '@angular/material/core';
import {MatSelect, MatSelectModule} from '@angular/material/select';
import { AttendanceRequestsCreateComponent } from '../attendance-requests-create.component';
import { MatInput } from '@angular/material/input';
import { combineLatest, filter, map, startWith } from 'rxjs';
import { toSignal } from '@angular/core/rxjs-interop';
import { formatDate } from '@angular/common';

@Component({
  selector: 'app-attendance-requests-advanced-options',
  standalone: true,
  imports: [
    MatRadioButton,
    MatRadioGroup,
    LanguagePipe,
    ReactiveFormsModule,
    MatFormField,
    <PERSON><PERSON><PERSON>l,
    MatSelectModule,
    MatInput
  ],
  templateUrl: './attendance-requests-advanced-options.component.html',
  styleUrl: './attendance-requests-advanced-options.component.scss',
  providers: [
    provideNativeDateAdapter(),
  ],
})
export class AttendanceRequestsAdvancedOptionsComponent {
  private attendanceRequestsCreate = inject(AttendanceRequestsCreateComponent);

  protected affectedDays = this.attendanceRequestsCreate.fixAffectedDays;
  protected attendanceFieldDisableSignal: any = this.attendanceRequestsCreate.config.fields.attendanceId.disabledSignal;
  protected attendanceIdControl = this.attendanceRequestsCreate.config.fields.attendanceId.control;
  protected modeControl = this.attendanceRequestsCreate.modeControl;

  protected fromValue = toSignal(
    combineLatest([
      this.modeControl.valueChanges.pipe(
        startWith('remove-all'),
      ),
      this.attendanceRequestsCreate.config.fields.checkIn.control.valueChanges,
    ]).pipe(
      filter(([_, checkIn]) => Boolean(checkIn)),
      map(([mode, checkIn]) => {
        if (mode === 'remove-all') {
          return formatDate(checkIn, 'yyyy-MM-dd', 'en-Us');
        } else if (mode === 'remove-overlapping-only') {
          return formatDate(checkIn, 'yyyy-MM-dd hh:mm a', 'en-Us');
        } else {
          return '';
        }
      })
    )
  );

  protected toValue = toSignal(
    combineLatest([
      this.modeControl.valueChanges.pipe(
        startWith('remove-all'),
      ),
      this.attendanceRequestsCreate.config.fields.checkOut.control.valueChanges,
    ]).pipe(
      filter(([_, checkOut]) => Boolean(checkOut)),
      map(([mode, checkOut]) => {
        if (mode === 'remove-all') {
          return formatDate(checkOut, 'yyyy-MM-dd', 'en-Us');
        } else if (mode === 'remove-overlapping-only') {
          return formatDate(checkOut, 'yyyy-MM-dd hh:mm a', 'en-Us');
        } else {
          return '';
        }
      })
    )
  );

  onSelectionChange(event: any){
   this.attendanceFieldDisableSignal.set(event.value !== 'fix');

  }
}

