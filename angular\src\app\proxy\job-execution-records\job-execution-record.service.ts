import type { JobExecutionRecordDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class JobExecutionRecordService {
  apiName = 'Default';
  

  getList = (input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<JobExecutionRecordDto>>({
      method: 'GET',
      url: '/api/app/job-execution-record',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  syncAttendance = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/job-execution-record/sync-attendance',
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
