import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, signal } from '@angular/core';
import { fields, LanguagePipe, LOADING, TtwrFormComponent } from '@ttwr-framework/ngx-main-visuals';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { attachment } from '@shared/components/attachment-grid-form/attachment.model';
import { EntityType } from '@proxy/attachment-types';
import { finalize, fromEvent, map, of, switchMap } from 'rxjs';
import { AttachmentService } from '@proxy/attachments';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { RuntimeAcceptFileCustomInputComponent } from './runtime-accept-file-custom-input.component';

@Component({
  selector: 'app-attachment-create-dialog',
  standalone: true,
  imports: [
    LanguagePipe,
    MatDialogTitle,
    Mat<PERSON><PERSON><PERSON>Content,
    TtwrFormComponent
  ],
  template: `
    <h1 mat-dialog-title>
      {{ '::AddNewAttachment' | i18n }}
    </h1>
    <mat-dialog-content>
      <ttwr-form [config]="config"/>
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-gap: 1rem;
    }
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AttachmentCreateDialogComponent {
  private attachment = inject(AttachmentService)
  private data = inject<EntityType>(MAT_DIALOG_DATA);
  private loading = inject(LOADING);
  private ref = inject(MatDialogRef);
  private destroyRef = inject(DestroyRef);

  public accept = signal<string | null>(null);
  private fileFieldDisabled = computed(() => this.accept() === null);

  private idToAllowedExtensionsMap = new Map<string, string>();

  protected config = attachment(
    of(this.data),
    this.idToAllowedExtensionsMap
  ).select({
    attachmentTypeId: true,
  }).extend({
    file: fields.file(),
    expiryDate: fields.date(),
  }).form({
    initialRequired: true,
    submitAction: {
      onSubmit: async body => {
        this.loading.set(true);

        const file = body.file[0];
        const reader = new FileReader();

        fromEvent(reader, 'load').pipe(
          map(event => (event.target as FileReader).result as string),
          map(fileString => fileString.split('base64,', 2)[1]),
          switchMap(fileString => this.attachment.create({
            attachmentTypeId: body.attachmentTypeId,
            expiryDate: body.expiryDate,
            fileName: file.name,
            file: fileString as any,
          })),
          finalize(() => this.loading.set(false)),
          takeUntilDestroyed(this.destroyRef),
        ).subscribe(res => this.ref.close(res));

        reader.readAsDataURL(file);
      }
    },
    fields: {
      attachmentTypeId: {
        label: '::Type',
        onChange: value => {
          if (!value) return;

          this.accept.set(this.idToAllowedExtensionsMap.get(value) ?? null);
        },
      },
      file: {
        label: '::File',
        disabledSignal: this.fileFieldDisabled,
        customInputComponent: RuntimeAcceptFileCustomInputComponent,
      },
      expiryDate: {
        validators: [],
        label: '::ExpiryDate',
      },
    },
  });
}
