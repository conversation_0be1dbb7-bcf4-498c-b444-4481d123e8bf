{"version": 3, "sources": ["../../../../../../node_modules/@abp/ng.identity/fesm2022/abp-ng.identity-proxy.mjs"], "sourcesContent": ["import * as i1 from '@abp/ng.core';\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\nclass IdentityRoleService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'AbpIdentity';\n    this.create = input => this.restService.request({\n      method: 'POST',\n      url: '/api/identity/roles',\n      body: input\n    }, {\n      apiName: this.apiName\n    });\n    this.delete = id => this.restService.request({\n      method: 'DELETE',\n      url: `/api/identity/roles/${id}`\n    }, {\n      apiName: this.apiName\n    });\n    this.get = id => this.restService.request({\n      method: 'GET',\n      url: `/api/identity/roles/${id}`\n    }, {\n      apiName: this.apiName\n    });\n    this.getAllList = () => this.restService.request({\n      method: 'GET',\n      url: '/api/identity/roles/all'\n    }, {\n      apiName: this.apiName\n    });\n    this.getList = input => this.restService.request({\n      method: 'GET',\n      url: '/api/identity/roles',\n      params: {\n        filter: input.filter,\n        sorting: input.sorting,\n        skipCount: input.skipCount,\n        maxResultCount: input.maxResultCount\n      }\n    }, {\n      apiName: this.apiName\n    });\n    this.update = (id, input) => this.restService.request({\n      method: 'PUT',\n      url: `/api/identity/roles/${id}`,\n      body: input\n    }, {\n      apiName: this.apiName\n    });\n  }\n  static {\n    this.ɵfac = function IdentityRoleService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || IdentityRoleService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: IdentityRoleService,\n      factory: IdentityRoleService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IdentityRoleService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.RestService\n  }], null);\n})();\nclass IdentityUserLookupService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'AbpIdentity';\n    this.findById = id => this.restService.request({\n      method: 'GET',\n      url: `/api/identity/users/lookup/${id}`\n    }, {\n      apiName: this.apiName\n    });\n    this.findByUserName = userName => this.restService.request({\n      method: 'GET',\n      url: `/api/identity/users/lookup/by-username/${userName}`\n    }, {\n      apiName: this.apiName\n    });\n    this.getCount = input => this.restService.request({\n      method: 'GET',\n      url: '/api/identity/users/lookup/count',\n      params: {\n        filter: input.filter\n      }\n    }, {\n      apiName: this.apiName\n    });\n    this.search = input => this.restService.request({\n      method: 'GET',\n      url: '/api/identity/users/lookup/search',\n      params: {\n        filter: input.filter,\n        sorting: input.sorting,\n        skipCount: input.skipCount,\n        maxResultCount: input.maxResultCount\n      }\n    }, {\n      apiName: this.apiName\n    });\n  }\n  static {\n    this.ɵfac = function IdentityUserLookupService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || IdentityUserLookupService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: IdentityUserLookupService,\n      factory: IdentityUserLookupService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IdentityUserLookupService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.RestService\n  }], null);\n})();\nclass IdentityUserService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'AbpIdentity';\n    this.create = input => this.restService.request({\n      method: 'POST',\n      url: '/api/identity/users',\n      body: input\n    }, {\n      apiName: this.apiName\n    });\n    this.delete = id => this.restService.request({\n      method: 'DELETE',\n      url: `/api/identity/users/${id}`\n    }, {\n      apiName: this.apiName\n    });\n    this.findByEmail = email => this.restService.request({\n      method: 'GET',\n      url: `/api/identity/users/by-email/${email}`\n    }, {\n      apiName: this.apiName\n    });\n    this.findByUsername = userName => this.restService.request({\n      method: 'GET',\n      url: `/api/identity/users/by-username/${userName}`\n    }, {\n      apiName: this.apiName\n    });\n    this.get = id => this.restService.request({\n      method: 'GET',\n      url: `/api/identity/users/${id}`\n    }, {\n      apiName: this.apiName\n    });\n    this.getAssignableRoles = () => this.restService.request({\n      method: 'GET',\n      url: '/api/identity/users/assignable-roles'\n    }, {\n      apiName: this.apiName\n    });\n    this.getList = input => this.restService.request({\n      method: 'GET',\n      url: '/api/identity/users',\n      params: {\n        filter: input.filter,\n        sorting: input.sorting,\n        skipCount: input.skipCount,\n        maxResultCount: input.maxResultCount\n      }\n    }, {\n      apiName: this.apiName\n    });\n    this.getRoles = id => this.restService.request({\n      method: 'GET',\n      url: `/api/identity/users/${id}/roles`\n    }, {\n      apiName: this.apiName\n    });\n    this.update = (id, input) => this.restService.request({\n      method: 'PUT',\n      url: `/api/identity/users/${id}`,\n      body: input\n    }, {\n      apiName: this.apiName\n    });\n    this.updateRoles = (id, input) => this.restService.request({\n      method: 'PUT',\n      url: `/api/identity/users/${id}/roles`,\n      body: input\n    }, {\n      apiName: this.apiName\n    });\n  }\n  static {\n    this.ɵfac = function IdentityUserService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || IdentityUserService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: IdentityUserService,\n      factory: IdentityUserService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IdentityUserService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.RestService\n  }], null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { IdentityRoleService, IdentityUserLookupService, IdentityUserService };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAGA,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,SAAS,WAAS,KAAK,YAAY,QAAQ;AAAA,MAC9C,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,MAAM;AAAA,IACR,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,SAAS,QAAM,KAAK,YAAY,QAAQ;AAAA,MAC3C,QAAQ;AAAA,MACR,KAAK,uBAAuB,EAAE;AAAA,IAChC,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,MAAM,QAAM,KAAK,YAAY,QAAQ;AAAA,MACxC,QAAQ;AAAA,MACR,KAAK,uBAAuB,EAAE;AAAA,IAChC,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,aAAa,MAAM,KAAK,YAAY,QAAQ;AAAA,MAC/C,QAAQ;AAAA,MACR,KAAK;AAAA,IACP,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,UAAU,WAAS,KAAK,YAAY,QAAQ;AAAA,MAC/C,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,QACN,QAAQ,MAAM;AAAA,QACd,SAAS,MAAM;AAAA,QACf,WAAW,MAAM;AAAA,QACjB,gBAAgB,MAAM;AAAA,MACxB;AAAA,IACF,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,SAAS,CAAC,IAAI,UAAU,KAAK,YAAY,QAAQ;AAAA,MACpD,QAAQ;AAAA,MACR,KAAK,uBAAuB,EAAE;AAAA,MAC9B,MAAM;AAAA,IACR,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAwB,SAAY,WAAW,CAAC;AAAA,IACnF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,qBAAoB;AAAA,MAC7B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,WAAW,QAAM,KAAK,YAAY,QAAQ;AAAA,MAC7C,QAAQ;AAAA,MACR,KAAK,8BAA8B,EAAE;AAAA,IACvC,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,iBAAiB,cAAY,KAAK,YAAY,QAAQ;AAAA,MACzD,QAAQ;AAAA,MACR,KAAK,0CAA0C,QAAQ;AAAA,IACzD,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,WAAW,WAAS,KAAK,YAAY,QAAQ;AAAA,MAChD,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,QACN,QAAQ,MAAM;AAAA,MAChB;AAAA,IACF,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,SAAS,WAAS,KAAK,YAAY,QAAQ;AAAA,MAC9C,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,QACN,QAAQ,MAAM;AAAA,QACd,SAAS,MAAM;AAAA,QACf,WAAW,MAAM;AAAA,QACjB,gBAAgB,MAAM;AAAA,MACxB;AAAA,IACF,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,mBAAmB;AACxE,aAAO,KAAK,qBAAqB,4BAA8B,SAAY,WAAW,CAAC;AAAA,IACzF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,2BAA0B;AAAA,MACnC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,SAAS,WAAS,KAAK,YAAY,QAAQ;AAAA,MAC9C,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,MAAM;AAAA,IACR,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,SAAS,QAAM,KAAK,YAAY,QAAQ;AAAA,MAC3C,QAAQ;AAAA,MACR,KAAK,uBAAuB,EAAE;AAAA,IAChC,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,cAAc,WAAS,KAAK,YAAY,QAAQ;AAAA,MACnD,QAAQ;AAAA,MACR,KAAK,gCAAgC,KAAK;AAAA,IAC5C,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,iBAAiB,cAAY,KAAK,YAAY,QAAQ;AAAA,MACzD,QAAQ;AAAA,MACR,KAAK,mCAAmC,QAAQ;AAAA,IAClD,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,MAAM,QAAM,KAAK,YAAY,QAAQ;AAAA,MACxC,QAAQ;AAAA,MACR,KAAK,uBAAuB,EAAE;AAAA,IAChC,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,qBAAqB,MAAM,KAAK,YAAY,QAAQ;AAAA,MACvD,QAAQ;AAAA,MACR,KAAK;AAAA,IACP,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,UAAU,WAAS,KAAK,YAAY,QAAQ;AAAA,MAC/C,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,QACN,QAAQ,MAAM;AAAA,QACd,SAAS,MAAM;AAAA,QACf,WAAW,MAAM;AAAA,QACjB,gBAAgB,MAAM;AAAA,MACxB;AAAA,IACF,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,WAAW,QAAM,KAAK,YAAY,QAAQ;AAAA,MAC7C,QAAQ;AAAA,MACR,KAAK,uBAAuB,EAAE;AAAA,IAChC,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,SAAS,CAAC,IAAI,UAAU,KAAK,YAAY,QAAQ;AAAA,MACpD,QAAQ;AAAA,MACR,KAAK,uBAAuB,EAAE;AAAA,MAC9B,MAAM;AAAA,IACR,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,cAAc,CAAC,IAAI,UAAU,KAAK,YAAY,QAAQ;AAAA,MACzD,QAAQ;AAAA,MACR,KAAK,uBAAuB,EAAE;AAAA,MAC9B,MAAM;AAAA,IACR,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAwB,SAAY,WAAW,CAAC;AAAA,IACnF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,qBAAoB;AAAA,MAC7B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;", "names": []}