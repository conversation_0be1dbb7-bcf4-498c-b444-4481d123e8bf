import {Component, DestroyRef, inject, signal} from '@angular/core';
import {AlertService, LanguagePipe, LOADING} from '@ttwr-framework/ngx-main-visuals';
import {MatCard, MatCardContent} from '@angular/material/card';
import {CalendarComponent} from '@shared/components';
import {HolidayService} from '@proxy/hr/holidays';
import {MatDialog} from '@angular/material/dialog';
import {map, shareReplay, startWith, Subject, switchMap} from 'rxjs';
import {takeUntilDestroyed, toSignal} from '@angular/core/rxjs-interop';
import {requireAllOperator} from '@shared/functions';

@Component({
  selector: 'app-my-holidays',
  standalone: true,
  imports: [LanguagePipe, MatCard, MatCardContent, CalendarComponent],
  templateUrl: './holidays.component.html',
  styleUrl: './holidays.component.scss'
})
export class HolidaysComponent {
  private holiday = inject(HolidayService);

  private refreshSubject = new Subject<void>();

  private allHolidays$ = this.refreshSubject.pipe(
    startWith(null),
    switchMap(() => this.holiday.getList({
      maxResultCount: 1000,
    }).pipe(
      requireAllOperator(),
      shareReplay({
        bufferSize: 1,
        refCount: true,
      })
    ))
  );

  protected holidays = toSignal(this.allHolidays$.pipe(
    map(res => res.items),
  ));
}
