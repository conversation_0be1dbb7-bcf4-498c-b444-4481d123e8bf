import { Component, DestroyRef, inject } from '@angular/core';
import { AlertService, LOADING, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { Subject } from 'rxjs';
import { requireAllOperator } from '@shared';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { salaryStructures } from '../../salary-structures.model';
import { SalaryStructureService } from '@proxy/payroll/salary-structures';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-salary-structures',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `<ttwr-grid class="actions-end" [config]="config" />`,
})
export class SalaryStructuresIndexComponent {
  private salaryStructure = inject(SalaryStructureService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private loading = inject(LOADING);

  private refreshSubject = new Subject<void>();

  protected config = salaryStructures.grid({
    title: '::PayrollSalaryStructures',
    refreshSubject: this.refreshSubject,
    dataFunc: pagination => this.salaryStructure.getList({
      skipCount: pagination.pageSize * pagination.pageIndex,
      maxResultCount: pagination.pageSize,
    }).pipe(requireAllOperator()),
    actions: [
      {
        matIcon: 'add',
        tooltip: 'Add',
        delegateFunc: () => this.router.navigate(['create'], { relativeTo: this.route.parent }),
      }
    ],
    fieldActions: [
      {
        matIcon: 'edit',
        tooltip: 'Edit',
        delegateFunc: obj => this.router.navigate(['update', obj.id], { relativeTo: this.route.parent }),
      },
      {
        matIcon: 'delete',
        tooltip: 'Delete',
        color: 'warn',
        confirmation: {
          title: '::Warning',
          message: '::AreYouSureYouWantToDelete?',
        },
        delegateFunc: (obj) => {
          this.loading.set(true);

          this.salaryStructure.delete(obj.id).pipe(
            takeUntilDestroyed(this.destroyRef)
          ).subscribe(() => {
            this.refreshSubject.next();
            this.alert.success('::SuccessDelete');
          })
        }
      }
    ],
  });
}
