using Volo.Abp.Modularity;
using Elsa.EntityFrameworkCore.Extensions;
using Elsa.EntityFrameworkCore.Modules.Management;
using Elsa.EntityFrameworkCore.Modules.Runtime;
using Elsa.Extensions;
using ElsaWorkflows.Domain;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace ElsaWorkflows.EntityFrameworkCore.EntityFrameworkCore;

[DependsOn(
    typeof(ElsaWorkflowsDomainModule)
)]
public class ElsaWorkflowsEntityFrameworkCoreModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var configuration = context.Services.GetConfiguration();
        var managementConnectionString = configuration.GetConnectionString("ElsaManagement");
        var runtimeConnectionString = configuration.GetConnectionString("ElsaRuntime");

        context.Services.GoAddElsaConfiguration(elsa =>
        {
            elsa.UseWorkflowManagement(management =>
            {
                management.UseEntityFrameworkCore(options => { options.UsePostgreSql(managementConnectionString!); });
            });

            elsa.UseWorkflowRuntime(runtime =>
            {
                runtime.UseEntityFrameworkCore(options => { options.UsePostgreSql(runtimeConnectionString!); });
            });
        });

    }
}
