import type { CreationAuditedEntityDto } from '@abp/ng.core';
import type { AttendanceRequestState } from '../attendance-request-state.enum';

export interface AttendanceRequestDto extends CreationAuditedEntityDto<string> {
  checkIn?: string;
  checkOut?: string;
  state: AttendanceRequestState;
  fixAttendanceFrom?: string;
  fixAttendanceTo?: string;
}

export interface CreateUpdateAttendanceRequestDto {
  checkIn?: string;
  checkOut?: string;
  fixAttendanceFrom?: string;
  fixAttendanceTo?: string;
  attendanceId?: string;
}
