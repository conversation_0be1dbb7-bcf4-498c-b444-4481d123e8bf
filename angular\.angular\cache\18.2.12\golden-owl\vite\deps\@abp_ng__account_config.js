import {
  NAVIGATE_TO_MANAGE_PROFILE,
  RoutesService
} from "./chunk-VITQ7ATO.js";
import {
  Router
} from "./chunk-JS23NXQZ.js";
import "./chunk-F7YCCNPX.js";
import "./chunk-AABMUNXW.js";
import "./chunk-K46JBGQH.js";
import "./chunk-B4FFJ7GE.js";
import "./chunk-VTW5CIPD.js";
import {
  APP_INITIALIZER,
  Injector,
  NgModule,
  makeEnvironmentProviders,
  setClassMetadata,
  ɵɵdefineInjector,
  ɵɵdefineNgModule
} from "./chunk-7YC2NMXI.js";
import "./chunk-ILOBLWYU.js";
import "./chunk-ZTELYOIP.js";

// node_modules/@abp/ng.account/fesm2022/abp-ng.account-config.mjs
var ACCOUNT_ROUTE_PROVIDERS = [{
  provide: APP_INITIALIZER,
  useFactory: configureRoutes,
  deps: [RoutesService],
  multi: true
}];
function configureRoutes(routes) {
  return () => {
    routes.add([{
      path: void 0,
      name: "AbpAccount::Menu:Account",
      invisible: true,
      layout: "account",
      order: 1
    }, {
      path: "/account/login",
      name: "AbpAccount::Login",
      parentName: "AbpAccount::Menu:Account",
      order: 1
    }, {
      path: "/account/register",
      name: "AbpAccount::Register",
      parentName: "AbpAccount::Menu:Account",
      order: 2
    }, {
      path: "/account/manage",
      name: "AbpAccount::MyAccount",
      parentName: "AbpAccount::Menu:Account",
      layout: "application",
      order: 3
    }, {
      path: "/account/forgot-password",
      parentName: "AbpAccount::Menu:Account",
      name: "AbpAccount::ForgotPassword",
      invisible: true
    }, {
      path: "/account/reset-password",
      parentName: "AbpAccount::Menu:Account",
      name: "AbpAccount::ResetPassword",
      invisible: true
    }]);
  };
}
function navigateToManageProfileFactory(injector) {
  return () => {
    const router = injector.get(Router);
    const routes = injector.get(RoutesService);
    const {
      path
    } = routes.find(
      (item) => item.name === "AbpAccount::MyAccount"
      /* eAccountRouteNames.ManageProfile */
    );
    router.navigateByUrl(path);
  };
}
function provideAccountConfig() {
  return makeEnvironmentProviders([ACCOUNT_ROUTE_PROVIDERS, {
    provide: NAVIGATE_TO_MANAGE_PROFILE,
    useFactory: navigateToManageProfileFactory,
    deps: [Injector]
  }]);
}
var AccountConfigModule = class _AccountConfigModule {
  static forRoot() {
    return {
      ngModule: _AccountConfigModule,
      providers: [provideAccountConfig()]
    };
  }
  static {
    this.ɵfac = function AccountConfigModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AccountConfigModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _AccountConfigModule
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({});
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AccountConfigModule, [{
    type: NgModule
  }], null, null);
})();
export {
  ACCOUNT_ROUTE_PROVIDERS,
  AccountConfigModule,
  configureRoutes,
  navigateToManageProfileFactory,
  provideAccountConfig
};
//# sourceMappingURL=@abp_ng__account_config.js.map
