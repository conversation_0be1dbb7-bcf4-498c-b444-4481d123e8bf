import { Component } from '@angular/core';
import { RouterLink } from '@angular/router';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-not-found',
  standalone: true,
  imports: [MatButton, MatIcon, RouterLink, LanguagePipe],
  template: `
    <div>
      <mat-icon color="warn">error_outline</mat-icon>
      <h1>404</h1>
    </div>
    <h2>{{ "Page not found" | i18n }}</h2>
    <button routerLink="/" mat-raised-button>
      {{ "back to home" | i18n }}
      <mat-icon>home</mat-icon>
    </button>
  `,
  styles: `
    :host {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      height: calc(100vh - 178px);

      div {
        display: flex;
        align-items: center;
        gap: 2rem;

        mat-icon {
          transform: scale(3);
        }

        h1 {
          font-size: 3rem;
          font-weight: bold;
          margin: 0;
        }
      }

      h2 {
        font-size: 2rem;
        margin: 0;
      }

      button {
        margin-top: 2rem;
      }
    }
  `
})
export class NotFoundComponent {}
