import { arrayMap, fields, model } from '@ttwr-framework/ngx-main-visuals';
import { requireAllOperator } from '@shared';
import { map } from 'rxjs';
import { inject } from '@angular/core';
import { EmployeeService } from '@proxy/hr/employees';

export const payslips = () => {
  const employees = inject(EmployeeService);

  return model({
    id: fields.text(),
    employeeId: fields.selectFetch('single', () => employees.getList(
      { maxResultCount: 999 }, '00000000-0000-0000-0000-000000000000', undefined as any
    ).pipe(
      requireAllOperator(),
      map(res => res.items),
      arrayMap(employee => ({
        label: `${employee.name} ${employee.surname}`,
        value: employee.id,
      }))
    )),
    employeeFullName: fields.text(),
    startDate: fields.date(),
    endDate: fields.date(),
  })
}
