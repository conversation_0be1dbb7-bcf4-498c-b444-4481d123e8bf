import { Component, inject, signal } from '@angular/core';
import { LanguagePipe, pagedMap, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { notifications } from './notifications.model';
import { NotificationService } from '@proxy/notifications';
import { requireAllOperator } from '@shared';
import { map, startWith, switchMap, tap } from 'rxjs';
import { NotificationDto } from '@proxy/notifications/dtos';
import {
  MatCard,
  MatCardActions,
  MatCardAvatar,
  MatCardContent,
  MatCardHeader,
  MatCardTitle
} from '@angular/material/card';
import { MatFormField, MatLabel } from '@angular/material/form-field';
import { MatOption, MatSelect } from '@angular/material/select';
import { NonNullableFormBuilder, ReactiveFormsModule } from '@angular/forms';
import { MatIcon } from '@angular/material/icon';
import humanizeDuration from 'humanize-duration';
import { LocalizationService } from '@abp/ng.core';
import { MatRipple } from '@angular/material/core';

@Component({
  selector: 'app-notifications',
  standalone: true,
  imports: [
    TtwrGridComponent,
    LanguagePipe,
    MatCard,
    MatFormField,
    MatSelect,
    MatLabel,
    MatOption,
    ReactiveFormsModule,
    MatCardHeader,
    MatCardAvatar,
    MatIcon,
    MatCardTitle,
    MatCardContent,
    MatCardActions,
    MatRipple,
  ],
  templateUrl: './notifications.component.html',
  styleUrl: './notifications.component.scss',
})
export class NotificationsComponent {
  private notification = inject(NotificationService);
  private localization = inject(LocalizationService);
  private fb = inject(NonNullableFormBuilder);

  protected notificationStatusFormControl = this.fb.control<NotificationStatus>('all');
  protected currentNotifications = signal<NotificationDto[]>([]);

  protected config = notifications.grid({
    title: '::GoldenOwl:Notifications',
    dataFunc: pagination => this.notificationStatusFormControl.valueChanges.pipe(
      startWith('all'),
      map(status => status === 'all' ? undefined : status === 'seen'),
      switchMap(isSeen => this.notification.getList({
        maxResultCount: pagination.pageSize,
        skipCount: pagination.pageSize * pagination.pageIndex,
        isSeen,
      })),
      requireAllOperator(),
      pagedMap(notification => ({
        ...notification,
        creationTime: humanizeDuration(
          new Date().getTime() - new Date(notification.creationTime as string).getTime(),
          {
            round: true,
            largest: 1,
            language: this.localization.currentLang,
            digitReplacements: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'],
          }
        ),
      })),
      tap(res => this.currentNotifications.set(res.items))
    )
  });

  protected markAsSeen(id: string) {
    this.notification.markAsSeen(id).subscribe();

    this.currentNotifications.update(
      pre => pre.map(notification => notification.id === id
        ? { ...notification, isSeen: true, }
        : notification
      )
    );
  }
}

type NotificationStatus = 'all' | 'seen' | 'not-seen';
