import { Component, DestroyRef, inject, signal, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';
import {
  AlertService,
  LanguagePipe,
  LOADING,
  requiredValidator,
  TtwrFormComponent
} from '@ttwr-framework/ngx-main-visuals';
import { finalize, of, switchMap } from 'rxjs';
import { contracts } from '../../contracts.model';
import { MatCard, MatCardContent } from '@angular/material/card';
import { DateRangeCustomInputComponent, extractTwoDates, ExtraFieldsFormComponent } from '@shared';
import { ContractService, WorkEntrySource } from '@proxy/hr/contracts';
import { EntityType } from '@proxy/extra-field-definitions';
import { ExtraFieldValueService } from '@proxy/extra-field-values';

@Component({
  selector: 'app-contracts-create',
  standalone: true,
  imports: [TtwrFormComponent, MatCardContent, Mat<PERSON>ard, LanguagePipe, ExtraFieldsFormComponent],
  templateUrl: './contracts-create.component.html',
  styleUrl: './contracts-create.component.scss',
})
export class ContractsCreateComponent {
  private contracts = inject(ContractService);
  private extraFieldValue = inject(ExtraFieldValueService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);

  private extraFieldsForm = viewChild.required(ExtraFieldsFormComponent);

  private hiddenWorkingSchedules = signal(false);

  protected entityType = EntityType.Contract;

  protected config = contracts().exclude({
    state: true,
  }).form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        const extraFieldsGroup = this.extraFieldsForm().form;

        if (extraFieldsGroup.invalid) {
          extraFieldsGroup.markAllAsTouched();
          return;
        }

        this.loading.set(true);

        const { start, end } = extractTwoDates(body.dateRange);

        this.contracts
          .create({
            ...body,
            start,
            end,
            contractWorkingSchedules: body.workEntrySource === WorkEntrySource.Attendance
              ? null as any
              : body.contractWorkingSchedules.map(schedule => {
                const { start, end } = extractTwoDates(schedule.dateRange);

                return {
                  workingScheduleId: schedule.workingScheduleId,
                  from: start,
                  to: end,
                }
              })
          })
          .pipe(
            switchMap(id => {
              if (this.extraFieldsForm().extraFieldsValue.length !== 0) {
                return this.extraFieldValue.set(this.extraFieldsForm().extraFieldsValue, id);
              }
              return of(undefined);
            }),
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::CreatedSuccessfully');
            this.router.navigate(['.'], { relativeTo: this.route.parent });
          })
      },
    },
    fields: {
      dateRange: {
        validators: [requiredValidator],
        customInputComponent: DateRangeCustomInputComponent,
      },
      type: {
        label: '::Type',
      },
      jobTitleId: {
        search: true,
        label: '::GoldenOwl:JobTitle',
      },
      employeeId: {
        search: true,
        label: '::GoldenOwl:Employee',
      },
      salaryStructureId: {
        search: true,
        label: '::GoldenOwl:SalaryStructure'
      },
      workEntrySource: {
        label: '::Contract:WorkEntrySource',
        onChange: value => {
          if (value === null || value === undefined) return;

          this.hiddenWorkingSchedules.set(value === WorkEntrySource.Attendance);

          if (value === WorkEntrySource.Attendance) {
            this.config.fields.contractWorkingSchedules.control.disable();
          } else {
            this.config.fields.contractWorkingSchedules.control.enable();
          }
        }
      },
      contractWorkingSchedules: {
        singleName: '::WorkingSchedule',
        formTitle: '::WorkingSchedules',
        hiddenSignal: this.hiddenWorkingSchedules,
        fields: {
          workingScheduleId: {
            search: true,
            label: '::WorkingSchedule',
          },
          dateRange: {
            validators: [requiredValidator],
            customInputComponent: DateRangeCustomInputComponent,
          },
        },
      },
    },
  });
}
