import { Component, DestroyRef, inject } from '@angular/core';
import { LOADING, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { currency } from '../../currency.model';
import { CurrencyService } from '@proxy/fn/currencies';
import { requireAllOperator } from '@shared';
import { Subject } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-currency-index',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `<ttwr-grid [config]="config" />`,
})
export class CurrencyIndexComponent {
  private currency = inject(CurrencyService);
  private destroyRef = inject(DestroyRef);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private loading = inject(LOADING);

  private refreshSubject = new Subject<void>();

  protected config = currency.exclude({ decimalPlaces: true }).grid({
    initialPageSize: 10,
    title: '::GoldenOwl:CurrenciesAndExchangeRate',
    refreshSubject: this.refreshSubject,
    dataFunc: (pagination, _, filters) => {
      const name = filters.find(f => f.attribute === 'fullName')?.value;
      const isActive = filters.find(f => f.attribute === 'isActive')?.value;

      return this.currency.getList({
        maxResultCount: pagination.pageSize,
        skipCount: pagination.pageSize * pagination.pageIndex,
      }, name, isActive).pipe(
        requireAllOperator(),
      );
    },
    fields: {
      fullName: {
        nonSearchable: false,
        columnName: '::Currency:FullName',
      },
      isActive: {
        nonSearchable: false,
        columnName: '::Currency:IsActive',
      },
      code: {
        columnName: '::Currency:Code',
      },
      symbol: {
        columnName: '::Currency:Symbol',
      },
      unitLabel: {
        columnName: '::Currency:UnitLabel',
      },
      subUnitLabel: {
        columnName: '::Currency:SubUnitLabel',
      },
    },
    fieldActions: [
      {
        label: 'Enable',
        showFunc: ({ isActive }) => !isActive,
        delegateFunc: ({ code }) => this.changeActivation(code),
      },
      {
        label: 'Disable',
        showFunc: ({ isActive }) => isActive,
        delegateFunc: ({ code }) => this.changeActivation(code),
      },
      {
        label: 'View',
        matButtonType: 'flat',
        delegateFunc: ({ code }) => this.router.navigate([code], { relativeTo: this.route }),
      }
    ],
  });

  changeActivation(code: string) {
    this.loading.set(true);

    this.currency.changeActivation(code).pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(() => {
      this.loading.set(false);
      this.refreshSubject.next();
    });
  }
}
