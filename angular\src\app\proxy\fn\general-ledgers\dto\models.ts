
export interface BalancesDto {
  totalDebitBalance: number;
  totalCreditBalance: number;
  totalBalance: number;
}

export interface GeneralLedgerDto {
  accountId?: string;
  accountCode?: string;
  accountName?: string;
  currencyCode?: string;
  fromDate?: string;
  toDate?: string;
  openingBalance: BalancesDto;
  totalBalance: BalancesDto;
  generalLedgerItems: GeneralLedgerItemDto[];
}

export interface GeneralLedgerItemDto {
  entrySerialNumber?: string;
  entryItemDescreption?: string;
  entryDate?: string;
  totalDebit: number;
  totalCredit: number;
  balance: number;
}

export interface GetGeneralLedgerRequestDto {
  accountId?: string;
  fromDate?: string;
  toDate?: string;
  currencyCode?: string;
  justPostedEntries: boolean;
}
