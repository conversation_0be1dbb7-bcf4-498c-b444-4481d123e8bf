@if (notifications(); as notifications) {
  <button
    [matBadge]="newNotificationsCount()"
    [matBadgeHidden]="newNotificationsCount() === 0"
    matBadgePosition="above before"
    mat-icon-button
    [cdkMenuTriggerFor]="menu"
  >
    <mat-icon>notifications</mat-icon>
  </button>
  <ng-template #menu>
    <mat-card @menuAnimation cdkMenu>
      <mat-nav-list>
        <a
          mat-list-item
          routerLink="/notifications"
          cdkMenuItem
        >
          <mat-icon matListItemIcon>menu</mat-icon>
          {{ 'ViewAllNotifications' | i18n }}
        </a>
        <mat-divider />
        @for (notification of notifications; track notification.id) {
          <mat-list-item
            [class.not-seen]="!notification.isSeen && !clickedNotifications().includes(notification.id!)"
            (click)="!notification.isSeen && !clickedNotifications().includes(notification.id!) && markAsSeen(notification.id!)"
            cdkMenuItem
          >
            {{ notification.title }}
          </mat-list-item>
        }
      </mat-nav-list>
    </mat-card>
  </ng-template>
}
