import { Component, DestroyRef, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  MatDialogContent,
  MatDialogRef,
  MatDialogTitle,
} from '@angular/material/dialog';
import {
  AlertService,
  LanguagePipe,
  LOADING,
  requiredValidator,
  TtwrFormComponent,
  arrayMap
} from '@ttwr-framework/ngx-main-visuals';
import { finalize, map } from 'rxjs';
import { clearDate, requireAllOperator } from '@shared';
import { FinancialPeriodService } from '@proxy/fn/financial-periods';
import { fiscalPeriod } from '../../fiscal-period.model';

@Component({
  selector: 'app-fiscal-period-create-dialog',
  standalone: true,
  imports: [TtwrFormComponent, MatDialogTitle, MatDialogContent, LanguagePipe],
  template: `
    <h1 mat-dialog-title>
      {{ '::FiscalPeriod:AddPeriod' | i18n }}
    </h1>
    <mat-dialog-content>
      <ttwr-form [config]="config" />
    </mat-dialog-content>
  `,
  styles: `
   :host {
     --ttwr-form-gap: 1rem;
    --ttwr-form-grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
   }
 `,
})
export class FiscalPeriodCreateDialogComponent {
  private financialPeriodService = inject(FinancialPeriodService);
  private alert = inject(AlertService);
  private dialogRef = inject(MatDialogRef);
  private destroyRef = inject(DestroyRef);
  private loading = inject(LOADING);

  protected config = fiscalPeriod
    .exclude({
      financialPeriodState: true,
      lastAccountingAuditing: true,
    })
    .form({
      submitAction: {
        onSubmit: (body) => {
          this.loading.set(true);

          this.financialPeriodService
            .create({
              ...body,
              startDate: clearDate(body.startDate),
              endDate: clearDate(body.endDate),
            })
            .pipe(
              finalize(() => this.loading.set(false)),
              takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(() => {
              this.alert.success('::CreatedSuccessfully');
              this.dialogRef.close(true);
            });
        },
      },
    });
}
