{"name": "golden-owl", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@abp/ng.account": "~8.3.4", "@abp/ng.components": "~8.3.4", "@abp/ng.core": "~8.3.4", "@abp/ng.identity": "~8.3.4", "@abp/ng.oauth": "~8.3.4", "@abp/ng.setting-management": "~8.3.4", "@abp/ng.tenant-management": "~8.3.4", "@abp/ng.theme.basic": "~8.3.4", "@abp/ng.theme.shared": "~8.3.4", "@angular/animations": "^18.2.0", "@angular/cdk": "18", "@angular/common": "^18.2.0", "@angular/compiler": "^18.2.0", "@angular/core": "^18.2.0", "@angular/forms": "^18.2.0", "@angular/material": "18", "@angular/platform-browser": "^18.2.0", "@angular/platform-browser-dynamic": "^18.2.0", "@angular/router": "^18.2.0", "@ckeditor/ckeditor5-angular": "^7.0.1", "@ckeditor/ckeditor5-build-classic": "^41.4.2", "@ckeditor/ckeditor5-core": "^41.4.2", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.36.2", "@fullcalendar/angular": "^6.1.16", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@ng-bootstrap/ng-bootstrap": "^18.0.0", "@ttwr-framework/ngx-main-visuals": "24.18.21", "apextree": "^1.3.0", "codemirror": "^6.0.1", "humanize-duration": "^3.32.1", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.2", "jwt-decode": "^4.0.0", "ngx-mat-select-search": "^8.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.2/xlsx-0.20.2.tgz", "zone.js": "~0.14.10"}, "devDependencies": {"@abp/ng.schematics": "^9.0.2", "@angular-devkit/build-angular": "^18.2.12", "@angular/cli": "^18.2.12", "@angular/compiler-cli": "^18.2.0", "@types/humanize-duration": "^3.27.4", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.5.2"}}