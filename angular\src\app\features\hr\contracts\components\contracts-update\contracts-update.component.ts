import { Component, DestroyRef, inject, signal, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';
import {
  AlertService,
  LanguagePipe,
  LOADING,
  requiredValidator,
  TtwrFormComponent
} from '@ttwr-framework/ngx-main-visuals';
import { finalize, map, of, switchMap } from 'rxjs';
import { contracts } from '../../contracts.model';
import {
  combineTwoDates,
  DateRangeCustomInputComponent,
  extractTwoDates, ExtraFieldsFormComponent,
  requireAllOperator
} from '@shared';
import { MatCard, MatCardContent } from '@angular/material/card';
import { ContractService, WorkEntrySource } from '@proxy/hr/contracts';
import { EntityType } from '@proxy/extra-field-definitions';
import { ExtraFieldValueService } from '@proxy/extra-field-values';

@Component({
  selector: 'app-contracts-update',
  standalone: true,
  imports: [TtwrFormComponent, LanguagePipe, MatCard, MatCardContent, ExtraFieldsFormComponent],
  templateUrl: './contracts-update.component.html',
  styleUrl: './contracts-update.component.scss',
})
export class ContractsUpdateComponent {
  private contracts = inject(ContractService);
  private extraFieldValue = inject(ExtraFieldValueService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);

  private extraFieldsForm = viewChild.required(ExtraFieldsFormComponent);

  protected entityType = EntityType.Contract;
  protected entityId = this.route.snapshot.params['id'];

  private hiddenWorkingSchedules = signal(false);

  protected config = contracts().exclude({
    state: true,
  }).form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        const extraFieldsGroup = this.extraFieldsForm().form;

        if (extraFieldsGroup.invalid) {
          extraFieldsGroup.markAllAsTouched();
          return;
        }

        this.loading.set(true);

        const { start, end } = extractTwoDates(body.dateRange);

        this.contracts
          .update(this.route.snapshot.params['id'], {
            ...body,
            start,
            end,
            contractWorkingSchedules: body.workEntrySource === WorkEntrySource.Attendance
              ? null as any
              : body.contractWorkingSchedules.map(schedule => {
                const { start, end } = extractTwoDates(schedule.dateRange);

                return {
                  workingScheduleId: schedule.workingScheduleId,
                  from: start,
                  to: end,
                }
              })
          })
          .pipe(
            switchMap(() => {
              if (this.extraFieldsForm().extraFieldsValue.length !== 0) {
                return this.extraFieldValue.set(this.extraFieldsForm().extraFieldsValue, this.route.snapshot.params['id']);
              }
              return of(undefined);
            }),
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::UpdatedSuccessfully');
            this.router.navigate(['.'], { relativeTo: this.route.parent });
          })
      },
    },
    viewFunc: () => this.contracts.get(this.route.snapshot.params['id']).pipe(
      requireAllOperator(),
      map(res => ({
        ...res,
        dateRange: combineTwoDates(res.start, res.end),
        contractWorkingSchedules: res.contractWorkingSchedules.map(schedule => ({
          ...schedule,
          dateRange: combineTwoDates(schedule.from, schedule.to),
        }))
      })),
    ),
    fields: {
      dateRange: {
        validators: [requiredValidator],
        customInputComponent: DateRangeCustomInputComponent,
      },
      type: {
        label: '::Type',
      },
      jobTitleId: {
        search: true,
        label: '::GoldenOwl:JobTitle',
      },
      employeeId: {
        search: true,
        label: '::GoldenOwl:Employee',
      },
      salaryStructureId: {
        search: true,
        label: '::GoldenOwl:SalaryStructure'
      },
      workEntrySource: {
        label: '::Contract:WorkEntrySource',
        onChange: value => {
          if (value === null || value === undefined) return;

          this.hiddenWorkingSchedules.set(value === WorkEntrySource.Attendance);

          if (value === WorkEntrySource.Attendance) {
            this.config.fields.contractWorkingSchedules.control.disable();
          } else {
            this.config.fields.contractWorkingSchedules.control.enable();
          }
        }
      },
      contractWorkingSchedules: {
        singleName: '::WorkingSchedule',
        formTitle: '::WorkingSchedules',
        hiddenSignal: this.hiddenWorkingSchedules,
        fields: {
          dateRange: {
            validators: [requiredValidator],
            customInputComponent: DateRangeCustomInputComponent,
          },
          workingScheduleId: {
            search: true,
            label: '::WorkingSchedule',
          },
        },
      },
    },
  });
}
