import { ABP } from '@abp/ng.core';
import { Routes } from '@angular/router';
import { routes as salaryStructuresRoutes } from './salary-structures/salary-structures.routes';
import { routes as salaryRulesRoutes } from './salary-rules/salary-rules.routes';
import { SalaryRulesCategoriesComponent } from './salary-rules-categories/salary-rules-categories.component';
import { PayslipsComponent } from './payslips/payslips.component';

const payrollManagementRoute = '::PayrollSalaryStructureManagement';

export const routes: Routes = [
  {
    path: 'salary-structures',
    children: salaryStructuresRoutes,
  },
  {
    path: 'salary-rules',
    children: salaryRulesRoutes,
  },
  {
    path: 'salary-rules-categories',
    component: SalaryRulesCategoriesComponent,
  },
  {
    path: 'payslips',
    component: PayslipsComponent,
  }
];

export const abpRoutes: ABP.Route[] = [

  {
    name: payrollManagementRoute,
    iconClass: 'manage_search',
    order: 6,
  },
  {
    path: '/payroll-management/salary-structures',
    name: '::PayrollSalaryStructures',
    iconClass: 'align_horizontal_center',
    parentName: payrollManagementRoute,
    requiredPolicy: 'PR.SalaryStructure.Index',
  },
  {
    path: '/payroll-management/salary-rules',
    name: '::PayrollSalaryRules',
    iconClass: 'format_list_numbered',
    parentName: payrollManagementRoute,
    requiredPolicy: 'PR.SalaryRule.Index',
  },
  {
    path: '/payroll-management/salary-rules-categories',
    name: '::PayrollSalaryRuleCategories',
    iconClass: 'category',
    parentName: payrollManagementRoute,
    requiredPolicy: 'PR.SalaryRuleCategory.Index',
  },
  {
    path: '/payroll-management/payslips',
    name: '::Payslips',
    iconClass: 'local_activity',
    parentName: payrollManagementRoute,
    requiredPolicy: 'PR.Payslip.Index',
  }
]
