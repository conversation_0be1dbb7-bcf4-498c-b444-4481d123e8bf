﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Elsa.EntityFrameworkCore" Version="3.3.1" />
      <PackageReference Include="Elsa.EntityFrameworkCore.PostgreSql" Version="3.3.1" />
      <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.12" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.12">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\ElsaWorkflows.Domain\ElsaWorkflows.Domain.csproj" />
      <ProjectReference Include="..\GoldenOwl.Domain\GoldenOwl.Domain.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="src\**" />
    </ItemGroup>

    <ItemGroup>
      <EmbeddedResource Remove="src\**" />
    </ItemGroup>

    <ItemGroup>
      <None Remove="src\**" />
    </ItemGroup>

</Project>
