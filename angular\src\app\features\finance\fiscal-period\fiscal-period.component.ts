import { Component, DestroyRef, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatDialog } from '@angular/material/dialog';
import {
  FinancialPeriodService,
  FinancialPeriodState,
} from '@proxy/fn/financial-periods';
import { LanguageService, pagedMap, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { map, Subject } from 'rxjs';
import { fiscalPeriod } from './fiscal-period.model';
import { FiscalPeriodCreateDialogComponent } from './components/fiscal-period-create-dialog/fiscal-period-create-dialog.component';
import { FiscalPeriodUpdateDialogComponent } from './components/fiscal-period-update-dialog/fiscal-period-update-dialog.component';
import { FiscalPeriodAddAccountingAuditingDialogComponent } from './components/fiscal-period-add-acconting-auditing-dialog/fiscal-period-add-acconting-auditing-dialog';

@Component({
  selector: 'app-fiscal-period',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `<ttwr-grid [config]="config" />`,
})
export class FiscalPeriodComponent {
  private financialPeriodService = inject(FinancialPeriodService);
  private dialog = inject(MatDialog);
  private destroyRef = inject(DestroyRef);
  private refreshSubject = new Subject<void>();
  private shouldHideCreateButton = signal(false);
  private language = inject(LanguageService);

  protected config = fiscalPeriod.grid({
    title: '::GoldenOwl:FiscalPeriod',
    refreshSubject: this.refreshSubject,

    dataFunc: (pagination, _, filters) => {
      const state = filters.find(
        (f) => f.attribute === 'financialPeriodState'
      )?.value;

      return this.financialPeriodService
        .getList(
          {
            maxResultCount: +pagination.pageSize,
            skipCount: +(pagination.pageSize * pagination.pageIndex),
          },
          state
        )
        .pipe(
          map((res) => {
            const hasOpened: boolean =
              res.items?.some(
                (p) => p.financialPeriodState === FinancialPeriodState.Opened
              ) ?? false;
            this.shouldHideCreateButton.set(hasOpened);
            return res;
          })
        ) as any;
    },

    fields: {
      financialPeriodState: {
        columnName: '::FiscalPeriod:State',
        nonSearchable: false,
        displayCell: cell => this.language.translate(`::FinancialPeriodState.${FinancialPeriodState[cell]}`),
      },
    },

    actions: [
      {
        matIcon: 'add',
        tooltip: 'Add',
        hiddenSignal: this.shouldHideCreateButton,
        delegateFunc: () => {
          const ref = this.dialog.open(FiscalPeriodCreateDialogComponent, {
            width: '100%',
            maxWidth: '700px',
          });
          ref
            .afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe((res) => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        },
      },
    ],
    fieldActions: [
      {
        matIcon: 'edit',
        tooltip: 'Edit',
        showFunc: (obj) =>
          obj.financialPeriodState === FinancialPeriodState.Opened,
        delegateFunc: (obj) => {
          const ref = this.dialog.open(FiscalPeriodUpdateDialogComponent, {
            width: '100%',
            maxWidth: '700px',
            data: obj,
          });
          ref
            .afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe((res) => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        },
      },
      {
        matIcon: 'add_alarm',
        color: 'info',
        tooltip: 'AddAccountingAuditing',
        delegateFunc: (obj) => {
          const ref = this.dialog.open(FiscalPeriodAddAccountingAuditingDialogComponent, {
            width: '100%',
            maxWidth: '500px',
            data: { id: obj.id },
          });
          ref
            .afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe((res) => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        },
      },
    ],
  });
}
