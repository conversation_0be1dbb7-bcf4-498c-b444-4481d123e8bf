{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/de.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    v = val.toString().replace(/^[^.]*\\.?/, '').length;\n  if (i === 1 && v === 0) return 1;\n  return 5;\n}\nexport default [\"de\", [[\"AM\", \"PM\"], u, u], u, [[\"S\", \"M\", \"D\", \"M\", \"D\", \"F\", \"S\"], [\"So.\", \"Mo.\", \"Di.\", \"Mi.\", \"Do.\", \"Fr.\", \"Sa.\"], [\"Sonntag\", \"Montag\", \"Dienstag\", \"Mittwoch\", \"Donnerstag\", \"Freitag\", \"Samstag\"], [\"So.\", \"<PERSON><PERSON>\", \"<PERSON>.\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>.\", \"Sa.\"]], [[\"S\", \"M\", \"D\", \"M\", \"D\", \"F\", \"S\"], [\"So\", \"<PERSON>\", \"<PERSON>\", \"Mi\", \"<PERSON>\", \"Fr\", \"Sa\"], [\"Sonntag\", \"Montag\", \"Dienstag\", \"Mittwoch\", \"Donnerstag\", \"Freitag\", \"Samstag\"], [\"So.\", \"Mo.\", \"Di.\", \"Mi.\", \"Do.\", \"Fr.\", \"Sa.\"]], [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"Jan.\", \"Feb.\", \"März\", \"Apr.\", \"Mai\", \"Juni\", \"Juli\", \"Aug.\", \"Sept.\", \"Okt.\", \"Nov.\", \"Dez.\"], [\"Januar\", \"Februar\", \"März\", \"April\", \"Mai\", \"Juni\", \"Juli\", \"August\", \"September\", \"Oktober\", \"November\", \"Dezember\"]], [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"Jan\", \"Feb\", \"Mär\", \"Apr\", \"Mai\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Okt\", \"Nov\", \"Dez\"], [\"Januar\", \"Februar\", \"März\", \"April\", \"Mai\", \"Juni\", \"Juli\", \"August\", \"September\", \"Oktober\", \"November\", \"Dezember\"]], [[\"v. Chr.\", \"n. Chr.\"], u, u], 1, [6, 0], [\"dd.MM.yy\", \"dd.MM.y\", \"d. MMMM y\", \"EEEE, d. MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1}, {0}\", u, \"{1} 'um' {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"·\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"Euro\", {\n  \"ATS\": [\"öS\"],\n  \"AUD\": [\"AU$\", \"$\"],\n  \"BGM\": [\"BGK\"],\n  \"BGO\": [\"BGJ\"],\n  \"BYN\": [u, \"р.\"],\n  \"CUC\": [u, \"Cub$\"],\n  \"DEM\": [\"DM\"],\n  \"FKP\": [u, \"Fl£\"],\n  \"GHS\": [u, \"₵\"],\n  \"GNF\": [u, \"F.G.\"],\n  \"KMF\": [u, \"FC\"],\n  \"PHP\": [u, \"₱\"],\n  \"RON\": [u, \"L\"],\n  \"RUR\": [u, \"р.\"],\n  \"RWF\": [u, \"F.Rw\"],\n  \"SYP\": [],\n  \"THB\": [\"฿\"],\n  \"TWD\": [\"NT$\"],\n  \"XXX\": [],\n  \"ZMW\": [u, \"K\"]\n}, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACnB,QAAM,IAAI,KACR,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,GAC5B,IAAI,IAAI,SAAS,EAAE,QAAQ,aAAa,EAAE,EAAE;AAC9C,MAAI,MAAM,KAAK,MAAM,EAAG,QAAO;AAC/B,SAAO;AACT;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,CAAC,WAAW,UAAU,YAAY,YAAY,cAAc,WAAW,SAAS,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,GAAG,CAAC,WAAW,UAAU,YAAY,YAAY,cAAc,WAAW,SAAS,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,SAAS,QAAQ,QAAQ,MAAM,GAAG,CAAC,UAAU,WAAW,QAAQ,SAAS,OAAO,QAAQ,QAAQ,UAAU,aAAa,WAAW,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,CAAC,UAAU,WAAW,QAAQ,SAAS,OAAO,QAAQ,QAAQ,UAAU,aAAa,WAAW,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,SAAS,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,YAAY,WAAW,aAAa,iBAAiB,GAAG,CAAC,SAAS,YAAY,cAAc,eAAe,GAAG,CAAC,YAAY,GAAG,gBAAgB,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,WAAW,cAAc,KAAK,GAAG,OAAO,KAAK,QAAQ;AAAA,EACj1C,OAAO,CAAC,IAAI;AAAA,EACZ,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,GAAG,MAAM;AAAA,EACjB,OAAO,CAAC,IAAI;AAAA,EACZ,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,MAAM;AAAA,EACjB,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,GAAG,MAAM;AAAA,EACjB,OAAO,CAAC;AAAA,EACR,OAAO,CAAC,GAAG;AAAA,EACX,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC;AAAA,EACR,OAAO,CAAC,GAAG,GAAG;AAChB,GAAG,OAAO,MAAM;", "names": []}