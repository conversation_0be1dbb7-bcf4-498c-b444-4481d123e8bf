{"version": 3, "sources": ["../../../../../../node_modules/@abp/ng.tenant-management/fesm2022/abp-ng.tenant-management-config.mjs"], "sourcesContent": ["import { RoutesService } from '@abp/ng.core';\nimport * as i0 from '@angular/core';\nimport { APP_INITIALIZER, makeEnvironmentProviders, NgModule } from '@angular/core';\nconst TENANT_MANAGEMENT_ROUTE_PROVIDERS = [{\n  provide: APP_INITIALIZER,\n  useFactory: configureRoutes,\n  deps: [RoutesService],\n  multi: true\n}];\nfunction configureRoutes(routes) {\n  return () => {\n    routes.add([{\n      path: undefined,\n      name: \"AbpTenantManagement::Menu:TenantManagement\" /* eTenantManagementRouteNames.TenantManagement */,\n      parentName: \"AbpUiNavigation::Menu:Administration\" /* eThemeSharedRouteNames.Administration */,\n      requiredPolicy: \"AbpTenantManagement.Tenants\" /* eTenantManagementPolicyNames.TenantManagement */,\n      layout: \"application\" /* eLayoutType.application */,\n      iconClass: 'fa fa-users',\n      order: 2\n    }, {\n      path: '/tenant-management/tenants',\n      name: \"AbpTenantManagement::Tenants\" /* eTenantManagementRouteNames.Tenants */,\n      parentName: \"AbpTenantManagement::Menu:TenantManagement\" /* eTenantManagementRouteNames.TenantManagement */,\n      requiredPolicy: \"AbpTenantManagement.Tenants\" /* eTenantManagementPolicyNames.Tenants */,\n      order: 1\n    }]);\n  };\n}\nfunction provideTenantManagementConfig() {\n  return makeEnvironmentProviders([TENANT_MANAGEMENT_ROUTE_PROVIDERS]);\n}\n\n/**\n * @deprecated TenantManagementConfigModule is deprecated use `provideTenantManagementConfig` *function* instead.\n */\nclass TenantManagementConfigModule {\n  static forRoot() {\n    return {\n      ngModule: TenantManagementConfigModule,\n      providers: [provideTenantManagementConfig()]\n    };\n  }\n  static {\n    this.ɵfac = function TenantManagementConfigModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TenantManagementConfigModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TenantManagementConfigModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TenantManagementConfigModule, [{\n    type: NgModule\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TENANT_MANAGEMENT_ROUTE_PROVIDERS, TenantManagementConfigModule, configureRoutes, provideTenantManagementConfig };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAGA,IAAM,oCAAoC,CAAC;AAAA,EACzC,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM,CAAC,aAAa;AAAA,EACpB,OAAO;AACT,CAAC;AACD,SAAS,gBAAgB,QAAQ;AAC/B,SAAO,MAAM;AACX,WAAO,IAAI,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,OAAO;AAAA,IACT,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AACF;AACA,SAAS,gCAAgC;AACvC,SAAO,yBAAyB,CAAC,iCAAiC,CAAC;AACrE;AAKA,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,OAAO,UAAU;AACf,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,8BAA8B,CAAC;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qCAAqC,mBAAmB;AAC3E,aAAO,KAAK,qBAAqB,+BAA8B;AAAA,IACjE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}