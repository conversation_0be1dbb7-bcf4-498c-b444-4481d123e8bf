import { inject } from '@angular/core';
import { map } from 'rxjs';
import { arrayMap, fields, model, takeOptions } from '@ttwr-framework/ngx-main-visuals';
import { SalaryRuleCategoryService } from '@proxy/payroll/salary-rule-categories';
import { RuleConditionType, RuleValueType } from '@proxy/payroll/salary-rules';

export const salaryRules = () => {
  const salaryRuleCategory = inject(SalaryRuleCategoryService);

  return model({
    id: fields.text(),
    name: fields.text(),
    code: fields.text(),
    categoryId: fields.selectFetch('single', () => salaryRuleCategory.getList({
      maxResultCount: 999,
    }).pipe(
      map(res => res.items!),
      arrayMap(category => ({
        value: category.id!,
        label: category.name!,
      }))
    )),
    categoryName: fields.text(),
    conditionBasedOn: fields.select('single', takeOptions(RuleConditionType)),
    conditionCode: fields.text(),
    valueBasedOn: fields.select('single', [
      // we did this instead of using takeOptions to
      // change the label of RuleValueType.Code
      { label: 'FixedAmount', value: RuleValueType.FixedAmount },
      { label: 'Equation', value: RuleValueType.Code },
    ]),
    valueFixedAmount: fields.number(),
    valueCode: fields.text(),
    isActive: fields.boolean(),
    appearForEmployee: fields.boolean(),
  })
};
