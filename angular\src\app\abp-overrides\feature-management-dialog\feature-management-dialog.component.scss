mat-progress-spinner {
  width: 60px !important;
}

mat-dialog-content {
  display: flex;
  justify-content: center;
  gap: 2rem;
}

mat-nav-list {
  width: 250px;
  padding-top: 0;
}

mat-nav-list + div {
  flex-grow: 1;
  display: grid;
  align-content: start;

  small {
    margin-top: 0.5rem;
    line-height: var(--mat-form-field-subscript-text-line-height);
  }

  .feature-field {
    display: grid;

    &:not(:first-of-type) {
      margin-top: 1.25rem;
    }
  }
}

mat-divider {
  margin: 1rem 0;
}

[mat-list-item] {
  border: none;
  text-align: start;
}

h4 {
  font-size: 1.25rem;
  margin: 0;
}

.error-text {
  font-family: monospace;
  color: red;
  text-align: center;
  border: 1px solid;
  padding: 1rem;
}
