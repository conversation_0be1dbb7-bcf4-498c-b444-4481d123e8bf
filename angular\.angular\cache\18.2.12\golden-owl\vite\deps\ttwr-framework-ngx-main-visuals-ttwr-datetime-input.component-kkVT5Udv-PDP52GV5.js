import {
  <PERSON><PERSON><PERSON><PERSON>,
  Ttwr<PERSON><PERSON><PERSON>picker,
  TtwrDatetimepickerInput,
  TtwrDatetimepickerToggle,
  provideNativeDatetimeAdapter
} from "./chunk-OXWKWIRE.js";
import "./chunk-HBKFUEBR.js";
import "./chunk-ZWEW5UFO.js";
import "./chunk-RJHJ33EQ.js";
import {
  MatIconModule
} from "./chunk-QOBLUORG.js";
import "./chunk-ZLN54CLB.js";
import {
  MatInput,
  MatInputModule
} from "./chunk-S3X4XAAX.js";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  MatFormField,
  MatFormFieldModule,
  MatHint,
  MatLabel,
  MatSuffix
} from "./chunk-DTMWAZLO.js";
import "./chunk-SRYXMW6U.js";
import "./chunk-LIDDHHFO.js";
import {
  MatButtonModule
} from "./chunk-JEURQPSQ.js";
import "./chunk-VLOTRHQZ.js";
import "./chunk-ANPRWV6C.js";
import "./chunk-WYMEUPLA.js";
import "./chunk-OO6V37AA.js";
import "./chunk-TL4LEKBN.js";
import "./chunk-MP7XVVED.js";
import "./chunk-TNZJNDY5.js";
import "./chunk-KW6MAD7U.js";
import "./chunk-2MXHA5U4.js";
import "./chunk-DNW4SGAD.js";
import "./chunk-HL4RP4FA.js";
import "./chunk-F7YCCNPX.js";
import {
  ControlContainer,
  DefaultValueAccessor,
  FormControlDirective,
  FormGroupDirective,
  NgControlStatus,
  ReactiveFormsModule
} from "./chunk-AABMUNXW.js";
import "./chunk-K46JBGQH.js";
import "./chunk-B4FFJ7GE.js";
import "./chunk-VTW5CIPD.js";
import {
  Component,
  input,
  setClassMetadata,
  ɵɵProvidersFeature,
  ɵɵStandaloneFeature,
  ɵɵadvance,
  ɵɵconditional,
  ɵɵdefineComponent,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind1,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵreference,
  ɵɵrepeater,
  ɵɵrepeaterCreate,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-7YC2NMXI.js";
import "./chunk-ILOBLWYU.js";
import "./chunk-ZTELYOIP.js";

// node_modules/@ttwr-framework/ngx-main-visuals/fesm2022/ttwr-framework-ngx-main-visuals-ttwr-datetime-input.component-kkVT5Udv.mjs
var _forTrack0 = ($index, $item) => $item.name;
var _c0 = () => [];
function TtwrDatetimeInputComponent_For_10_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "span", 4);
    ɵɵtext(1);
    ɵɵpipe(2, "i18n");
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const validator_r1 = ctx.$implicit;
    const ctx_r1 = ɵɵnextContext();
    ɵɵproperty("hidden", !ctx_r1.field().control.hasError(validator_r1.name));
    ɵɵadvance();
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(2, 2, validator_r1.message), " ");
  }
}
function TtwrDatetimeInputComponent_Conditional_11_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "mat-hint");
    ɵɵtext(1);
    ɵɵpipe(2, "i18n");
    ɵɵelementEnd();
  }
  if (rf & 2) {
    ɵɵadvance();
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(2, 1, ctx), " ");
  }
}
var TtwrDatetimeInputComponent = class _TtwrDatetimeInputComponent {
  constructor() {
    this.field = input.required();
  }
  static {
    this.ɵfac = function TtwrDatetimeInputComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TtwrDatetimeInputComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TtwrDatetimeInputComponent,
      selectors: [["ttwr-datetime-input"]],
      inputs: {
        field: [1, "field"]
      },
      standalone: true,
      features: [ɵɵProvidersFeature([provideNativeDatetimeAdapter()], [{
        provide: ControlContainer,
        useExisting: FormGroupDirective
      }]), ɵɵStandaloneFeature],
      decls: 12,
      vars: 15,
      consts: [["datetimePicker", ""], [3, "twelvehour", "touchUi", "timeInput", "mode", "type"], ["matInput", "", 3, "ttwrDatetimepicker", "formControl", "readonly"], ["matSuffix", "", 3, "disabled", "for"], [3, "hidden"]],
      template: function TtwrDatetimeInputComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelementStart(0, "mat-form-field")(1, "mat-label");
          ɵɵtext(2);
          ɵɵpipe(3, "i18n");
          ɵɵelementEnd();
          ɵɵelement(4, "ttwr-datetimepicker", 1, 0)(6, "input", 2)(7, "ttwr-datetimepicker-toggle", 3);
          ɵɵelementStart(8, "mat-error");
          ɵɵrepeaterCreate(9, TtwrDatetimeInputComponent_For_10_Template, 3, 4, "span", 4, _forTrack0);
          ɵɵelementEnd();
          ɵɵtemplate(11, TtwrDatetimeInputComponent_Conditional_11_Template, 3, 3, "mat-hint");
          ɵɵelementEnd();
        }
        if (rf & 2) {
          let tmp_1_0;
          let tmp_2_0;
          let tmp_3_0;
          let tmp_4_0;
          let tmp_5_0;
          let tmp_6_0;
          let tmp_9_0;
          let tmp_10_0;
          let tmp_12_0;
          let tmp_13_0;
          const datetimePicker_r3 = ɵɵreference(5);
          ɵɵadvance(2);
          ɵɵtextInterpolate(ɵɵpipeBind1(3, 12, (tmp_1_0 = ctx.field().label) !== null && tmp_1_0 !== void 0 ? tmp_1_0 : ctx.field().name));
          ɵɵadvance(2);
          ɵɵproperty("twelvehour", (tmp_2_0 = ctx.field().twelveHour) !== null && tmp_2_0 !== void 0 ? tmp_2_0 : false)("touchUi", (tmp_3_0 = (tmp_3_0 = ctx.field().touchUISignal) == null ? null : tmp_3_0()) !== null && tmp_3_0 !== void 0 ? tmp_3_0 : false)("timeInput", (tmp_4_0 = ctx.field().timeInput) !== null && tmp_4_0 !== void 0 ? tmp_4_0 : true)("mode", (tmp_5_0 = ctx.field().mode) !== null && tmp_5_0 !== void 0 ? tmp_5_0 : "auto")("type", (tmp_6_0 = ctx.field().pickerType) !== null && tmp_6_0 !== void 0 ? tmp_6_0 : "datetime");
          ɵɵadvance(2);
          ɵɵproperty("ttwrDatetimepicker", datetimePicker_r3)("formControl", ctx.field().control)("readonly", (tmp_9_0 = ctx.field().readonlySignal) == null ? null : tmp_9_0());
          ɵɵadvance();
          ɵɵproperty("disabled", ((tmp_10_0 = ctx.field().disabledSignal) == null ? null : tmp_10_0()) || ((tmp_10_0 = ctx.field().readonlySignal) == null ? null : tmp_10_0()))("for", datetimePicker_r3);
          ɵɵadvance(2);
          ɵɵrepeater((tmp_12_0 = ctx.field().validators) !== null && tmp_12_0 !== void 0 ? tmp_12_0 : ɵɵpureFunction0(14, _c0));
          ɵɵadvance(2);
          ɵɵconditional((tmp_13_0 = ctx.field().hint) ? 11 : -1, tmp_13_0);
        }
      },
      dependencies: [LanguagePipe, MatFormFieldModule, MatFormField, MatLabel, MatHint, MatError, MatSuffix, MatInputModule, MatInput, MatButtonModule, MatIconModule, ReactiveFormsModule, DefaultValueAccessor, NgControlStatus, FormControlDirective, TtwrDatetimepicker, TtwrDatetimepickerInput, TtwrDatetimepickerToggle],
      styles: ["mat-form-field[_ngcontent-%COMP%]{width:100%}"]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TtwrDatetimeInputComponent, [{
    type: Component,
    args: [{
      selector: "ttwr-datetime-input",
      standalone: true,
      imports: [LanguagePipe, MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, ReactiveFormsModule, TtwrDatetimepicker, TtwrDatetimepickerInput, TtwrDatetimepickerToggle],
      viewProviders: [{
        provide: ControlContainer,
        useExisting: FormGroupDirective
      }],
      providers: [provideNativeDatetimeAdapter()],
      template: `<mat-form-field>
  <mat-label>{{ (field().label ?? field().name) | i18n }}</mat-label>
  <ttwr-datetimepicker
    [twelvehour]="field().twelveHour ?? false"
    [touchUi]="field().touchUISignal?.() ?? false"
    [timeInput]="field().timeInput ?? true"
    [mode]="field().mode ?? 'auto'"
    [type]="field().pickerType ?? 'datetime'"
    #datetimePicker
  />
  <input
    matInput
    [ttwrDatetimepicker]="datetimePicker"
    [formControl]="field().control"
    [readonly]="field().readonlySignal?.()"
  >
  <ttwr-datetimepicker-toggle
    [disabled]="field().disabledSignal?.() || field().readonlySignal?.()"
    [for]="datetimePicker"
    matSuffix
  />
  <mat-error>
    @for (validator of field().validators ?? []; track validator.name) {
      <span [hidden]="!field().control.hasError(validator.name)">
          {{ validator.message | i18n }}
        </span>
    }
  </mat-error>
  @if (field().hint; as hint) {
    <mat-hint>
      {{ hint | i18n }}
    </mat-hint>
  }
</mat-form-field>
`,
      styles: ["mat-form-field{width:100%}\n"]
    }]
  }], null, null);
})();
export {
  TtwrDatetimeInputComponent
};
//# sourceMappingURL=ttwr-framework-ngx-main-visuals-ttwr-datetime-input.component-kkVT5Udv-PDP52GV5.js.map
