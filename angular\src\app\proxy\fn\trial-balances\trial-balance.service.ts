import type { GetTrialBalanceRequestDto, TrialBalanceDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class TrialBalanceService {
  apiName = 'Default';
  

  postGetTrialBalanceReportByInput = (input: GetTrialBalanceRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, TrialBalanceDto>({
      method: 'POST',
      url: '/api/app/trial-balance/get-trial-balance-report',
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
