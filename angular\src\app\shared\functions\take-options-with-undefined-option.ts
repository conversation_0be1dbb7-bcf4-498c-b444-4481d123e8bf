import { Option, takeOptions } from '@ttwr-framework/ngx-main-visuals';
import { CashFlowType } from '@proxy/fn/accounts';

// the purpose of this function is to change label from
// 'Undefined' to '_Undefined' to not be translated as
// undefined (which its result is '').
export const takeOptionsWithUndefinedOption = <T>(enumObject: any): Option<T>[] => takeOptions(enumObject).map(option => option.value === enumObject['Undefined'] ? ({
  label: '_Undefined',
  value: CashFlowType.Undefined,
}) : option);
