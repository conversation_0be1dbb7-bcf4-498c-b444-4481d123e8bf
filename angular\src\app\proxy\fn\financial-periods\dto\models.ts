import type { FullAuditedEntityDto } from '@abp/ng.core';
import type { FinancialPeriodState } from '../financial-period-state.enum';

export interface AddAccountingAuditingDto {
  id?: string;
  accountingAuditingDate?: string;
}

export interface CloseFinancialPeriodDto {
  id?: string;
  nextPeriodCode?: string;
  nextPeriodEndDate?: string;
}

export interface CreateFinancialPeriodDto {
  code?: string;
  startDate?: string;
  endDate?: string;
}

export interface FinancialPeriodDto extends FullAuditedEntityDto<string> {
  startDate?: string;
  endDate?: string;
  lastAccountingAuditing?: string;
  financialPeriodState: FinancialPeriodState;
  code?: string;
}

export interface UpdateFinancialPeriodDto {
  id?: string;
  code?: string;
  endDate?: string;
}
