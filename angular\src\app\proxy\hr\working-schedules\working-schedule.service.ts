import type { CreateOrUpdateWorkingScheduleDto, WorkingScheduleDto, WorkingScheduleNameDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class WorkingScheduleService {
  apiName = 'Default';
  

  create = (workingScheduleDto: CreateOrUpdateWorkingScheduleDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/working-schedule',
      body: workingScheduleDto,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, WorkingScheduleDto>({
      method: 'GET',
      url: `/api/app/working-schedule/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<WorkingScheduleDto>>({
      method: 'GET',
      url: '/api/app/working-schedule',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getName = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, WorkingScheduleNameDto>({
      method: 'GET',
      url: `/api/app/working-schedule/${id}/name`,
    },
    { apiName: this.apiName,...config });
  

  update = (workingScheduleDto: CreateOrUpdateWorkingScheduleDto, id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: `/api/app/working-schedule/${id}`,
      body: workingScheduleDto,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
