using Elsa.Extensions;
using Elsa.Workflows;
using Elsa.Workflows.Activities.Flowchart.Attributes;
using Elsa.Workflows.Attributes;
using Elsa.Workflows.Models;
using ElsaWorkflows.GoldenOwl.Common.HR.Employee;

namespace ElsaWorkflows.Domain.Activities.HR.Employee;


[Activity("Employee", "Gets a list of employee ids for the employees with specified position, input: JobTitle, output: List<Guid> employeeIds")]
[FlowNode("Found", "NotFound")]
public class GetEmployeeByPosition : CodeActivity<List<Guid>>
{
    public Input<string> JobTitle { get; set; } = default!;

    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        var employeeElsaHelper = context.GetOrCreateService<IEmployeeElsaHelper>();

        var jobTitle = JobTitle.GetOrDefault(context);
        
        if (string.IsNullOrEmpty(jobTitle))
        {
            throw new InvalidOperationException("jobTitle is required");
        }
        
        var employeeId = await employeeElsaHelper.GetEmployeesByJobTitleAsync(jobTitle);
        context.SetResult(employeeId);

        if (employeeId.IsNullOrEmpty())
        {
            await context.CompleteActivityWithOutcomesAsync("NotFound");
        }
        else
        {
            await context.CompleteActivityWithOutcomesAsync("Found");
        }
    }
}