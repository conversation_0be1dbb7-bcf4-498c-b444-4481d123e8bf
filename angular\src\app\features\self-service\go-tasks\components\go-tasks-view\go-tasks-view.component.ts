import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  signal,
} from '@angular/core';
import {
  AlertService,
  LanguagePipe,
  LOADING,
  TtwrViewComponent,
} from '@ttwr-framework/ngx-main-visuals';
import { ActivatedRoute } from '@angular/router';
import { requireAllOperator } from '@shared';
import { MatCard, MatCardContent } from '@angular/material/card';
import { MatDivider } from '@angular/material/divider';
import {
  BehaviorSubject,
  filter,
  finalize,
  map,
  Subject,
  switchMap,
  tap,
} from 'rxjs';
import { GoTaskService, GoTaskState } from '@proxy/go-tasks';
import { LocalizationService } from '@abp/ng.core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatButton } from '@angular/material/button';
import { AsyncPipe } from '@angular/common';
import { CommonActionsService } from '@shared/services/common-actions/common-actions.service';
import { request, task } from '../../go-tasks.model';
import { AttendanceRequestService } from '@proxy/self-service/attendance-requests';

@Component({
  selector: 'app-go-tasks-view',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    TtwrViewComponent,
    MatCard,
    MatCardContent,
    LanguagePipe,
    MatDivider,
    MatButton,
    AsyncPipe,
  ],
  templateUrl: `./go-tasks-view.component.html`,
  styles: `
    :host {
      --ttwr-view-field-label-width: 9.4rem;
      --ttwr-view-grid-template-columns: 1fr 1fr;
    }
  `,
})
export class GoTasksViewComponent {
  private task = inject(GoTaskService);
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);
  private localizationService = inject(LocalizationService);
  protected taskSubject = new BehaviorSubject<Task | null>(null);
  protected GoTaskState = GoTaskState;
  private commonActions = inject(CommonActionsService);
  private refreshSubject = new Subject<void>();
  private attendanceRequestService = inject(AttendanceRequestService);

  protected viewConfig = task.view({
    viewFunc: () =>
      this.task.getGoTask(this.route.snapshot.params['id']).pipe(
        requireAllOperator(),
        tap((taskData) => {
          this.taskSubject.next(taskData);
        })
      ) as any,
    fields: {
      creationTime: {
        label: '::IssuedOn',
      },
      name: {
        label: '::Name',
      },
      state: {
        label: '::State',
        displayField: (state: string | number) => {
          return this.getLocalizedState(state);
        },
      },

      bindingModelId: {
        hiddenSignal: signal(true),
      },
    },
  });

  protected viewRequestConfig = request.view({
    viewFunc: () =>
      this.taskSubject.pipe(
        filter((taskData) => !!taskData?.bindingModelId),
        switchMap((taskData) =>
          this.attendanceRequestService.getRequest(taskData!.bindingModelId!)
        ),
        map((data) => ({
          checkIn: data?.checkIn || '',
          checkOut: data?.checkOut || '',
          fixAttendanceFrom: data?.fixAttendanceFrom || '',
          fixAttendanceTo: data?.fixAttendanceTo || '',
        }))
      ),
    fields: {
      checkIn: { label: '::GoldenOwl:CheckIn' },
      checkOut: { label: '::GoldenOwl:CheckOut' },
      fixAttendanceFrom: { label: '::GoldenOwl:FixAttendanceFrom' },
      fixAttendanceTo: { label: '::GoldenOwl:FixAttendanceTo' },
    },
  });

  private getLocalizedState(state: string | number): string {
    switch (state) {
      case GoTaskState.Open.valueOf():
        return this.localizationService.instant('::unfinished');
      case GoTaskState.Closed.valueOf():
        return this.localizationService.instant('::finished');
      default:
        return 'Unknown';
    }
  }

  public approveTask() {
    this.taskSubject
      .pipe(
        map((task) => task?.id),
        tap(() => this.loading.set(true)),
        switchMap((taskId) => this.task.approveGoTaskByGoTaskId(taskId!)),
        finalize(() => this.loading.set(false)),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(() => {
        this.refreshSubject.next();
        this.alert.success('::SuccessApprove');
      });
  }

  public rejectTask() {
    this.commonActions
      .confirmationAction('::Warning', '::ConfirmRejectTask')
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((confirmed) => {
        if (!confirmed) {
          return;
        }
        this.taskSubject
          .pipe(
            map((task) => task?.id),
            tap(() => this.loading.set(true)),
            switchMap((taskId) => this.task.rejectGoTaskByGoTaskId(taskId!)),
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef)
          )
          .subscribe(() => {
            this.refreshSubject.next();
            this.alert.success('::SuccessReject');
          });
      });
  }

  public goBack() {
    history.back();
  }
}

export interface Task {
  id: string;
  name: string;
  state: GoTaskState;
  creationTime: any;
  bindingModelId?: any;
}


