using Elsa.Expressions.Helpers;
using Elsa.Extensions;
using Elsa.Workflows;
using Elsa.Workflows.Activities.Flowchart.Attributes;
using Elsa.Workflows.Attributes;

namespace ElsaWorkflows.Domain.Activities;

[Activity("Activity", "Gets the first item from the previous List, input: List<object>, output: object")]
[FlowNode("Pass", "EmptyList")]
public class GetFirstFromList : CodeActivity<Guid>
{
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        var input = context.GetLastResult();
        
        if (input is null)
        {
            throw new InvalidOperationException("The last result is null or invalid");
        }
        
        var list = input.ConvertTo<List<object>>();
        if (list is null)
        {
            throw new InvalidOperationException("the previous result is not a list");
        }
        
        var result = list.FirstOrDefault();
        context.SetResult(result);

        if (result is null)
        {
            await context.CompleteActivityWithOutcomesAsync("NoUser");
        }
        else
        {
            await context.CompleteActivityWithOutcomesAsync("Pass");
        }
    }
}