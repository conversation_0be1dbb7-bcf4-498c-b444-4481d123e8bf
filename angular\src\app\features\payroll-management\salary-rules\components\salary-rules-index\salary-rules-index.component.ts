import { Component, DestroyRef, inject } from '@angular/core';
import { LOADING, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { SalaryRuleService } from '@proxy/payroll/salary-rules';
import { salaryRules } from '../../salary-rules.model';
import { requireAllOperator } from '@shared';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-salary-rules-index',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `<ttwr-grid class="actions-end" [config]="config" />`,
})
export class SalaryRulesIndexComponent {
  private salaryRule = inject(SalaryRuleService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private loading = inject(LOADING);

  private refreshSubject = new Subject<void>();

  protected config = salaryRules().select({
    id: true,
    name: true,
    code: true,
    isActive: true,
    categoryName: true,
  }).grid({
    title: '::PayrollSalaryRules',
    refreshSubject: this.refreshSubject,
    dataFunc: pagination => this.salaryRule.getList({
      maxResultCount: pagination.pageSize,
      skipCount: pagination.pageSize * pagination.pageIndex,
    }).pipe(requireAllOperator()),
    fields: {
      code: {
        columnName: '::Code',
      },
      isActive: {
        columnName: '::GoldenOwl:IsActive',
      },
      categoryName: {
        columnName: '::GoldenOwl:Category',
      },
    },
    actions: [
      {
        matIcon: 'add',
        tooltip: 'Add',
        delegateFunc: () => this.router.navigate(['create'], { relativeTo: this.route.parent }),
      },
    ],
    fieldActions: [
      {
        matIcon: 'edit',
        tooltip: 'Edit',
        delegateFunc: obj => this.router.navigate(['update', obj.id], { relativeTo: this.route.parent }),
      },
      {
        matIcon: 'delete',
        tooltip: 'Delete',
        color: 'warn',
        confirmation: {
          title: '::Warning',
          message: '::AreYouSureYouWantToDelete?',
        },
        delegateFunc: obj => {
          this.loading.set(true);

          this.salaryRule.delete(obj.id).pipe(
            takeUntilDestroyed(this.destroyRef),
          ).subscribe(() => {
            this.refreshSubject.next();
            this.loading.set(false);
          });
        },
      }
    ],
  });
}
