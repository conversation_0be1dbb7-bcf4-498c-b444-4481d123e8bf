import { Component, DestroyRef, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';
import { AlertService, LanguagePipe, LOADING } from '@ttwr-framework/ngx-main-visuals';
import { finalize } from 'rxjs';
import { MatCard, MatCardActions, MatCardContent } from '@angular/material/card';
import { WorkingScheduleService } from '@proxy/hr/working-schedules';
import { isoToTime, requireAllOperator, timeToIso } from '@shared';
import { MatError, MatFormField, MatLabel } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { WorkingHoursGridFormComponent } from '../working-hours-grid-form/working-hours-grid-form.component';
import { NonNullableFormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { WorkingHoursDto } from '@proxy/hr/working-schedules/dto';
import { MatButton } from '@angular/material/button';

@Component({
  selector: 'app-working-schedules-update',
  standalone: true,
  imports: [LanguagePipe, MatCard, MatCardContent, ReactiveFormsModule, MatError, MatFormField, MatInput, MatLabel, WorkingHoursGridFormComponent, MatButton, MatCardActions],
  templateUrl: './working-schedules-update.component.html',
  styles: `
    mat-form-field {
      min-width: 400px;
    }
  `,
})
export class WorkingSchedulesUpdateComponent {
  private workingSchedule = inject(WorkingScheduleService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private fb = inject(NonNullableFormBuilder);
  private loading = inject(LOADING);

  protected nameControl = this.fb.control('', [Validators.required]);

  protected lastId = signal(0);
  protected workingHours = signal<(WorkingHoursDto & { id: number })[]>([]);

  constructor() {
    this.workingSchedule
      .get(this.route.snapshot.params['id'])
      .pipe(
        requireAllOperator(),
        takeUntilDestroyed(),
      )
      .subscribe(workingSchedule => {
        this.lastId.set(workingSchedule.workingHours.length)

        this.nameControl.setValue(workingSchedule.name);
        this.workingHours.set(
          workingSchedule.workingHours.map((workingHour, index) => ({
            day: workingHour.day,
            from: timeToIso(workingHour.from),
            to: timeToIso(workingHour.to),
            expectedAttendance: timeToIso(workingHour.expectedAttendance),
            id: index,
          })),
        );
      })
  }

  submit() {
    if (this.nameControl.invalid) {
      this.nameControl.markAsTouched();
      return;
    }

    if (this.workingHours().length === 0) {
      this.alert.error('::GoldenOwl:MustHaveAtLeastOneWorkingHour')
      return;
    }

    this.loading.set(true);

    this.workingSchedule
      .update({
        name: this.nameControl.value,
        workingHours: this.workingHours().map(workingHour => ({
          day: workingHour.day,
          from: isoToTime(workingHour.from!),
          to: isoToTime(workingHour.to!),
          expectedAttendance: isoToTime(workingHour.expectedAttendance!),
        })),
      }, this.route.snapshot.params['id'])
      .pipe(
        finalize(() => this.loading.set(false)),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(() => {
        this.alert.success('::UpdatedSuccessfully');
        this.router.navigate(['.'], { relativeTo: this.route.parent });
      })
  }
}
