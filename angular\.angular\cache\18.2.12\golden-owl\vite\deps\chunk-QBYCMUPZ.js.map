{"version": 3, "sources": ["../../../../../../node_modules/@marijn/find-cluster-break/src/index.js", "../../../../../../node_modules/@codemirror/state/dist/index.js"], "sourcesContent": ["// These are filled with ranges (rangeFrom[i] up to but not including\n// rangeTo[i]) of code points that count as extending characters.\nlet rangeFrom = [],\n  rangeTo = [];\n(() => {\n  // Compressed representation of the Grapheme_Cluster_Break=Extend\n  // information from\n  // http://www.unicode.org/Public/16.0.0/ucd/auxiliary/GraphemeBreakProperty.txt.\n  // Each pair of elements represents a range, as an offet from the\n  // previous range and a length. Numbers are in base-36, with the empty\n  // string being a shorthand for 1.\n  let numbers = \"lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o\".split(\",\").map(s => s ? parseInt(s, 36) : 1);\n  for (let i = 0, n = 0; i < numbers.length; i++) (i % 2 ? rangeTo : rangeFrom).push(n = n + numbers[i]);\n})();\nexport function isExtendingChar(code) {\n  if (code < 768) return false;\n  for (let from = 0, to = rangeFrom.length;;) {\n    let mid = from + to >> 1;\n    if (code < rangeFrom[mid]) to = mid;else if (code >= rangeTo[mid]) from = mid + 1;else return true;\n    if (from == to) return false;\n  }\n}\nfunction isRegionalIndicator(code) {\n  return code >= 0x1F1E6 && code <= 0x1F1FF;\n}\nfunction check(code) {\n  for (let i = 0; i < rangeFrom.length; i++) {\n    if (rangeTo[i] > code) return rangeFrom[i] <= code;\n  }\n  return false;\n}\nconst ZWJ = 0x200d;\nexport function findClusterBreak(str, pos, forward = true, includeExtending = true) {\n  return (forward ? nextClusterBreak : prevClusterBreak)(str, pos, includeExtending);\n}\nfunction nextClusterBreak(str, pos, includeExtending) {\n  if (pos == str.length) return pos;\n  // If pos is in the middle of a surrogate pair, move to its start\n  if (pos && surrogateLow(str.charCodeAt(pos)) && surrogateHigh(str.charCodeAt(pos - 1))) pos--;\n  let prev = codePointAt(str, pos);\n  pos += codePointSize(prev);\n  while (pos < str.length) {\n    let next = codePointAt(str, pos);\n    if (prev == ZWJ || next == ZWJ || includeExtending && isExtendingChar(next)) {\n      pos += codePointSize(next);\n      prev = next;\n    } else if (isRegionalIndicator(next)) {\n      let countBefore = 0,\n        i = pos - 2;\n      while (i >= 0 && isRegionalIndicator(codePointAt(str, i))) {\n        countBefore++;\n        i -= 2;\n      }\n      if (countBefore % 2 == 0) break;else pos += 2;\n    } else {\n      break;\n    }\n  }\n  return pos;\n}\nfunction prevClusterBreak(str, pos, includeExtending) {\n  while (pos > 0) {\n    let found = nextClusterBreak(str, pos - 2, includeExtending);\n    if (found < pos) return found;\n    pos--;\n  }\n  return 0;\n}\nfunction codePointAt(str, pos) {\n  let code0 = str.charCodeAt(pos);\n  if (!surrogateHigh(code0) || pos + 1 == str.length) return code0;\n  let code1 = str.charCodeAt(pos + 1);\n  if (!surrogateLow(code1)) return code0;\n  return (code0 - 0xd800 << 10) + (code1 - 0xdc00) + 0x10000;\n}\nfunction surrogateLow(ch) {\n  return ch >= 0xDC00 && ch < 0xE000;\n}\nfunction surrogateHigh(ch) {\n  return ch >= 0xD800 && ch < 0xDC00;\n}\nfunction codePointSize(code) {\n  return code < 0x10000 ? 1 : 2;\n}", "import { find<PERSON>lusterBreak as findClusterBreak$1 } from '@marijn/find-cluster-break';\n\n/**\nThe data structure for documents. @nonabstract\n*/\nclass Text {\n  /**\n  Get the line description around the given position.\n  */\n  lineAt(pos) {\n    if (pos < 0 || pos > this.length) throw new RangeError(`Invalid position ${pos} in document of length ${this.length}`);\n    return this.lineInner(pos, false, 1, 0);\n  }\n  /**\n  Get the description for the given (1-based) line number.\n  */\n  line(n) {\n    if (n < 1 || n > this.lines) throw new RangeError(`Invalid line number ${n} in ${this.lines}-line document`);\n    return this.lineInner(n, true, 1, 0);\n  }\n  /**\n  Replace a range of the text with the given content.\n  */\n  replace(from, to, text) {\n    [from, to] = clip(this, from, to);\n    let parts = [];\n    this.decompose(0, from, parts, 2 /* Open.To */);\n    if (text.length) text.decompose(0, text.length, parts, 1 /* Open.From */ | 2 /* Open.To */);\n    this.decompose(to, this.length, parts, 1 /* Open.From */);\n    return TextNode.from(parts, this.length - (to - from) + text.length);\n  }\n  /**\n  Append another document to this one.\n  */\n  append(other) {\n    return this.replace(this.length, this.length, other);\n  }\n  /**\n  Retrieve the text between the given points.\n  */\n  slice(from, to = this.length) {\n    [from, to] = clip(this, from, to);\n    let parts = [];\n    this.decompose(from, to, parts, 0);\n    return TextNode.from(parts, to - from);\n  }\n  /**\n  Test whether this text is equal to another instance.\n  */\n  eq(other) {\n    if (other == this) return true;\n    if (other.length != this.length || other.lines != this.lines) return false;\n    let start = this.scanIdentical(other, 1),\n      end = this.length - this.scanIdentical(other, -1);\n    let a = new RawTextCursor(this),\n      b = new RawTextCursor(other);\n    for (let skip = start, pos = start;;) {\n      a.next(skip);\n      b.next(skip);\n      skip = 0;\n      if (a.lineBreak != b.lineBreak || a.done != b.done || a.value != b.value) return false;\n      pos += a.value.length;\n      if (a.done || pos >= end) return true;\n    }\n  }\n  /**\n  Iterate over the text. When `dir` is `-1`, iteration happens\n  from end to start. This will return lines and the breaks between\n  them as separate strings.\n  */\n  iter(dir = 1) {\n    return new RawTextCursor(this, dir);\n  }\n  /**\n  Iterate over a range of the text. When `from` > `to`, the\n  iterator will run in reverse.\n  */\n  iterRange(from, to = this.length) {\n    return new PartialTextCursor(this, from, to);\n  }\n  /**\n  Return a cursor that iterates over the given range of lines,\n  _without_ returning the line breaks between, and yielding empty\n  strings for empty lines.\n  \n  When `from` and `to` are given, they should be 1-based line numbers.\n  */\n  iterLines(from, to) {\n    let inner;\n    if (from == null) {\n      inner = this.iter();\n    } else {\n      if (to == null) to = this.lines + 1;\n      let start = this.line(from).from;\n      inner = this.iterRange(start, Math.max(start, to == this.lines + 1 ? this.length : to <= 1 ? 0 : this.line(to - 1).to));\n    }\n    return new LineCursor(inner);\n  }\n  /**\n  Return the document as a string, using newline characters to\n  separate lines.\n  */\n  toString() {\n    return this.sliceString(0);\n  }\n  /**\n  Convert the document to an array of lines (which can be\n  deserialized again via [`Text.of`](https://codemirror.net/6/docs/ref/#state.Text^of)).\n  */\n  toJSON() {\n    let lines = [];\n    this.flatten(lines);\n    return lines;\n  }\n  /**\n  @internal\n  */\n  constructor() {}\n  /**\n  Create a `Text` instance for the given array of lines.\n  */\n  static of(text) {\n    if (text.length == 0) throw new RangeError(\"A document must have at least one line\");\n    if (text.length == 1 && !text[0]) return Text.empty;\n    return text.length <= 32 /* Tree.Branch */ ? new TextLeaf(text) : TextNode.from(TextLeaf.split(text, []));\n  }\n}\n// Leaves store an array of line strings. There are always line breaks\n// between these strings. Leaves are limited in size and have to be\n// contained in TextNode instances for bigger documents.\nclass TextLeaf extends Text {\n  constructor(text, length = textLength(text)) {\n    super();\n    this.text = text;\n    this.length = length;\n  }\n  get lines() {\n    return this.text.length;\n  }\n  get children() {\n    return null;\n  }\n  lineInner(target, isLine, line, offset) {\n    for (let i = 0;; i++) {\n      let string = this.text[i],\n        end = offset + string.length;\n      if ((isLine ? line : end) >= target) return new Line(offset, end, line, string);\n      offset = end + 1;\n      line++;\n    }\n  }\n  decompose(from, to, target, open) {\n    let text = from <= 0 && to >= this.length ? this : new TextLeaf(sliceText(this.text, from, to), Math.min(to, this.length) - Math.max(0, from));\n    if (open & 1 /* Open.From */) {\n      let prev = target.pop();\n      let joined = appendText(text.text, prev.text.slice(), 0, text.length);\n      if (joined.length <= 32 /* Tree.Branch */) {\n        target.push(new TextLeaf(joined, prev.length + text.length));\n      } else {\n        let mid = joined.length >> 1;\n        target.push(new TextLeaf(joined.slice(0, mid)), new TextLeaf(joined.slice(mid)));\n      }\n    } else {\n      target.push(text);\n    }\n  }\n  replace(from, to, text) {\n    if (!(text instanceof TextLeaf)) return super.replace(from, to, text);\n    [from, to] = clip(this, from, to);\n    let lines = appendText(this.text, appendText(text.text, sliceText(this.text, 0, from)), to);\n    let newLen = this.length + text.length - (to - from);\n    if (lines.length <= 32 /* Tree.Branch */) return new TextLeaf(lines, newLen);\n    return TextNode.from(TextLeaf.split(lines, []), newLen);\n  }\n  sliceString(from, to = this.length, lineSep = \"\\n\") {\n    [from, to] = clip(this, from, to);\n    let result = \"\";\n    for (let pos = 0, i = 0; pos <= to && i < this.text.length; i++) {\n      let line = this.text[i],\n        end = pos + line.length;\n      if (pos > from && i) result += lineSep;\n      if (from < end && to > pos) result += line.slice(Math.max(0, from - pos), to - pos);\n      pos = end + 1;\n    }\n    return result;\n  }\n  flatten(target) {\n    for (let line of this.text) target.push(line);\n  }\n  scanIdentical() {\n    return 0;\n  }\n  static split(text, target) {\n    let part = [],\n      len = -1;\n    for (let line of text) {\n      part.push(line);\n      len += line.length + 1;\n      if (part.length == 32 /* Tree.Branch */) {\n        target.push(new TextLeaf(part, len));\n        part = [];\n        len = -1;\n      }\n    }\n    if (len > -1) target.push(new TextLeaf(part, len));\n    return target;\n  }\n}\n// Nodes provide the tree structure of the `Text` type. They store a\n// number of other nodes or leaves, taking care to balance themselves\n// on changes. There are implied line breaks _between_ the children of\n// a node (but not before the first or after the last child).\nclass TextNode extends Text {\n  constructor(children, length) {\n    super();\n    this.children = children;\n    this.length = length;\n    this.lines = 0;\n    for (let child of children) this.lines += child.lines;\n  }\n  lineInner(target, isLine, line, offset) {\n    for (let i = 0;; i++) {\n      let child = this.children[i],\n        end = offset + child.length,\n        endLine = line + child.lines - 1;\n      if ((isLine ? endLine : end) >= target) return child.lineInner(target, isLine, line, offset);\n      offset = end + 1;\n      line = endLine + 1;\n    }\n  }\n  decompose(from, to, target, open) {\n    for (let i = 0, pos = 0; pos <= to && i < this.children.length; i++) {\n      let child = this.children[i],\n        end = pos + child.length;\n      if (from <= end && to >= pos) {\n        let childOpen = open & ((pos <= from ? 1 /* Open.From */ : 0) | (end >= to ? 2 /* Open.To */ : 0));\n        if (pos >= from && end <= to && !childOpen) target.push(child);else child.decompose(from - pos, to - pos, target, childOpen);\n      }\n      pos = end + 1;\n    }\n  }\n  replace(from, to, text) {\n    [from, to] = clip(this, from, to);\n    if (text.lines < this.lines) for (let i = 0, pos = 0; i < this.children.length; i++) {\n      let child = this.children[i],\n        end = pos + child.length;\n      // Fast path: if the change only affects one child and the\n      // child's size remains in the acceptable range, only update\n      // that child\n      if (from >= pos && to <= end) {\n        let updated = child.replace(from - pos, to - pos, text);\n        let totalLines = this.lines - child.lines + updated.lines;\n        if (updated.lines < totalLines >> 5 /* Tree.BranchShift */ - 1 && updated.lines > totalLines >> 5 /* Tree.BranchShift */ + 1) {\n          let copy = this.children.slice();\n          copy[i] = updated;\n          return new TextNode(copy, this.length - (to - from) + text.length);\n        }\n        return super.replace(pos, end, updated);\n      }\n      pos = end + 1;\n    }\n    return super.replace(from, to, text);\n  }\n  sliceString(from, to = this.length, lineSep = \"\\n\") {\n    [from, to] = clip(this, from, to);\n    let result = \"\";\n    for (let i = 0, pos = 0; i < this.children.length && pos <= to; i++) {\n      let child = this.children[i],\n        end = pos + child.length;\n      if (pos > from && i) result += lineSep;\n      if (from < end && to > pos) result += child.sliceString(from - pos, to - pos, lineSep);\n      pos = end + 1;\n    }\n    return result;\n  }\n  flatten(target) {\n    for (let child of this.children) child.flatten(target);\n  }\n  scanIdentical(other, dir) {\n    if (!(other instanceof TextNode)) return 0;\n    let length = 0;\n    let [iA, iB, eA, eB] = dir > 0 ? [0, 0, this.children.length, other.children.length] : [this.children.length - 1, other.children.length - 1, -1, -1];\n    for (;; iA += dir, iB += dir) {\n      if (iA == eA || iB == eB) return length;\n      let chA = this.children[iA],\n        chB = other.children[iB];\n      if (chA != chB) return length + chA.scanIdentical(chB, dir);\n      length += chA.length + 1;\n    }\n  }\n  static from(children, length = children.reduce((l, ch) => l + ch.length + 1, -1)) {\n    let lines = 0;\n    for (let ch of children) lines += ch.lines;\n    if (lines < 32 /* Tree.Branch */) {\n      let flat = [];\n      for (let ch of children) ch.flatten(flat);\n      return new TextLeaf(flat, length);\n    }\n    let chunk = Math.max(32 /* Tree.Branch */, lines >> 5 /* Tree.BranchShift */),\n      maxChunk = chunk << 1,\n      minChunk = chunk >> 1;\n    let chunked = [],\n      currentLines = 0,\n      currentLen = -1,\n      currentChunk = [];\n    function add(child) {\n      let last;\n      if (child.lines > maxChunk && child instanceof TextNode) {\n        for (let node of child.children) add(node);\n      } else if (child.lines > minChunk && (currentLines > minChunk || !currentLines)) {\n        flush();\n        chunked.push(child);\n      } else if (child instanceof TextLeaf && currentLines && (last = currentChunk[currentChunk.length - 1]) instanceof TextLeaf && child.lines + last.lines <= 32 /* Tree.Branch */) {\n        currentLines += child.lines;\n        currentLen += child.length + 1;\n        currentChunk[currentChunk.length - 1] = new TextLeaf(last.text.concat(child.text), last.length + 1 + child.length);\n      } else {\n        if (currentLines + child.lines > chunk) flush();\n        currentLines += child.lines;\n        currentLen += child.length + 1;\n        currentChunk.push(child);\n      }\n    }\n    function flush() {\n      if (currentLines == 0) return;\n      chunked.push(currentChunk.length == 1 ? currentChunk[0] : TextNode.from(currentChunk, currentLen));\n      currentLen = -1;\n      currentLines = currentChunk.length = 0;\n    }\n    for (let child of children) add(child);\n    flush();\n    return chunked.length == 1 ? chunked[0] : new TextNode(chunked, length);\n  }\n}\nText.empty = /*@__PURE__*/new TextLeaf([\"\"], 0);\nfunction textLength(text) {\n  let length = -1;\n  for (let line of text) length += line.length + 1;\n  return length;\n}\nfunction appendText(text, target, from = 0, to = 1e9) {\n  for (let pos = 0, i = 0, first = true; i < text.length && pos <= to; i++) {\n    let line = text[i],\n      end = pos + line.length;\n    if (end >= from) {\n      if (end > to) line = line.slice(0, to - pos);\n      if (pos < from) line = line.slice(from - pos);\n      if (first) {\n        target[target.length - 1] += line;\n        first = false;\n      } else target.push(line);\n    }\n    pos = end + 1;\n  }\n  return target;\n}\nfunction sliceText(text, from, to) {\n  return appendText(text, [\"\"], from, to);\n}\nclass RawTextCursor {\n  constructor(text, dir = 1) {\n    this.dir = dir;\n    this.done = false;\n    this.lineBreak = false;\n    this.value = \"\";\n    this.nodes = [text];\n    this.offsets = [dir > 0 ? 1 : (text instanceof TextLeaf ? text.text.length : text.children.length) << 1];\n  }\n  nextInner(skip, dir) {\n    this.done = this.lineBreak = false;\n    for (;;) {\n      let last = this.nodes.length - 1;\n      let top = this.nodes[last],\n        offsetValue = this.offsets[last],\n        offset = offsetValue >> 1;\n      let size = top instanceof TextLeaf ? top.text.length : top.children.length;\n      if (offset == (dir > 0 ? size : 0)) {\n        if (last == 0) {\n          this.done = true;\n          this.value = \"\";\n          return this;\n        }\n        if (dir > 0) this.offsets[last - 1]++;\n        this.nodes.pop();\n        this.offsets.pop();\n      } else if ((offsetValue & 1) == (dir > 0 ? 0 : 1)) {\n        this.offsets[last] += dir;\n        if (skip == 0) {\n          this.lineBreak = true;\n          this.value = \"\\n\";\n          return this;\n        }\n        skip--;\n      } else if (top instanceof TextLeaf) {\n        // Move to the next string\n        let next = top.text[offset + (dir < 0 ? -1 : 0)];\n        this.offsets[last] += dir;\n        if (next.length > Math.max(0, skip)) {\n          this.value = skip == 0 ? next : dir > 0 ? next.slice(skip) : next.slice(0, next.length - skip);\n          return this;\n        }\n        skip -= next.length;\n      } else {\n        let next = top.children[offset + (dir < 0 ? -1 : 0)];\n        if (skip > next.length) {\n          skip -= next.length;\n          this.offsets[last] += dir;\n        } else {\n          if (dir < 0) this.offsets[last]--;\n          this.nodes.push(next);\n          this.offsets.push(dir > 0 ? 1 : (next instanceof TextLeaf ? next.text.length : next.children.length) << 1);\n        }\n      }\n    }\n  }\n  next(skip = 0) {\n    if (skip < 0) {\n      this.nextInner(-skip, -this.dir);\n      skip = this.value.length;\n    }\n    return this.nextInner(skip, this.dir);\n  }\n}\nclass PartialTextCursor {\n  constructor(text, start, end) {\n    this.value = \"\";\n    this.done = false;\n    this.cursor = new RawTextCursor(text, start > end ? -1 : 1);\n    this.pos = start > end ? text.length : 0;\n    this.from = Math.min(start, end);\n    this.to = Math.max(start, end);\n  }\n  nextInner(skip, dir) {\n    if (dir < 0 ? this.pos <= this.from : this.pos >= this.to) {\n      this.value = \"\";\n      this.done = true;\n      return this;\n    }\n    skip += Math.max(0, dir < 0 ? this.pos - this.to : this.from - this.pos);\n    let limit = dir < 0 ? this.pos - this.from : this.to - this.pos;\n    if (skip > limit) skip = limit;\n    limit -= skip;\n    let {\n      value\n    } = this.cursor.next(skip);\n    this.pos += (value.length + skip) * dir;\n    this.value = value.length <= limit ? value : dir < 0 ? value.slice(value.length - limit) : value.slice(0, limit);\n    this.done = !this.value;\n    return this;\n  }\n  next(skip = 0) {\n    if (skip < 0) skip = Math.max(skip, this.from - this.pos);else if (skip > 0) skip = Math.min(skip, this.to - this.pos);\n    return this.nextInner(skip, this.cursor.dir);\n  }\n  get lineBreak() {\n    return this.cursor.lineBreak && this.value != \"\";\n  }\n}\nclass LineCursor {\n  constructor(inner) {\n    this.inner = inner;\n    this.afterBreak = true;\n    this.value = \"\";\n    this.done = false;\n  }\n  next(skip = 0) {\n    let {\n      done,\n      lineBreak,\n      value\n    } = this.inner.next(skip);\n    if (done && this.afterBreak) {\n      this.value = \"\";\n      this.afterBreak = false;\n    } else if (done) {\n      this.done = true;\n      this.value = \"\";\n    } else if (lineBreak) {\n      if (this.afterBreak) {\n        this.value = \"\";\n      } else {\n        this.afterBreak = true;\n        this.next();\n      }\n    } else {\n      this.value = value;\n      this.afterBreak = false;\n    }\n    return this;\n  }\n  get lineBreak() {\n    return false;\n  }\n}\nif (typeof Symbol != \"undefined\") {\n  Text.prototype[Symbol.iterator] = function () {\n    return this.iter();\n  };\n  RawTextCursor.prototype[Symbol.iterator] = PartialTextCursor.prototype[Symbol.iterator] = LineCursor.prototype[Symbol.iterator] = function () {\n    return this;\n  };\n}\n/**\nThis type describes a line in the document. It is created\non-demand when lines are [queried](https://codemirror.net/6/docs/ref/#state.Text.lineAt).\n*/\nclass Line {\n  /**\n  @internal\n  */\n  constructor(\n  /**\n  The position of the start of the line.\n  */\n  from,\n  /**\n  The position at the end of the line (_before_ the line break,\n  or at the end of document for the last line).\n  */\n  to,\n  /**\n  This line's line number (1-based).\n  */\n  number,\n  /**\n  The line's content.\n  */\n  text) {\n    this.from = from;\n    this.to = to;\n    this.number = number;\n    this.text = text;\n  }\n  /**\n  The length of the line (not including any line break after it).\n  */\n  get length() {\n    return this.to - this.from;\n  }\n}\nfunction clip(text, from, to) {\n  from = Math.max(0, Math.min(text.length, from));\n  return [from, Math.max(from, Math.min(text.length, to))];\n}\n\n/**\nReturns a next grapheme cluster break _after_ (not equal to)\n`pos`, if `forward` is true, or before otherwise. Returns `pos`\nitself if no further cluster break is available in the string.\nMoves across surrogate pairs, extending characters (when\n`includeExtending` is true), characters joined with zero-width\njoiners, and flag emoji.\n*/\nfunction findClusterBreak(str, pos, forward = true, includeExtending = true) {\n  return findClusterBreak$1(str, pos, forward, includeExtending);\n}\nfunction surrogateLow(ch) {\n  return ch >= 0xDC00 && ch < 0xE000;\n}\nfunction surrogateHigh(ch) {\n  return ch >= 0xD800 && ch < 0xDC00;\n}\n/**\nFind the code point at the given position in a string (like the\n[`codePointAt`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/codePointAt)\nstring method).\n*/\nfunction codePointAt(str, pos) {\n  let code0 = str.charCodeAt(pos);\n  if (!surrogateHigh(code0) || pos + 1 == str.length) return code0;\n  let code1 = str.charCodeAt(pos + 1);\n  if (!surrogateLow(code1)) return code0;\n  return (code0 - 0xd800 << 10) + (code1 - 0xdc00) + 0x10000;\n}\n/**\nGiven a Unicode codepoint, return the JavaScript string that\nrespresents it (like\n[`String.fromCodePoint`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/fromCodePoint)).\n*/\nfunction fromCodePoint(code) {\n  if (code <= 0xffff) return String.fromCharCode(code);\n  code -= 0x10000;\n  return String.fromCharCode((code >> 10) + 0xd800, (code & 1023) + 0xdc00);\n}\n/**\nThe amount of positions a character takes up in a JavaScript string.\n*/\nfunction codePointSize(code) {\n  return code < 0x10000 ? 1 : 2;\n}\nconst DefaultSplit = /\\r\\n?|\\n/;\n/**\nDistinguishes different ways in which positions can be mapped.\n*/\nvar MapMode = /*@__PURE__*/function (MapMode) {\n  /**\n  Map a position to a valid new position, even when its context\n  was deleted.\n  */\n  MapMode[MapMode[\"Simple\"] = 0] = \"Simple\";\n  /**\n  Return null if deletion happens across the position.\n  */\n  MapMode[MapMode[\"TrackDel\"] = 1] = \"TrackDel\";\n  /**\n  Return null if the character _before_ the position is deleted.\n  */\n  MapMode[MapMode[\"TrackBefore\"] = 2] = \"TrackBefore\";\n  /**\n  Return null if the character _after_ the position is deleted.\n  */\n  MapMode[MapMode[\"TrackAfter\"] = 3] = \"TrackAfter\";\n  return MapMode;\n}(MapMode || (MapMode = {}));\n/**\nA change description is a variant of [change set](https://codemirror.net/6/docs/ref/#state.ChangeSet)\nthat doesn't store the inserted text. As such, it can't be\napplied, but is cheaper to store and manipulate.\n*/\nclass ChangeDesc {\n  // Sections are encoded as pairs of integers. The first is the\n  // length in the current document, and the second is -1 for\n  // unaffected sections, and the length of the replacement content\n  // otherwise. So an insertion would be (0, n>0), a deletion (n>0,\n  // 0), and a replacement two positive numbers.\n  /**\n  @internal\n  */\n  constructor(\n  /**\n  @internal\n  */\n  sections) {\n    this.sections = sections;\n  }\n  /**\n  The length of the document before the change.\n  */\n  get length() {\n    let result = 0;\n    for (let i = 0; i < this.sections.length; i += 2) result += this.sections[i];\n    return result;\n  }\n  /**\n  The length of the document after the change.\n  */\n  get newLength() {\n    let result = 0;\n    for (let i = 0; i < this.sections.length; i += 2) {\n      let ins = this.sections[i + 1];\n      result += ins < 0 ? this.sections[i] : ins;\n    }\n    return result;\n  }\n  /**\n  False when there are actual changes in this set.\n  */\n  get empty() {\n    return this.sections.length == 0 || this.sections.length == 2 && this.sections[1] < 0;\n  }\n  /**\n  Iterate over the unchanged parts left by these changes. `posA`\n  provides the position of the range in the old document, `posB`\n  the new position in the changed document.\n  */\n  iterGaps(f) {\n    for (let i = 0, posA = 0, posB = 0; i < this.sections.length;) {\n      let len = this.sections[i++],\n        ins = this.sections[i++];\n      if (ins < 0) {\n        f(posA, posB, len);\n        posB += len;\n      } else {\n        posB += ins;\n      }\n      posA += len;\n    }\n  }\n  /**\n  Iterate over the ranges changed by these changes. (See\n  [`ChangeSet.iterChanges`](https://codemirror.net/6/docs/ref/#state.ChangeSet.iterChanges) for a\n  variant that also provides you with the inserted text.)\n  `fromA`/`toA` provides the extent of the change in the starting\n  document, `fromB`/`toB` the extent of the replacement in the\n  changed document.\n  \n  When `individual` is true, adjacent changes (which are kept\n  separate for [position mapping](https://codemirror.net/6/docs/ref/#state.ChangeDesc.mapPos)) are\n  reported separately.\n  */\n  iterChangedRanges(f, individual = false) {\n    iterChanges(this, f, individual);\n  }\n  /**\n  Get a description of the inverted form of these changes.\n  */\n  get invertedDesc() {\n    let sections = [];\n    for (let i = 0; i < this.sections.length;) {\n      let len = this.sections[i++],\n        ins = this.sections[i++];\n      if (ins < 0) sections.push(len, ins);else sections.push(ins, len);\n    }\n    return new ChangeDesc(sections);\n  }\n  /**\n  Compute the combined effect of applying another set of changes\n  after this one. The length of the document after this set should\n  match the length before `other`.\n  */\n  composeDesc(other) {\n    return this.empty ? other : other.empty ? this : composeSets(this, other);\n  }\n  /**\n  Map this description, which should start with the same document\n  as `other`, over another set of changes, so that it can be\n  applied after it. When `before` is true, map as if the changes\n  in `this` happened before the ones in `other`.\n  */\n  mapDesc(other, before = false) {\n    return other.empty ? this : mapSet(this, other, before);\n  }\n  mapPos(pos, assoc = -1, mode = MapMode.Simple) {\n    let posA = 0,\n      posB = 0;\n    for (let i = 0; i < this.sections.length;) {\n      let len = this.sections[i++],\n        ins = this.sections[i++],\n        endA = posA + len;\n      if (ins < 0) {\n        if (endA > pos) return posB + (pos - posA);\n        posB += len;\n      } else {\n        if (mode != MapMode.Simple && endA >= pos && (mode == MapMode.TrackDel && posA < pos && endA > pos || mode == MapMode.TrackBefore && posA < pos || mode == MapMode.TrackAfter && endA > pos)) return null;\n        if (endA > pos || endA == pos && assoc < 0 && !len) return pos == posA || assoc < 0 ? posB : posB + ins;\n        posB += ins;\n      }\n      posA = endA;\n    }\n    if (pos > posA) throw new RangeError(`Position ${pos} is out of range for changeset of length ${posA}`);\n    return posB;\n  }\n  /**\n  Check whether these changes touch a given range. When one of the\n  changes entirely covers the range, the string `\"cover\"` is\n  returned.\n  */\n  touchesRange(from, to = from) {\n    for (let i = 0, pos = 0; i < this.sections.length && pos <= to;) {\n      let len = this.sections[i++],\n        ins = this.sections[i++],\n        end = pos + len;\n      if (ins >= 0 && pos <= to && end >= from) return pos < from && end > to ? \"cover\" : true;\n      pos = end;\n    }\n    return false;\n  }\n  /**\n  @internal\n  */\n  toString() {\n    let result = \"\";\n    for (let i = 0; i < this.sections.length;) {\n      let len = this.sections[i++],\n        ins = this.sections[i++];\n      result += (result ? \" \" : \"\") + len + (ins >= 0 ? \":\" + ins : \"\");\n    }\n    return result;\n  }\n  /**\n  Serialize this change desc to a JSON-representable value.\n  */\n  toJSON() {\n    return this.sections;\n  }\n  /**\n  Create a change desc from its JSON representation (as produced\n  by [`toJSON`](https://codemirror.net/6/docs/ref/#state.ChangeDesc.toJSON).\n  */\n  static fromJSON(json) {\n    if (!Array.isArray(json) || json.length % 2 || json.some(a => typeof a != \"number\")) throw new RangeError(\"Invalid JSON representation of ChangeDesc\");\n    return new ChangeDesc(json);\n  }\n  /**\n  @internal\n  */\n  static create(sections) {\n    return new ChangeDesc(sections);\n  }\n}\n/**\nA change set represents a group of modifications to a document. It\nstores the document length, and can only be applied to documents\nwith exactly that length.\n*/\nclass ChangeSet extends ChangeDesc {\n  constructor(sections,\n  /**\n  @internal\n  */\n  inserted) {\n    super(sections);\n    this.inserted = inserted;\n  }\n  /**\n  Apply the changes to a document, returning the modified\n  document.\n  */\n  apply(doc) {\n    if (this.length != doc.length) throw new RangeError(\"Applying change set to a document with the wrong length\");\n    iterChanges(this, (fromA, toA, fromB, _toB, text) => doc = doc.replace(fromB, fromB + (toA - fromA), text), false);\n    return doc;\n  }\n  mapDesc(other, before = false) {\n    return mapSet(this, other, before, true);\n  }\n  /**\n  Given the document as it existed _before_ the changes, return a\n  change set that represents the inverse of this set, which could\n  be used to go from the document created by the changes back to\n  the document as it existed before the changes.\n  */\n  invert(doc) {\n    let sections = this.sections.slice(),\n      inserted = [];\n    for (let i = 0, pos = 0; i < sections.length; i += 2) {\n      let len = sections[i],\n        ins = sections[i + 1];\n      if (ins >= 0) {\n        sections[i] = ins;\n        sections[i + 1] = len;\n        let index = i >> 1;\n        while (inserted.length < index) inserted.push(Text.empty);\n        inserted.push(len ? doc.slice(pos, pos + len) : Text.empty);\n      }\n      pos += len;\n    }\n    return new ChangeSet(sections, inserted);\n  }\n  /**\n  Combine two subsequent change sets into a single set. `other`\n  must start in the document produced by `this`. If `this` goes\n  `docA` → `docB` and `other` represents `docB` → `docC`, the\n  returned value will represent the change `docA` → `docC`.\n  */\n  compose(other) {\n    return this.empty ? other : other.empty ? this : composeSets(this, other, true);\n  }\n  /**\n  Given another change set starting in the same document, maps this\n  change set over the other, producing a new change set that can be\n  applied to the document produced by applying `other`. When\n  `before` is `true`, order changes as if `this` comes before\n  `other`, otherwise (the default) treat `other` as coming first.\n  \n  Given two changes `A` and `B`, `A.compose(B.map(A))` and\n  `B.compose(A.map(B, true))` will produce the same document. This\n  provides a basic form of [operational\n  transformation](https://en.wikipedia.org/wiki/Operational_transformation),\n  and can be used for collaborative editing.\n  */\n  map(other, before = false) {\n    return other.empty ? this : mapSet(this, other, before, true);\n  }\n  /**\n  Iterate over the changed ranges in the document, calling `f` for\n  each, with the range in the original document (`fromA`-`toA`)\n  and the range that replaces it in the new document\n  (`fromB`-`toB`).\n  \n  When `individual` is true, adjacent changes are reported\n  separately.\n  */\n  iterChanges(f, individual = false) {\n    iterChanges(this, f, individual);\n  }\n  /**\n  Get a [change description](https://codemirror.net/6/docs/ref/#state.ChangeDesc) for this change\n  set.\n  */\n  get desc() {\n    return ChangeDesc.create(this.sections);\n  }\n  /**\n  @internal\n  */\n  filter(ranges) {\n    let resultSections = [],\n      resultInserted = [],\n      filteredSections = [];\n    let iter = new SectionIter(this);\n    done: for (let i = 0, pos = 0;;) {\n      let next = i == ranges.length ? 1e9 : ranges[i++];\n      while (pos < next || pos == next && iter.len == 0) {\n        if (iter.done) break done;\n        let len = Math.min(iter.len, next - pos);\n        addSection(filteredSections, len, -1);\n        let ins = iter.ins == -1 ? -1 : iter.off == 0 ? iter.ins : 0;\n        addSection(resultSections, len, ins);\n        if (ins > 0) addInsert(resultInserted, resultSections, iter.text);\n        iter.forward(len);\n        pos += len;\n      }\n      let end = ranges[i++];\n      while (pos < end) {\n        if (iter.done) break done;\n        let len = Math.min(iter.len, end - pos);\n        addSection(resultSections, len, -1);\n        addSection(filteredSections, len, iter.ins == -1 ? -1 : iter.off == 0 ? iter.ins : 0);\n        iter.forward(len);\n        pos += len;\n      }\n    }\n    return {\n      changes: new ChangeSet(resultSections, resultInserted),\n      filtered: ChangeDesc.create(filteredSections)\n    };\n  }\n  /**\n  Serialize this change set to a JSON-representable value.\n  */\n  toJSON() {\n    let parts = [];\n    for (let i = 0; i < this.sections.length; i += 2) {\n      let len = this.sections[i],\n        ins = this.sections[i + 1];\n      if (ins < 0) parts.push(len);else if (ins == 0) parts.push([len]);else parts.push([len].concat(this.inserted[i >> 1].toJSON()));\n    }\n    return parts;\n  }\n  /**\n  Create a change set for the given changes, for a document of the\n  given length, using `lineSep` as line separator.\n  */\n  static of(changes, length, lineSep) {\n    let sections = [],\n      inserted = [],\n      pos = 0;\n    let total = null;\n    function flush(force = false) {\n      if (!force && !sections.length) return;\n      if (pos < length) addSection(sections, length - pos, -1);\n      let set = new ChangeSet(sections, inserted);\n      total = total ? total.compose(set.map(total)) : set;\n      sections = [];\n      inserted = [];\n      pos = 0;\n    }\n    function process(spec) {\n      if (Array.isArray(spec)) {\n        for (let sub of spec) process(sub);\n      } else if (spec instanceof ChangeSet) {\n        if (spec.length != length) throw new RangeError(`Mismatched change set length (got ${spec.length}, expected ${length})`);\n        flush();\n        total = total ? total.compose(spec.map(total)) : spec;\n      } else {\n        let {\n          from,\n          to = from,\n          insert\n        } = spec;\n        if (from > to || from < 0 || to > length) throw new RangeError(`Invalid change range ${from} to ${to} (in doc of length ${length})`);\n        let insText = !insert ? Text.empty : typeof insert == \"string\" ? Text.of(insert.split(lineSep || DefaultSplit)) : insert;\n        let insLen = insText.length;\n        if (from == to && insLen == 0) return;\n        if (from < pos) flush();\n        if (from > pos) addSection(sections, from - pos, -1);\n        addSection(sections, to - from, insLen);\n        addInsert(inserted, sections, insText);\n        pos = to;\n      }\n    }\n    process(changes);\n    flush(!total);\n    return total;\n  }\n  /**\n  Create an empty changeset of the given length.\n  */\n  static empty(length) {\n    return new ChangeSet(length ? [length, -1] : [], []);\n  }\n  /**\n  Create a changeset from its JSON representation (as produced by\n  [`toJSON`](https://codemirror.net/6/docs/ref/#state.ChangeSet.toJSON).\n  */\n  static fromJSON(json) {\n    if (!Array.isArray(json)) throw new RangeError(\"Invalid JSON representation of ChangeSet\");\n    let sections = [],\n      inserted = [];\n    for (let i = 0; i < json.length; i++) {\n      let part = json[i];\n      if (typeof part == \"number\") {\n        sections.push(part, -1);\n      } else if (!Array.isArray(part) || typeof part[0] != \"number\" || part.some((e, i) => i && typeof e != \"string\")) {\n        throw new RangeError(\"Invalid JSON representation of ChangeSet\");\n      } else if (part.length == 1) {\n        sections.push(part[0], 0);\n      } else {\n        while (inserted.length < i) inserted.push(Text.empty);\n        inserted[i] = Text.of(part.slice(1));\n        sections.push(part[0], inserted[i].length);\n      }\n    }\n    return new ChangeSet(sections, inserted);\n  }\n  /**\n  @internal\n  */\n  static createSet(sections, inserted) {\n    return new ChangeSet(sections, inserted);\n  }\n}\nfunction addSection(sections, len, ins, forceJoin = false) {\n  if (len == 0 && ins <= 0) return;\n  let last = sections.length - 2;\n  if (last >= 0 && ins <= 0 && ins == sections[last + 1]) sections[last] += len;else if (last >= 0 && len == 0 && sections[last] == 0) sections[last + 1] += ins;else if (forceJoin) {\n    sections[last] += len;\n    sections[last + 1] += ins;\n  } else sections.push(len, ins);\n}\nfunction addInsert(values, sections, value) {\n  if (value.length == 0) return;\n  let index = sections.length - 2 >> 1;\n  if (index < values.length) {\n    values[values.length - 1] = values[values.length - 1].append(value);\n  } else {\n    while (values.length < index) values.push(Text.empty);\n    values.push(value);\n  }\n}\nfunction iterChanges(desc, f, individual) {\n  let inserted = desc.inserted;\n  for (let posA = 0, posB = 0, i = 0; i < desc.sections.length;) {\n    let len = desc.sections[i++],\n      ins = desc.sections[i++];\n    if (ins < 0) {\n      posA += len;\n      posB += len;\n    } else {\n      let endA = posA,\n        endB = posB,\n        text = Text.empty;\n      for (;;) {\n        endA += len;\n        endB += ins;\n        if (ins && inserted) text = text.append(inserted[i - 2 >> 1]);\n        if (individual || i == desc.sections.length || desc.sections[i + 1] < 0) break;\n        len = desc.sections[i++];\n        ins = desc.sections[i++];\n      }\n      f(posA, endA, posB, endB, text);\n      posA = endA;\n      posB = endB;\n    }\n  }\n}\nfunction mapSet(setA, setB, before, mkSet = false) {\n  // Produce a copy of setA that applies to the document after setB\n  // has been applied (assuming both start at the same document).\n  let sections = [],\n    insert = mkSet ? [] : null;\n  let a = new SectionIter(setA),\n    b = new SectionIter(setB);\n  // Iterate over both sets in parallel. inserted tracks, for changes\n  // in A that have to be processed piece-by-piece, whether their\n  // content has been inserted already, and refers to the section\n  // index.\n  for (let inserted = -1;;) {\n    if (a.done && b.len || b.done && a.len) {\n      throw new Error(\"Mismatched change set lengths\");\n    } else if (a.ins == -1 && b.ins == -1) {\n      // Move across ranges skipped by both sets.\n      let len = Math.min(a.len, b.len);\n      addSection(sections, len, -1);\n      a.forward(len);\n      b.forward(len);\n    } else if (b.ins >= 0 && (a.ins < 0 || inserted == a.i || a.off == 0 && (b.len < a.len || b.len == a.len && !before))) {\n      // If there's a change in B that comes before the next change in\n      // A (ordered by start pos, then len, then before flag), skip\n      // that (and process any changes in A it covers).\n      let len = b.len;\n      addSection(sections, b.ins, -1);\n      while (len) {\n        let piece = Math.min(a.len, len);\n        if (a.ins >= 0 && inserted < a.i && a.len <= piece) {\n          addSection(sections, 0, a.ins);\n          if (insert) addInsert(insert, sections, a.text);\n          inserted = a.i;\n        }\n        a.forward(piece);\n        len -= piece;\n      }\n      b.next();\n    } else if (a.ins >= 0) {\n      // Process the part of a change in A up to the start of the next\n      // non-deletion change in B (if overlapping).\n      let len = 0,\n        left = a.len;\n      while (left) {\n        if (b.ins == -1) {\n          let piece = Math.min(left, b.len);\n          len += piece;\n          left -= piece;\n          b.forward(piece);\n        } else if (b.ins == 0 && b.len < left) {\n          left -= b.len;\n          b.next();\n        } else {\n          break;\n        }\n      }\n      addSection(sections, len, inserted < a.i ? a.ins : 0);\n      if (insert && inserted < a.i) addInsert(insert, sections, a.text);\n      inserted = a.i;\n      a.forward(a.len - left);\n    } else if (a.done && b.done) {\n      return insert ? ChangeSet.createSet(sections, insert) : ChangeDesc.create(sections);\n    } else {\n      throw new Error(\"Mismatched change set lengths\");\n    }\n  }\n}\nfunction composeSets(setA, setB, mkSet = false) {\n  let sections = [];\n  let insert = mkSet ? [] : null;\n  let a = new SectionIter(setA),\n    b = new SectionIter(setB);\n  for (let open = false;;) {\n    if (a.done && b.done) {\n      return insert ? ChangeSet.createSet(sections, insert) : ChangeDesc.create(sections);\n    } else if (a.ins == 0) {\n      // Deletion in A\n      addSection(sections, a.len, 0, open);\n      a.next();\n    } else if (b.len == 0 && !b.done) {\n      // Insertion in B\n      addSection(sections, 0, b.ins, open);\n      if (insert) addInsert(insert, sections, b.text);\n      b.next();\n    } else if (a.done || b.done) {\n      throw new Error(\"Mismatched change set lengths\");\n    } else {\n      let len = Math.min(a.len2, b.len),\n        sectionLen = sections.length;\n      if (a.ins == -1) {\n        let insB = b.ins == -1 ? -1 : b.off ? 0 : b.ins;\n        addSection(sections, len, insB, open);\n        if (insert && insB) addInsert(insert, sections, b.text);\n      } else if (b.ins == -1) {\n        addSection(sections, a.off ? 0 : a.len, len, open);\n        if (insert) addInsert(insert, sections, a.textBit(len));\n      } else {\n        addSection(sections, a.off ? 0 : a.len, b.off ? 0 : b.ins, open);\n        if (insert && !b.off) addInsert(insert, sections, b.text);\n      }\n      open = (a.ins > len || b.ins >= 0 && b.len > len) && (open || sections.length > sectionLen);\n      a.forward2(len);\n      b.forward(len);\n    }\n  }\n}\nclass SectionIter {\n  constructor(set) {\n    this.set = set;\n    this.i = 0;\n    this.next();\n  }\n  next() {\n    let {\n      sections\n    } = this.set;\n    if (this.i < sections.length) {\n      this.len = sections[this.i++];\n      this.ins = sections[this.i++];\n    } else {\n      this.len = 0;\n      this.ins = -2;\n    }\n    this.off = 0;\n  }\n  get done() {\n    return this.ins == -2;\n  }\n  get len2() {\n    return this.ins < 0 ? this.len : this.ins;\n  }\n  get text() {\n    let {\n        inserted\n      } = this.set,\n      index = this.i - 2 >> 1;\n    return index >= inserted.length ? Text.empty : inserted[index];\n  }\n  textBit(len) {\n    let {\n        inserted\n      } = this.set,\n      index = this.i - 2 >> 1;\n    return index >= inserted.length && !len ? Text.empty : inserted[index].slice(this.off, len == null ? undefined : this.off + len);\n  }\n  forward(len) {\n    if (len == this.len) this.next();else {\n      this.len -= len;\n      this.off += len;\n    }\n  }\n  forward2(len) {\n    if (this.ins == -1) this.forward(len);else if (len == this.ins) this.next();else {\n      this.ins -= len;\n      this.off += len;\n    }\n  }\n}\n\n/**\nA single selection range. When\n[`allowMultipleSelections`](https://codemirror.net/6/docs/ref/#state.EditorState^allowMultipleSelections)\nis enabled, a [selection](https://codemirror.net/6/docs/ref/#state.EditorSelection) may hold\nmultiple ranges. By default, selections hold exactly one range.\n*/\nclass SelectionRange {\n  constructor(\n  /**\n  The lower boundary of the range.\n  */\n  from,\n  /**\n  The upper boundary of the range.\n  */\n  to, flags) {\n    this.from = from;\n    this.to = to;\n    this.flags = flags;\n  }\n  /**\n  The anchor of the range—the side that doesn't move when you\n  extend it.\n  */\n  get anchor() {\n    return this.flags & 32 /* RangeFlag.Inverted */ ? this.to : this.from;\n  }\n  /**\n  The head of the range, which is moved when the range is\n  [extended](https://codemirror.net/6/docs/ref/#state.SelectionRange.extend).\n  */\n  get head() {\n    return this.flags & 32 /* RangeFlag.Inverted */ ? this.from : this.to;\n  }\n  /**\n  True when `anchor` and `head` are at the same position.\n  */\n  get empty() {\n    return this.from == this.to;\n  }\n  /**\n  If this is a cursor that is explicitly associated with the\n  character on one of its sides, this returns the side. -1 means\n  the character before its position, 1 the character after, and 0\n  means no association.\n  */\n  get assoc() {\n    return this.flags & 8 /* RangeFlag.AssocBefore */ ? -1 : this.flags & 16 /* RangeFlag.AssocAfter */ ? 1 : 0;\n  }\n  /**\n  The bidirectional text level associated with this cursor, if\n  any.\n  */\n  get bidiLevel() {\n    let level = this.flags & 7 /* RangeFlag.BidiLevelMask */;\n    return level == 7 ? null : level;\n  }\n  /**\n  The goal column (stored vertical offset) associated with a\n  cursor. This is used to preserve the vertical position when\n  [moving](https://codemirror.net/6/docs/ref/#view.EditorView.moveVertically) across\n  lines of different length.\n  */\n  get goalColumn() {\n    let value = this.flags >> 6 /* RangeFlag.GoalColumnOffset */;\n    return value == 16777215 /* RangeFlag.NoGoalColumn */ ? undefined : value;\n  }\n  /**\n  Map this range through a change, producing a valid range in the\n  updated document.\n  */\n  map(change, assoc = -1) {\n    let from, to;\n    if (this.empty) {\n      from = to = change.mapPos(this.from, assoc);\n    } else {\n      from = change.mapPos(this.from, 1);\n      to = change.mapPos(this.to, -1);\n    }\n    return from == this.from && to == this.to ? this : new SelectionRange(from, to, this.flags);\n  }\n  /**\n  Extend this range to cover at least `from` to `to`.\n  */\n  extend(from, to = from) {\n    if (from <= this.anchor && to >= this.anchor) return EditorSelection.range(from, to);\n    let head = Math.abs(from - this.anchor) > Math.abs(to - this.anchor) ? from : to;\n    return EditorSelection.range(this.anchor, head);\n  }\n  /**\n  Compare this range to another range.\n  */\n  eq(other, includeAssoc = false) {\n    return this.anchor == other.anchor && this.head == other.head && (!includeAssoc || !this.empty || this.assoc == other.assoc);\n  }\n  /**\n  Return a JSON-serializable object representing the range.\n  */\n  toJSON() {\n    return {\n      anchor: this.anchor,\n      head: this.head\n    };\n  }\n  /**\n  Convert a JSON representation of a range to a `SelectionRange`\n  instance.\n  */\n  static fromJSON(json) {\n    if (!json || typeof json.anchor != \"number\" || typeof json.head != \"number\") throw new RangeError(\"Invalid JSON representation for SelectionRange\");\n    return EditorSelection.range(json.anchor, json.head);\n  }\n  /**\n  @internal\n  */\n  static create(from, to, flags) {\n    return new SelectionRange(from, to, flags);\n  }\n}\n/**\nAn editor selection holds one or more selection ranges.\n*/\nclass EditorSelection {\n  constructor(\n  /**\n  The ranges in the selection, sorted by position. Ranges cannot\n  overlap (but they may touch, if they aren't empty).\n  */\n  ranges,\n  /**\n  The index of the _main_ range in the selection (which is\n  usually the range that was added last).\n  */\n  mainIndex) {\n    this.ranges = ranges;\n    this.mainIndex = mainIndex;\n  }\n  /**\n  Map a selection through a change. Used to adjust the selection\n  position for changes.\n  */\n  map(change, assoc = -1) {\n    if (change.empty) return this;\n    return EditorSelection.create(this.ranges.map(r => r.map(change, assoc)), this.mainIndex);\n  }\n  /**\n  Compare this selection to another selection. By default, ranges\n  are compared only by position. When `includeAssoc` is true,\n  cursor ranges must also have the same\n  [`assoc`](https://codemirror.net/6/docs/ref/#state.SelectionRange.assoc) value.\n  */\n  eq(other, includeAssoc = false) {\n    if (this.ranges.length != other.ranges.length || this.mainIndex != other.mainIndex) return false;\n    for (let i = 0; i < this.ranges.length; i++) if (!this.ranges[i].eq(other.ranges[i], includeAssoc)) return false;\n    return true;\n  }\n  /**\n  Get the primary selection range. Usually, you should make sure\n  your code applies to _all_ ranges, by using methods like\n  [`changeByRange`](https://codemirror.net/6/docs/ref/#state.EditorState.changeByRange).\n  */\n  get main() {\n    return this.ranges[this.mainIndex];\n  }\n  /**\n  Make sure the selection only has one range. Returns a selection\n  holding only the main range from this selection.\n  */\n  asSingle() {\n    return this.ranges.length == 1 ? this : new EditorSelection([this.main], 0);\n  }\n  /**\n  Extend this selection with an extra range.\n  */\n  addRange(range, main = true) {\n    return EditorSelection.create([range].concat(this.ranges), main ? 0 : this.mainIndex + 1);\n  }\n  /**\n  Replace a given range with another range, and then normalize the\n  selection to merge and sort ranges if necessary.\n  */\n  replaceRange(range, which = this.mainIndex) {\n    let ranges = this.ranges.slice();\n    ranges[which] = range;\n    return EditorSelection.create(ranges, this.mainIndex);\n  }\n  /**\n  Convert this selection to an object that can be serialized to\n  JSON.\n  */\n  toJSON() {\n    return {\n      ranges: this.ranges.map(r => r.toJSON()),\n      main: this.mainIndex\n    };\n  }\n  /**\n  Create a selection from a JSON representation.\n  */\n  static fromJSON(json) {\n    if (!json || !Array.isArray(json.ranges) || typeof json.main != \"number\" || json.main >= json.ranges.length) throw new RangeError(\"Invalid JSON representation for EditorSelection\");\n    return new EditorSelection(json.ranges.map(r => SelectionRange.fromJSON(r)), json.main);\n  }\n  /**\n  Create a selection holding a single range.\n  */\n  static single(anchor, head = anchor) {\n    return new EditorSelection([EditorSelection.range(anchor, head)], 0);\n  }\n  /**\n  Sort and merge the given set of ranges, creating a valid\n  selection.\n  */\n  static create(ranges, mainIndex = 0) {\n    if (ranges.length == 0) throw new RangeError(\"A selection needs at least one range\");\n    for (let pos = 0, i = 0; i < ranges.length; i++) {\n      let range = ranges[i];\n      if (range.empty ? range.from <= pos : range.from < pos) return EditorSelection.normalized(ranges.slice(), mainIndex);\n      pos = range.to;\n    }\n    return new EditorSelection(ranges, mainIndex);\n  }\n  /**\n  Create a cursor selection range at the given position. You can\n  safely ignore the optional arguments in most situations.\n  */\n  static cursor(pos, assoc = 0, bidiLevel, goalColumn) {\n    return SelectionRange.create(pos, pos, (assoc == 0 ? 0 : assoc < 0 ? 8 /* RangeFlag.AssocBefore */ : 16 /* RangeFlag.AssocAfter */) | (bidiLevel == null ? 7 : Math.min(6, bidiLevel)) | (goalColumn !== null && goalColumn !== void 0 ? goalColumn : 16777215 /* RangeFlag.NoGoalColumn */) << 6 /* RangeFlag.GoalColumnOffset */);\n  }\n  /**\n  Create a selection range.\n  */\n  static range(anchor, head, goalColumn, bidiLevel) {\n    let flags = (goalColumn !== null && goalColumn !== void 0 ? goalColumn : 16777215 /* RangeFlag.NoGoalColumn */) << 6 /* RangeFlag.GoalColumnOffset */ | (bidiLevel == null ? 7 : Math.min(6, bidiLevel));\n    return head < anchor ? SelectionRange.create(head, anchor, 32 /* RangeFlag.Inverted */ | 16 /* RangeFlag.AssocAfter */ | flags) : SelectionRange.create(anchor, head, (head > anchor ? 8 /* RangeFlag.AssocBefore */ : 0) | flags);\n  }\n  /**\n  @internal\n  */\n  static normalized(ranges, mainIndex = 0) {\n    let main = ranges[mainIndex];\n    ranges.sort((a, b) => a.from - b.from);\n    mainIndex = ranges.indexOf(main);\n    for (let i = 1; i < ranges.length; i++) {\n      let range = ranges[i],\n        prev = ranges[i - 1];\n      if (range.empty ? range.from <= prev.to : range.from < prev.to) {\n        let from = prev.from,\n          to = Math.max(range.to, prev.to);\n        if (i <= mainIndex) mainIndex--;\n        ranges.splice(--i, 2, range.anchor > range.head ? EditorSelection.range(to, from) : EditorSelection.range(from, to));\n      }\n    }\n    return new EditorSelection(ranges, mainIndex);\n  }\n}\nfunction checkSelection(selection, docLength) {\n  for (let range of selection.ranges) if (range.to > docLength) throw new RangeError(\"Selection points outside of document\");\n}\nlet nextID = 0;\n/**\nA facet is a labeled value that is associated with an editor\nstate. It takes inputs from any number of extensions, and combines\nthose into a single output value.\n\nExamples of uses of facets are the [tab\nsize](https://codemirror.net/6/docs/ref/#state.EditorState^tabSize), [editor\nattributes](https://codemirror.net/6/docs/ref/#view.EditorView^editorAttributes), and [update\nlisteners](https://codemirror.net/6/docs/ref/#view.EditorView^updateListener).\n\nNote that `Facet` instances can be used anywhere where\n[`FacetReader`](https://codemirror.net/6/docs/ref/#state.FacetReader) is expected.\n*/\nclass Facet {\n  constructor(\n  /**\n  @internal\n  */\n  combine,\n  /**\n  @internal\n  */\n  compareInput,\n  /**\n  @internal\n  */\n  compare, isStatic, enables) {\n    this.combine = combine;\n    this.compareInput = compareInput;\n    this.compare = compare;\n    this.isStatic = isStatic;\n    /**\n    @internal\n    */\n    this.id = nextID++;\n    this.default = combine([]);\n    this.extensions = typeof enables == \"function\" ? enables(this) : enables;\n  }\n  /**\n  Returns a facet reader for this facet, which can be used to\n  [read](https://codemirror.net/6/docs/ref/#state.EditorState.facet) it but not to define values for it.\n  */\n  get reader() {\n    return this;\n  }\n  /**\n  Define a new facet.\n  */\n  static define(config = {}) {\n    return new Facet(config.combine || (a => a), config.compareInput || ((a, b) => a === b), config.compare || (!config.combine ? sameArray : (a, b) => a === b), !!config.static, config.enables);\n  }\n  /**\n  Returns an extension that adds the given value to this facet.\n  */\n  of(value) {\n    return new FacetProvider([], this, 0 /* Provider.Static */, value);\n  }\n  /**\n  Create an extension that computes a value for the facet from a\n  state. You must take care to declare the parts of the state that\n  this value depends on, since your function is only called again\n  for a new state when one of those parts changed.\n  \n  In cases where your value depends only on a single field, you'll\n  want to use the [`from`](https://codemirror.net/6/docs/ref/#state.Facet.from) method instead.\n  */\n  compute(deps, get) {\n    if (this.isStatic) throw new Error(\"Can't compute a static facet\");\n    return new FacetProvider(deps, this, 1 /* Provider.Single */, get);\n  }\n  /**\n  Create an extension that computes zero or more values for this\n  facet from a state.\n  */\n  computeN(deps, get) {\n    if (this.isStatic) throw new Error(\"Can't compute a static facet\");\n    return new FacetProvider(deps, this, 2 /* Provider.Multi */, get);\n  }\n  from(field, get) {\n    if (!get) get = x => x;\n    return this.compute([field], state => get(state.field(field)));\n  }\n}\nfunction sameArray(a, b) {\n  return a == b || a.length == b.length && a.every((e, i) => e === b[i]);\n}\nclass FacetProvider {\n  constructor(dependencies, facet, type, value) {\n    this.dependencies = dependencies;\n    this.facet = facet;\n    this.type = type;\n    this.value = value;\n    this.id = nextID++;\n  }\n  dynamicSlot(addresses) {\n    var _a;\n    let getter = this.value;\n    let compare = this.facet.compareInput;\n    let id = this.id,\n      idx = addresses[id] >> 1,\n      multi = this.type == 2 /* Provider.Multi */;\n    let depDoc = false,\n      depSel = false,\n      depAddrs = [];\n    for (let dep of this.dependencies) {\n      if (dep == \"doc\") depDoc = true;else if (dep == \"selection\") depSel = true;else if ((((_a = addresses[dep.id]) !== null && _a !== void 0 ? _a : 1) & 1) == 0) depAddrs.push(addresses[dep.id]);\n    }\n    return {\n      create(state) {\n        state.values[idx] = getter(state);\n        return 1 /* SlotStatus.Changed */;\n      },\n      update(state, tr) {\n        if (depDoc && tr.docChanged || depSel && (tr.docChanged || tr.selection) || ensureAll(state, depAddrs)) {\n          let newVal = getter(state);\n          if (multi ? !compareArray(newVal, state.values[idx], compare) : !compare(newVal, state.values[idx])) {\n            state.values[idx] = newVal;\n            return 1 /* SlotStatus.Changed */;\n          }\n        }\n        return 0;\n      },\n      reconfigure: (state, oldState) => {\n        let newVal,\n          oldAddr = oldState.config.address[id];\n        if (oldAddr != null) {\n          let oldVal = getAddr(oldState, oldAddr);\n          if (this.dependencies.every(dep => {\n            return dep instanceof Facet ? oldState.facet(dep) === state.facet(dep) : dep instanceof StateField ? oldState.field(dep, false) == state.field(dep, false) : true;\n          }) || (multi ? compareArray(newVal = getter(state), oldVal, compare) : compare(newVal = getter(state), oldVal))) {\n            state.values[idx] = oldVal;\n            return 0;\n          }\n        } else {\n          newVal = getter(state);\n        }\n        state.values[idx] = newVal;\n        return 1 /* SlotStatus.Changed */;\n      }\n    };\n  }\n}\nfunction compareArray(a, b, compare) {\n  if (a.length != b.length) return false;\n  for (let i = 0; i < a.length; i++) if (!compare(a[i], b[i])) return false;\n  return true;\n}\nfunction ensureAll(state, addrs) {\n  let changed = false;\n  for (let addr of addrs) if (ensureAddr(state, addr) & 1 /* SlotStatus.Changed */) changed = true;\n  return changed;\n}\nfunction dynamicFacetSlot(addresses, facet, providers) {\n  let providerAddrs = providers.map(p => addresses[p.id]);\n  let providerTypes = providers.map(p => p.type);\n  let dynamic = providerAddrs.filter(p => !(p & 1));\n  let idx = addresses[facet.id] >> 1;\n  function get(state) {\n    let values = [];\n    for (let i = 0; i < providerAddrs.length; i++) {\n      let value = getAddr(state, providerAddrs[i]);\n      if (providerTypes[i] == 2 /* Provider.Multi */) for (let val of value) values.push(val);else values.push(value);\n    }\n    return facet.combine(values);\n  }\n  return {\n    create(state) {\n      for (let addr of providerAddrs) ensureAddr(state, addr);\n      state.values[idx] = get(state);\n      return 1 /* SlotStatus.Changed */;\n    },\n    update(state, tr) {\n      if (!ensureAll(state, dynamic)) return 0;\n      let value = get(state);\n      if (facet.compare(value, state.values[idx])) return 0;\n      state.values[idx] = value;\n      return 1 /* SlotStatus.Changed */;\n    },\n    reconfigure(state, oldState) {\n      let depChanged = ensureAll(state, providerAddrs);\n      let oldProviders = oldState.config.facets[facet.id],\n        oldValue = oldState.facet(facet);\n      if (oldProviders && !depChanged && sameArray(providers, oldProviders)) {\n        state.values[idx] = oldValue;\n        return 0;\n      }\n      let value = get(state);\n      if (facet.compare(value, oldValue)) {\n        state.values[idx] = oldValue;\n        return 0;\n      }\n      state.values[idx] = value;\n      return 1 /* SlotStatus.Changed */;\n    }\n  };\n}\nconst initField = /*@__PURE__*/Facet.define({\n  static: true\n});\n/**\nFields can store additional information in an editor state, and\nkeep it in sync with the rest of the state.\n*/\nclass StateField {\n  constructor(\n  /**\n  @internal\n  */\n  id, createF, updateF, compareF,\n  /**\n  @internal\n  */\n  spec) {\n    this.id = id;\n    this.createF = createF;\n    this.updateF = updateF;\n    this.compareF = compareF;\n    this.spec = spec;\n    /**\n    @internal\n    */\n    this.provides = undefined;\n  }\n  /**\n  Define a state field.\n  */\n  static define(config) {\n    let field = new StateField(nextID++, config.create, config.update, config.compare || ((a, b) => a === b), config);\n    if (config.provide) field.provides = config.provide(field);\n    return field;\n  }\n  create(state) {\n    let init = state.facet(initField).find(i => i.field == this);\n    return ((init === null || init === void 0 ? void 0 : init.create) || this.createF)(state);\n  }\n  /**\n  @internal\n  */\n  slot(addresses) {\n    let idx = addresses[this.id] >> 1;\n    return {\n      create: state => {\n        state.values[idx] = this.create(state);\n        return 1 /* SlotStatus.Changed */;\n      },\n      update: (state, tr) => {\n        let oldVal = state.values[idx];\n        let value = this.updateF(oldVal, tr);\n        if (this.compareF(oldVal, value)) return 0;\n        state.values[idx] = value;\n        return 1 /* SlotStatus.Changed */;\n      },\n      reconfigure: (state, oldState) => {\n        let init = state.facet(initField),\n          oldInit = oldState.facet(initField),\n          reInit;\n        if ((reInit = init.find(i => i.field == this)) && reInit != oldInit.find(i => i.field == this)) {\n          state.values[idx] = reInit.create(state);\n          return 1 /* SlotStatus.Changed */;\n        }\n        if (oldState.config.address[this.id] != null) {\n          state.values[idx] = oldState.field(this);\n          return 0;\n        }\n        state.values[idx] = this.create(state);\n        return 1 /* SlotStatus.Changed */;\n      }\n    };\n  }\n  /**\n  Returns an extension that enables this field and overrides the\n  way it is initialized. Can be useful when you need to provide a\n  non-default starting value for the field.\n  */\n  init(create) {\n    return [this, initField.of({\n      field: this,\n      create\n    })];\n  }\n  /**\n  State field instances can be used as\n  [`Extension`](https://codemirror.net/6/docs/ref/#state.Extension) values to enable the field in a\n  given state.\n  */\n  get extension() {\n    return this;\n  }\n}\nconst Prec_ = {\n  lowest: 4,\n  low: 3,\n  default: 2,\n  high: 1,\n  highest: 0\n};\nfunction prec(value) {\n  return ext => new PrecExtension(ext, value);\n}\n/**\nBy default extensions are registered in the order they are found\nin the flattened form of nested array that was provided.\nIndividual extension values can be assigned a precedence to\noverride this. Extensions that do not have a precedence set get\nthe precedence of the nearest parent with a precedence, or\n[`default`](https://codemirror.net/6/docs/ref/#state.Prec.default) if there is no such parent. The\nfinal ordering of extensions is determined by first sorting by\nprecedence and then by order within each precedence.\n*/\nconst Prec = {\n  /**\n  The highest precedence level, for extensions that should end up\n  near the start of the precedence ordering.\n  */\n  highest: /*@__PURE__*/prec(Prec_.highest),\n  /**\n  A higher-than-default precedence, for extensions that should\n  come before those with default precedence.\n  */\n  high: /*@__PURE__*/prec(Prec_.high),\n  /**\n  The default precedence, which is also used for extensions\n  without an explicit precedence.\n  */\n  default: /*@__PURE__*/prec(Prec_.default),\n  /**\n  A lower-than-default precedence.\n  */\n  low: /*@__PURE__*/prec(Prec_.low),\n  /**\n  The lowest precedence level. Meant for things that should end up\n  near the end of the extension order.\n  */\n  lowest: /*@__PURE__*/prec(Prec_.lowest)\n};\nclass PrecExtension {\n  constructor(inner, prec) {\n    this.inner = inner;\n    this.prec = prec;\n  }\n}\n/**\nExtension compartments can be used to make a configuration\ndynamic. By [wrapping](https://codemirror.net/6/docs/ref/#state.Compartment.of) part of your\nconfiguration in a compartment, you can later\n[replace](https://codemirror.net/6/docs/ref/#state.Compartment.reconfigure) that part through a\ntransaction.\n*/\nclass Compartment {\n  /**\n  Create an instance of this compartment to add to your [state\n  configuration](https://codemirror.net/6/docs/ref/#state.EditorStateConfig.extensions).\n  */\n  of(ext) {\n    return new CompartmentInstance(this, ext);\n  }\n  /**\n  Create an [effect](https://codemirror.net/6/docs/ref/#state.TransactionSpec.effects) that\n  reconfigures this compartment.\n  */\n  reconfigure(content) {\n    return Compartment.reconfigure.of({\n      compartment: this,\n      extension: content\n    });\n  }\n  /**\n  Get the current content of the compartment in the state, or\n  `undefined` if it isn't present.\n  */\n  get(state) {\n    return state.config.compartments.get(this);\n  }\n}\nclass CompartmentInstance {\n  constructor(compartment, inner) {\n    this.compartment = compartment;\n    this.inner = inner;\n  }\n}\nclass Configuration {\n  constructor(base, compartments, dynamicSlots, address, staticValues, facets) {\n    this.base = base;\n    this.compartments = compartments;\n    this.dynamicSlots = dynamicSlots;\n    this.address = address;\n    this.staticValues = staticValues;\n    this.facets = facets;\n    this.statusTemplate = [];\n    while (this.statusTemplate.length < dynamicSlots.length) this.statusTemplate.push(0 /* SlotStatus.Unresolved */);\n  }\n  staticFacet(facet) {\n    let addr = this.address[facet.id];\n    return addr == null ? facet.default : this.staticValues[addr >> 1];\n  }\n  static resolve(base, compartments, oldState) {\n    let fields = [];\n    let facets = Object.create(null);\n    let newCompartments = new Map();\n    for (let ext of flatten(base, compartments, newCompartments)) {\n      if (ext instanceof StateField) fields.push(ext);else (facets[ext.facet.id] || (facets[ext.facet.id] = [])).push(ext);\n    }\n    let address = Object.create(null);\n    let staticValues = [];\n    let dynamicSlots = [];\n    for (let field of fields) {\n      address[field.id] = dynamicSlots.length << 1;\n      dynamicSlots.push(a => field.slot(a));\n    }\n    let oldFacets = oldState === null || oldState === void 0 ? void 0 : oldState.config.facets;\n    for (let id in facets) {\n      let providers = facets[id],\n        facet = providers[0].facet;\n      let oldProviders = oldFacets && oldFacets[id] || [];\n      if (providers.every(p => p.type == 0 /* Provider.Static */)) {\n        address[facet.id] = staticValues.length << 1 | 1;\n        if (sameArray(oldProviders, providers)) {\n          staticValues.push(oldState.facet(facet));\n        } else {\n          let value = facet.combine(providers.map(p => p.value));\n          staticValues.push(oldState && facet.compare(value, oldState.facet(facet)) ? oldState.facet(facet) : value);\n        }\n      } else {\n        for (let p of providers) {\n          if (p.type == 0 /* Provider.Static */) {\n            address[p.id] = staticValues.length << 1 | 1;\n            staticValues.push(p.value);\n          } else {\n            address[p.id] = dynamicSlots.length << 1;\n            dynamicSlots.push(a => p.dynamicSlot(a));\n          }\n        }\n        address[facet.id] = dynamicSlots.length << 1;\n        dynamicSlots.push(a => dynamicFacetSlot(a, facet, providers));\n      }\n    }\n    let dynamic = dynamicSlots.map(f => f(address));\n    return new Configuration(base, newCompartments, dynamic, address, staticValues, facets);\n  }\n}\nfunction flatten(extension, compartments, newCompartments) {\n  let result = [[], [], [], [], []];\n  let seen = new Map();\n  function inner(ext, prec) {\n    let known = seen.get(ext);\n    if (known != null) {\n      if (known <= prec) return;\n      let found = result[known].indexOf(ext);\n      if (found > -1) result[known].splice(found, 1);\n      if (ext instanceof CompartmentInstance) newCompartments.delete(ext.compartment);\n    }\n    seen.set(ext, prec);\n    if (Array.isArray(ext)) {\n      for (let e of ext) inner(e, prec);\n    } else if (ext instanceof CompartmentInstance) {\n      if (newCompartments.has(ext.compartment)) throw new RangeError(`Duplicate use of compartment in extensions`);\n      let content = compartments.get(ext.compartment) || ext.inner;\n      newCompartments.set(ext.compartment, content);\n      inner(content, prec);\n    } else if (ext instanceof PrecExtension) {\n      inner(ext.inner, ext.prec);\n    } else if (ext instanceof StateField) {\n      result[prec].push(ext);\n      if (ext.provides) inner(ext.provides, prec);\n    } else if (ext instanceof FacetProvider) {\n      result[prec].push(ext);\n      if (ext.facet.extensions) inner(ext.facet.extensions, Prec_.default);\n    } else {\n      let content = ext.extension;\n      if (!content) throw new Error(`Unrecognized extension value in extension set (${ext}). This sometimes happens because multiple instances of @codemirror/state are loaded, breaking instanceof checks.`);\n      inner(content, prec);\n    }\n  }\n  inner(extension, Prec_.default);\n  return result.reduce((a, b) => a.concat(b));\n}\nfunction ensureAddr(state, addr) {\n  if (addr & 1) return 2 /* SlotStatus.Computed */;\n  let idx = addr >> 1;\n  let status = state.status[idx];\n  if (status == 4 /* SlotStatus.Computing */) throw new Error(\"Cyclic dependency between fields and/or facets\");\n  if (status & 2 /* SlotStatus.Computed */) return status;\n  state.status[idx] = 4 /* SlotStatus.Computing */;\n  let changed = state.computeSlot(state, state.config.dynamicSlots[idx]);\n  return state.status[idx] = 2 /* SlotStatus.Computed */ | changed;\n}\nfunction getAddr(state, addr) {\n  return addr & 1 ? state.config.staticValues[addr >> 1] : state.values[addr >> 1];\n}\nconst languageData = /*@__PURE__*/Facet.define();\nconst allowMultipleSelections = /*@__PURE__*/Facet.define({\n  combine: values => values.some(v => v),\n  static: true\n});\nconst lineSeparator = /*@__PURE__*/Facet.define({\n  combine: values => values.length ? values[0] : undefined,\n  static: true\n});\nconst changeFilter = /*@__PURE__*/Facet.define();\nconst transactionFilter = /*@__PURE__*/Facet.define();\nconst transactionExtender = /*@__PURE__*/Facet.define();\nconst readOnly = /*@__PURE__*/Facet.define({\n  combine: values => values.length ? values[0] : false\n});\n\n/**\nAnnotations are tagged values that are used to add metadata to\ntransactions in an extensible way. They should be used to model\nthings that effect the entire transaction (such as its [time\nstamp](https://codemirror.net/6/docs/ref/#state.Transaction^time) or information about its\n[origin](https://codemirror.net/6/docs/ref/#state.Transaction^userEvent)). For effects that happen\n_alongside_ the other changes made by the transaction, [state\neffects](https://codemirror.net/6/docs/ref/#state.StateEffect) are more appropriate.\n*/\nclass Annotation {\n  /**\n  @internal\n  */\n  constructor(\n  /**\n  The annotation type.\n  */\n  type,\n  /**\n  The value of this annotation.\n  */\n  value) {\n    this.type = type;\n    this.value = value;\n  }\n  /**\n  Define a new type of annotation.\n  */\n  static define() {\n    return new AnnotationType();\n  }\n}\n/**\nMarker that identifies a type of [annotation](https://codemirror.net/6/docs/ref/#state.Annotation).\n*/\nclass AnnotationType {\n  /**\n  Create an instance of this annotation.\n  */\n  of(value) {\n    return new Annotation(this, value);\n  }\n}\n/**\nRepresentation of a type of state effect. Defined with\n[`StateEffect.define`](https://codemirror.net/6/docs/ref/#state.StateEffect^define).\n*/\nclass StateEffectType {\n  /**\n  @internal\n  */\n  constructor(\n  // The `any` types in these function types are there to work\n  // around TypeScript issue #37631, where the type guard on\n  // `StateEffect.is` mysteriously stops working when these properly\n  // have type `Value`.\n  /**\n  @internal\n  */\n  map) {\n    this.map = map;\n  }\n  /**\n  Create a [state effect](https://codemirror.net/6/docs/ref/#state.StateEffect) instance of this\n  type.\n  */\n  of(value) {\n    return new StateEffect(this, value);\n  }\n}\n/**\nState effects can be used to represent additional effects\nassociated with a [transaction](https://codemirror.net/6/docs/ref/#state.Transaction.effects). They\nare often useful to model changes to custom [state\nfields](https://codemirror.net/6/docs/ref/#state.StateField), when those changes aren't implicit in\ndocument or selection changes.\n*/\nclass StateEffect {\n  /**\n  @internal\n  */\n  constructor(\n  /**\n  @internal\n  */\n  type,\n  /**\n  The value of this effect.\n  */\n  value) {\n    this.type = type;\n    this.value = value;\n  }\n  /**\n  Map this effect through a position mapping. Will return\n  `undefined` when that ends up deleting the effect.\n  */\n  map(mapping) {\n    let mapped = this.type.map(this.value, mapping);\n    return mapped === undefined ? undefined : mapped == this.value ? this : new StateEffect(this.type, mapped);\n  }\n  /**\n  Tells you whether this effect object is of a given\n  [type](https://codemirror.net/6/docs/ref/#state.StateEffectType).\n  */\n  is(type) {\n    return this.type == type;\n  }\n  /**\n  Define a new effect type. The type parameter indicates the type\n  of values that his effect holds. It should be a type that\n  doesn't include `undefined`, since that is used in\n  [mapping](https://codemirror.net/6/docs/ref/#state.StateEffect.map) to indicate that an effect is\n  removed.\n  */\n  static define(spec = {}) {\n    return new StateEffectType(spec.map || (v => v));\n  }\n  /**\n  Map an array of effects through a change set.\n  */\n  static mapEffects(effects, mapping) {\n    if (!effects.length) return effects;\n    let result = [];\n    for (let effect of effects) {\n      let mapped = effect.map(mapping);\n      if (mapped) result.push(mapped);\n    }\n    return result;\n  }\n}\n/**\nThis effect can be used to reconfigure the root extensions of\nthe editor. Doing this will discard any extensions\n[appended](https://codemirror.net/6/docs/ref/#state.StateEffect^appendConfig), but does not reset\nthe content of [reconfigured](https://codemirror.net/6/docs/ref/#state.Compartment.reconfigure)\ncompartments.\n*/\nStateEffect.reconfigure = /*@__PURE__*/StateEffect.define();\n/**\nAppend extensions to the top-level configuration of the editor.\n*/\nStateEffect.appendConfig = /*@__PURE__*/StateEffect.define();\n/**\nChanges to the editor state are grouped into transactions.\nTypically, a user action creates a single transaction, which may\ncontain any number of document changes, may change the selection,\nor have other effects. Create a transaction by calling\n[`EditorState.update`](https://codemirror.net/6/docs/ref/#state.EditorState.update), or immediately\ndispatch one by calling\n[`EditorView.dispatch`](https://codemirror.net/6/docs/ref/#view.EditorView.dispatch).\n*/\nclass Transaction {\n  constructor(\n  /**\n  The state from which the transaction starts.\n  */\n  startState,\n  /**\n  The document changes made by this transaction.\n  */\n  changes,\n  /**\n  The selection set by this transaction, or undefined if it\n  doesn't explicitly set a selection.\n  */\n  selection,\n  /**\n  The effects added to the transaction.\n  */\n  effects,\n  /**\n  @internal\n  */\n  annotations,\n  /**\n  Whether the selection should be scrolled into view after this\n  transaction is dispatched.\n  */\n  scrollIntoView) {\n    this.startState = startState;\n    this.changes = changes;\n    this.selection = selection;\n    this.effects = effects;\n    this.annotations = annotations;\n    this.scrollIntoView = scrollIntoView;\n    /**\n    @internal\n    */\n    this._doc = null;\n    /**\n    @internal\n    */\n    this._state = null;\n    if (selection) checkSelection(selection, changes.newLength);\n    if (!annotations.some(a => a.type == Transaction.time)) this.annotations = annotations.concat(Transaction.time.of(Date.now()));\n  }\n  /**\n  @internal\n  */\n  static create(startState, changes, selection, effects, annotations, scrollIntoView) {\n    return new Transaction(startState, changes, selection, effects, annotations, scrollIntoView);\n  }\n  /**\n  The new document produced by the transaction. Contrary to\n  [`.state`](https://codemirror.net/6/docs/ref/#state.Transaction.state)`.doc`, accessing this won't\n  force the entire new state to be computed right away, so it is\n  recommended that [transaction\n  filters](https://codemirror.net/6/docs/ref/#state.EditorState^transactionFilter) use this getter\n  when they need to look at the new document.\n  */\n  get newDoc() {\n    return this._doc || (this._doc = this.changes.apply(this.startState.doc));\n  }\n  /**\n  The new selection produced by the transaction. If\n  [`this.selection`](https://codemirror.net/6/docs/ref/#state.Transaction.selection) is undefined,\n  this will [map](https://codemirror.net/6/docs/ref/#state.EditorSelection.map) the start state's\n  current selection through the changes made by the transaction.\n  */\n  get newSelection() {\n    return this.selection || this.startState.selection.map(this.changes);\n  }\n  /**\n  The new state created by the transaction. Computed on demand\n  (but retained for subsequent access), so it is recommended not to\n  access it in [transaction\n  filters](https://codemirror.net/6/docs/ref/#state.EditorState^transactionFilter) when possible.\n  */\n  get state() {\n    if (!this._state) this.startState.applyTransaction(this);\n    return this._state;\n  }\n  /**\n  Get the value of the given annotation type, if any.\n  */\n  annotation(type) {\n    for (let ann of this.annotations) if (ann.type == type) return ann.value;\n    return undefined;\n  }\n  /**\n  Indicates whether the transaction changed the document.\n  */\n  get docChanged() {\n    return !this.changes.empty;\n  }\n  /**\n  Indicates whether this transaction reconfigures the state\n  (through a [configuration compartment](https://codemirror.net/6/docs/ref/#state.Compartment) or\n  with a top-level configuration\n  [effect](https://codemirror.net/6/docs/ref/#state.StateEffect^reconfigure).\n  */\n  get reconfigured() {\n    return this.startState.config != this.state.config;\n  }\n  /**\n  Returns true if the transaction has a [user\n  event](https://codemirror.net/6/docs/ref/#state.Transaction^userEvent) annotation that is equal to\n  or more specific than `event`. For example, if the transaction\n  has `\"select.pointer\"` as user event, `\"select\"` and\n  `\"select.pointer\"` will match it.\n  */\n  isUserEvent(event) {\n    let e = this.annotation(Transaction.userEvent);\n    return !!(e && (e == event || e.length > event.length && e.slice(0, event.length) == event && e[event.length] == \".\"));\n  }\n}\n/**\nAnnotation used to store transaction timestamps. Automatically\nadded to every transaction, holding `Date.now()`.\n*/\nTransaction.time = /*@__PURE__*/Annotation.define();\n/**\nAnnotation used to associate a transaction with a user interface\nevent. Holds a string identifying the event, using a\ndot-separated format to support attaching more specific\ninformation. The events used by the core libraries are:\n\n - `\"input\"` when content is entered\n   - `\"input.type\"` for typed input\n     - `\"input.type.compose\"` for composition\n   - `\"input.paste\"` for pasted input\n   - `\"input.drop\"` when adding content with drag-and-drop\n   - `\"input.complete\"` when autocompleting\n - `\"delete\"` when the user deletes content\n   - `\"delete.selection\"` when deleting the selection\n   - `\"delete.forward\"` when deleting forward from the selection\n   - `\"delete.backward\"` when deleting backward from the selection\n   - `\"delete.cut\"` when cutting to the clipboard\n - `\"move\"` when content is moved\n   - `\"move.drop\"` when content is moved within the editor through drag-and-drop\n - `\"select\"` when explicitly changing the selection\n   - `\"select.pointer\"` when selecting with a mouse or other pointing device\n - `\"undo\"` and `\"redo\"` for history actions\n\nUse [`isUserEvent`](https://codemirror.net/6/docs/ref/#state.Transaction.isUserEvent) to check\nwhether the annotation matches a given event.\n*/\nTransaction.userEvent = /*@__PURE__*/Annotation.define();\n/**\nAnnotation indicating whether a transaction should be added to\nthe undo history or not.\n*/\nTransaction.addToHistory = /*@__PURE__*/Annotation.define();\n/**\nAnnotation indicating (when present and true) that a transaction\nrepresents a change made by some other actor, not the user. This\nis used, for example, to tag other people's changes in\ncollaborative editing.\n*/\nTransaction.remote = /*@__PURE__*/Annotation.define();\nfunction joinRanges(a, b) {\n  let result = [];\n  for (let iA = 0, iB = 0;;) {\n    let from, to;\n    if (iA < a.length && (iB == b.length || b[iB] >= a[iA])) {\n      from = a[iA++];\n      to = a[iA++];\n    } else if (iB < b.length) {\n      from = b[iB++];\n      to = b[iB++];\n    } else return result;\n    if (!result.length || result[result.length - 1] < from) result.push(from, to);else if (result[result.length - 1] < to) result[result.length - 1] = to;\n  }\n}\nfunction mergeTransaction(a, b, sequential) {\n  var _a;\n  let mapForA, mapForB, changes;\n  if (sequential) {\n    mapForA = b.changes;\n    mapForB = ChangeSet.empty(b.changes.length);\n    changes = a.changes.compose(b.changes);\n  } else {\n    mapForA = b.changes.map(a.changes);\n    mapForB = a.changes.mapDesc(b.changes, true);\n    changes = a.changes.compose(mapForA);\n  }\n  return {\n    changes,\n    selection: b.selection ? b.selection.map(mapForB) : (_a = a.selection) === null || _a === void 0 ? void 0 : _a.map(mapForA),\n    effects: StateEffect.mapEffects(a.effects, mapForA).concat(StateEffect.mapEffects(b.effects, mapForB)),\n    annotations: a.annotations.length ? a.annotations.concat(b.annotations) : b.annotations,\n    scrollIntoView: a.scrollIntoView || b.scrollIntoView\n  };\n}\nfunction resolveTransactionInner(state, spec, docSize) {\n  let sel = spec.selection,\n    annotations = asArray(spec.annotations);\n  if (spec.userEvent) annotations = annotations.concat(Transaction.userEvent.of(spec.userEvent));\n  return {\n    changes: spec.changes instanceof ChangeSet ? spec.changes : ChangeSet.of(spec.changes || [], docSize, state.facet(lineSeparator)),\n    selection: sel && (sel instanceof EditorSelection ? sel : EditorSelection.single(sel.anchor, sel.head)),\n    effects: asArray(spec.effects),\n    annotations,\n    scrollIntoView: !!spec.scrollIntoView\n  };\n}\nfunction resolveTransaction(state, specs, filter) {\n  let s = resolveTransactionInner(state, specs.length ? specs[0] : {}, state.doc.length);\n  if (specs.length && specs[0].filter === false) filter = false;\n  for (let i = 1; i < specs.length; i++) {\n    if (specs[i].filter === false) filter = false;\n    let seq = !!specs[i].sequential;\n    s = mergeTransaction(s, resolveTransactionInner(state, specs[i], seq ? s.changes.newLength : state.doc.length), seq);\n  }\n  let tr = Transaction.create(state, s.changes, s.selection, s.effects, s.annotations, s.scrollIntoView);\n  return extendTransaction(filter ? filterTransaction(tr) : tr);\n}\n// Finish a transaction by applying filters if necessary.\nfunction filterTransaction(tr) {\n  let state = tr.startState;\n  // Change filters\n  let result = true;\n  for (let filter of state.facet(changeFilter)) {\n    let value = filter(tr);\n    if (value === false) {\n      result = false;\n      break;\n    }\n    if (Array.isArray(value)) result = result === true ? value : joinRanges(result, value);\n  }\n  if (result !== true) {\n    let changes, back;\n    if (result === false) {\n      back = tr.changes.invertedDesc;\n      changes = ChangeSet.empty(state.doc.length);\n    } else {\n      let filtered = tr.changes.filter(result);\n      changes = filtered.changes;\n      back = filtered.filtered.mapDesc(filtered.changes).invertedDesc;\n    }\n    tr = Transaction.create(state, changes, tr.selection && tr.selection.map(back), StateEffect.mapEffects(tr.effects, back), tr.annotations, tr.scrollIntoView);\n  }\n  // Transaction filters\n  let filters = state.facet(transactionFilter);\n  for (let i = filters.length - 1; i >= 0; i--) {\n    let filtered = filters[i](tr);\n    if (filtered instanceof Transaction) tr = filtered;else if (Array.isArray(filtered) && filtered.length == 1 && filtered[0] instanceof Transaction) tr = filtered[0];else tr = resolveTransaction(state, asArray(filtered), false);\n  }\n  return tr;\n}\nfunction extendTransaction(tr) {\n  let state = tr.startState,\n    extenders = state.facet(transactionExtender),\n    spec = tr;\n  for (let i = extenders.length - 1; i >= 0; i--) {\n    let extension = extenders[i](tr);\n    if (extension && Object.keys(extension).length) spec = mergeTransaction(spec, resolveTransactionInner(state, extension, tr.changes.newLength), true);\n  }\n  return spec == tr ? tr : Transaction.create(state, tr.changes, tr.selection, spec.effects, spec.annotations, spec.scrollIntoView);\n}\nconst none = [];\nfunction asArray(value) {\n  return value == null ? none : Array.isArray(value) ? value : [value];\n}\n\n/**\nThe categories produced by a [character\ncategorizer](https://codemirror.net/6/docs/ref/#state.EditorState.charCategorizer). These are used\ndo things like selecting by word.\n*/\nvar CharCategory = /*@__PURE__*/function (CharCategory) {\n  /**\n  Word characters.\n  */\n  CharCategory[CharCategory[\"Word\"] = 0] = \"Word\";\n  /**\n  Whitespace.\n  */\n  CharCategory[CharCategory[\"Space\"] = 1] = \"Space\";\n  /**\n  Anything else.\n  */\n  CharCategory[CharCategory[\"Other\"] = 2] = \"Other\";\n  return CharCategory;\n}(CharCategory || (CharCategory = {}));\nconst nonASCIISingleCaseWordChar = /[\\u00df\\u0587\\u0590-\\u05f4\\u0600-\\u06ff\\u3040-\\u309f\\u30a0-\\u30ff\\u3400-\\u4db5\\u4e00-\\u9fcc\\uac00-\\ud7af]/;\nlet wordChar;\ntry {\n  wordChar = /*@__PURE__*/new RegExp(\"[\\\\p{Alphabetic}\\\\p{Number}_]\", \"u\");\n} catch (_) {}\nfunction hasWordChar(str) {\n  if (wordChar) return wordChar.test(str);\n  for (let i = 0; i < str.length; i++) {\n    let ch = str[i];\n    if (/\\w/.test(ch) || ch > \"\\x80\" && (ch.toUpperCase() != ch.toLowerCase() || nonASCIISingleCaseWordChar.test(ch))) return true;\n  }\n  return false;\n}\nfunction makeCategorizer(wordChars) {\n  return char => {\n    if (!/\\S/.test(char)) return CharCategory.Space;\n    if (hasWordChar(char)) return CharCategory.Word;\n    for (let i = 0; i < wordChars.length; i++) if (char.indexOf(wordChars[i]) > -1) return CharCategory.Word;\n    return CharCategory.Other;\n  };\n}\n\n/**\nThe editor state class is a persistent (immutable) data structure.\nTo update a state, you [create](https://codemirror.net/6/docs/ref/#state.EditorState.update) a\n[transaction](https://codemirror.net/6/docs/ref/#state.Transaction), which produces a _new_ state\ninstance, without modifying the original object.\n\nAs such, _never_ mutate properties of a state directly. That'll\njust break things.\n*/\nclass EditorState {\n  constructor(\n  /**\n  @internal\n  */\n  config,\n  /**\n  The current document.\n  */\n  doc,\n  /**\n  The current selection.\n  */\n  selection,\n  /**\n  @internal\n  */\n  values, computeSlot, tr) {\n    this.config = config;\n    this.doc = doc;\n    this.selection = selection;\n    this.values = values;\n    this.status = config.statusTemplate.slice();\n    this.computeSlot = computeSlot;\n    // Fill in the computed state immediately, so that further queries\n    // for it made during the update return this state\n    if (tr) tr._state = this;\n    for (let i = 0; i < this.config.dynamicSlots.length; i++) ensureAddr(this, i << 1);\n    this.computeSlot = null;\n  }\n  field(field, require = true) {\n    let addr = this.config.address[field.id];\n    if (addr == null) {\n      if (require) throw new RangeError(\"Field is not present in this state\");\n      return undefined;\n    }\n    ensureAddr(this, addr);\n    return getAddr(this, addr);\n  }\n  /**\n  Create a [transaction](https://codemirror.net/6/docs/ref/#state.Transaction) that updates this\n  state. Any number of [transaction specs](https://codemirror.net/6/docs/ref/#state.TransactionSpec)\n  can be passed. Unless\n  [`sequential`](https://codemirror.net/6/docs/ref/#state.TransactionSpec.sequential) is set, the\n  [changes](https://codemirror.net/6/docs/ref/#state.TransactionSpec.changes) (if any) of each spec\n  are assumed to start in the _current_ document (not the document\n  produced by previous specs), and its\n  [selection](https://codemirror.net/6/docs/ref/#state.TransactionSpec.selection) and\n  [effects](https://codemirror.net/6/docs/ref/#state.TransactionSpec.effects) are assumed to refer\n  to the document created by its _own_ changes. The resulting\n  transaction contains the combined effect of all the different\n  specs. For [selection](https://codemirror.net/6/docs/ref/#state.TransactionSpec.selection), later\n  specs take precedence over earlier ones.\n  */\n  update(...specs) {\n    return resolveTransaction(this, specs, true);\n  }\n  /**\n  @internal\n  */\n  applyTransaction(tr) {\n    let conf = this.config,\n      {\n        base,\n        compartments\n      } = conf;\n    for (let effect of tr.effects) {\n      if (effect.is(Compartment.reconfigure)) {\n        if (conf) {\n          compartments = new Map();\n          conf.compartments.forEach((val, key) => compartments.set(key, val));\n          conf = null;\n        }\n        compartments.set(effect.value.compartment, effect.value.extension);\n      } else if (effect.is(StateEffect.reconfigure)) {\n        conf = null;\n        base = effect.value;\n      } else if (effect.is(StateEffect.appendConfig)) {\n        conf = null;\n        base = asArray(base).concat(effect.value);\n      }\n    }\n    let startValues;\n    if (!conf) {\n      conf = Configuration.resolve(base, compartments, this);\n      let intermediateState = new EditorState(conf, this.doc, this.selection, conf.dynamicSlots.map(() => null), (state, slot) => slot.reconfigure(state, this), null);\n      startValues = intermediateState.values;\n    } else {\n      startValues = tr.startState.values.slice();\n    }\n    let selection = tr.startState.facet(allowMultipleSelections) ? tr.newSelection : tr.newSelection.asSingle();\n    new EditorState(conf, tr.newDoc, selection, startValues, (state, slot) => slot.update(state, tr), tr);\n  }\n  /**\n  Create a [transaction spec](https://codemirror.net/6/docs/ref/#state.TransactionSpec) that\n  replaces every selection range with the given content.\n  */\n  replaceSelection(text) {\n    if (typeof text == \"string\") text = this.toText(text);\n    return this.changeByRange(range => ({\n      changes: {\n        from: range.from,\n        to: range.to,\n        insert: text\n      },\n      range: EditorSelection.cursor(range.from + text.length)\n    }));\n  }\n  /**\n  Create a set of changes and a new selection by running the given\n  function for each range in the active selection. The function\n  can return an optional set of changes (in the coordinate space\n  of the start document), plus an updated range (in the coordinate\n  space of the document produced by the call's own changes). This\n  method will merge all the changes and ranges into a single\n  changeset and selection, and return it as a [transaction\n  spec](https://codemirror.net/6/docs/ref/#state.TransactionSpec), which can be passed to\n  [`update`](https://codemirror.net/6/docs/ref/#state.EditorState.update).\n  */\n  changeByRange(f) {\n    let sel = this.selection;\n    let result1 = f(sel.ranges[0]);\n    let changes = this.changes(result1.changes),\n      ranges = [result1.range];\n    let effects = asArray(result1.effects);\n    for (let i = 1; i < sel.ranges.length; i++) {\n      let result = f(sel.ranges[i]);\n      let newChanges = this.changes(result.changes),\n        newMapped = newChanges.map(changes);\n      for (let j = 0; j < i; j++) ranges[j] = ranges[j].map(newMapped);\n      let mapBy = changes.mapDesc(newChanges, true);\n      ranges.push(result.range.map(mapBy));\n      changes = changes.compose(newMapped);\n      effects = StateEffect.mapEffects(effects, newMapped).concat(StateEffect.mapEffects(asArray(result.effects), mapBy));\n    }\n    return {\n      changes,\n      selection: EditorSelection.create(ranges, sel.mainIndex),\n      effects\n    };\n  }\n  /**\n  Create a [change set](https://codemirror.net/6/docs/ref/#state.ChangeSet) from the given change\n  description, taking the state's document length and line\n  separator into account.\n  */\n  changes(spec = []) {\n    if (spec instanceof ChangeSet) return spec;\n    return ChangeSet.of(spec, this.doc.length, this.facet(EditorState.lineSeparator));\n  }\n  /**\n  Using the state's [line\n  separator](https://codemirror.net/6/docs/ref/#state.EditorState^lineSeparator), create a\n  [`Text`](https://codemirror.net/6/docs/ref/#state.Text) instance from the given string.\n  */\n  toText(string) {\n    return Text.of(string.split(this.facet(EditorState.lineSeparator) || DefaultSplit));\n  }\n  /**\n  Return the given range of the document as a string.\n  */\n  sliceDoc(from = 0, to = this.doc.length) {\n    return this.doc.sliceString(from, to, this.lineBreak);\n  }\n  /**\n  Get the value of a state [facet](https://codemirror.net/6/docs/ref/#state.Facet).\n  */\n  facet(facet) {\n    let addr = this.config.address[facet.id];\n    if (addr == null) return facet.default;\n    ensureAddr(this, addr);\n    return getAddr(this, addr);\n  }\n  /**\n  Convert this state to a JSON-serializable object. When custom\n  fields should be serialized, you can pass them in as an object\n  mapping property names (in the resulting object, which should\n  not use `doc` or `selection`) to fields.\n  */\n  toJSON(fields) {\n    let result = {\n      doc: this.sliceDoc(),\n      selection: this.selection.toJSON()\n    };\n    if (fields) for (let prop in fields) {\n      let value = fields[prop];\n      if (value instanceof StateField && this.config.address[value.id] != null) result[prop] = value.spec.toJSON(this.field(fields[prop]), this);\n    }\n    return result;\n  }\n  /**\n  Deserialize a state from its JSON representation. When custom\n  fields should be deserialized, pass the same object you passed\n  to [`toJSON`](https://codemirror.net/6/docs/ref/#state.EditorState.toJSON) when serializing as\n  third argument.\n  */\n  static fromJSON(json, config = {}, fields) {\n    if (!json || typeof json.doc != \"string\") throw new RangeError(\"Invalid JSON representation for EditorState\");\n    let fieldInit = [];\n    if (fields) for (let prop in fields) {\n      if (Object.prototype.hasOwnProperty.call(json, prop)) {\n        let field = fields[prop],\n          value = json[prop];\n        fieldInit.push(field.init(state => field.spec.fromJSON(value, state)));\n      }\n    }\n    return EditorState.create({\n      doc: json.doc,\n      selection: EditorSelection.fromJSON(json.selection),\n      extensions: config.extensions ? fieldInit.concat([config.extensions]) : fieldInit\n    });\n  }\n  /**\n  Create a new state. You'll usually only need this when\n  initializing an editor—updated states are created by applying\n  transactions.\n  */\n  static create(config = {}) {\n    let configuration = Configuration.resolve(config.extensions || [], new Map());\n    let doc = config.doc instanceof Text ? config.doc : Text.of((config.doc || \"\").split(configuration.staticFacet(EditorState.lineSeparator) || DefaultSplit));\n    let selection = !config.selection ? EditorSelection.single(0) : config.selection instanceof EditorSelection ? config.selection : EditorSelection.single(config.selection.anchor, config.selection.head);\n    checkSelection(selection, doc.length);\n    if (!configuration.staticFacet(allowMultipleSelections)) selection = selection.asSingle();\n    return new EditorState(configuration, doc, selection, configuration.dynamicSlots.map(() => null), (state, slot) => slot.create(state), null);\n  }\n  /**\n  The size (in columns) of a tab in the document, determined by\n  the [`tabSize`](https://codemirror.net/6/docs/ref/#state.EditorState^tabSize) facet.\n  */\n  get tabSize() {\n    return this.facet(EditorState.tabSize);\n  }\n  /**\n  Get the proper [line-break](https://codemirror.net/6/docs/ref/#state.EditorState^lineSeparator)\n  string for this state.\n  */\n  get lineBreak() {\n    return this.facet(EditorState.lineSeparator) || \"\\n\";\n  }\n  /**\n  Returns true when the editor is\n  [configured](https://codemirror.net/6/docs/ref/#state.EditorState^readOnly) to be read-only.\n  */\n  get readOnly() {\n    return this.facet(readOnly);\n  }\n  /**\n  Look up a translation for the given phrase (via the\n  [`phrases`](https://codemirror.net/6/docs/ref/#state.EditorState^phrases) facet), or return the\n  original string if no translation is found.\n  \n  If additional arguments are passed, they will be inserted in\n  place of markers like `$1` (for the first value) and `$2`, etc.\n  A single `$` is equivalent to `$1`, and `$$` will produce a\n  literal dollar sign.\n  */\n  phrase(phrase, ...insert) {\n    for (let map of this.facet(EditorState.phrases)) if (Object.prototype.hasOwnProperty.call(map, phrase)) {\n      phrase = map[phrase];\n      break;\n    }\n    if (insert.length) phrase = phrase.replace(/\\$(\\$|\\d*)/g, (m, i) => {\n      if (i == \"$\") return \"$\";\n      let n = +(i || 1);\n      return !n || n > insert.length ? m : insert[n - 1];\n    });\n    return phrase;\n  }\n  /**\n  Find the values for a given language data field, provided by the\n  the [`languageData`](https://codemirror.net/6/docs/ref/#state.EditorState^languageData) facet.\n  \n  Examples of language data fields are...\n  \n  - [`\"commentTokens\"`](https://codemirror.net/6/docs/ref/#commands.CommentTokens) for specifying\n    comment syntax.\n  - [`\"autocomplete\"`](https://codemirror.net/6/docs/ref/#autocomplete.autocompletion^config.override)\n    for providing language-specific completion sources.\n  - [`\"wordChars\"`](https://codemirror.net/6/docs/ref/#state.EditorState.charCategorizer) for adding\n    characters that should be considered part of words in this\n    language.\n  - [`\"closeBrackets\"`](https://codemirror.net/6/docs/ref/#autocomplete.CloseBracketConfig) controls\n    bracket closing behavior.\n  */\n  languageDataAt(name, pos, side = -1) {\n    let values = [];\n    for (let provider of this.facet(languageData)) {\n      for (let result of provider(this, pos, side)) {\n        if (Object.prototype.hasOwnProperty.call(result, name)) values.push(result[name]);\n      }\n    }\n    return values;\n  }\n  /**\n  Return a function that can categorize strings (expected to\n  represent a single [grapheme cluster](https://codemirror.net/6/docs/ref/#state.findClusterBreak))\n  into one of:\n  \n   - Word (contains an alphanumeric character or a character\n     explicitly listed in the local language's `\"wordChars\"`\n     language data, which should be a string)\n   - Space (contains only whitespace)\n   - Other (anything else)\n  */\n  charCategorizer(at) {\n    return makeCategorizer(this.languageDataAt(\"wordChars\", at).join(\"\"));\n  }\n  /**\n  Find the word at the given position, meaning the range\n  containing all [word](https://codemirror.net/6/docs/ref/#state.CharCategory.Word) characters\n  around it. If no word characters are adjacent to the position,\n  this returns null.\n  */\n  wordAt(pos) {\n    let {\n      text,\n      from,\n      length\n    } = this.doc.lineAt(pos);\n    let cat = this.charCategorizer(pos);\n    let start = pos - from,\n      end = pos - from;\n    while (start > 0) {\n      let prev = findClusterBreak(text, start, false);\n      if (cat(text.slice(prev, start)) != CharCategory.Word) break;\n      start = prev;\n    }\n    while (end < length) {\n      let next = findClusterBreak(text, end);\n      if (cat(text.slice(end, next)) != CharCategory.Word) break;\n      end = next;\n    }\n    return start == end ? null : EditorSelection.range(start + from, end + from);\n  }\n}\n/**\nA facet that, when enabled, causes the editor to allow multiple\nranges to be selected. Be careful though, because by default the\neditor relies on the native DOM selection, which cannot handle\nmultiple selections. An extension like\n[`drawSelection`](https://codemirror.net/6/docs/ref/#view.drawSelection) can be used to make\nsecondary selections visible to the user.\n*/\nEditorState.allowMultipleSelections = allowMultipleSelections;\n/**\nConfigures the tab size to use in this state. The first\n(highest-precedence) value of the facet is used. If no value is\ngiven, this defaults to 4.\n*/\nEditorState.tabSize = /*@__PURE__*/Facet.define({\n  combine: values => values.length ? values[0] : 4\n});\n/**\nThe line separator to use. By default, any of `\"\\n\"`, `\"\\r\\n\"`\nand `\"\\r\"` is treated as a separator when splitting lines, and\nlines are joined with `\"\\n\"`.\n\nWhen you configure a value here, only that precise separator\nwill be used, allowing you to round-trip documents through the\neditor without normalizing line separators.\n*/\nEditorState.lineSeparator = lineSeparator;\n/**\nThis facet controls the value of the\n[`readOnly`](https://codemirror.net/6/docs/ref/#state.EditorState.readOnly) getter, which is\nconsulted by commands and extensions that implement editing\nfunctionality to determine whether they should apply. It\ndefaults to false, but when its highest-precedence value is\n`true`, such functionality disables itself.\n\nNot to be confused with\n[`EditorView.editable`](https://codemirror.net/6/docs/ref/#view.EditorView^editable), which\ncontrols whether the editor's DOM is set to be editable (and\nthus focusable).\n*/\nEditorState.readOnly = readOnly;\n/**\nRegisters translation phrases. The\n[`phrase`](https://codemirror.net/6/docs/ref/#state.EditorState.phrase) method will look through\nall objects registered with this facet to find translations for\nits argument.\n*/\nEditorState.phrases = /*@__PURE__*/Facet.define({\n  compare(a, b) {\n    let kA = Object.keys(a),\n      kB = Object.keys(b);\n    return kA.length == kB.length && kA.every(k => a[k] == b[k]);\n  }\n});\n/**\nA facet used to register [language\ndata](https://codemirror.net/6/docs/ref/#state.EditorState.languageDataAt) providers.\n*/\nEditorState.languageData = languageData;\n/**\nFacet used to register change filters, which are called for each\ntransaction (unless explicitly\n[disabled](https://codemirror.net/6/docs/ref/#state.TransactionSpec.filter)), and can suppress\npart of the transaction's changes.\n\nSuch a function can return `true` to indicate that it doesn't\nwant to do anything, `false` to completely stop the changes in\nthe transaction, or a set of ranges in which changes should be\nsuppressed. Such ranges are represented as an array of numbers,\nwith each pair of two numbers indicating the start and end of a\nrange. So for example `[10, 20, 100, 110]` suppresses changes\nbetween 10 and 20, and between 100 and 110.\n*/\nEditorState.changeFilter = changeFilter;\n/**\nFacet used to register a hook that gets a chance to update or\nreplace transaction specs before they are applied. This will\nonly be applied for transactions that don't have\n[`filter`](https://codemirror.net/6/docs/ref/#state.TransactionSpec.filter) set to `false`. You\ncan either return a single transaction spec (possibly the input\ntransaction), or an array of specs (which will be combined in\nthe same way as the arguments to\n[`EditorState.update`](https://codemirror.net/6/docs/ref/#state.EditorState.update)).\n\nWhen possible, it is recommended to avoid accessing\n[`Transaction.state`](https://codemirror.net/6/docs/ref/#state.Transaction.state) in a filter,\nsince it will force creation of a state that will then be\ndiscarded again, if the transaction is actually filtered.\n\n(This functionality should be used with care. Indiscriminately\nmodifying transaction is likely to break something or degrade\nthe user experience.)\n*/\nEditorState.transactionFilter = transactionFilter;\n/**\nThis is a more limited form of\n[`transactionFilter`](https://codemirror.net/6/docs/ref/#state.EditorState^transactionFilter),\nwhich can only add\n[annotations](https://codemirror.net/6/docs/ref/#state.TransactionSpec.annotations) and\n[effects](https://codemirror.net/6/docs/ref/#state.TransactionSpec.effects). _But_, this type\nof filter runs even if the transaction has disabled regular\n[filtering](https://codemirror.net/6/docs/ref/#state.TransactionSpec.filter), making it suitable\nfor effects that don't need to touch the changes or selection,\nbut do want to process every transaction.\n\nExtenders run _after_ filters, when both are present.\n*/\nEditorState.transactionExtender = transactionExtender;\nCompartment.reconfigure = /*@__PURE__*/StateEffect.define();\n\n/**\nUtility function for combining behaviors to fill in a config\nobject from an array of provided configs. `defaults` should hold\ndefault values for all optional fields in `Config`.\n\nThe function will, by default, error\nwhen a field gets two values that aren't `===`-equal, but you can\nprovide combine functions per field to do something else.\n*/\nfunction combineConfig(configs, defaults,\n// Should hold only the optional properties of Config, but I haven't managed to express that\ncombine = {}) {\n  let result = {};\n  for (let config of configs) for (let key of Object.keys(config)) {\n    let value = config[key],\n      current = result[key];\n    if (current === undefined) result[key] = value;else if (current === value || value === undefined) ; // No conflict\n    else if (Object.hasOwnProperty.call(combine, key)) result[key] = combine[key](current, value);else throw new Error(\"Config merge conflict for field \" + key);\n  }\n  for (let key in defaults) if (result[key] === undefined) result[key] = defaults[key];\n  return result;\n}\n\n/**\nEach range is associated with a value, which must inherit from\nthis class.\n*/\nclass RangeValue {\n  /**\n  Compare this value with another value. Used when comparing\n  rangesets. The default implementation compares by identity.\n  Unless you are only creating a fixed number of unique instances\n  of your value type, it is a good idea to implement this\n  properly.\n  */\n  eq(other) {\n    return this == other;\n  }\n  /**\n  Create a [range](https://codemirror.net/6/docs/ref/#state.Range) with this value.\n  */\n  range(from, to = from) {\n    return Range.create(from, to, this);\n  }\n}\nRangeValue.prototype.startSide = RangeValue.prototype.endSide = 0;\nRangeValue.prototype.point = false;\nRangeValue.prototype.mapMode = MapMode.TrackDel;\n/**\nA range associates a value with a range of positions.\n*/\nclass Range {\n  constructor(\n  /**\n  The range's start position.\n  */\n  from,\n  /**\n  Its end position.\n  */\n  to,\n  /**\n  The value associated with this range.\n  */\n  value) {\n    this.from = from;\n    this.to = to;\n    this.value = value;\n  }\n  /**\n  @internal\n  */\n  static create(from, to, value) {\n    return new Range(from, to, value);\n  }\n}\nfunction cmpRange(a, b) {\n  return a.from - b.from || a.value.startSide - b.value.startSide;\n}\nclass Chunk {\n  constructor(from, to, value,\n  // Chunks are marked with the largest point that occurs\n  // in them (or -1 for no points), so that scans that are\n  // only interested in points (such as the\n  // heightmap-related logic) can skip range-only chunks.\n  maxPoint) {\n    this.from = from;\n    this.to = to;\n    this.value = value;\n    this.maxPoint = maxPoint;\n  }\n  get length() {\n    return this.to[this.to.length - 1];\n  }\n  // Find the index of the given position and side. Use the ranges'\n  // `from` pos when `end == false`, `to` when `end == true`.\n  findIndex(pos, side, end, startAt = 0) {\n    let arr = end ? this.to : this.from;\n    for (let lo = startAt, hi = arr.length;;) {\n      if (lo == hi) return lo;\n      let mid = lo + hi >> 1;\n      let diff = arr[mid] - pos || (end ? this.value[mid].endSide : this.value[mid].startSide) - side;\n      if (mid == lo) return diff >= 0 ? lo : hi;\n      if (diff >= 0) hi = mid;else lo = mid + 1;\n    }\n  }\n  between(offset, from, to, f) {\n    for (let i = this.findIndex(from, -********** /* C.Far */, true), e = this.findIndex(to, ********** /* C.Far */, false, i); i < e; i++) if (f(this.from[i] + offset, this.to[i] + offset, this.value[i]) === false) return false;\n  }\n  map(offset, changes) {\n    let value = [],\n      from = [],\n      to = [],\n      newPos = -1,\n      maxPoint = -1;\n    for (let i = 0; i < this.value.length; i++) {\n      let val = this.value[i],\n        curFrom = this.from[i] + offset,\n        curTo = this.to[i] + offset,\n        newFrom,\n        newTo;\n      if (curFrom == curTo) {\n        let mapped = changes.mapPos(curFrom, val.startSide, val.mapMode);\n        if (mapped == null) continue;\n        newFrom = newTo = mapped;\n        if (val.startSide != val.endSide) {\n          newTo = changes.mapPos(curFrom, val.endSide);\n          if (newTo < newFrom) continue;\n        }\n      } else {\n        newFrom = changes.mapPos(curFrom, val.startSide);\n        newTo = changes.mapPos(curTo, val.endSide);\n        if (newFrom > newTo || newFrom == newTo && val.startSide > 0 && val.endSide <= 0) continue;\n      }\n      if ((newTo - newFrom || val.endSide - val.startSide) < 0) continue;\n      if (newPos < 0) newPos = newFrom;\n      if (val.point) maxPoint = Math.max(maxPoint, newTo - newFrom);\n      value.push(val);\n      from.push(newFrom - newPos);\n      to.push(newTo - newPos);\n    }\n    return {\n      mapped: value.length ? new Chunk(from, to, value, maxPoint) : null,\n      pos: newPos\n    };\n  }\n}\n/**\nA range set stores a collection of [ranges](https://codemirror.net/6/docs/ref/#state.Range) in a\nway that makes them efficient to [map](https://codemirror.net/6/docs/ref/#state.RangeSet.map) and\n[update](https://codemirror.net/6/docs/ref/#state.RangeSet.update). This is an immutable data\nstructure.\n*/\nclass RangeSet {\n  constructor(\n  /**\n  @internal\n  */\n  chunkPos,\n  /**\n  @internal\n  */\n  chunk,\n  /**\n  @internal\n  */\n  nextLayer,\n  /**\n  @internal\n  */\n  maxPoint) {\n    this.chunkPos = chunkPos;\n    this.chunk = chunk;\n    this.nextLayer = nextLayer;\n    this.maxPoint = maxPoint;\n  }\n  /**\n  @internal\n  */\n  static create(chunkPos, chunk, nextLayer, maxPoint) {\n    return new RangeSet(chunkPos, chunk, nextLayer, maxPoint);\n  }\n  /**\n  @internal\n  */\n  get length() {\n    let last = this.chunk.length - 1;\n    return last < 0 ? 0 : Math.max(this.chunkEnd(last), this.nextLayer.length);\n  }\n  /**\n  The number of ranges in the set.\n  */\n  get size() {\n    if (this.isEmpty) return 0;\n    let size = this.nextLayer.size;\n    for (let chunk of this.chunk) size += chunk.value.length;\n    return size;\n  }\n  /**\n  @internal\n  */\n  chunkEnd(index) {\n    return this.chunkPos[index] + this.chunk[index].length;\n  }\n  /**\n  Update the range set, optionally adding new ranges or filtering\n  out existing ones.\n  \n  (Note: The type parameter is just there as a kludge to work\n  around TypeScript variance issues that prevented `RangeSet<X>`\n  from being a subtype of `RangeSet<Y>` when `X` is a subtype of\n  `Y`.)\n  */\n  update(updateSpec) {\n    let {\n      add = [],\n      sort = false,\n      filterFrom = 0,\n      filterTo = this.length\n    } = updateSpec;\n    let filter = updateSpec.filter;\n    if (add.length == 0 && !filter) return this;\n    if (sort) add = add.slice().sort(cmpRange);\n    if (this.isEmpty) return add.length ? RangeSet.of(add) : this;\n    let cur = new LayerCursor(this, null, -1).goto(0),\n      i = 0,\n      spill = [];\n    let builder = new RangeSetBuilder();\n    while (cur.value || i < add.length) {\n      if (i < add.length && (cur.from - add[i].from || cur.startSide - add[i].value.startSide) >= 0) {\n        let range = add[i++];\n        if (!builder.addInner(range.from, range.to, range.value)) spill.push(range);\n      } else if (cur.rangeIndex == 1 && cur.chunkIndex < this.chunk.length && (i == add.length || this.chunkEnd(cur.chunkIndex) < add[i].from) && (!filter || filterFrom > this.chunkEnd(cur.chunkIndex) || filterTo < this.chunkPos[cur.chunkIndex]) && builder.addChunk(this.chunkPos[cur.chunkIndex], this.chunk[cur.chunkIndex])) {\n        cur.nextChunk();\n      } else {\n        if (!filter || filterFrom > cur.to || filterTo < cur.from || filter(cur.from, cur.to, cur.value)) {\n          if (!builder.addInner(cur.from, cur.to, cur.value)) spill.push(Range.create(cur.from, cur.to, cur.value));\n        }\n        cur.next();\n      }\n    }\n    return builder.finishInner(this.nextLayer.isEmpty && !spill.length ? RangeSet.empty : this.nextLayer.update({\n      add: spill,\n      filter,\n      filterFrom,\n      filterTo\n    }));\n  }\n  /**\n  Map this range set through a set of changes, return the new set.\n  */\n  map(changes) {\n    if (changes.empty || this.isEmpty) return this;\n    let chunks = [],\n      chunkPos = [],\n      maxPoint = -1;\n    for (let i = 0; i < this.chunk.length; i++) {\n      let start = this.chunkPos[i],\n        chunk = this.chunk[i];\n      let touch = changes.touchesRange(start, start + chunk.length);\n      if (touch === false) {\n        maxPoint = Math.max(maxPoint, chunk.maxPoint);\n        chunks.push(chunk);\n        chunkPos.push(changes.mapPos(start));\n      } else if (touch === true) {\n        let {\n          mapped,\n          pos\n        } = chunk.map(start, changes);\n        if (mapped) {\n          maxPoint = Math.max(maxPoint, mapped.maxPoint);\n          chunks.push(mapped);\n          chunkPos.push(pos);\n        }\n      }\n    }\n    let next = this.nextLayer.map(changes);\n    return chunks.length == 0 ? next : new RangeSet(chunkPos, chunks, next || RangeSet.empty, maxPoint);\n  }\n  /**\n  Iterate over the ranges that touch the region `from` to `to`,\n  calling `f` for each. There is no guarantee that the ranges will\n  be reported in any specific order. When the callback returns\n  `false`, iteration stops.\n  */\n  between(from, to, f) {\n    if (this.isEmpty) return;\n    for (let i = 0; i < this.chunk.length; i++) {\n      let start = this.chunkPos[i],\n        chunk = this.chunk[i];\n      if (to >= start && from <= start + chunk.length && chunk.between(start, from - start, to - start, f) === false) return;\n    }\n    this.nextLayer.between(from, to, f);\n  }\n  /**\n  Iterate over the ranges in this set, in order, including all\n  ranges that end at or after `from`.\n  */\n  iter(from = 0) {\n    return HeapCursor.from([this]).goto(from);\n  }\n  /**\n  @internal\n  */\n  get isEmpty() {\n    return this.nextLayer == this;\n  }\n  /**\n  Iterate over the ranges in a collection of sets, in order,\n  starting from `from`.\n  */\n  static iter(sets, from = 0) {\n    return HeapCursor.from(sets).goto(from);\n  }\n  /**\n  Iterate over two groups of sets, calling methods on `comparator`\n  to notify it of possible differences.\n  */\n  static compare(oldSets, newSets,\n  /**\n  This indicates how the underlying data changed between these\n  ranges, and is needed to synchronize the iteration.\n  */\n  textDiff, comparator,\n  /**\n  Can be used to ignore all non-point ranges, and points below\n  the given size. When -1, all ranges are compared.\n  */\n  minPointSize = -1) {\n    let a = oldSets.filter(set => set.maxPoint > 0 || !set.isEmpty && set.maxPoint >= minPointSize);\n    let b = newSets.filter(set => set.maxPoint > 0 || !set.isEmpty && set.maxPoint >= minPointSize);\n    let sharedChunks = findSharedChunks(a, b, textDiff);\n    let sideA = new SpanCursor(a, sharedChunks, minPointSize);\n    let sideB = new SpanCursor(b, sharedChunks, minPointSize);\n    textDiff.iterGaps((fromA, fromB, length) => compare(sideA, fromA, sideB, fromB, length, comparator));\n    if (textDiff.empty && textDiff.length == 0) compare(sideA, 0, sideB, 0, 0, comparator);\n  }\n  /**\n  Compare the contents of two groups of range sets, returning true\n  if they are equivalent in the given range.\n  */\n  static eq(oldSets, newSets, from = 0, to) {\n    if (to == null) to = ********** /* C.Far */ - 1;\n    let a = oldSets.filter(set => !set.isEmpty && newSets.indexOf(set) < 0);\n    let b = newSets.filter(set => !set.isEmpty && oldSets.indexOf(set) < 0);\n    if (a.length != b.length) return false;\n    if (!a.length) return true;\n    let sharedChunks = findSharedChunks(a, b);\n    let sideA = new SpanCursor(a, sharedChunks, 0).goto(from),\n      sideB = new SpanCursor(b, sharedChunks, 0).goto(from);\n    for (;;) {\n      if (sideA.to != sideB.to || !sameValues(sideA.active, sideB.active) || sideA.point && (!sideB.point || !sideA.point.eq(sideB.point))) return false;\n      if (sideA.to > to) return true;\n      sideA.next();\n      sideB.next();\n    }\n  }\n  /**\n  Iterate over a group of range sets at the same time, notifying\n  the iterator about the ranges covering every given piece of\n  content. Returns the open count (see\n  [`SpanIterator.span`](https://codemirror.net/6/docs/ref/#state.SpanIterator.span)) at the end\n  of the iteration.\n  */\n  static spans(sets, from, to, iterator,\n  /**\n  When given and greater than -1, only points of at least this\n  size are taken into account.\n  */\n  minPointSize = -1) {\n    let cursor = new SpanCursor(sets, null, minPointSize).goto(from),\n      pos = from;\n    let openRanges = cursor.openStart;\n    for (;;) {\n      let curTo = Math.min(cursor.to, to);\n      if (cursor.point) {\n        let active = cursor.activeForPoint(cursor.to);\n        let openCount = cursor.pointFrom < from ? active.length + 1 : cursor.point.startSide < 0 ? active.length : Math.min(active.length, openRanges);\n        iterator.point(pos, curTo, cursor.point, active, openCount, cursor.pointRank);\n        openRanges = Math.min(cursor.openEnd(curTo), active.length);\n      } else if (curTo > pos) {\n        iterator.span(pos, curTo, cursor.active, openRanges);\n        openRanges = cursor.openEnd(curTo);\n      }\n      if (cursor.to > to) return openRanges + (cursor.point && cursor.to > to ? 1 : 0);\n      pos = cursor.to;\n      cursor.next();\n    }\n  }\n  /**\n  Create a range set for the given range or array of ranges. By\n  default, this expects the ranges to be _sorted_ (by start\n  position and, if two start at the same position,\n  `value.startSide`). You can pass `true` as second argument to\n  cause the method to sort them.\n  */\n  static of(ranges, sort = false) {\n    let build = new RangeSetBuilder();\n    for (let range of ranges instanceof Range ? [ranges] : sort ? lazySort(ranges) : ranges) build.add(range.from, range.to, range.value);\n    return build.finish();\n  }\n  /**\n  Join an array of range sets into a single set.\n  */\n  static join(sets) {\n    if (!sets.length) return RangeSet.empty;\n    let result = sets[sets.length - 1];\n    for (let i = sets.length - 2; i >= 0; i--) {\n      for (let layer = sets[i]; layer != RangeSet.empty; layer = layer.nextLayer) result = new RangeSet(layer.chunkPos, layer.chunk, result, Math.max(layer.maxPoint, result.maxPoint));\n    }\n    return result;\n  }\n}\n/**\nThe empty set of ranges.\n*/\nRangeSet.empty = /*@__PURE__*/new RangeSet([], [], null, -1);\nfunction lazySort(ranges) {\n  if (ranges.length > 1) for (let prev = ranges[0], i = 1; i < ranges.length; i++) {\n    let cur = ranges[i];\n    if (cmpRange(prev, cur) > 0) return ranges.slice().sort(cmpRange);\n    prev = cur;\n  }\n  return ranges;\n}\nRangeSet.empty.nextLayer = RangeSet.empty;\n/**\nA range set builder is a data structure that helps build up a\n[range set](https://codemirror.net/6/docs/ref/#state.RangeSet) directly, without first allocating\nan array of [`Range`](https://codemirror.net/6/docs/ref/#state.Range) objects.\n*/\nclass RangeSetBuilder {\n  finishChunk(newArrays) {\n    this.chunks.push(new Chunk(this.from, this.to, this.value, this.maxPoint));\n    this.chunkPos.push(this.chunkStart);\n    this.chunkStart = -1;\n    this.setMaxPoint = Math.max(this.setMaxPoint, this.maxPoint);\n    this.maxPoint = -1;\n    if (newArrays) {\n      this.from = [];\n      this.to = [];\n      this.value = [];\n    }\n  }\n  /**\n  Create an empty builder.\n  */\n  constructor() {\n    this.chunks = [];\n    this.chunkPos = [];\n    this.chunkStart = -1;\n    this.last = null;\n    this.lastFrom = -********** /* C.Far */;\n    this.lastTo = -********** /* C.Far */;\n    this.from = [];\n    this.to = [];\n    this.value = [];\n    this.maxPoint = -1;\n    this.setMaxPoint = -1;\n    this.nextLayer = null;\n  }\n  /**\n  Add a range. Ranges should be added in sorted (by `from` and\n  `value.startSide`) order.\n  */\n  add(from, to, value) {\n    if (!this.addInner(from, to, value)) (this.nextLayer || (this.nextLayer = new RangeSetBuilder())).add(from, to, value);\n  }\n  /**\n  @internal\n  */\n  addInner(from, to, value) {\n    let diff = from - this.lastTo || value.startSide - this.last.endSide;\n    if (diff <= 0 && (from - this.lastFrom || value.startSide - this.last.startSide) < 0) throw new Error(\"Ranges must be added sorted by `from` position and `startSide`\");\n    if (diff < 0) return false;\n    if (this.from.length == 250 /* C.ChunkSize */) this.finishChunk(true);\n    if (this.chunkStart < 0) this.chunkStart = from;\n    this.from.push(from - this.chunkStart);\n    this.to.push(to - this.chunkStart);\n    this.last = value;\n    this.lastFrom = from;\n    this.lastTo = to;\n    this.value.push(value);\n    if (value.point) this.maxPoint = Math.max(this.maxPoint, to - from);\n    return true;\n  }\n  /**\n  @internal\n  */\n  addChunk(from, chunk) {\n    if ((from - this.lastTo || chunk.value[0].startSide - this.last.endSide) < 0) return false;\n    if (this.from.length) this.finishChunk(true);\n    this.setMaxPoint = Math.max(this.setMaxPoint, chunk.maxPoint);\n    this.chunks.push(chunk);\n    this.chunkPos.push(from);\n    let last = chunk.value.length - 1;\n    this.last = chunk.value[last];\n    this.lastFrom = chunk.from[last] + from;\n    this.lastTo = chunk.to[last] + from;\n    return true;\n  }\n  /**\n  Finish the range set. Returns the new set. The builder can't be\n  used anymore after this has been called.\n  */\n  finish() {\n    return this.finishInner(RangeSet.empty);\n  }\n  /**\n  @internal\n  */\n  finishInner(next) {\n    if (this.from.length) this.finishChunk(false);\n    if (this.chunks.length == 0) return next;\n    let result = RangeSet.create(this.chunkPos, this.chunks, this.nextLayer ? this.nextLayer.finishInner(next) : next, this.setMaxPoint);\n    this.from = null; // Make sure further `add` calls produce errors\n    return result;\n  }\n}\nfunction findSharedChunks(a, b, textDiff) {\n  let inA = new Map();\n  for (let set of a) for (let i = 0; i < set.chunk.length; i++) if (set.chunk[i].maxPoint <= 0) inA.set(set.chunk[i], set.chunkPos[i]);\n  let shared = new Set();\n  for (let set of b) for (let i = 0; i < set.chunk.length; i++) {\n    let known = inA.get(set.chunk[i]);\n    if (known != null && (textDiff ? textDiff.mapPos(known) : known) == set.chunkPos[i] && !(textDiff === null || textDiff === void 0 ? void 0 : textDiff.touchesRange(known, known + set.chunk[i].length))) shared.add(set.chunk[i]);\n  }\n  return shared;\n}\nclass LayerCursor {\n  constructor(layer, skip, minPoint, rank = 0) {\n    this.layer = layer;\n    this.skip = skip;\n    this.minPoint = minPoint;\n    this.rank = rank;\n  }\n  get startSide() {\n    return this.value ? this.value.startSide : 0;\n  }\n  get endSide() {\n    return this.value ? this.value.endSide : 0;\n  }\n  goto(pos, side = -********** /* C.Far */) {\n    this.chunkIndex = this.rangeIndex = 0;\n    this.gotoInner(pos, side, false);\n    return this;\n  }\n  gotoInner(pos, side, forward) {\n    while (this.chunkIndex < this.layer.chunk.length) {\n      let next = this.layer.chunk[this.chunkIndex];\n      if (!(this.skip && this.skip.has(next) || this.layer.chunkEnd(this.chunkIndex) < pos || next.maxPoint < this.minPoint)) break;\n      this.chunkIndex++;\n      forward = false;\n    }\n    if (this.chunkIndex < this.layer.chunk.length) {\n      let rangeIndex = this.layer.chunk[this.chunkIndex].findIndex(pos - this.layer.chunkPos[this.chunkIndex], side, true);\n      if (!forward || this.rangeIndex < rangeIndex) this.setRangeIndex(rangeIndex);\n    }\n    this.next();\n  }\n  forward(pos, side) {\n    if ((this.to - pos || this.endSide - side) < 0) this.gotoInner(pos, side, true);\n  }\n  next() {\n    for (;;) {\n      if (this.chunkIndex == this.layer.chunk.length) {\n        this.from = this.to = ********** /* C.Far */;\n        this.value = null;\n        break;\n      } else {\n        let chunkPos = this.layer.chunkPos[this.chunkIndex],\n          chunk = this.layer.chunk[this.chunkIndex];\n        let from = chunkPos + chunk.from[this.rangeIndex];\n        this.from = from;\n        this.to = chunkPos + chunk.to[this.rangeIndex];\n        this.value = chunk.value[this.rangeIndex];\n        this.setRangeIndex(this.rangeIndex + 1);\n        if (this.minPoint < 0 || this.value.point && this.to - this.from >= this.minPoint) break;\n      }\n    }\n  }\n  setRangeIndex(index) {\n    if (index == this.layer.chunk[this.chunkIndex].value.length) {\n      this.chunkIndex++;\n      if (this.skip) {\n        while (this.chunkIndex < this.layer.chunk.length && this.skip.has(this.layer.chunk[this.chunkIndex])) this.chunkIndex++;\n      }\n      this.rangeIndex = 0;\n    } else {\n      this.rangeIndex = index;\n    }\n  }\n  nextChunk() {\n    this.chunkIndex++;\n    this.rangeIndex = 0;\n    this.next();\n  }\n  compare(other) {\n    return this.from - other.from || this.startSide - other.startSide || this.rank - other.rank || this.to - other.to || this.endSide - other.endSide;\n  }\n}\nclass HeapCursor {\n  constructor(heap) {\n    this.heap = heap;\n  }\n  static from(sets, skip = null, minPoint = -1) {\n    let heap = [];\n    for (let i = 0; i < sets.length; i++) {\n      for (let cur = sets[i]; !cur.isEmpty; cur = cur.nextLayer) {\n        if (cur.maxPoint >= minPoint) heap.push(new LayerCursor(cur, skip, minPoint, i));\n      }\n    }\n    return heap.length == 1 ? heap[0] : new HeapCursor(heap);\n  }\n  get startSide() {\n    return this.value ? this.value.startSide : 0;\n  }\n  goto(pos, side = -********** /* C.Far */) {\n    for (let cur of this.heap) cur.goto(pos, side);\n    for (let i = this.heap.length >> 1; i >= 0; i--) heapBubble(this.heap, i);\n    this.next();\n    return this;\n  }\n  forward(pos, side) {\n    for (let cur of this.heap) cur.forward(pos, side);\n    for (let i = this.heap.length >> 1; i >= 0; i--) heapBubble(this.heap, i);\n    if ((this.to - pos || this.value.endSide - side) < 0) this.next();\n  }\n  next() {\n    if (this.heap.length == 0) {\n      this.from = this.to = ********** /* C.Far */;\n      this.value = null;\n      this.rank = -1;\n    } else {\n      let top = this.heap[0];\n      this.from = top.from;\n      this.to = top.to;\n      this.value = top.value;\n      this.rank = top.rank;\n      if (top.value) top.next();\n      heapBubble(this.heap, 0);\n    }\n  }\n}\nfunction heapBubble(heap, index) {\n  for (let cur = heap[index];;) {\n    let childIndex = (index << 1) + 1;\n    if (childIndex >= heap.length) break;\n    let child = heap[childIndex];\n    if (childIndex + 1 < heap.length && child.compare(heap[childIndex + 1]) >= 0) {\n      child = heap[childIndex + 1];\n      childIndex++;\n    }\n    if (cur.compare(child) < 0) break;\n    heap[childIndex] = cur;\n    heap[index] = child;\n    index = childIndex;\n  }\n}\nclass SpanCursor {\n  constructor(sets, skip, minPoint) {\n    this.minPoint = minPoint;\n    this.active = [];\n    this.activeTo = [];\n    this.activeRank = [];\n    this.minActive = -1;\n    // A currently active point range, if any\n    this.point = null;\n    this.pointFrom = 0;\n    this.pointRank = 0;\n    this.to = -********** /* C.Far */;\n    this.endSide = 0;\n    // The amount of open active ranges at the start of the iterator.\n    // Not including points.\n    this.openStart = -1;\n    this.cursor = HeapCursor.from(sets, skip, minPoint);\n  }\n  goto(pos, side = -********** /* C.Far */) {\n    this.cursor.goto(pos, side);\n    this.active.length = this.activeTo.length = this.activeRank.length = 0;\n    this.minActive = -1;\n    this.to = pos;\n    this.endSide = side;\n    this.openStart = -1;\n    this.next();\n    return this;\n  }\n  forward(pos, side) {\n    while (this.minActive > -1 && (this.activeTo[this.minActive] - pos || this.active[this.minActive].endSide - side) < 0) this.removeActive(this.minActive);\n    this.cursor.forward(pos, side);\n  }\n  removeActive(index) {\n    remove(this.active, index);\n    remove(this.activeTo, index);\n    remove(this.activeRank, index);\n    this.minActive = findMinIndex(this.active, this.activeTo);\n  }\n  addActive(trackOpen) {\n    let i = 0,\n      {\n        value,\n        to,\n        rank\n      } = this.cursor;\n    // Organize active marks by rank first, then by size\n    while (i < this.activeRank.length && (rank - this.activeRank[i] || to - this.activeTo[i]) > 0) i++;\n    insert(this.active, i, value);\n    insert(this.activeTo, i, to);\n    insert(this.activeRank, i, rank);\n    if (trackOpen) insert(trackOpen, i, this.cursor.from);\n    this.minActive = findMinIndex(this.active, this.activeTo);\n  }\n  // After calling this, if `this.point` != null, the next range is a\n  // point. Otherwise, it's a regular range, covered by `this.active`.\n  next() {\n    let from = this.to,\n      wasPoint = this.point;\n    this.point = null;\n    let trackOpen = this.openStart < 0 ? [] : null;\n    for (;;) {\n      let a = this.minActive;\n      if (a > -1 && (this.activeTo[a] - this.cursor.from || this.active[a].endSide - this.cursor.startSide) < 0) {\n        if (this.activeTo[a] > from) {\n          this.to = this.activeTo[a];\n          this.endSide = this.active[a].endSide;\n          break;\n        }\n        this.removeActive(a);\n        if (trackOpen) remove(trackOpen, a);\n      } else if (!this.cursor.value) {\n        this.to = this.endSide = ********** /* C.Far */;\n        break;\n      } else if (this.cursor.from > from) {\n        this.to = this.cursor.from;\n        this.endSide = this.cursor.startSide;\n        break;\n      } else {\n        let nextVal = this.cursor.value;\n        if (!nextVal.point) {\n          // Opening a range\n          this.addActive(trackOpen);\n          this.cursor.next();\n        } else if (wasPoint && this.cursor.to == this.to && this.cursor.from < this.cursor.to) {\n          // Ignore any non-empty points that end precisely at the end of the prev point\n          this.cursor.next();\n        } else {\n          // New point\n          this.point = nextVal;\n          this.pointFrom = this.cursor.from;\n          this.pointRank = this.cursor.rank;\n          this.to = this.cursor.to;\n          this.endSide = nextVal.endSide;\n          this.cursor.next();\n          this.forward(this.to, this.endSide);\n          break;\n        }\n      }\n    }\n    if (trackOpen) {\n      this.openStart = 0;\n      for (let i = trackOpen.length - 1; i >= 0 && trackOpen[i] < from; i--) this.openStart++;\n    }\n  }\n  activeForPoint(to) {\n    if (!this.active.length) return this.active;\n    let active = [];\n    for (let i = this.active.length - 1; i >= 0; i--) {\n      if (this.activeRank[i] < this.pointRank) break;\n      if (this.activeTo[i] > to || this.activeTo[i] == to && this.active[i].endSide >= this.point.endSide) active.push(this.active[i]);\n    }\n    return active.reverse();\n  }\n  openEnd(to) {\n    let open = 0;\n    for (let i = this.activeTo.length - 1; i >= 0 && this.activeTo[i] > to; i--) open++;\n    return open;\n  }\n}\nfunction compare(a, startA, b, startB, length, comparator) {\n  a.goto(startA);\n  b.goto(startB);\n  let endB = startB + length;\n  let pos = startB,\n    dPos = startB - startA;\n  for (;;) {\n    let dEnd = a.to + dPos - b.to,\n      diff = dEnd || a.endSide - b.endSide;\n    let end = diff < 0 ? a.to + dPos : b.to,\n      clipEnd = Math.min(end, endB);\n    if (a.point || b.point) {\n      if (!(a.point && b.point && (a.point == b.point || a.point.eq(b.point)) && sameValues(a.activeForPoint(a.to), b.activeForPoint(b.to)))) comparator.comparePoint(pos, clipEnd, a.point, b.point);\n    } else {\n      if (clipEnd > pos && !sameValues(a.active, b.active)) comparator.compareRange(pos, clipEnd, a.active, b.active);\n    }\n    if (end > endB) break;\n    if ((dEnd || a.openEnd != b.openEnd) && comparator.boundChange) comparator.boundChange(end);\n    pos = end;\n    if (diff <= 0) a.next();\n    if (diff >= 0) b.next();\n  }\n}\nfunction sameValues(a, b) {\n  if (a.length != b.length) return false;\n  for (let i = 0; i < a.length; i++) if (a[i] != b[i] && !a[i].eq(b[i])) return false;\n  return true;\n}\nfunction remove(array, index) {\n  for (let i = index, e = array.length - 1; i < e; i++) array[i] = array[i + 1];\n  array.pop();\n}\nfunction insert(array, index, value) {\n  for (let i = array.length - 1; i >= index; i--) array[i + 1] = array[i];\n  array[index] = value;\n}\nfunction findMinIndex(value, array) {\n  let found = -1,\n    foundPos = ********** /* C.Far */;\n  for (let i = 0; i < array.length; i++) if ((array[i] - foundPos || value[i].endSide - value[found].endSide) < 0) {\n    found = i;\n    foundPos = array[i];\n  }\n  return found;\n}\n\n/**\nCount the column position at the given offset into the string,\ntaking extending characters and tab size into account.\n*/\nfunction countColumn(string, tabSize, to = string.length) {\n  let n = 0;\n  for (let i = 0; i < to && i < string.length;) {\n    if (string.charCodeAt(i) == 9) {\n      n += tabSize - n % tabSize;\n      i++;\n    } else {\n      n++;\n      i = findClusterBreak(string, i);\n    }\n  }\n  return n;\n}\n/**\nFind the offset that corresponds to the given column position in a\nstring, taking extending characters and tab size into account. By\ndefault, the string length is returned when it is too short to\nreach the column. Pass `strict` true to make it return -1 in that\nsituation.\n*/\nfunction findColumn(string, col, tabSize, strict) {\n  for (let i = 0, n = 0;;) {\n    if (n >= col) return i;\n    if (i == string.length) break;\n    n += string.charCodeAt(i) == 9 ? tabSize - n % tabSize : 1;\n    i = findClusterBreak(string, i);\n  }\n  return strict === true ? -1 : string.length;\n}\nexport { Annotation, AnnotationType, ChangeDesc, ChangeSet, CharCategory, Compartment, EditorSelection, EditorState, Facet, Line, MapMode, Prec, Range, RangeSet, RangeSetBuilder, RangeValue, SelectionRange, StateEffect, StateEffectType, StateField, Text, Transaction, codePointAt, codePointSize, combineConfig, countColumn, findClusterBreak, findColumn, fromCodePoint };"], "mappings": ";AAEA,IAAI,YAAY,CAAC;AAAjB,IACE,UAAU,CAAC;AAAA,CACZ,MAAM;AAOL,MAAI,UAAU,izCAAizC,MAAM,GAAG,EAAE,IAAI,OAAK,IAAI,SAAS,GAAG,EAAE,IAAI,CAAC;AAC12C,WAAS,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAK,EAAC,IAAI,IAAI,UAAU,WAAW,KAAK,IAAI,IAAI,QAAQ,CAAC,CAAC;AACvG,GAAG;AACI,SAAS,gBAAgB,MAAM;AACpC,MAAI,OAAO,IAAK,QAAO;AACvB,WAAS,OAAO,GAAG,KAAK,UAAU,YAAU;AAC1C,QAAI,MAAM,OAAO,MAAM;AACvB,QAAI,OAAO,UAAU,GAAG,EAAG,MAAK;AAAA,aAAa,QAAQ,QAAQ,GAAG,EAAG,QAAO,MAAM;AAAA,QAAO,QAAO;AAC9F,QAAI,QAAQ,GAAI,QAAO;AAAA,EACzB;AACF;AACA,SAAS,oBAAoB,MAAM;AACjC,SAAO,QAAQ,UAAW,QAAQ;AACpC;AAOA,IAAM,MAAM;AACL,SAAS,iBAAiB,KAAK,KAAK,UAAU,MAAM,mBAAmB,MAAM;AAClF,UAAQ,UAAU,mBAAmB,kBAAkB,KAAK,KAAK,gBAAgB;AACnF;AACA,SAAS,iBAAiB,KAAK,KAAK,kBAAkB;AACpD,MAAI,OAAO,IAAI,OAAQ,QAAO;AAE9B,MAAI,OAAO,aAAa,IAAI,WAAW,GAAG,CAAC,KAAK,cAAc,IAAI,WAAW,MAAM,CAAC,CAAC,EAAG;AACxF,MAAI,OAAO,YAAY,KAAK,GAAG;AAC/B,SAAO,cAAc,IAAI;AACzB,SAAO,MAAM,IAAI,QAAQ;AACvB,QAAI,OAAO,YAAY,KAAK,GAAG;AAC/B,QAAI,QAAQ,OAAO,QAAQ,OAAO,oBAAoB,gBAAgB,IAAI,GAAG;AAC3E,aAAO,cAAc,IAAI;AACzB,aAAO;AAAA,IACT,WAAW,oBAAoB,IAAI,GAAG;AACpC,UAAI,cAAc,GAChB,IAAI,MAAM;AACZ,aAAO,KAAK,KAAK,oBAAoB,YAAY,KAAK,CAAC,CAAC,GAAG;AACzD;AACA,aAAK;AAAA,MACP;AACA,UAAI,cAAc,KAAK,EAAG;AAAA,UAAW,QAAO;AAAA,IAC9C,OAAO;AACL;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,KAAK,KAAK,kBAAkB;AACpD,SAAO,MAAM,GAAG;AACd,QAAI,QAAQ,iBAAiB,KAAK,MAAM,GAAG,gBAAgB;AAC3D,QAAI,QAAQ,IAAK,QAAO;AACxB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,YAAY,KAAK,KAAK;AAC7B,MAAI,QAAQ,IAAI,WAAW,GAAG;AAC9B,MAAI,CAAC,cAAc,KAAK,KAAK,MAAM,KAAK,IAAI,OAAQ,QAAO;AAC3D,MAAI,QAAQ,IAAI,WAAW,MAAM,CAAC;AAClC,MAAI,CAAC,aAAa,KAAK,EAAG,QAAO;AACjC,UAAQ,QAAQ,SAAU,OAAO,QAAQ,SAAU;AACrD;AACA,SAAS,aAAa,IAAI;AACxB,SAAO,MAAM,SAAU,KAAK;AAC9B;AACA,SAAS,cAAc,IAAI;AACzB,SAAO,MAAM,SAAU,KAAK;AAC9B;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,OAAO,QAAU,IAAI;AAC9B;;;AC9EA,IAAM,OAAN,MAAM,MAAK;AAAA;AAAA;AAAA;AAAA,EAIT,OAAO,KAAK;AACV,QAAI,MAAM,KAAK,MAAM,KAAK,OAAQ,OAAM,IAAI,WAAW,oBAAoB,GAAG,0BAA0B,KAAK,MAAM,EAAE;AACrH,WAAO,KAAK,UAAU,KAAK,OAAO,GAAG,CAAC;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAIA,KAAK,GAAG;AACN,QAAI,IAAI,KAAK,IAAI,KAAK,MAAO,OAAM,IAAI,WAAW,uBAAuB,CAAC,OAAO,KAAK,KAAK,gBAAgB;AAC3G,WAAO,KAAK,UAAU,GAAG,MAAM,GAAG,CAAC;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,MAAM,IAAI,MAAM;AACtB,KAAC,MAAM,EAAE,IAAI,KAAK,MAAM,MAAM,EAAE;AAChC,QAAI,QAAQ,CAAC;AACb,SAAK;AAAA,MAAU;AAAA,MAAG;AAAA,MAAM;AAAA,MAAO;AAAA;AAAA,IAAe;AAC9C,QAAI,KAAK,OAAQ,MAAK;AAAA,MAAU;AAAA,MAAG,KAAK;AAAA,MAAQ;AAAA,MAAO,IAAoB;AAAA;AAAA,IAAe;AAC1F,SAAK;AAAA,MAAU;AAAA,MAAI,KAAK;AAAA,MAAQ;AAAA,MAAO;AAAA;AAAA,IAAiB;AACxD,WAAO,SAAS,KAAK,OAAO,KAAK,UAAU,KAAK,QAAQ,KAAK,MAAM;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO;AACZ,WAAO,KAAK,QAAQ,KAAK,QAAQ,KAAK,QAAQ,KAAK;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,MAAM,KAAK,KAAK,QAAQ;AAC5B,KAAC,MAAM,EAAE,IAAI,KAAK,MAAM,MAAM,EAAE;AAChC,QAAI,QAAQ,CAAC;AACb,SAAK,UAAU,MAAM,IAAI,OAAO,CAAC;AACjC,WAAO,SAAS,KAAK,OAAO,KAAK,IAAI;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAIA,GAAG,OAAO;AACR,QAAI,SAAS,KAAM,QAAO;AAC1B,QAAI,MAAM,UAAU,KAAK,UAAU,MAAM,SAAS,KAAK,MAAO,QAAO;AACrE,QAAI,QAAQ,KAAK,cAAc,OAAO,CAAC,GACrC,MAAM,KAAK,SAAS,KAAK,cAAc,OAAO,EAAE;AAClD,QAAI,IAAI,IAAI,cAAc,IAAI,GAC5B,IAAI,IAAI,cAAc,KAAK;AAC7B,aAAS,OAAO,OAAO,MAAM,WAAS;AACpC,QAAE,KAAK,IAAI;AACX,QAAE,KAAK,IAAI;AACX,aAAO;AACP,UAAI,EAAE,aAAa,EAAE,aAAa,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAO,QAAO;AACjF,aAAO,EAAE,MAAM;AACf,UAAI,EAAE,QAAQ,OAAO,IAAK,QAAO;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,MAAM,GAAG;AACZ,WAAO,IAAI,cAAc,MAAM,GAAG;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,MAAM,KAAK,KAAK,QAAQ;AAChC,WAAO,IAAI,kBAAkB,MAAM,MAAM,EAAE;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,MAAM,IAAI;AAClB,QAAI;AACJ,QAAI,QAAQ,MAAM;AAChB,cAAQ,KAAK,KAAK;AAAA,IACpB,OAAO;AACL,UAAI,MAAM,KAAM,MAAK,KAAK,QAAQ;AAClC,UAAI,QAAQ,KAAK,KAAK,IAAI,EAAE;AAC5B,cAAQ,KAAK,UAAU,OAAO,KAAK,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,KAAK,SAAS,MAAM,IAAI,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,EAAE,CAAC;AAAA,IACxH;AACA,WAAO,IAAI,WAAW,KAAK;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK,YAAY,CAAC;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,QAAI,QAAQ,CAAC;AACb,SAAK,QAAQ,KAAK;AAClB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA,EAIf,OAAO,GAAG,MAAM;AACd,QAAI,KAAK,UAAU,EAAG,OAAM,IAAI,WAAW,wCAAwC;AACnF,QAAI,KAAK,UAAU,KAAK,CAAC,KAAK,CAAC,EAAG,QAAO,MAAK;AAC9C,WAAO,KAAK,UAAU,KAAuB,IAAI,SAAS,IAAI,IAAI,SAAS,KAAK,SAAS,MAAM,MAAM,CAAC,CAAC,CAAC;AAAA,EAC1G;AACF;AAIA,IAAM,WAAN,MAAM,kBAAiB,KAAK;AAAA,EAC1B,YAAY,MAAM,SAAS,WAAW,IAAI,GAAG;AAC3C,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,IAAI,WAAW;AACb,WAAO;AAAA,EACT;AAAA,EACA,UAAU,QAAQ,QAAQ,MAAM,QAAQ;AACtC,aAAS,IAAI,KAAI,KAAK;AACpB,UAAI,SAAS,KAAK,KAAK,CAAC,GACtB,MAAM,SAAS,OAAO;AACxB,WAAK,SAAS,OAAO,QAAQ,OAAQ,QAAO,IAAI,KAAK,QAAQ,KAAK,MAAM,MAAM;AAC9E,eAAS,MAAM;AACf;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,MAAM,IAAI,QAAQ,MAAM;AAChC,QAAI,OAAO,QAAQ,KAAK,MAAM,KAAK,SAAS,OAAO,IAAI,UAAS,UAAU,KAAK,MAAM,MAAM,EAAE,GAAG,KAAK,IAAI,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC;AAC7I,QAAI,OAAO,GAAmB;AAC5B,UAAI,OAAO,OAAO,IAAI;AACtB,UAAI,SAAS,WAAW,KAAK,MAAM,KAAK,KAAK,MAAM,GAAG,GAAG,KAAK,MAAM;AACpE,UAAI,OAAO,UAAU,IAAsB;AACzC,eAAO,KAAK,IAAI,UAAS,QAAQ,KAAK,SAAS,KAAK,MAAM,CAAC;AAAA,MAC7D,OAAO;AACL,YAAI,MAAM,OAAO,UAAU;AAC3B,eAAO,KAAK,IAAI,UAAS,OAAO,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI,UAAS,OAAO,MAAM,GAAG,CAAC,CAAC;AAAA,MACjF;AAAA,IACF,OAAO;AACL,aAAO,KAAK,IAAI;AAAA,IAClB;AAAA,EACF;AAAA,EACA,QAAQ,MAAM,IAAI,MAAM;AACtB,QAAI,EAAE,gBAAgB,WAAW,QAAO,MAAM,QAAQ,MAAM,IAAI,IAAI;AACpE,KAAC,MAAM,EAAE,IAAI,KAAK,MAAM,MAAM,EAAE;AAChC,QAAI,QAAQ,WAAW,KAAK,MAAM,WAAW,KAAK,MAAM,UAAU,KAAK,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE;AAC1F,QAAI,SAAS,KAAK,SAAS,KAAK,UAAU,KAAK;AAC/C,QAAI,MAAM,UAAU,GAAsB,QAAO,IAAI,UAAS,OAAO,MAAM;AAC3E,WAAO,SAAS,KAAK,UAAS,MAAM,OAAO,CAAC,CAAC,GAAG,MAAM;AAAA,EACxD;AAAA,EACA,YAAY,MAAM,KAAK,KAAK,QAAQ,UAAU,MAAM;AAClD,KAAC,MAAM,EAAE,IAAI,KAAK,MAAM,MAAM,EAAE;AAChC,QAAI,SAAS;AACb,aAAS,MAAM,GAAG,IAAI,GAAG,OAAO,MAAM,IAAI,KAAK,KAAK,QAAQ,KAAK;AAC/D,UAAI,OAAO,KAAK,KAAK,CAAC,GACpB,MAAM,MAAM,KAAK;AACnB,UAAI,MAAM,QAAQ,EAAG,WAAU;AAC/B,UAAI,OAAO,OAAO,KAAK,IAAK,WAAU,KAAK,MAAM,KAAK,IAAI,GAAG,OAAO,GAAG,GAAG,KAAK,GAAG;AAClF,YAAM,MAAM;AAAA,IACd;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,QAAQ;AACd,aAAS,QAAQ,KAAK,KAAM,QAAO,KAAK,IAAI;AAAA,EAC9C;AAAA,EACA,gBAAgB;AACd,WAAO;AAAA,EACT;AAAA,EACA,OAAO,MAAM,MAAM,QAAQ;AACzB,QAAI,OAAO,CAAC,GACV,MAAM;AACR,aAAS,QAAQ,MAAM;AACrB,WAAK,KAAK,IAAI;AACd,aAAO,KAAK,SAAS;AACrB,UAAI,KAAK,UAAU,IAAsB;AACvC,eAAO,KAAK,IAAI,UAAS,MAAM,GAAG,CAAC;AACnC,eAAO,CAAC;AACR,cAAM;AAAA,MACR;AAAA,IACF;AACA,QAAI,MAAM,GAAI,QAAO,KAAK,IAAI,UAAS,MAAM,GAAG,CAAC;AACjD,WAAO;AAAA,EACT;AACF;AAKA,IAAM,WAAN,MAAM,kBAAiB,KAAK;AAAA,EAC1B,YAAY,UAAU,QAAQ;AAC5B,UAAM;AACN,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,aAAS,SAAS,SAAU,MAAK,SAAS,MAAM;AAAA,EAClD;AAAA,EACA,UAAU,QAAQ,QAAQ,MAAM,QAAQ;AACtC,aAAS,IAAI,KAAI,KAAK;AACpB,UAAI,QAAQ,KAAK,SAAS,CAAC,GACzB,MAAM,SAAS,MAAM,QACrB,UAAU,OAAO,MAAM,QAAQ;AACjC,WAAK,SAAS,UAAU,QAAQ,OAAQ,QAAO,MAAM,UAAU,QAAQ,QAAQ,MAAM,MAAM;AAC3F,eAAS,MAAM;AACf,aAAO,UAAU;AAAA,IACnB;AAAA,EACF;AAAA,EACA,UAAU,MAAM,IAAI,QAAQ,MAAM;AAChC,aAAS,IAAI,GAAG,MAAM,GAAG,OAAO,MAAM,IAAI,KAAK,SAAS,QAAQ,KAAK;AACnE,UAAI,QAAQ,KAAK,SAAS,CAAC,GACzB,MAAM,MAAM,MAAM;AACpB,UAAI,QAAQ,OAAO,MAAM,KAAK;AAC5B,YAAI,YAAY,SAAS,OAAO,OAAO,IAAoB,MAAM,OAAO,KAAK,IAAkB;AAC/F,YAAI,OAAO,QAAQ,OAAO,MAAM,CAAC,UAAW,QAAO,KAAK,KAAK;AAAA,YAAO,OAAM,UAAU,OAAO,KAAK,KAAK,KAAK,QAAQ,SAAS;AAAA,MAC7H;AACA,YAAM,MAAM;AAAA,IACd;AAAA,EACF;AAAA,EACA,QAAQ,MAAM,IAAI,MAAM;AACtB,KAAC,MAAM,EAAE,IAAI,KAAK,MAAM,MAAM,EAAE;AAChC,QAAI,KAAK,QAAQ,KAAK,MAAO,UAAS,IAAI,GAAG,MAAM,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AACnF,UAAI,QAAQ,KAAK,SAAS,CAAC,GACzB,MAAM,MAAM,MAAM;AAIpB,UAAI,QAAQ,OAAO,MAAM,KAAK;AAC5B,YAAI,UAAU,MAAM,QAAQ,OAAO,KAAK,KAAK,KAAK,IAAI;AACtD,YAAI,aAAa,KAAK,QAAQ,MAAM,QAAQ,QAAQ;AACpD,YAAI,QAAQ,QAAQ,cAAc,IAA2B,KAAK,QAAQ,QAAQ,cAAc,IAA2B,GAAG;AAC5H,cAAI,OAAO,KAAK,SAAS,MAAM;AAC/B,eAAK,CAAC,IAAI;AACV,iBAAO,IAAI,UAAS,MAAM,KAAK,UAAU,KAAK,QAAQ,KAAK,MAAM;AAAA,QACnE;AACA,eAAO,MAAM,QAAQ,KAAK,KAAK,OAAO;AAAA,MACxC;AACA,YAAM,MAAM;AAAA,IACd;AACA,WAAO,MAAM,QAAQ,MAAM,IAAI,IAAI;AAAA,EACrC;AAAA,EACA,YAAY,MAAM,KAAK,KAAK,QAAQ,UAAU,MAAM;AAClD,KAAC,MAAM,EAAE,IAAI,KAAK,MAAM,MAAM,EAAE;AAChC,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,MAAM,GAAG,IAAI,KAAK,SAAS,UAAU,OAAO,IAAI,KAAK;AACnE,UAAI,QAAQ,KAAK,SAAS,CAAC,GACzB,MAAM,MAAM,MAAM;AACpB,UAAI,MAAM,QAAQ,EAAG,WAAU;AAC/B,UAAI,OAAO,OAAO,KAAK,IAAK,WAAU,MAAM,YAAY,OAAO,KAAK,KAAK,KAAK,OAAO;AACrF,YAAM,MAAM;AAAA,IACd;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,QAAQ;AACd,aAAS,SAAS,KAAK,SAAU,OAAM,QAAQ,MAAM;AAAA,EACvD;AAAA,EACA,cAAc,OAAO,KAAK;AACxB,QAAI,EAAE,iBAAiB,WAAW,QAAO;AACzC,QAAI,SAAS;AACb,QAAI,CAAC,IAAI,IAAI,IAAI,EAAE,IAAI,MAAM,IAAI,CAAC,GAAG,GAAG,KAAK,SAAS,QAAQ,MAAM,SAAS,MAAM,IAAI,CAAC,KAAK,SAAS,SAAS,GAAG,MAAM,SAAS,SAAS,GAAG,IAAI,EAAE;AACnJ,aAAQ,MAAM,KAAK,MAAM,KAAK;AAC5B,UAAI,MAAM,MAAM,MAAM,GAAI,QAAO;AACjC,UAAI,MAAM,KAAK,SAAS,EAAE,GACxB,MAAM,MAAM,SAAS,EAAE;AACzB,UAAI,OAAO,IAAK,QAAO,SAAS,IAAI,cAAc,KAAK,GAAG;AAC1D,gBAAU,IAAI,SAAS;AAAA,IACzB;AAAA,EACF;AAAA,EACA,OAAO,KAAK,UAAU,SAAS,SAAS,OAAO,CAAC,GAAG,OAAO,IAAI,GAAG,SAAS,GAAG,EAAE,GAAG;AAChF,QAAI,QAAQ;AACZ,aAAS,MAAM,SAAU,UAAS,GAAG;AACrC,QAAI,QAAQ,IAAsB;AAChC,UAAI,OAAO,CAAC;AACZ,eAAS,MAAM,SAAU,IAAG,QAAQ,IAAI;AACxC,aAAO,IAAI,SAAS,MAAM,MAAM;AAAA,IAClC;AACA,QAAI,QAAQ,KAAK;AAAA,MAAI;AAAA,MAAsB,SAAS;AAAA;AAAA,IAAwB,GAC1E,WAAW,SAAS,GACpB,WAAW,SAAS;AACtB,QAAI,UAAU,CAAC,GACb,eAAe,GACf,aAAa,IACb,eAAe,CAAC;AAClB,aAAS,IAAI,OAAO;AAClB,UAAI;AACJ,UAAI,MAAM,QAAQ,YAAY,iBAAiB,WAAU;AACvD,iBAAS,QAAQ,MAAM,SAAU,KAAI,IAAI;AAAA,MAC3C,WAAW,MAAM,QAAQ,aAAa,eAAe,YAAY,CAAC,eAAe;AAC/E,cAAM;AACN,gBAAQ,KAAK,KAAK;AAAA,MACpB,WAAW,iBAAiB,YAAY,iBAAiB,OAAO,aAAa,aAAa,SAAS,CAAC,cAAc,YAAY,MAAM,QAAQ,KAAK,SAAS,IAAsB;AAC9K,wBAAgB,MAAM;AACtB,sBAAc,MAAM,SAAS;AAC7B,qBAAa,aAAa,SAAS,CAAC,IAAI,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,IAAI,GAAG,KAAK,SAAS,IAAI,MAAM,MAAM;AAAA,MACnH,OAAO;AACL,YAAI,eAAe,MAAM,QAAQ,MAAO,OAAM;AAC9C,wBAAgB,MAAM;AACtB,sBAAc,MAAM,SAAS;AAC7B,qBAAa,KAAK,KAAK;AAAA,MACzB;AAAA,IACF;AACA,aAAS,QAAQ;AACf,UAAI,gBAAgB,EAAG;AACvB,cAAQ,KAAK,aAAa,UAAU,IAAI,aAAa,CAAC,IAAI,UAAS,KAAK,cAAc,UAAU,CAAC;AACjG,mBAAa;AACb,qBAAe,aAAa,SAAS;AAAA,IACvC;AACA,aAAS,SAAS,SAAU,KAAI,KAAK;AACrC,UAAM;AACN,WAAO,QAAQ,UAAU,IAAI,QAAQ,CAAC,IAAI,IAAI,UAAS,SAAS,MAAM;AAAA,EACxE;AACF;AACA,KAAK,QAAqB,IAAI,SAAS,CAAC,EAAE,GAAG,CAAC;AAC9C,SAAS,WAAW,MAAM;AACxB,MAAI,SAAS;AACb,WAAS,QAAQ,KAAM,WAAU,KAAK,SAAS;AAC/C,SAAO;AACT;AACA,SAAS,WAAW,MAAM,QAAQ,OAAO,GAAG,KAAK,KAAK;AACpD,WAAS,MAAM,GAAG,IAAI,GAAG,QAAQ,MAAM,IAAI,KAAK,UAAU,OAAO,IAAI,KAAK;AACxE,QAAI,OAAO,KAAK,CAAC,GACf,MAAM,MAAM,KAAK;AACnB,QAAI,OAAO,MAAM;AACf,UAAI,MAAM,GAAI,QAAO,KAAK,MAAM,GAAG,KAAK,GAAG;AAC3C,UAAI,MAAM,KAAM,QAAO,KAAK,MAAM,OAAO,GAAG;AAC5C,UAAI,OAAO;AACT,eAAO,OAAO,SAAS,CAAC,KAAK;AAC7B,gBAAQ;AAAA,MACV,MAAO,QAAO,KAAK,IAAI;AAAA,IACzB;AACA,UAAM,MAAM;AAAA,EACd;AACA,SAAO;AACT;AACA,SAAS,UAAU,MAAM,MAAM,IAAI;AACjC,SAAO,WAAW,MAAM,CAAC,EAAE,GAAG,MAAM,EAAE;AACxC;AACA,IAAM,gBAAN,MAAoB;AAAA,EAClB,YAAY,MAAM,MAAM,GAAG;AACzB,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,QAAQ,CAAC,IAAI;AAClB,SAAK,UAAU,CAAC,MAAM,IAAI,KAAK,gBAAgB,WAAW,KAAK,KAAK,SAAS,KAAK,SAAS,WAAW,CAAC;AAAA,EACzG;AAAA,EACA,UAAU,MAAM,KAAK;AACnB,SAAK,OAAO,KAAK,YAAY;AAC7B,eAAS;AACP,UAAI,OAAO,KAAK,MAAM,SAAS;AAC/B,UAAI,MAAM,KAAK,MAAM,IAAI,GACvB,cAAc,KAAK,QAAQ,IAAI,GAC/B,SAAS,eAAe;AAC1B,UAAI,OAAO,eAAe,WAAW,IAAI,KAAK,SAAS,IAAI,SAAS;AACpE,UAAI,WAAW,MAAM,IAAI,OAAO,IAAI;AAClC,YAAI,QAAQ,GAAG;AACb,eAAK,OAAO;AACZ,eAAK,QAAQ;AACb,iBAAO;AAAA,QACT;AACA,YAAI,MAAM,EAAG,MAAK,QAAQ,OAAO,CAAC;AAClC,aAAK,MAAM,IAAI;AACf,aAAK,QAAQ,IAAI;AAAA,MACnB,YAAY,cAAc,OAAO,MAAM,IAAI,IAAI,IAAI;AACjD,aAAK,QAAQ,IAAI,KAAK;AACtB,YAAI,QAAQ,GAAG;AACb,eAAK,YAAY;AACjB,eAAK,QAAQ;AACb,iBAAO;AAAA,QACT;AACA;AAAA,MACF,WAAW,eAAe,UAAU;AAElC,YAAI,OAAO,IAAI,KAAK,UAAU,MAAM,IAAI,KAAK,EAAE;AAC/C,aAAK,QAAQ,IAAI,KAAK;AACtB,YAAI,KAAK,SAAS,KAAK,IAAI,GAAG,IAAI,GAAG;AACnC,eAAK,QAAQ,QAAQ,IAAI,OAAO,MAAM,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,MAAM,GAAG,KAAK,SAAS,IAAI;AAC7F,iBAAO;AAAA,QACT;AACA,gBAAQ,KAAK;AAAA,MACf,OAAO;AACL,YAAI,OAAO,IAAI,SAAS,UAAU,MAAM,IAAI,KAAK,EAAE;AACnD,YAAI,OAAO,KAAK,QAAQ;AACtB,kBAAQ,KAAK;AACb,eAAK,QAAQ,IAAI,KAAK;AAAA,QACxB,OAAO;AACL,cAAI,MAAM,EAAG,MAAK,QAAQ,IAAI;AAC9B,eAAK,MAAM,KAAK,IAAI;AACpB,eAAK,QAAQ,KAAK,MAAM,IAAI,KAAK,gBAAgB,WAAW,KAAK,KAAK,SAAS,KAAK,SAAS,WAAW,CAAC;AAAA,QAC3G;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,KAAK,OAAO,GAAG;AACb,QAAI,OAAO,GAAG;AACZ,WAAK,UAAU,CAAC,MAAM,CAAC,KAAK,GAAG;AAC/B,aAAO,KAAK,MAAM;AAAA,IACpB;AACA,WAAO,KAAK,UAAU,MAAM,KAAK,GAAG;AAAA,EACtC;AACF;AACA,IAAM,oBAAN,MAAwB;AAAA,EACtB,YAAY,MAAM,OAAO,KAAK;AAC5B,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,SAAS,IAAI,cAAc,MAAM,QAAQ,MAAM,KAAK,CAAC;AAC1D,SAAK,MAAM,QAAQ,MAAM,KAAK,SAAS;AACvC,SAAK,OAAO,KAAK,IAAI,OAAO,GAAG;AAC/B,SAAK,KAAK,KAAK,IAAI,OAAO,GAAG;AAAA,EAC/B;AAAA,EACA,UAAU,MAAM,KAAK;AACnB,QAAI,MAAM,IAAI,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI;AACzD,WAAK,QAAQ;AACb,WAAK,OAAO;AACZ,aAAO;AAAA,IACT;AACA,YAAQ,KAAK,IAAI,GAAG,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK,OAAO,KAAK,GAAG;AACvE,QAAI,QAAQ,MAAM,IAAI,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,KAAK;AAC5D,QAAI,OAAO,MAAO,QAAO;AACzB,aAAS;AACT,QAAI;AAAA,MACF;AAAA,IACF,IAAI,KAAK,OAAO,KAAK,IAAI;AACzB,SAAK,QAAQ,MAAM,SAAS,QAAQ;AACpC,SAAK,QAAQ,MAAM,UAAU,QAAQ,QAAQ,MAAM,IAAI,MAAM,MAAM,MAAM,SAAS,KAAK,IAAI,MAAM,MAAM,GAAG,KAAK;AAC/G,SAAK,OAAO,CAAC,KAAK;AAClB,WAAO;AAAA,EACT;AAAA,EACA,KAAK,OAAO,GAAG;AACb,QAAI,OAAO,EAAG,QAAO,KAAK,IAAI,MAAM,KAAK,OAAO,KAAK,GAAG;AAAA,aAAW,OAAO,EAAG,QAAO,KAAK,IAAI,MAAM,KAAK,KAAK,KAAK,GAAG;AACrH,WAAO,KAAK,UAAU,MAAM,KAAK,OAAO,GAAG;AAAA,EAC7C;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,OAAO,aAAa,KAAK,SAAS;AAAA,EAChD;AACF;AACA,IAAM,aAAN,MAAiB;AAAA,EACf,YAAY,OAAO;AACjB,SAAK,QAAQ;AACb,SAAK,aAAa;AAClB,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EACd;AAAA,EACA,KAAK,OAAO,GAAG;AACb,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK,MAAM,KAAK,IAAI;AACxB,QAAI,QAAQ,KAAK,YAAY;AAC3B,WAAK,QAAQ;AACb,WAAK,aAAa;AAAA,IACpB,WAAW,MAAM;AACf,WAAK,OAAO;AACZ,WAAK,QAAQ;AAAA,IACf,WAAW,WAAW;AACpB,UAAI,KAAK,YAAY;AACnB,aAAK,QAAQ;AAAA,MACf,OAAO;AACL,aAAK,aAAa;AAClB,aAAK,KAAK;AAAA,MACZ;AAAA,IACF,OAAO;AACL,WAAK,QAAQ;AACb,WAAK,aAAa;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AAAA,EACA,IAAI,YAAY;AACd,WAAO;AAAA,EACT;AACF;AACA,IAAI,OAAO,UAAU,aAAa;AAChC,OAAK,UAAU,OAAO,QAAQ,IAAI,WAAY;AAC5C,WAAO,KAAK,KAAK;AAAA,EACnB;AACA,gBAAc,UAAU,OAAO,QAAQ,IAAI,kBAAkB,UAAU,OAAO,QAAQ,IAAI,WAAW,UAAU,OAAO,QAAQ,IAAI,WAAY;AAC5I,WAAO;AAAA,EACT;AACF;AAKA,IAAM,OAAN,MAAW;AAAA;AAAA;AAAA;AAAA,EAIT,YAIA,MAKA,IAIA,QAIA,MAAM;AACJ,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS;AACX,WAAO,KAAK,KAAK,KAAK;AAAA,EACxB;AACF;AACA,SAAS,KAAK,MAAM,MAAM,IAAI;AAC5B,SAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,QAAQ,IAAI,CAAC;AAC9C,SAAO,CAAC,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,KAAK,QAAQ,EAAE,CAAC,CAAC;AACzD;AAUA,SAASA,kBAAiB,KAAK,KAAK,UAAU,MAAM,mBAAmB,MAAM;AAC3E,SAAO,iBAAmB,KAAK,KAAK,SAAS,gBAAgB;AAC/D;AACA,SAASC,cAAa,IAAI;AACxB,SAAO,MAAM,SAAU,KAAK;AAC9B;AACA,SAASC,eAAc,IAAI;AACzB,SAAO,MAAM,SAAU,KAAK;AAC9B;AAMA,SAASC,aAAY,KAAK,KAAK;AAC7B,MAAI,QAAQ,IAAI,WAAW,GAAG;AAC9B,MAAI,CAACD,eAAc,KAAK,KAAK,MAAM,KAAK,IAAI,OAAQ,QAAO;AAC3D,MAAI,QAAQ,IAAI,WAAW,MAAM,CAAC;AAClC,MAAI,CAACD,cAAa,KAAK,EAAG,QAAO;AACjC,UAAQ,QAAQ,SAAU,OAAO,QAAQ,SAAU;AACrD;AAMA,SAAS,cAAc,MAAM;AAC3B,MAAI,QAAQ,MAAQ,QAAO,OAAO,aAAa,IAAI;AACnD,UAAQ;AACR,SAAO,OAAO,cAAc,QAAQ,MAAM,QAAS,OAAO,QAAQ,KAAM;AAC1E;AAIA,SAASG,eAAc,MAAM;AAC3B,SAAO,OAAO,QAAU,IAAI;AAC9B;AACA,IAAM,eAAe;AAIrB,IAAI,UAAuB,SAAUC,UAAS;AAK5C,EAAAA,SAAQA,SAAQ,QAAQ,IAAI,CAAC,IAAI;AAIjC,EAAAA,SAAQA,SAAQ,UAAU,IAAI,CAAC,IAAI;AAInC,EAAAA,SAAQA,SAAQ,aAAa,IAAI,CAAC,IAAI;AAItC,EAAAA,SAAQA,SAAQ,YAAY,IAAI,CAAC,IAAI;AACrC,SAAOA;AACT,EAAE,YAAY,UAAU,CAAC,EAAE;AAM3B,IAAM,aAAN,MAAM,YAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASf,YAIA,UAAU;AACR,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS;AACX,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK,EAAG,WAAU,KAAK,SAAS,CAAC;AAC3E,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,YAAY;AACd,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK,GAAG;AAChD,UAAI,MAAM,KAAK,SAAS,IAAI,CAAC;AAC7B,gBAAU,MAAM,IAAI,KAAK,SAAS,CAAC,IAAI;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,QAAQ;AACV,WAAO,KAAK,SAAS,UAAU,KAAK,KAAK,SAAS,UAAU,KAAK,KAAK,SAAS,CAAC,IAAI;AAAA,EACtF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,GAAG;AACV,aAAS,IAAI,GAAG,OAAO,GAAG,OAAO,GAAG,IAAI,KAAK,SAAS,UAAS;AAC7D,UAAI,MAAM,KAAK,SAAS,GAAG,GACzB,MAAM,KAAK,SAAS,GAAG;AACzB,UAAI,MAAM,GAAG;AACX,UAAE,MAAM,MAAM,GAAG;AACjB,gBAAQ;AAAA,MACV,OAAO;AACL,gBAAQ;AAAA,MACV;AACA,cAAQ;AAAA,IACV;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,kBAAkB,GAAG,aAAa,OAAO;AACvC,gBAAY,MAAM,GAAG,UAAU;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,eAAe;AACjB,QAAI,WAAW,CAAC;AAChB,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,UAAS;AACzC,UAAI,MAAM,KAAK,SAAS,GAAG,GACzB,MAAM,KAAK,SAAS,GAAG;AACzB,UAAI,MAAM,EAAG,UAAS,KAAK,KAAK,GAAG;AAAA,UAAO,UAAS,KAAK,KAAK,GAAG;AAAA,IAClE;AACA,WAAO,IAAI,YAAW,QAAQ;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,OAAO;AACjB,WAAO,KAAK,QAAQ,QAAQ,MAAM,QAAQ,OAAO,YAAY,MAAM,KAAK;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,OAAO,SAAS,OAAO;AAC7B,WAAO,MAAM,QAAQ,OAAO,OAAO,MAAM,OAAO,MAAM;AAAA,EACxD;AAAA,EACA,OAAO,KAAK,QAAQ,IAAI,OAAO,QAAQ,QAAQ;AAC7C,QAAI,OAAO,GACT,OAAO;AACT,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,UAAS;AACzC,UAAI,MAAM,KAAK,SAAS,GAAG,GACzB,MAAM,KAAK,SAAS,GAAG,GACvB,OAAO,OAAO;AAChB,UAAI,MAAM,GAAG;AACX,YAAI,OAAO,IAAK,QAAO,QAAQ,MAAM;AACrC,gBAAQ;AAAA,MACV,OAAO;AACL,YAAI,QAAQ,QAAQ,UAAU,QAAQ,QAAQ,QAAQ,QAAQ,YAAY,OAAO,OAAO,OAAO,OAAO,QAAQ,QAAQ,eAAe,OAAO,OAAO,QAAQ,QAAQ,cAAc,OAAO,KAAM,QAAO;AACrM,YAAI,OAAO,OAAO,QAAQ,OAAO,QAAQ,KAAK,CAAC,IAAK,QAAO,OAAO,QAAQ,QAAQ,IAAI,OAAO,OAAO;AACpG,gBAAQ;AAAA,MACV;AACA,aAAO;AAAA,IACT;AACA,QAAI,MAAM,KAAM,OAAM,IAAI,WAAW,YAAY,GAAG,4CAA4C,IAAI,EAAE;AACtG,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,MAAM,KAAK,MAAM;AAC5B,aAAS,IAAI,GAAG,MAAM,GAAG,IAAI,KAAK,SAAS,UAAU,OAAO,MAAK;AAC/D,UAAI,MAAM,KAAK,SAAS,GAAG,GACzB,MAAM,KAAK,SAAS,GAAG,GACvB,MAAM,MAAM;AACd,UAAI,OAAO,KAAK,OAAO,MAAM,OAAO,KAAM,QAAO,MAAM,QAAQ,MAAM,KAAK,UAAU;AACpF,YAAM;AAAA,IACR;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACT,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,UAAS;AACzC,UAAI,MAAM,KAAK,SAAS,GAAG,GACzB,MAAM,KAAK,SAAS,GAAG;AACzB,iBAAW,SAAS,MAAM,MAAM,OAAO,OAAO,IAAI,MAAM,MAAM;AAAA,IAChE;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACP,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,SAAS,MAAM;AACpB,QAAI,CAAC,MAAM,QAAQ,IAAI,KAAK,KAAK,SAAS,KAAK,KAAK,KAAK,OAAK,OAAO,KAAK,QAAQ,EAAG,OAAM,IAAI,WAAW,2CAA2C;AACrJ,WAAO,IAAI,YAAW,IAAI;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,UAAU;AACtB,WAAO,IAAI,YAAW,QAAQ;AAAA,EAChC;AACF;AAMA,IAAM,YAAN,MAAM,mBAAkB,WAAW;AAAA,EACjC,YAAY,UAIZ,UAAU;AACR,UAAM,QAAQ;AACd,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,KAAK;AACT,QAAI,KAAK,UAAU,IAAI,OAAQ,OAAM,IAAI,WAAW,yDAAyD;AAC7G,gBAAY,MAAM,CAAC,OAAO,KAAK,OAAO,MAAM,SAAS,MAAM,IAAI,QAAQ,OAAO,SAAS,MAAM,QAAQ,IAAI,GAAG,KAAK;AACjH,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,OAAO,SAAS,OAAO;AAC7B,WAAO,OAAO,MAAM,OAAO,QAAQ,IAAI;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,KAAK;AACV,QAAI,WAAW,KAAK,SAAS,MAAM,GACjC,WAAW,CAAC;AACd,aAAS,IAAI,GAAG,MAAM,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AACpD,UAAI,MAAM,SAAS,CAAC,GAClB,MAAM,SAAS,IAAI,CAAC;AACtB,UAAI,OAAO,GAAG;AACZ,iBAAS,CAAC,IAAI;AACd,iBAAS,IAAI,CAAC,IAAI;AAClB,YAAI,QAAQ,KAAK;AACjB,eAAO,SAAS,SAAS,MAAO,UAAS,KAAK,KAAK,KAAK;AACxD,iBAAS,KAAK,MAAM,IAAI,MAAM,KAAK,MAAM,GAAG,IAAI,KAAK,KAAK;AAAA,MAC5D;AACA,aAAO;AAAA,IACT;AACA,WAAO,IAAI,WAAU,UAAU,QAAQ;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,OAAO;AACb,WAAO,KAAK,QAAQ,QAAQ,MAAM,QAAQ,OAAO,YAAY,MAAM,OAAO,IAAI;AAAA,EAChF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,IAAI,OAAO,SAAS,OAAO;AACzB,WAAO,MAAM,QAAQ,OAAO,OAAO,MAAM,OAAO,QAAQ,IAAI;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,YAAY,GAAG,aAAa,OAAO;AACjC,gBAAY,MAAM,GAAG,UAAU;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AACT,WAAO,WAAW,OAAO,KAAK,QAAQ;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,QAAQ;AACb,QAAI,iBAAiB,CAAC,GACpB,iBAAiB,CAAC,GAClB,mBAAmB,CAAC;AACtB,QAAI,OAAO,IAAI,YAAY,IAAI;AAC/B,SAAM,UAAS,IAAI,GAAG,MAAM,OAAK;AAC/B,UAAI,OAAO,KAAK,OAAO,SAAS,MAAM,OAAO,GAAG;AAChD,aAAO,MAAM,QAAQ,OAAO,QAAQ,KAAK,OAAO,GAAG;AACjD,YAAI,KAAK,KAAM,OAAM;AACrB,YAAI,MAAM,KAAK,IAAI,KAAK,KAAK,OAAO,GAAG;AACvC,mBAAW,kBAAkB,KAAK,EAAE;AACpC,YAAI,MAAM,KAAK,OAAO,KAAK,KAAK,KAAK,OAAO,IAAI,KAAK,MAAM;AAC3D,mBAAW,gBAAgB,KAAK,GAAG;AACnC,YAAI,MAAM,EAAG,WAAU,gBAAgB,gBAAgB,KAAK,IAAI;AAChE,aAAK,QAAQ,GAAG;AAChB,eAAO;AAAA,MACT;AACA,UAAI,MAAM,OAAO,GAAG;AACpB,aAAO,MAAM,KAAK;AAChB,YAAI,KAAK,KAAM,OAAM;AACrB,YAAI,MAAM,KAAK,IAAI,KAAK,KAAK,MAAM,GAAG;AACtC,mBAAW,gBAAgB,KAAK,EAAE;AAClC,mBAAW,kBAAkB,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,OAAO,IAAI,KAAK,MAAM,CAAC;AACpF,aAAK,QAAQ,GAAG;AAChB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,MACL,SAAS,IAAI,WAAU,gBAAgB,cAAc;AAAA,MACrD,UAAU,WAAW,OAAO,gBAAgB;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACP,QAAI,QAAQ,CAAC;AACb,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK,GAAG;AAChD,UAAI,MAAM,KAAK,SAAS,CAAC,GACvB,MAAM,KAAK,SAAS,IAAI,CAAC;AAC3B,UAAI,MAAM,EAAG,OAAM,KAAK,GAAG;AAAA,eAAW,OAAO,EAAG,OAAM,KAAK,CAAC,GAAG,CAAC;AAAA,UAAO,OAAM,KAAK,CAAC,GAAG,EAAE,OAAO,KAAK,SAAS,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;AAAA,IAChI;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,GAAG,SAAS,QAAQ,SAAS;AAClC,QAAI,WAAW,CAAC,GACd,WAAW,CAAC,GACZ,MAAM;AACR,QAAI,QAAQ;AACZ,aAAS,MAAM,QAAQ,OAAO;AAC5B,UAAI,CAAC,SAAS,CAAC,SAAS,OAAQ;AAChC,UAAI,MAAM,OAAQ,YAAW,UAAU,SAAS,KAAK,EAAE;AACvD,UAAI,MAAM,IAAI,WAAU,UAAU,QAAQ;AAC1C,cAAQ,QAAQ,MAAM,QAAQ,IAAI,IAAI,KAAK,CAAC,IAAI;AAChD,iBAAW,CAAC;AACZ,iBAAW,CAAC;AACZ,YAAM;AAAA,IACR;AACA,aAAS,QAAQ,MAAM;AACrB,UAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,iBAAS,OAAO,KAAM,SAAQ,GAAG;AAAA,MACnC,WAAW,gBAAgB,YAAW;AACpC,YAAI,KAAK,UAAU,OAAQ,OAAM,IAAI,WAAW,qCAAqC,KAAK,MAAM,cAAc,MAAM,GAAG;AACvH,cAAM;AACN,gBAAQ,QAAQ,MAAM,QAAQ,KAAK,IAAI,KAAK,CAAC,IAAI;AAAA,MACnD,OAAO;AACL,YAAI;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL,QAAAC;AAAA,QACF,IAAI;AACJ,YAAI,OAAO,MAAM,OAAO,KAAK,KAAK,OAAQ,OAAM,IAAI,WAAW,wBAAwB,IAAI,OAAO,EAAE,sBAAsB,MAAM,GAAG;AACnI,YAAI,UAAU,CAACA,UAAS,KAAK,QAAQ,OAAOA,WAAU,WAAW,KAAK,GAAGA,QAAO,MAAM,WAAW,YAAY,CAAC,IAAIA;AAClH,YAAI,SAAS,QAAQ;AACrB,YAAI,QAAQ,MAAM,UAAU,EAAG;AAC/B,YAAI,OAAO,IAAK,OAAM;AACtB,YAAI,OAAO,IAAK,YAAW,UAAU,OAAO,KAAK,EAAE;AACnD,mBAAW,UAAU,KAAK,MAAM,MAAM;AACtC,kBAAU,UAAU,UAAU,OAAO;AACrC,cAAM;AAAA,MACR;AAAA,IACF;AACA,YAAQ,OAAO;AACf,UAAM,CAAC,KAAK;AACZ,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,MAAM,QAAQ;AACnB,WAAO,IAAI,WAAU,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,SAAS,MAAM;AACpB,QAAI,CAAC,MAAM,QAAQ,IAAI,EAAG,OAAM,IAAI,WAAW,0CAA0C;AACzF,QAAI,WAAW,CAAC,GACd,WAAW,CAAC;AACd,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAI,OAAO,KAAK,CAAC;AACjB,UAAI,OAAO,QAAQ,UAAU;AAC3B,iBAAS,KAAK,MAAM,EAAE;AAAA,MACxB,WAAW,CAAC,MAAM,QAAQ,IAAI,KAAK,OAAO,KAAK,CAAC,KAAK,YAAY,KAAK,KAAK,CAAC,GAAGC,OAAMA,MAAK,OAAO,KAAK,QAAQ,GAAG;AAC/G,cAAM,IAAI,WAAW,0CAA0C;AAAA,MACjE,WAAW,KAAK,UAAU,GAAG;AAC3B,iBAAS,KAAK,KAAK,CAAC,GAAG,CAAC;AAAA,MAC1B,OAAO;AACL,eAAO,SAAS,SAAS,EAAG,UAAS,KAAK,KAAK,KAAK;AACpD,iBAAS,CAAC,IAAI,KAAK,GAAG,KAAK,MAAM,CAAC,CAAC;AACnC,iBAAS,KAAK,KAAK,CAAC,GAAG,SAAS,CAAC,EAAE,MAAM;AAAA,MAC3C;AAAA,IACF;AACA,WAAO,IAAI,WAAU,UAAU,QAAQ;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,UAAU,UAAU,UAAU;AACnC,WAAO,IAAI,WAAU,UAAU,QAAQ;AAAA,EACzC;AACF;AACA,SAAS,WAAW,UAAU,KAAK,KAAK,YAAY,OAAO;AACzD,MAAI,OAAO,KAAK,OAAO,EAAG;AAC1B,MAAI,OAAO,SAAS,SAAS;AAC7B,MAAI,QAAQ,KAAK,OAAO,KAAK,OAAO,SAAS,OAAO,CAAC,EAAG,UAAS,IAAI,KAAK;AAAA,WAAa,QAAQ,KAAK,OAAO,KAAK,SAAS,IAAI,KAAK,EAAG,UAAS,OAAO,CAAC,KAAK;AAAA,WAAa,WAAW;AACjL,aAAS,IAAI,KAAK;AAClB,aAAS,OAAO,CAAC,KAAK;AAAA,EACxB,MAAO,UAAS,KAAK,KAAK,GAAG;AAC/B;AACA,SAAS,UAAU,QAAQ,UAAU,OAAO;AAC1C,MAAI,MAAM,UAAU,EAAG;AACvB,MAAI,QAAQ,SAAS,SAAS,KAAK;AACnC,MAAI,QAAQ,OAAO,QAAQ;AACzB,WAAO,OAAO,SAAS,CAAC,IAAI,OAAO,OAAO,SAAS,CAAC,EAAE,OAAO,KAAK;AAAA,EACpE,OAAO;AACL,WAAO,OAAO,SAAS,MAAO,QAAO,KAAK,KAAK,KAAK;AACpD,WAAO,KAAK,KAAK;AAAA,EACnB;AACF;AACA,SAAS,YAAY,MAAM,GAAG,YAAY;AACxC,MAAI,WAAW,KAAK;AACpB,WAAS,OAAO,GAAG,OAAO,GAAG,IAAI,GAAG,IAAI,KAAK,SAAS,UAAS;AAC7D,QAAI,MAAM,KAAK,SAAS,GAAG,GACzB,MAAM,KAAK,SAAS,GAAG;AACzB,QAAI,MAAM,GAAG;AACX,cAAQ;AACR,cAAQ;AAAA,IACV,OAAO;AACL,UAAI,OAAO,MACT,OAAO,MACP,OAAO,KAAK;AACd,iBAAS;AACP,gBAAQ;AACR,gBAAQ;AACR,YAAI,OAAO,SAAU,QAAO,KAAK,OAAO,SAAS,IAAI,KAAK,CAAC,CAAC;AAC5D,YAAI,cAAc,KAAK,KAAK,SAAS,UAAU,KAAK,SAAS,IAAI,CAAC,IAAI,EAAG;AACzE,cAAM,KAAK,SAAS,GAAG;AACvB,cAAM,KAAK,SAAS,GAAG;AAAA,MACzB;AACA,QAAE,MAAM,MAAM,MAAM,MAAM,IAAI;AAC9B,aAAO;AACP,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,SAAS,OAAO,MAAM,MAAM,QAAQ,QAAQ,OAAO;AAGjD,MAAI,WAAW,CAAC,GACdD,UAAS,QAAQ,CAAC,IAAI;AACxB,MAAI,IAAI,IAAI,YAAY,IAAI,GAC1B,IAAI,IAAI,YAAY,IAAI;AAK1B,WAAS,WAAW,QAAM;AACxB,QAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK;AACtC,YAAM,IAAI,MAAM,+BAA+B;AAAA,IACjD,WAAW,EAAE,OAAO,MAAM,EAAE,OAAO,IAAI;AAErC,UAAI,MAAM,KAAK,IAAI,EAAE,KAAK,EAAE,GAAG;AAC/B,iBAAW,UAAU,KAAK,EAAE;AAC5B,QAAE,QAAQ,GAAG;AACb,QAAE,QAAQ,GAAG;AAAA,IACf,WAAW,EAAE,OAAO,MAAM,EAAE,MAAM,KAAK,YAAY,EAAE,KAAK,EAAE,OAAO,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,UAAU;AAIrH,UAAI,MAAM,EAAE;AACZ,iBAAW,UAAU,EAAE,KAAK,EAAE;AAC9B,aAAO,KAAK;AACV,YAAI,QAAQ,KAAK,IAAI,EAAE,KAAK,GAAG;AAC/B,YAAI,EAAE,OAAO,KAAK,WAAW,EAAE,KAAK,EAAE,OAAO,OAAO;AAClD,qBAAW,UAAU,GAAG,EAAE,GAAG;AAC7B,cAAIA,QAAQ,WAAUA,SAAQ,UAAU,EAAE,IAAI;AAC9C,qBAAW,EAAE;AAAA,QACf;AACA,UAAE,QAAQ,KAAK;AACf,eAAO;AAAA,MACT;AACA,QAAE,KAAK;AAAA,IACT,WAAW,EAAE,OAAO,GAAG;AAGrB,UAAI,MAAM,GACR,OAAO,EAAE;AACX,aAAO,MAAM;AACX,YAAI,EAAE,OAAO,IAAI;AACf,cAAI,QAAQ,KAAK,IAAI,MAAM,EAAE,GAAG;AAChC,iBAAO;AACP,kBAAQ;AACR,YAAE,QAAQ,KAAK;AAAA,QACjB,WAAW,EAAE,OAAO,KAAK,EAAE,MAAM,MAAM;AACrC,kBAAQ,EAAE;AACV,YAAE,KAAK;AAAA,QACT,OAAO;AACL;AAAA,QACF;AAAA,MACF;AACA,iBAAW,UAAU,KAAK,WAAW,EAAE,IAAI,EAAE,MAAM,CAAC;AACpD,UAAIA,WAAU,WAAW,EAAE,EAAG,WAAUA,SAAQ,UAAU,EAAE,IAAI;AAChE,iBAAW,EAAE;AACb,QAAE,QAAQ,EAAE,MAAM,IAAI;AAAA,IACxB,WAAW,EAAE,QAAQ,EAAE,MAAM;AAC3B,aAAOA,UAAS,UAAU,UAAU,UAAUA,OAAM,IAAI,WAAW,OAAO,QAAQ;AAAA,IACpF,OAAO;AACL,YAAM,IAAI,MAAM,+BAA+B;AAAA,IACjD;AAAA,EACF;AACF;AACA,SAAS,YAAY,MAAM,MAAM,QAAQ,OAAO;AAC9C,MAAI,WAAW,CAAC;AAChB,MAAIA,UAAS,QAAQ,CAAC,IAAI;AAC1B,MAAI,IAAI,IAAI,YAAY,IAAI,GAC1B,IAAI,IAAI,YAAY,IAAI;AAC1B,WAAS,OAAO,WAAS;AACvB,QAAI,EAAE,QAAQ,EAAE,MAAM;AACpB,aAAOA,UAAS,UAAU,UAAU,UAAUA,OAAM,IAAI,WAAW,OAAO,QAAQ;AAAA,IACpF,WAAW,EAAE,OAAO,GAAG;AAErB,iBAAW,UAAU,EAAE,KAAK,GAAG,IAAI;AACnC,QAAE,KAAK;AAAA,IACT,WAAW,EAAE,OAAO,KAAK,CAAC,EAAE,MAAM;AAEhC,iBAAW,UAAU,GAAG,EAAE,KAAK,IAAI;AACnC,UAAIA,QAAQ,WAAUA,SAAQ,UAAU,EAAE,IAAI;AAC9C,QAAE,KAAK;AAAA,IACT,WAAW,EAAE,QAAQ,EAAE,MAAM;AAC3B,YAAM,IAAI,MAAM,+BAA+B;AAAA,IACjD,OAAO;AACL,UAAI,MAAM,KAAK,IAAI,EAAE,MAAM,EAAE,GAAG,GAC9B,aAAa,SAAS;AACxB,UAAI,EAAE,OAAO,IAAI;AACf,YAAI,OAAO,EAAE,OAAO,KAAK,KAAK,EAAE,MAAM,IAAI,EAAE;AAC5C,mBAAW,UAAU,KAAK,MAAM,IAAI;AACpC,YAAIA,WAAU,KAAM,WAAUA,SAAQ,UAAU,EAAE,IAAI;AAAA,MACxD,WAAW,EAAE,OAAO,IAAI;AACtB,mBAAW,UAAU,EAAE,MAAM,IAAI,EAAE,KAAK,KAAK,IAAI;AACjD,YAAIA,QAAQ,WAAUA,SAAQ,UAAU,EAAE,QAAQ,GAAG,CAAC;AAAA,MACxD,OAAO;AACL,mBAAW,UAAU,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE,KAAK,IAAI;AAC/D,YAAIA,WAAU,CAAC,EAAE,IAAK,WAAUA,SAAQ,UAAU,EAAE,IAAI;AAAA,MAC1D;AACA,cAAQ,EAAE,MAAM,OAAO,EAAE,OAAO,KAAK,EAAE,MAAM,SAAS,QAAQ,SAAS,SAAS;AAChF,QAAE,SAAS,GAAG;AACd,QAAE,QAAQ,GAAG;AAAA,IACf;AAAA,EACF;AACF;AACA,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,KAAK;AACf,SAAK,MAAM;AACX,SAAK,IAAI;AACT,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,OAAO;AACL,QAAI;AAAA,MACF;AAAA,IACF,IAAI,KAAK;AACT,QAAI,KAAK,IAAI,SAAS,QAAQ;AAC5B,WAAK,MAAM,SAAS,KAAK,GAAG;AAC5B,WAAK,MAAM,SAAS,KAAK,GAAG;AAAA,IAC9B,OAAO;AACL,WAAK,MAAM;AACX,WAAK,MAAM;AAAA,IACb;AACA,SAAK,MAAM;AAAA,EACb;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,MAAM,IAAI,KAAK,MAAM,KAAK;AAAA,EACxC;AAAA,EACA,IAAI,OAAO;AACT,QAAI;AAAA,MACA;AAAA,IACF,IAAI,KAAK,KACT,QAAQ,KAAK,IAAI,KAAK;AACxB,WAAO,SAAS,SAAS,SAAS,KAAK,QAAQ,SAAS,KAAK;AAAA,EAC/D;AAAA,EACA,QAAQ,KAAK;AACX,QAAI;AAAA,MACA;AAAA,IACF,IAAI,KAAK,KACT,QAAQ,KAAK,IAAI,KAAK;AACxB,WAAO,SAAS,SAAS,UAAU,CAAC,MAAM,KAAK,QAAQ,SAAS,KAAK,EAAE,MAAM,KAAK,KAAK,OAAO,OAAO,SAAY,KAAK,MAAM,GAAG;AAAA,EACjI;AAAA,EACA,QAAQ,KAAK;AACX,QAAI,OAAO,KAAK,IAAK,MAAK,KAAK;AAAA,SAAO;AACpC,WAAK,OAAO;AACZ,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA,EACA,SAAS,KAAK;AACZ,QAAI,KAAK,OAAO,GAAI,MAAK,QAAQ,GAAG;AAAA,aAAW,OAAO,KAAK,IAAK,MAAK,KAAK;AAAA,SAAO;AAC/E,WAAK,OAAO;AACZ,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AACF;AAQA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,YAIA,MAIA,IAAI,OAAO;AACT,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAS;AACX,WAAO,KAAK,QAAQ,KAA8B,KAAK,KAAK,KAAK;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AACT,WAAO,KAAK,QAAQ,KAA8B,KAAK,OAAO,KAAK;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,QAAQ;AACV,WAAO,KAAK,QAAQ,KAAK;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,QAAQ;AACV,WAAO,KAAK,QAAQ,IAAgC,KAAK,KAAK,QAAQ,KAAgC,IAAI;AAAA,EAC5G;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AACd,QAAI,QAAQ,KAAK,QAAQ;AACzB,WAAO,SAAS,IAAI,OAAO;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,aAAa;AACf,QAAI,QAAQ,KAAK,SAAS;AAC1B,WAAO,SAAS,WAAwC,SAAY;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ,QAAQ,IAAI;AACtB,QAAI,MAAM;AACV,QAAI,KAAK,OAAO;AACd,aAAO,KAAK,OAAO,OAAO,KAAK,MAAM,KAAK;AAAA,IAC5C,OAAO;AACL,aAAO,OAAO,OAAO,KAAK,MAAM,CAAC;AACjC,WAAK,OAAO,OAAO,KAAK,IAAI,EAAE;AAAA,IAChC;AACA,WAAO,QAAQ,KAAK,QAAQ,MAAM,KAAK,KAAK,OAAO,IAAI,gBAAe,MAAM,IAAI,KAAK,KAAK;AAAA,EAC5F;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,MAAM,KAAK,MAAM;AACtB,QAAI,QAAQ,KAAK,UAAU,MAAM,KAAK,OAAQ,QAAO,gBAAgB,MAAM,MAAM,EAAE;AACnF,QAAI,OAAO,KAAK,IAAI,OAAO,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,MAAM,IAAI,OAAO;AAC9E,WAAO,gBAAgB,MAAM,KAAK,QAAQ,IAAI;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA,EAIA,GAAG,OAAO,eAAe,OAAO;AAC9B,WAAO,KAAK,UAAU,MAAM,UAAU,KAAK,QAAQ,MAAM,SAAS,CAAC,gBAAgB,CAAC,KAAK,SAAS,KAAK,SAAS,MAAM;AAAA,EACxH;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACP,WAAO;AAAA,MACL,QAAQ,KAAK;AAAA,MACb,MAAM,KAAK;AAAA,IACb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,SAAS,MAAM;AACpB,QAAI,CAAC,QAAQ,OAAO,KAAK,UAAU,YAAY,OAAO,KAAK,QAAQ,SAAU,OAAM,IAAI,WAAW,gDAAgD;AAClJ,WAAO,gBAAgB,MAAM,KAAK,QAAQ,KAAK,IAAI;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,MAAM,IAAI,OAAO;AAC7B,WAAO,IAAI,gBAAe,MAAM,IAAI,KAAK;AAAA,EAC3C;AACF;AAIA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAKA,QAKA,WAAW;AACT,SAAK,SAAS;AACd,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ,QAAQ,IAAI;AACtB,QAAI,OAAO,MAAO,QAAO;AACzB,WAAO,iBAAgB,OAAO,KAAK,OAAO,IAAI,OAAK,EAAE,IAAI,QAAQ,KAAK,CAAC,GAAG,KAAK,SAAS;AAAA,EAC1F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,GAAG,OAAO,eAAe,OAAO;AAC9B,QAAI,KAAK,OAAO,UAAU,MAAM,OAAO,UAAU,KAAK,aAAa,MAAM,UAAW,QAAO;AAC3F,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,IAAK,KAAI,CAAC,KAAK,OAAO,CAAC,EAAE,GAAG,MAAM,OAAO,CAAC,GAAG,YAAY,EAAG,QAAO;AAC3G,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,OAAO;AACT,WAAO,KAAK,OAAO,KAAK,SAAS;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK,OAAO,UAAU,IAAI,OAAO,IAAI,iBAAgB,CAAC,KAAK,IAAI,GAAG,CAAC;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,OAAO,OAAO,MAAM;AAC3B,WAAO,iBAAgB,OAAO,CAAC,KAAK,EAAE,OAAO,KAAK,MAAM,GAAG,OAAO,IAAI,KAAK,YAAY,CAAC;AAAA,EAC1F;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,OAAO,QAAQ,KAAK,WAAW;AAC1C,QAAI,SAAS,KAAK,OAAO,MAAM;AAC/B,WAAO,KAAK,IAAI;AAChB,WAAO,iBAAgB,OAAO,QAAQ,KAAK,SAAS;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,WAAO;AAAA,MACL,QAAQ,KAAK,OAAO,IAAI,OAAK,EAAE,OAAO,CAAC;AAAA,MACvC,MAAM,KAAK;AAAA,IACb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,MAAM;AACpB,QAAI,CAAC,QAAQ,CAAC,MAAM,QAAQ,KAAK,MAAM,KAAK,OAAO,KAAK,QAAQ,YAAY,KAAK,QAAQ,KAAK,OAAO,OAAQ,OAAM,IAAI,WAAW,iDAAiD;AACnL,WAAO,IAAI,iBAAgB,KAAK,OAAO,IAAI,OAAK,eAAe,SAAS,CAAC,CAAC,GAAG,KAAK,IAAI;AAAA,EACxF;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,QAAQ,OAAO,QAAQ;AACnC,WAAO,IAAI,iBAAgB,CAAC,iBAAgB,MAAM,QAAQ,IAAI,CAAC,GAAG,CAAC;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,OAAO,QAAQ,YAAY,GAAG;AACnC,QAAI,OAAO,UAAU,EAAG,OAAM,IAAI,WAAW,sCAAsC;AACnF,aAAS,MAAM,GAAG,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAC/C,UAAI,QAAQ,OAAO,CAAC;AACpB,UAAI,MAAM,QAAQ,MAAM,QAAQ,MAAM,MAAM,OAAO,IAAK,QAAO,iBAAgB,WAAW,OAAO,MAAM,GAAG,SAAS;AACnH,YAAM,MAAM;AAAA,IACd;AACA,WAAO,IAAI,iBAAgB,QAAQ,SAAS;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,OAAO,KAAK,QAAQ,GAAG,WAAW,YAAY;AACnD,WAAO,eAAe;AAAA,MAAO;AAAA,MAAK;AAAA,OAAM,SAAS,IAAI,IAAI,QAAQ,IAAI,IAAgC,OAAkC,aAAa,OAAO,IAAI,KAAK,IAAI,GAAG,SAAS,MAAM,eAAe,QAAQ,eAAe,SAAS,aAAa,aAA0C;AAAA;AAAA,IAAkC;AAAA,EACpU;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,MAAM,QAAQ,MAAM,YAAY,WAAW;AAChD,QAAI,SAAS,eAAe,QAAQ,eAAe,SAAS,aAAa,aAA0C,KAAsC,aAAa,OAAO,IAAI,KAAK,IAAI,GAAG,SAAS;AACtM,WAAO,OAAO,SAAS,eAAe,OAAO,MAAM,QAAQ,KAA8B,KAAgC,KAAK,IAAI,eAAe,OAAO,QAAQ,OAAO,OAAO,SAAS,IAAgC,KAAK,KAAK;AAAA,EACnO;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,WAAW,QAAQ,YAAY,GAAG;AACvC,QAAI,OAAO,OAAO,SAAS;AAC3B,WAAO,KAAK,CAAC,GAAG,MAAM,EAAE,OAAO,EAAE,IAAI;AACrC,gBAAY,OAAO,QAAQ,IAAI;AAC/B,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAI,QAAQ,OAAO,CAAC,GAClB,OAAO,OAAO,IAAI,CAAC;AACrB,UAAI,MAAM,QAAQ,MAAM,QAAQ,KAAK,KAAK,MAAM,OAAO,KAAK,IAAI;AAC9D,YAAI,OAAO,KAAK,MACd,KAAK,KAAK,IAAI,MAAM,IAAI,KAAK,EAAE;AACjC,YAAI,KAAK,UAAW;AACpB,eAAO,OAAO,EAAE,GAAG,GAAG,MAAM,SAAS,MAAM,OAAO,iBAAgB,MAAM,IAAI,IAAI,IAAI,iBAAgB,MAAM,MAAM,EAAE,CAAC;AAAA,MACrH;AAAA,IACF;AACA,WAAO,IAAI,iBAAgB,QAAQ,SAAS;AAAA,EAC9C;AACF;AACA,SAAS,eAAe,WAAW,WAAW;AAC5C,WAAS,SAAS,UAAU,OAAQ,KAAI,MAAM,KAAK,UAAW,OAAM,IAAI,WAAW,sCAAsC;AAC3H;AACA,IAAI,SAAS;AAcb,IAAM,QAAN,MAAM,OAAM;AAAA,EACV,YAIA,SAIA,cAIAE,UAAS,UAAU,SAAS;AAC1B,SAAK,UAAU;AACf,SAAK,eAAe;AACpB,SAAK,UAAUA;AACf,SAAK,WAAW;AAIhB,SAAK,KAAK;AACV,SAAK,UAAU,QAAQ,CAAC,CAAC;AACzB,SAAK,aAAa,OAAO,WAAW,aAAa,QAAQ,IAAI,IAAI;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAS;AACX,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,SAAS,CAAC,GAAG;AACzB,WAAO,IAAI,OAAM,OAAO,YAAY,OAAK,IAAI,OAAO,iBAAiB,CAAC,GAAG,MAAM,MAAM,IAAI,OAAO,YAAY,CAAC,OAAO,UAAU,YAAY,CAAC,GAAG,MAAM,MAAM,IAAI,CAAC,CAAC,OAAO,QAAQ,OAAO,OAAO;AAAA,EAC/L;AAAA;AAAA;AAAA;AAAA,EAIA,GAAG,OAAO;AACR,WAAO,IAAI,cAAc,CAAC,GAAG,MAAM,GAAyB,KAAK;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,QAAQ,MAAM,KAAK;AACjB,QAAI,KAAK,SAAU,OAAM,IAAI,MAAM,8BAA8B;AACjE,WAAO,IAAI,cAAc,MAAM,MAAM,GAAyB,GAAG;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,MAAM,KAAK;AAClB,QAAI,KAAK,SAAU,OAAM,IAAI,MAAM,8BAA8B;AACjE,WAAO,IAAI,cAAc,MAAM,MAAM,GAAwB,GAAG;AAAA,EAClE;AAAA,EACA,KAAK,OAAO,KAAK;AACf,QAAI,CAAC,IAAK,OAAM,OAAK;AACrB,WAAO,KAAK,QAAQ,CAAC,KAAK,GAAG,WAAS,IAAI,MAAM,MAAM,KAAK,CAAC,CAAC;AAAA,EAC/D;AACF;AACA,SAAS,UAAU,GAAG,GAAG;AACvB,SAAO,KAAK,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC,GAAG,MAAM,MAAM,EAAE,CAAC,CAAC;AACvE;AACA,IAAM,gBAAN,MAAoB;AAAA,EAClB,YAAY,cAAc,OAAO,MAAM,OAAO;AAC5C,SAAK,eAAe;AACpB,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,YAAY,WAAW;AACrB,QAAI;AACJ,QAAI,SAAS,KAAK;AAClB,QAAIA,WAAU,KAAK,MAAM;AACzB,QAAI,KAAK,KAAK,IACZ,MAAM,UAAU,EAAE,KAAK,GACvB,QAAQ,KAAK,QAAQ;AACvB,QAAI,SAAS,OACX,SAAS,OACT,WAAW,CAAC;AACd,aAAS,OAAO,KAAK,cAAc;AACjC,UAAI,OAAO,MAAO,UAAS;AAAA,eAAc,OAAO,YAAa,UAAS;AAAA,kBAAiB,KAAK,UAAU,IAAI,EAAE,OAAO,QAAQ,OAAO,SAAS,KAAK,KAAK,MAAM,EAAG,UAAS,KAAK,UAAU,IAAI,EAAE,CAAC;AAAA,IAC/L;AACA,WAAO;AAAA,MACL,OAAO,OAAO;AACZ,cAAM,OAAO,GAAG,IAAI,OAAO,KAAK;AAChC,eAAO;AAAA,MACT;AAAA,MACA,OAAO,OAAO,IAAI;AAChB,YAAI,UAAU,GAAG,cAAc,WAAW,GAAG,cAAc,GAAG,cAAc,UAAU,OAAO,QAAQ,GAAG;AACtG,cAAI,SAAS,OAAO,KAAK;AACzB,cAAI,QAAQ,CAAC,aAAa,QAAQ,MAAM,OAAO,GAAG,GAAGA,QAAO,IAAI,CAACA,SAAQ,QAAQ,MAAM,OAAO,GAAG,CAAC,GAAG;AACnG,kBAAM,OAAO,GAAG,IAAI;AACpB,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,MACA,aAAa,CAAC,OAAO,aAAa;AAChC,YAAI,QACF,UAAU,SAAS,OAAO,QAAQ,EAAE;AACtC,YAAI,WAAW,MAAM;AACnB,cAAI,SAAS,QAAQ,UAAU,OAAO;AACtC,cAAI,KAAK,aAAa,MAAM,SAAO;AACjC,mBAAO,eAAe,QAAQ,SAAS,MAAM,GAAG,MAAM,MAAM,MAAM,GAAG,IAAI,eAAe,aAAa,SAAS,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,IAAI;AAAA,UAC/J,CAAC,MAAM,QAAQ,aAAa,SAAS,OAAO,KAAK,GAAG,QAAQA,QAAO,IAAIA,SAAQ,SAAS,OAAO,KAAK,GAAG,MAAM,IAAI;AAC/G,kBAAM,OAAO,GAAG,IAAI;AACpB,mBAAO;AAAA,UACT;AAAA,QACF,OAAO;AACL,mBAAS,OAAO,KAAK;AAAA,QACvB;AACA,cAAM,OAAO,GAAG,IAAI;AACpB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,aAAa,GAAG,GAAGA,UAAS;AACnC,MAAI,EAAE,UAAU,EAAE,OAAQ,QAAO;AACjC,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAK,KAAI,CAACA,SAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAG,QAAO;AACpE,SAAO;AACT;AACA,SAAS,UAAU,OAAO,OAAO;AAC/B,MAAI,UAAU;AACd,WAAS,QAAQ,MAAO,KAAI,WAAW,OAAO,IAAI,IAAI,EAA4B,WAAU;AAC5F,SAAO;AACT;AACA,SAAS,iBAAiB,WAAW,OAAO,WAAW;AACrD,MAAI,gBAAgB,UAAU,IAAI,OAAK,UAAU,EAAE,EAAE,CAAC;AACtD,MAAI,gBAAgB,UAAU,IAAI,OAAK,EAAE,IAAI;AAC7C,MAAI,UAAU,cAAc,OAAO,OAAK,EAAE,IAAI,EAAE;AAChD,MAAI,MAAM,UAAU,MAAM,EAAE,KAAK;AACjC,WAAS,IAAI,OAAO;AAClB,QAAI,SAAS,CAAC;AACd,aAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,UAAI,QAAQ,QAAQ,OAAO,cAAc,CAAC,CAAC;AAC3C,UAAI,cAAc,CAAC,KAAK,EAAwB,UAAS,OAAO,MAAO,QAAO,KAAK,GAAG;AAAA,UAAO,QAAO,KAAK,KAAK;AAAA,IAChH;AACA,WAAO,MAAM,QAAQ,MAAM;AAAA,EAC7B;AACA,SAAO;AAAA,IACL,OAAO,OAAO;AACZ,eAAS,QAAQ,cAAe,YAAW,OAAO,IAAI;AACtD,YAAM,OAAO,GAAG,IAAI,IAAI,KAAK;AAC7B,aAAO;AAAA,IACT;AAAA,IACA,OAAO,OAAO,IAAI;AAChB,UAAI,CAAC,UAAU,OAAO,OAAO,EAAG,QAAO;AACvC,UAAI,QAAQ,IAAI,KAAK;AACrB,UAAI,MAAM,QAAQ,OAAO,MAAM,OAAO,GAAG,CAAC,EAAG,QAAO;AACpD,YAAM,OAAO,GAAG,IAAI;AACpB,aAAO;AAAA,IACT;AAAA,IACA,YAAY,OAAO,UAAU;AAC3B,UAAI,aAAa,UAAU,OAAO,aAAa;AAC/C,UAAI,eAAe,SAAS,OAAO,OAAO,MAAM,EAAE,GAChD,WAAW,SAAS,MAAM,KAAK;AACjC,UAAI,gBAAgB,CAAC,cAAc,UAAU,WAAW,YAAY,GAAG;AACrE,cAAM,OAAO,GAAG,IAAI;AACpB,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,IAAI,KAAK;AACrB,UAAI,MAAM,QAAQ,OAAO,QAAQ,GAAG;AAClC,cAAM,OAAO,GAAG,IAAI;AACpB,eAAO;AAAA,MACT;AACA,YAAM,OAAO,GAAG,IAAI;AACpB,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,IAAM,YAAyB,MAAM,OAAO;AAAA,EAC1C,QAAQ;AACV,CAAC;AAKD,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,YAIA,IAAI,SAAS,SAAS,UAItB,MAAM;AACJ,SAAK,KAAK;AACV,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,OAAO;AAIZ,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,QAAQ;AACpB,QAAI,QAAQ,IAAI,YAAW,UAAU,OAAO,QAAQ,OAAO,QAAQ,OAAO,YAAY,CAAC,GAAG,MAAM,MAAM,IAAI,MAAM;AAChH,QAAI,OAAO,QAAS,OAAM,WAAW,OAAO,QAAQ,KAAK;AACzD,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO;AACZ,QAAI,OAAO,MAAM,MAAM,SAAS,EAAE,KAAK,OAAK,EAAE,SAAS,IAAI;AAC3D,aAAS,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,WAAW,KAAK,SAAS,KAAK;AAAA,EAC1F;AAAA;AAAA;AAAA;AAAA,EAIA,KAAK,WAAW;AACd,QAAI,MAAM,UAAU,KAAK,EAAE,KAAK;AAChC,WAAO;AAAA,MACL,QAAQ,WAAS;AACf,cAAM,OAAO,GAAG,IAAI,KAAK,OAAO,KAAK;AACrC,eAAO;AAAA,MACT;AAAA,MACA,QAAQ,CAAC,OAAO,OAAO;AACrB,YAAI,SAAS,MAAM,OAAO,GAAG;AAC7B,YAAI,QAAQ,KAAK,QAAQ,QAAQ,EAAE;AACnC,YAAI,KAAK,SAAS,QAAQ,KAAK,EAAG,QAAO;AACzC,cAAM,OAAO,GAAG,IAAI;AACpB,eAAO;AAAA,MACT;AAAA,MACA,aAAa,CAAC,OAAO,aAAa;AAChC,YAAI,OAAO,MAAM,MAAM,SAAS,GAC9B,UAAU,SAAS,MAAM,SAAS,GAClC;AACF,aAAK,SAAS,KAAK,KAAK,OAAK,EAAE,SAAS,IAAI,MAAM,UAAU,QAAQ,KAAK,OAAK,EAAE,SAAS,IAAI,GAAG;AAC9F,gBAAM,OAAO,GAAG,IAAI,OAAO,OAAO,KAAK;AACvC,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,OAAO,QAAQ,KAAK,EAAE,KAAK,MAAM;AAC5C,gBAAM,OAAO,GAAG,IAAI,SAAS,MAAM,IAAI;AACvC,iBAAO;AAAA,QACT;AACA,cAAM,OAAO,GAAG,IAAI,KAAK,OAAO,KAAK;AACrC,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,QAAQ;AACX,WAAO,CAAC,MAAM,UAAU,GAAG;AAAA,MACzB,OAAO;AAAA,MACP;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,YAAY;AACd,WAAO;AAAA,EACT;AACF;AACA,IAAM,QAAQ;AAAA,EACZ,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,SAAS;AAAA,EACT,MAAM;AAAA,EACN,SAAS;AACX;AACA,SAAS,KAAK,OAAO;AACnB,SAAO,SAAO,IAAI,cAAc,KAAK,KAAK;AAC5C;AAWA,IAAM,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,SAAsB,KAAK,MAAM,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxC,MAAmB,KAAK,MAAM,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlC,SAAsB,KAAK,MAAM,OAAO;AAAA;AAAA;AAAA;AAAA,EAIxC,KAAkB,KAAK,MAAM,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhC,QAAqB,KAAK,MAAM,MAAM;AACxC;AACA,IAAM,gBAAN,MAAoB;AAAA,EAClB,YAAY,OAAOC,OAAM;AACvB,SAAK,QAAQ;AACb,SAAK,OAAOA;AAAA,EACd;AACF;AAQA,IAAM,cAAN,MAAM,aAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhB,GAAG,KAAK;AACN,WAAO,IAAI,oBAAoB,MAAM,GAAG;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,SAAS;AACnB,WAAO,aAAY,YAAY,GAAG;AAAA,MAChC,aAAa;AAAA,MACb,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AACT,WAAO,MAAM,OAAO,aAAa,IAAI,IAAI;AAAA,EAC3C;AACF;AACA,IAAM,sBAAN,MAA0B;AAAA,EACxB,YAAY,aAAa,OAAO;AAC9B,SAAK,cAAc;AACnB,SAAK,QAAQ;AAAA,EACf;AACF;AACA,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,YAAY,MAAM,cAAc,cAAc,SAAS,cAAc,QAAQ;AAC3E,SAAK,OAAO;AACZ,SAAK,eAAe;AACpB,SAAK,eAAe;AACpB,SAAK,UAAU;AACf,SAAK,eAAe;AACpB,SAAK,SAAS;AACd,SAAK,iBAAiB,CAAC;AACvB,WAAO,KAAK,eAAe,SAAS,aAAa,OAAQ,MAAK,eAAe;AAAA,MAAK;AAAA;AAAA,IAA6B;AAAA,EACjH;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,OAAO,KAAK,QAAQ,MAAM,EAAE;AAChC,WAAO,QAAQ,OAAO,MAAM,UAAU,KAAK,aAAa,QAAQ,CAAC;AAAA,EACnE;AAAA,EACA,OAAO,QAAQ,MAAM,cAAc,UAAU;AAC3C,QAAI,SAAS,CAAC;AACd,QAAI,SAAS,uBAAO,OAAO,IAAI;AAC/B,QAAI,kBAAkB,oBAAI,IAAI;AAC9B,aAAS,OAAO,QAAQ,MAAM,cAAc,eAAe,GAAG;AAC5D,UAAI,eAAe,WAAY,QAAO,KAAK,GAAG;AAAA,UAAO,EAAC,OAAO,IAAI,MAAM,EAAE,MAAM,OAAO,IAAI,MAAM,EAAE,IAAI,CAAC,IAAI,KAAK,GAAG;AAAA,IACrH;AACA,QAAI,UAAU,uBAAO,OAAO,IAAI;AAChC,QAAI,eAAe,CAAC;AACpB,QAAI,eAAe,CAAC;AACpB,aAAS,SAAS,QAAQ;AACxB,cAAQ,MAAM,EAAE,IAAI,aAAa,UAAU;AAC3C,mBAAa,KAAK,OAAK,MAAM,KAAK,CAAC,CAAC;AAAA,IACtC;AACA,QAAI,YAAY,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,OAAO;AACpF,aAAS,MAAM,QAAQ;AACrB,UAAI,YAAY,OAAO,EAAE,GACvB,QAAQ,UAAU,CAAC,EAAE;AACvB,UAAI,eAAe,aAAa,UAAU,EAAE,KAAK,CAAC;AAClD,UAAI,UAAU;AAAA,QAAM,OAAK,EAAE,QAAQ;AAAA;AAAA,MAAuB,GAAG;AAC3D,gBAAQ,MAAM,EAAE,IAAI,aAAa,UAAU,IAAI;AAC/C,YAAI,UAAU,cAAc,SAAS,GAAG;AACtC,uBAAa,KAAK,SAAS,MAAM,KAAK,CAAC;AAAA,QACzC,OAAO;AACL,cAAI,QAAQ,MAAM,QAAQ,UAAU,IAAI,OAAK,EAAE,KAAK,CAAC;AACrD,uBAAa,KAAK,YAAY,MAAM,QAAQ,OAAO,SAAS,MAAM,KAAK,CAAC,IAAI,SAAS,MAAM,KAAK,IAAI,KAAK;AAAA,QAC3G;AAAA,MACF,OAAO;AACL,iBAAS,KAAK,WAAW;AACvB,cAAI,EAAE,QAAQ,GAAyB;AACrC,oBAAQ,EAAE,EAAE,IAAI,aAAa,UAAU,IAAI;AAC3C,yBAAa,KAAK,EAAE,KAAK;AAAA,UAC3B,OAAO;AACL,oBAAQ,EAAE,EAAE,IAAI,aAAa,UAAU;AACvC,yBAAa,KAAK,OAAK,EAAE,YAAY,CAAC,CAAC;AAAA,UACzC;AAAA,QACF;AACA,gBAAQ,MAAM,EAAE,IAAI,aAAa,UAAU;AAC3C,qBAAa,KAAK,OAAK,iBAAiB,GAAG,OAAO,SAAS,CAAC;AAAA,MAC9D;AAAA,IACF;AACA,QAAI,UAAU,aAAa,IAAI,OAAK,EAAE,OAAO,CAAC;AAC9C,WAAO,IAAI,eAAc,MAAM,iBAAiB,SAAS,SAAS,cAAc,MAAM;AAAA,EACxF;AACF;AACA,SAAS,QAAQ,WAAW,cAAc,iBAAiB;AACzD,MAAI,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAChC,MAAI,OAAO,oBAAI,IAAI;AACnB,WAAS,MAAM,KAAKA,OAAM;AACxB,QAAI,QAAQ,KAAK,IAAI,GAAG;AACxB,QAAI,SAAS,MAAM;AACjB,UAAI,SAASA,MAAM;AACnB,UAAI,QAAQ,OAAO,KAAK,EAAE,QAAQ,GAAG;AACrC,UAAI,QAAQ,GAAI,QAAO,KAAK,EAAE,OAAO,OAAO,CAAC;AAC7C,UAAI,eAAe,oBAAqB,iBAAgB,OAAO,IAAI,WAAW;AAAA,IAChF;AACA,SAAK,IAAI,KAAKA,KAAI;AAClB,QAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,eAAS,KAAK,IAAK,OAAM,GAAGA,KAAI;AAAA,IAClC,WAAW,eAAe,qBAAqB;AAC7C,UAAI,gBAAgB,IAAI,IAAI,WAAW,EAAG,OAAM,IAAI,WAAW,4CAA4C;AAC3G,UAAI,UAAU,aAAa,IAAI,IAAI,WAAW,KAAK,IAAI;AACvD,sBAAgB,IAAI,IAAI,aAAa,OAAO;AAC5C,YAAM,SAASA,KAAI;AAAA,IACrB,WAAW,eAAe,eAAe;AACvC,YAAM,IAAI,OAAO,IAAI,IAAI;AAAA,IAC3B,WAAW,eAAe,YAAY;AACpC,aAAOA,KAAI,EAAE,KAAK,GAAG;AACrB,UAAI,IAAI,SAAU,OAAM,IAAI,UAAUA,KAAI;AAAA,IAC5C,WAAW,eAAe,eAAe;AACvC,aAAOA,KAAI,EAAE,KAAK,GAAG;AACrB,UAAI,IAAI,MAAM,WAAY,OAAM,IAAI,MAAM,YAAY,MAAM,OAAO;AAAA,IACrE,OAAO;AACL,UAAI,UAAU,IAAI;AAClB,UAAI,CAAC,QAAS,OAAM,IAAI,MAAM,kDAAkD,GAAG,mHAAmH;AACtM,YAAM,SAASA,KAAI;AAAA,IACrB;AAAA,EACF;AACA,QAAM,WAAW,MAAM,OAAO;AAC9B,SAAO,OAAO,OAAO,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,CAAC;AAC5C;AACA,SAAS,WAAW,OAAO,MAAM;AAC/B,MAAI,OAAO,EAAG,QAAO;AACrB,MAAI,MAAM,QAAQ;AAClB,MAAI,SAAS,MAAM,OAAO,GAAG;AAC7B,MAAI,UAAU,EAA8B,OAAM,IAAI,MAAM,gDAAgD;AAC5G,MAAI,SAAS,EAA6B,QAAO;AACjD,QAAM,OAAO,GAAG,IAAI;AACpB,MAAI,UAAU,MAAM,YAAY,OAAO,MAAM,OAAO,aAAa,GAAG,CAAC;AACrE,SAAO,MAAM,OAAO,GAAG,IAAI,IAA8B;AAC3D;AACA,SAAS,QAAQ,OAAO,MAAM;AAC5B,SAAO,OAAO,IAAI,MAAM,OAAO,aAAa,QAAQ,CAAC,IAAI,MAAM,OAAO,QAAQ,CAAC;AACjF;AACA,IAAM,eAA4B,MAAM,OAAO;AAC/C,IAAM,0BAAuC,MAAM,OAAO;AAAA,EACxD,SAAS,YAAU,OAAO,KAAK,OAAK,CAAC;AAAA,EACrC,QAAQ;AACV,CAAC;AACD,IAAM,gBAA6B,MAAM,OAAO;AAAA,EAC9C,SAAS,YAAU,OAAO,SAAS,OAAO,CAAC,IAAI;AAAA,EAC/C,QAAQ;AACV,CAAC;AACD,IAAM,eAA4B,MAAM,OAAO;AAC/C,IAAM,oBAAiC,MAAM,OAAO;AACpD,IAAM,sBAAmC,MAAM,OAAO;AACtD,IAAM,WAAwB,MAAM,OAAO;AAAA,EACzC,SAAS,YAAU,OAAO,SAAS,OAAO,CAAC,IAAI;AACjD,CAAC;AAWD,IAAM,aAAN,MAAiB;AAAA;AAAA;AAAA;AAAA,EAIf,YAIA,MAIA,OAAO;AACL,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS;AACd,WAAO,IAAI,eAAe;AAAA,EAC5B;AACF;AAIA,IAAM,iBAAN,MAAqB;AAAA;AAAA;AAAA;AAAA,EAInB,GAAG,OAAO;AACR,WAAO,IAAI,WAAW,MAAM,KAAK;AAAA,EACnC;AACF;AAKA,IAAM,kBAAN,MAAsB;AAAA;AAAA;AAAA;AAAA,EAIpB,YAQA,KAAK;AACH,SAAK,MAAM;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,GAAG,OAAO;AACR,WAAO,IAAI,YAAY,MAAM,KAAK;AAAA,EACpC;AACF;AAQA,IAAM,cAAN,MAAM,aAAY;AAAA;AAAA;AAAA;AAAA,EAIhB,YAIA,MAIA,OAAO;AACL,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAS;AACX,QAAI,SAAS,KAAK,KAAK,IAAI,KAAK,OAAO,OAAO;AAC9C,WAAO,WAAW,SAAY,SAAY,UAAU,KAAK,QAAQ,OAAO,IAAI,aAAY,KAAK,MAAM,MAAM;AAAA,EAC3G;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,GAAG,MAAM;AACP,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,OAAO,OAAO,CAAC,GAAG;AACvB,WAAO,IAAI,gBAAgB,KAAK,QAAQ,OAAK,EAAE;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,WAAW,SAAS,SAAS;AAClC,QAAI,CAAC,QAAQ,OAAQ,QAAO;AAC5B,QAAI,SAAS,CAAC;AACd,aAAS,UAAU,SAAS;AAC1B,UAAI,SAAS,OAAO,IAAI,OAAO;AAC/B,UAAI,OAAQ,QAAO,KAAK,MAAM;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AACF;AAQA,YAAY,cAA2B,YAAY,OAAO;AAI1D,YAAY,eAA4B,YAAY,OAAO;AAU3D,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,YAIA,YAIA,SAKA,WAIA,SAIA,aAKA,gBAAgB;AACd,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,iBAAiB;AAItB,SAAK,OAAO;AAIZ,SAAK,SAAS;AACd,QAAI,UAAW,gBAAe,WAAW,QAAQ,SAAS;AAC1D,QAAI,CAAC,YAAY,KAAK,OAAK,EAAE,QAAQ,aAAY,IAAI,EAAG,MAAK,cAAc,YAAY,OAAO,aAAY,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;AAAA,EAC/H;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,YAAY,SAAS,WAAW,SAAS,aAAa,gBAAgB;AAClF,WAAO,IAAI,aAAY,YAAY,SAAS,WAAW,SAAS,aAAa,cAAc;AAAA,EAC7F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,SAAS;AACX,WAAO,KAAK,SAAS,KAAK,OAAO,KAAK,QAAQ,MAAM,KAAK,WAAW,GAAG;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,eAAe;AACjB,WAAO,KAAK,aAAa,KAAK,WAAW,UAAU,IAAI,KAAK,OAAO;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,QAAQ;AACV,QAAI,CAAC,KAAK,OAAQ,MAAK,WAAW,iBAAiB,IAAI;AACvD,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,MAAM;AACf,aAAS,OAAO,KAAK,YAAa,KAAI,IAAI,QAAQ,KAAM,QAAO,IAAI;AACnE,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,aAAa;AACf,WAAO,CAAC,KAAK,QAAQ;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,eAAe;AACjB,WAAO,KAAK,WAAW,UAAU,KAAK,MAAM;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,OAAO;AACjB,QAAI,IAAI,KAAK,WAAW,aAAY,SAAS;AAC7C,WAAO,CAAC,EAAE,MAAM,KAAK,SAAS,EAAE,SAAS,MAAM,UAAU,EAAE,MAAM,GAAG,MAAM,MAAM,KAAK,SAAS,EAAE,MAAM,MAAM,KAAK;AAAA,EACnH;AACF;AAKA,YAAY,OAAoB,WAAW,OAAO;AA2BlD,YAAY,YAAyB,WAAW,OAAO;AAKvD,YAAY,eAA4B,WAAW,OAAO;AAO1D,YAAY,SAAsB,WAAW,OAAO;AACpD,SAAS,WAAW,GAAG,GAAG;AACxB,MAAI,SAAS,CAAC;AACd,WAAS,KAAK,GAAG,KAAK,OAAK;AACzB,QAAI,MAAM;AACV,QAAI,KAAK,EAAE,WAAW,MAAM,EAAE,UAAU,EAAE,EAAE,KAAK,EAAE,EAAE,IAAI;AACvD,aAAO,EAAE,IAAI;AACb,WAAK,EAAE,IAAI;AAAA,IACb,WAAW,KAAK,EAAE,QAAQ;AACxB,aAAO,EAAE,IAAI;AACb,WAAK,EAAE,IAAI;AAAA,IACb,MAAO,QAAO;AACd,QAAI,CAAC,OAAO,UAAU,OAAO,OAAO,SAAS,CAAC,IAAI,KAAM,QAAO,KAAK,MAAM,EAAE;AAAA,aAAW,OAAO,OAAO,SAAS,CAAC,IAAI,GAAI,QAAO,OAAO,SAAS,CAAC,IAAI;AAAA,EACrJ;AACF;AACA,SAAS,iBAAiB,GAAG,GAAG,YAAY;AAC1C,MAAI;AACJ,MAAI,SAAS,SAAS;AACtB,MAAI,YAAY;AACd,cAAU,EAAE;AACZ,cAAU,UAAU,MAAM,EAAE,QAAQ,MAAM;AAC1C,cAAU,EAAE,QAAQ,QAAQ,EAAE,OAAO;AAAA,EACvC,OAAO;AACL,cAAU,EAAE,QAAQ,IAAI,EAAE,OAAO;AACjC,cAAU,EAAE,QAAQ,QAAQ,EAAE,SAAS,IAAI;AAC3C,cAAU,EAAE,QAAQ,QAAQ,OAAO;AAAA,EACrC;AACA,SAAO;AAAA,IACL;AAAA,IACA,WAAW,EAAE,YAAY,EAAE,UAAU,IAAI,OAAO,KAAK,KAAK,EAAE,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,OAAO;AAAA,IAC1H,SAAS,YAAY,WAAW,EAAE,SAAS,OAAO,EAAE,OAAO,YAAY,WAAW,EAAE,SAAS,OAAO,CAAC;AAAA,IACrG,aAAa,EAAE,YAAY,SAAS,EAAE,YAAY,OAAO,EAAE,WAAW,IAAI,EAAE;AAAA,IAC5E,gBAAgB,EAAE,kBAAkB,EAAE;AAAA,EACxC;AACF;AACA,SAAS,wBAAwB,OAAO,MAAM,SAAS;AACrD,MAAI,MAAM,KAAK,WACb,cAAc,QAAQ,KAAK,WAAW;AACxC,MAAI,KAAK,UAAW,eAAc,YAAY,OAAO,YAAY,UAAU,GAAG,KAAK,SAAS,CAAC;AAC7F,SAAO;AAAA,IACL,SAAS,KAAK,mBAAmB,YAAY,KAAK,UAAU,UAAU,GAAG,KAAK,WAAW,CAAC,GAAG,SAAS,MAAM,MAAM,aAAa,CAAC;AAAA,IAChI,WAAW,QAAQ,eAAe,kBAAkB,MAAM,gBAAgB,OAAO,IAAI,QAAQ,IAAI,IAAI;AAAA,IACrG,SAAS,QAAQ,KAAK,OAAO;AAAA,IAC7B;AAAA,IACA,gBAAgB,CAAC,CAAC,KAAK;AAAA,EACzB;AACF;AACA,SAAS,mBAAmB,OAAO,OAAO,QAAQ;AAChD,MAAI,IAAI,wBAAwB,OAAO,MAAM,SAAS,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,MAAM;AACrF,MAAI,MAAM,UAAU,MAAM,CAAC,EAAE,WAAW,MAAO,UAAS;AACxD,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,MAAM,CAAC,EAAE,WAAW,MAAO,UAAS;AACxC,QAAI,MAAM,CAAC,CAAC,MAAM,CAAC,EAAE;AACrB,QAAI,iBAAiB,GAAG,wBAAwB,OAAO,MAAM,CAAC,GAAG,MAAM,EAAE,QAAQ,YAAY,MAAM,IAAI,MAAM,GAAG,GAAG;AAAA,EACrH;AACA,MAAI,KAAK,YAAY,OAAO,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc;AACrG,SAAO,kBAAkB,SAAS,kBAAkB,EAAE,IAAI,EAAE;AAC9D;AAEA,SAAS,kBAAkB,IAAI;AAC7B,MAAI,QAAQ,GAAG;AAEf,MAAI,SAAS;AACb,WAAS,UAAU,MAAM,MAAM,YAAY,GAAG;AAC5C,QAAI,QAAQ,OAAO,EAAE;AACrB,QAAI,UAAU,OAAO;AACnB,eAAS;AACT;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,KAAK,EAAG,UAAS,WAAW,OAAO,QAAQ,WAAW,QAAQ,KAAK;AAAA,EACvF;AACA,MAAI,WAAW,MAAM;AACnB,QAAI,SAAS;AACb,QAAI,WAAW,OAAO;AACpB,aAAO,GAAG,QAAQ;AAClB,gBAAU,UAAU,MAAM,MAAM,IAAI,MAAM;AAAA,IAC5C,OAAO;AACL,UAAI,WAAW,GAAG,QAAQ,OAAO,MAAM;AACvC,gBAAU,SAAS;AACnB,aAAO,SAAS,SAAS,QAAQ,SAAS,OAAO,EAAE;AAAA,IACrD;AACA,SAAK,YAAY,OAAO,OAAO,SAAS,GAAG,aAAa,GAAG,UAAU,IAAI,IAAI,GAAG,YAAY,WAAW,GAAG,SAAS,IAAI,GAAG,GAAG,aAAa,GAAG,cAAc;AAAA,EAC7J;AAEA,MAAI,UAAU,MAAM,MAAM,iBAAiB;AAC3C,WAAS,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AAC5C,QAAI,WAAW,QAAQ,CAAC,EAAE,EAAE;AAC5B,QAAI,oBAAoB,YAAa,MAAK;AAAA,aAAkB,MAAM,QAAQ,QAAQ,KAAK,SAAS,UAAU,KAAK,SAAS,CAAC,aAAa,YAAa,MAAK,SAAS,CAAC;AAAA,QAAO,MAAK,mBAAmB,OAAO,QAAQ,QAAQ,GAAG,KAAK;AAAA,EAClO;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,IAAI;AAC7B,MAAI,QAAQ,GAAG,YACb,YAAY,MAAM,MAAM,mBAAmB,GAC3C,OAAO;AACT,WAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9C,QAAI,YAAY,UAAU,CAAC,EAAE,EAAE;AAC/B,QAAI,aAAa,OAAO,KAAK,SAAS,EAAE,OAAQ,QAAO,iBAAiB,MAAM,wBAAwB,OAAO,WAAW,GAAG,QAAQ,SAAS,GAAG,IAAI;AAAA,EACrJ;AACA,SAAO,QAAQ,KAAK,KAAK,YAAY,OAAO,OAAO,GAAG,SAAS,GAAG,WAAW,KAAK,SAAS,KAAK,aAAa,KAAK,cAAc;AAClI;AACA,IAAM,OAAO,CAAC;AACd,SAAS,QAAQ,OAAO;AACtB,SAAO,SAAS,OAAO,OAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AACrE;AAOA,IAAI,eAA4B,SAAUC,eAAc;AAItD,EAAAA,cAAaA,cAAa,MAAM,IAAI,CAAC,IAAI;AAIzC,EAAAA,cAAaA,cAAa,OAAO,IAAI,CAAC,IAAI;AAI1C,EAAAA,cAAaA,cAAa,OAAO,IAAI,CAAC,IAAI;AAC1C,SAAOA;AACT,EAAE,iBAAiB,eAAe,CAAC,EAAE;AACrC,IAAM,6BAA6B;AACnC,IAAI;AACJ,IAAI;AACF,aAAwB,IAAI,OAAO,iCAAiC,GAAG;AACzE,SAAS,GAAG;AAAC;AACb,SAAS,YAAY,KAAK;AACxB,MAAI,SAAU,QAAO,SAAS,KAAK,GAAG;AACtC,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,QAAI,KAAK,IAAI,CAAC;AACd,QAAI,KAAK,KAAK,EAAE,KAAK,KAAK,QAAW,GAAG,YAAY,KAAK,GAAG,YAAY,KAAK,2BAA2B,KAAK,EAAE,GAAI,QAAO;AAAA,EAC5H;AACA,SAAO;AACT;AACA,SAAS,gBAAgB,WAAW;AAClC,SAAO,UAAQ;AACb,QAAI,CAAC,KAAK,KAAK,IAAI,EAAG,QAAO,aAAa;AAC1C,QAAI,YAAY,IAAI,EAAG,QAAO,aAAa;AAC3C,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAK,KAAI,KAAK,QAAQ,UAAU,CAAC,CAAC,IAAI,GAAI,QAAO,aAAa;AACpG,WAAO,aAAa;AAAA,EACtB;AACF;AAWA,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,YAIA,QAIA,KAIA,WAIA,QAAQ,aAAa,IAAI;AACvB,SAAK,SAAS;AACd,SAAK,MAAM;AACX,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,SAAS,OAAO,eAAe,MAAM;AAC1C,SAAK,cAAc;AAGnB,QAAI,GAAI,IAAG,SAAS;AACpB,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,aAAa,QAAQ,IAAK,YAAW,MAAM,KAAK,CAAC;AACjF,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,MAAM,OAAOC,WAAU,MAAM;AAC3B,QAAI,OAAO,KAAK,OAAO,QAAQ,MAAM,EAAE;AACvC,QAAI,QAAQ,MAAM;AAChB,UAAIA,SAAS,OAAM,IAAI,WAAW,oCAAoC;AACtE,aAAO;AAAA,IACT;AACA,eAAW,MAAM,IAAI;AACrB,WAAO,QAAQ,MAAM,IAAI;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,UAAU,OAAO;AACf,WAAO,mBAAmB,MAAM,OAAO,IAAI;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,IAAI;AACnB,QAAI,OAAO,KAAK,QACd;AAAA,MACE;AAAA,MACA;AAAA,IACF,IAAI;AACN,aAAS,UAAU,GAAG,SAAS;AAC7B,UAAI,OAAO,GAAG,YAAY,WAAW,GAAG;AACtC,YAAI,MAAM;AACR,yBAAe,oBAAI,IAAI;AACvB,eAAK,aAAa,QAAQ,CAAC,KAAK,QAAQ,aAAa,IAAI,KAAK,GAAG,CAAC;AAClE,iBAAO;AAAA,QACT;AACA,qBAAa,IAAI,OAAO,MAAM,aAAa,OAAO,MAAM,SAAS;AAAA,MACnE,WAAW,OAAO,GAAG,YAAY,WAAW,GAAG;AAC7C,eAAO;AACP,eAAO,OAAO;AAAA,MAChB,WAAW,OAAO,GAAG,YAAY,YAAY,GAAG;AAC9C,eAAO;AACP,eAAO,QAAQ,IAAI,EAAE,OAAO,OAAO,KAAK;AAAA,MAC1C;AAAA,IACF;AACA,QAAI;AACJ,QAAI,CAAC,MAAM;AACT,aAAO,cAAc,QAAQ,MAAM,cAAc,IAAI;AACrD,UAAI,oBAAoB,IAAI,aAAY,MAAM,KAAK,KAAK,KAAK,WAAW,KAAK,aAAa,IAAI,MAAM,IAAI,GAAG,CAAC,OAAO,SAAS,KAAK,YAAY,OAAO,IAAI,GAAG,IAAI;AAC/J,oBAAc,kBAAkB;AAAA,IAClC,OAAO;AACL,oBAAc,GAAG,WAAW,OAAO,MAAM;AAAA,IAC3C;AACA,QAAI,YAAY,GAAG,WAAW,MAAM,uBAAuB,IAAI,GAAG,eAAe,GAAG,aAAa,SAAS;AAC1G,QAAI,aAAY,MAAM,GAAG,QAAQ,WAAW,aAAa,CAAC,OAAO,SAAS,KAAK,OAAO,OAAO,EAAE,GAAG,EAAE;AAAA,EACtG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,MAAM;AACrB,QAAI,OAAO,QAAQ,SAAU,QAAO,KAAK,OAAO,IAAI;AACpD,WAAO,KAAK,cAAc,YAAU;AAAA,MAClC,SAAS;AAAA,QACP,MAAM,MAAM;AAAA,QACZ,IAAI,MAAM;AAAA,QACV,QAAQ;AAAA,MACV;AAAA,MACA,OAAO,gBAAgB,OAAO,MAAM,OAAO,KAAK,MAAM;AAAA,IACxD,EAAE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,cAAc,GAAG;AACf,QAAI,MAAM,KAAK;AACf,QAAI,UAAU,EAAE,IAAI,OAAO,CAAC,CAAC;AAC7B,QAAI,UAAU,KAAK,QAAQ,QAAQ,OAAO,GACxC,SAAS,CAAC,QAAQ,KAAK;AACzB,QAAI,UAAU,QAAQ,QAAQ,OAAO;AACrC,aAAS,IAAI,GAAG,IAAI,IAAI,OAAO,QAAQ,KAAK;AAC1C,UAAI,SAAS,EAAE,IAAI,OAAO,CAAC,CAAC;AAC5B,UAAI,aAAa,KAAK,QAAQ,OAAO,OAAO,GAC1C,YAAY,WAAW,IAAI,OAAO;AACpC,eAAS,IAAI,GAAG,IAAI,GAAG,IAAK,QAAO,CAAC,IAAI,OAAO,CAAC,EAAE,IAAI,SAAS;AAC/D,UAAI,QAAQ,QAAQ,QAAQ,YAAY,IAAI;AAC5C,aAAO,KAAK,OAAO,MAAM,IAAI,KAAK,CAAC;AACnC,gBAAU,QAAQ,QAAQ,SAAS;AACnC,gBAAU,YAAY,WAAW,SAAS,SAAS,EAAE,OAAO,YAAY,WAAW,QAAQ,OAAO,OAAO,GAAG,KAAK,CAAC;AAAA,IACpH;AACA,WAAO;AAAA,MACL;AAAA,MACA,WAAW,gBAAgB,OAAO,QAAQ,IAAI,SAAS;AAAA,MACvD;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,OAAO,CAAC,GAAG;AACjB,QAAI,gBAAgB,UAAW,QAAO;AACtC,WAAO,UAAU,GAAG,MAAM,KAAK,IAAI,QAAQ,KAAK,MAAM,aAAY,aAAa,CAAC;AAAA,EAClF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,QAAQ;AACb,WAAO,KAAK,GAAG,OAAO,MAAM,KAAK,MAAM,aAAY,aAAa,KAAK,YAAY,CAAC;AAAA,EACpF;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,OAAO,GAAG,KAAK,KAAK,IAAI,QAAQ;AACvC,WAAO,KAAK,IAAI,YAAY,MAAM,IAAI,KAAK,SAAS;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,OAAO;AACX,QAAI,OAAO,KAAK,OAAO,QAAQ,MAAM,EAAE;AACvC,QAAI,QAAQ,KAAM,QAAO,MAAM;AAC/B,eAAW,MAAM,IAAI;AACrB,WAAO,QAAQ,MAAM,IAAI;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,QAAQ;AACb,QAAI,SAAS;AAAA,MACX,KAAK,KAAK,SAAS;AAAA,MACnB,WAAW,KAAK,UAAU,OAAO;AAAA,IACnC;AACA,QAAI,OAAQ,UAAS,QAAQ,QAAQ;AACnC,UAAI,QAAQ,OAAO,IAAI;AACvB,UAAI,iBAAiB,cAAc,KAAK,OAAO,QAAQ,MAAM,EAAE,KAAK,KAAM,QAAO,IAAI,IAAI,MAAM,KAAK,OAAO,KAAK,MAAM,OAAO,IAAI,CAAC,GAAG,IAAI;AAAA,IAC3I;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,SAAS,MAAM,SAAS,CAAC,GAAG,QAAQ;AACzC,QAAI,CAAC,QAAQ,OAAO,KAAK,OAAO,SAAU,OAAM,IAAI,WAAW,6CAA6C;AAC5G,QAAI,YAAY,CAAC;AACjB,QAAI,OAAQ,UAAS,QAAQ,QAAQ;AACnC,UAAI,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,GAAG;AACpD,YAAI,QAAQ,OAAO,IAAI,GACrB,QAAQ,KAAK,IAAI;AACnB,kBAAU,KAAK,MAAM,KAAK,WAAS,MAAM,KAAK,SAAS,OAAO,KAAK,CAAC,CAAC;AAAA,MACvE;AAAA,IACF;AACA,WAAO,aAAY,OAAO;AAAA,MACxB,KAAK,KAAK;AAAA,MACV,WAAW,gBAAgB,SAAS,KAAK,SAAS;AAAA,MAClD,YAAY,OAAO,aAAa,UAAU,OAAO,CAAC,OAAO,UAAU,CAAC,IAAI;AAAA,IAC1E,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,OAAO,SAAS,CAAC,GAAG;AACzB,QAAI,gBAAgB,cAAc,QAAQ,OAAO,cAAc,CAAC,GAAG,oBAAI,IAAI,CAAC;AAC5E,QAAI,MAAM,OAAO,eAAe,OAAO,OAAO,MAAM,KAAK,IAAI,OAAO,OAAO,IAAI,MAAM,cAAc,YAAY,aAAY,aAAa,KAAK,YAAY,CAAC;AAC1J,QAAI,YAAY,CAAC,OAAO,YAAY,gBAAgB,OAAO,CAAC,IAAI,OAAO,qBAAqB,kBAAkB,OAAO,YAAY,gBAAgB,OAAO,OAAO,UAAU,QAAQ,OAAO,UAAU,IAAI;AACtM,mBAAe,WAAW,IAAI,MAAM;AACpC,QAAI,CAAC,cAAc,YAAY,uBAAuB,EAAG,aAAY,UAAU,SAAS;AACxF,WAAO,IAAI,aAAY,eAAe,KAAK,WAAW,cAAc,aAAa,IAAI,MAAM,IAAI,GAAG,CAAC,OAAO,SAAS,KAAK,OAAO,KAAK,GAAG,IAAI;AAAA,EAC7I;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AACZ,WAAO,KAAK,MAAM,aAAY,OAAO;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AACd,WAAO,KAAK,MAAM,aAAY,aAAa,KAAK;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK,MAAM,QAAQ;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,WAAWL,SAAQ;AACxB,aAAS,OAAO,KAAK,MAAM,aAAY,OAAO,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,KAAK,MAAM,GAAG;AACtG,eAAS,IAAI,MAAM;AACnB;AAAA,IACF;AACA,QAAIA,QAAO,OAAQ,UAAS,OAAO,QAAQ,eAAe,CAAC,GAAG,MAAM;AAClE,UAAI,KAAK,IAAK,QAAO;AACrB,UAAI,IAAI,EAAE,KAAK;AACf,aAAO,CAAC,KAAK,IAAIA,QAAO,SAAS,IAAIA,QAAO,IAAI,CAAC;AAAA,IACnD,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,eAAe,MAAM,KAAK,OAAO,IAAI;AACnC,QAAI,SAAS,CAAC;AACd,aAAS,YAAY,KAAK,MAAM,YAAY,GAAG;AAC7C,eAAS,UAAU,SAAS,MAAM,KAAK,IAAI,GAAG;AAC5C,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,IAAI,EAAG,QAAO,KAAK,OAAO,IAAI,CAAC;AAAA,MAClF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,gBAAgB,IAAI;AAClB,WAAO,gBAAgB,KAAK,eAAe,aAAa,EAAE,EAAE,KAAK,EAAE,CAAC;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,KAAK;AACV,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK,IAAI,OAAO,GAAG;AACvB,QAAI,MAAM,KAAK,gBAAgB,GAAG;AAClC,QAAI,QAAQ,MAAM,MAChB,MAAM,MAAM;AACd,WAAO,QAAQ,GAAG;AAChB,UAAI,OAAON,kBAAiB,MAAM,OAAO,KAAK;AAC9C,UAAI,IAAI,KAAK,MAAM,MAAM,KAAK,CAAC,KAAK,aAAa,KAAM;AACvD,cAAQ;AAAA,IACV;AACA,WAAO,MAAM,QAAQ;AACnB,UAAI,OAAOA,kBAAiB,MAAM,GAAG;AACrC,UAAI,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC,KAAK,aAAa,KAAM;AACrD,YAAM;AAAA,IACR;AACA,WAAO,SAAS,MAAM,OAAO,gBAAgB,MAAM,QAAQ,MAAM,MAAM,IAAI;AAAA,EAC7E;AACF;AASA,YAAY,0BAA0B;AAMtC,YAAY,UAAuB,MAAM,OAAO;AAAA,EAC9C,SAAS,YAAU,OAAO,SAAS,OAAO,CAAC,IAAI;AACjD,CAAC;AAUD,YAAY,gBAAgB;AAc5B,YAAY,WAAW;AAOvB,YAAY,UAAuB,MAAM,OAAO;AAAA,EAC9C,QAAQ,GAAG,GAAG;AACZ,QAAI,KAAK,OAAO,KAAK,CAAC,GACpB,KAAK,OAAO,KAAK,CAAC;AACpB,WAAO,GAAG,UAAU,GAAG,UAAU,GAAG,MAAM,OAAK,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;AAAA,EAC7D;AACF,CAAC;AAKD,YAAY,eAAe;AAe3B,YAAY,eAAe;AAoB3B,YAAY,oBAAoB;AAchC,YAAY,sBAAsB;AAClC,YAAY,cAA2B,YAAY,OAAO;AAW1D,SAAS,cAAc,SAAS,UAEhC,UAAU,CAAC,GAAG;AACZ,MAAI,SAAS,CAAC;AACd,WAAS,UAAU,QAAS,UAAS,OAAO,OAAO,KAAK,MAAM,GAAG;AAC/D,QAAI,QAAQ,OAAO,GAAG,GACpB,UAAU,OAAO,GAAG;AACtB,QAAI,YAAY,OAAW,QAAO,GAAG,IAAI;AAAA,aAAe,YAAY,SAAS,UAAU,OAAW;AAAA,aACzF,OAAO,eAAe,KAAK,SAAS,GAAG,EAAG,QAAO,GAAG,IAAI,QAAQ,GAAG,EAAE,SAAS,KAAK;AAAA,QAAO,OAAM,IAAI,MAAM,qCAAqC,GAAG;AAAA,EAC7J;AACA,WAAS,OAAO,SAAU,KAAI,OAAO,GAAG,MAAM,OAAW,QAAO,GAAG,IAAI,SAAS,GAAG;AACnF,SAAO;AACT;AAMA,IAAM,aAAN,MAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQf,GAAG,OAAO;AACR,WAAO,QAAQ;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,MAAM,KAAK,MAAM;AACrB,WAAO,MAAM,OAAO,MAAM,IAAI,IAAI;AAAA,EACpC;AACF;AACA,WAAW,UAAU,YAAY,WAAW,UAAU,UAAU;AAChE,WAAW,UAAU,QAAQ;AAC7B,WAAW,UAAU,UAAU,QAAQ;AAIvC,IAAM,QAAN,MAAM,OAAM;AAAA,EACV,YAIA,MAIA,IAIA,OAAO;AACL,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,MAAM,IAAI,OAAO;AAC7B,WAAO,IAAI,OAAM,MAAM,IAAI,KAAK;AAAA,EAClC;AACF;AACA,SAAS,SAAS,GAAG,GAAG;AACtB,SAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,EAAE,MAAM;AACxD;AACA,IAAM,QAAN,MAAM,OAAM;AAAA,EACV,YAAY,MAAM,IAAI,OAKtB,UAAU;AACR,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,QAAQ;AACb,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,GAAG,KAAK,GAAG,SAAS,CAAC;AAAA,EACnC;AAAA;AAAA;AAAA,EAGA,UAAU,KAAK,MAAM,KAAK,UAAU,GAAG;AACrC,QAAI,MAAM,MAAM,KAAK,KAAK,KAAK;AAC/B,aAAS,KAAK,SAAS,KAAK,IAAI,YAAU;AACxC,UAAI,MAAM,GAAI,QAAO;AACrB,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,OAAO,IAAI,GAAG,IAAI,QAAQ,MAAM,KAAK,MAAM,GAAG,EAAE,UAAU,KAAK,MAAM,GAAG,EAAE,aAAa;AAC3F,UAAI,OAAO,GAAI,QAAO,QAAQ,IAAI,KAAK;AACvC,UAAI,QAAQ,EAAG,MAAK;AAAA,UAAS,MAAK,MAAM;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,QAAQ,QAAQ,MAAM,IAAI,GAAG;AAC3B,aAAS,IAAI,KAAK,UAAU,MAAM,MAAyB,IAAI,GAAG,IAAI,KAAK,UAAU,IAAI,KAAwB,OAAO,CAAC,GAAG,IAAI,GAAG,IAAK,KAAI,EAAE,KAAK,KAAK,CAAC,IAAI,QAAQ,KAAK,GAAG,CAAC,IAAI,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM,MAAO,QAAO;AAAA,EAC7N;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,QAAI,QAAQ,CAAC,GACX,OAAO,CAAC,GACR,KAAK,CAAC,GACN,SAAS,IACT,WAAW;AACb,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,UAAI,MAAM,KAAK,MAAM,CAAC,GACpB,UAAU,KAAK,KAAK,CAAC,IAAI,QACzB,QAAQ,KAAK,GAAG,CAAC,IAAI,QACrB,SACA;AACF,UAAI,WAAW,OAAO;AACpB,YAAI,SAAS,QAAQ,OAAO,SAAS,IAAI,WAAW,IAAI,OAAO;AAC/D,YAAI,UAAU,KAAM;AACpB,kBAAU,QAAQ;AAClB,YAAI,IAAI,aAAa,IAAI,SAAS;AAChC,kBAAQ,QAAQ,OAAO,SAAS,IAAI,OAAO;AAC3C,cAAI,QAAQ,QAAS;AAAA,QACvB;AAAA,MACF,OAAO;AACL,kBAAU,QAAQ,OAAO,SAAS,IAAI,SAAS;AAC/C,gBAAQ,QAAQ,OAAO,OAAO,IAAI,OAAO;AACzC,YAAI,UAAU,SAAS,WAAW,SAAS,IAAI,YAAY,KAAK,IAAI,WAAW,EAAG;AAAA,MACpF;AACA,WAAK,QAAQ,WAAW,IAAI,UAAU,IAAI,aAAa,EAAG;AAC1D,UAAI,SAAS,EAAG,UAAS;AACzB,UAAI,IAAI,MAAO,YAAW,KAAK,IAAI,UAAU,QAAQ,OAAO;AAC5D,YAAM,KAAK,GAAG;AACd,WAAK,KAAK,UAAU,MAAM;AAC1B,SAAG,KAAK,QAAQ,MAAM;AAAA,IACxB;AACA,WAAO;AAAA,MACL,QAAQ,MAAM,SAAS,IAAI,OAAM,MAAM,IAAI,OAAO,QAAQ,IAAI;AAAA,MAC9D,KAAK;AAAA,IACP;AAAA,EACF;AACF;AAOA,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,YAIA,UAIA,OAIA,WAIA,UAAU;AACR,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,UAAU,OAAO,WAAW,UAAU;AAClD,WAAO,IAAI,UAAS,UAAU,OAAO,WAAW,QAAQ;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS;AACX,QAAI,OAAO,KAAK,MAAM,SAAS;AAC/B,WAAO,OAAO,IAAI,IAAI,KAAK,IAAI,KAAK,SAAS,IAAI,GAAG,KAAK,UAAU,MAAM;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACT,QAAI,KAAK,QAAS,QAAO;AACzB,QAAI,OAAO,KAAK,UAAU;AAC1B,aAAS,SAAS,KAAK,MAAO,SAAQ,MAAM,MAAM;AAClD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,OAAO;AACd,WAAO,KAAK,SAAS,KAAK,IAAI,KAAK,MAAM,KAAK,EAAE;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,YAAY;AACjB,QAAI;AAAA,MACF,MAAM,CAAC;AAAA,MACP,OAAO;AAAA,MACP,aAAa;AAAA,MACb,WAAW,KAAK;AAAA,IAClB,IAAI;AACJ,QAAI,SAAS,WAAW;AACxB,QAAI,IAAI,UAAU,KAAK,CAAC,OAAQ,QAAO;AACvC,QAAI,KAAM,OAAM,IAAI,MAAM,EAAE,KAAK,QAAQ;AACzC,QAAI,KAAK,QAAS,QAAO,IAAI,SAAS,UAAS,GAAG,GAAG,IAAI;AACzD,QAAI,MAAM,IAAI,YAAY,MAAM,MAAM,EAAE,EAAE,KAAK,CAAC,GAC9C,IAAI,GACJ,QAAQ,CAAC;AACX,QAAI,UAAU,IAAI,gBAAgB;AAClC,WAAO,IAAI,SAAS,IAAI,IAAI,QAAQ;AAClC,UAAI,IAAI,IAAI,WAAW,IAAI,OAAO,IAAI,CAAC,EAAE,QAAQ,IAAI,YAAY,IAAI,CAAC,EAAE,MAAM,cAAc,GAAG;AAC7F,YAAI,QAAQ,IAAI,GAAG;AACnB,YAAI,CAAC,QAAQ,SAAS,MAAM,MAAM,MAAM,IAAI,MAAM,KAAK,EAAG,OAAM,KAAK,KAAK;AAAA,MAC5E,WAAW,IAAI,cAAc,KAAK,IAAI,aAAa,KAAK,MAAM,WAAW,KAAK,IAAI,UAAU,KAAK,SAAS,IAAI,UAAU,IAAI,IAAI,CAAC,EAAE,UAAU,CAAC,UAAU,aAAa,KAAK,SAAS,IAAI,UAAU,KAAK,WAAW,KAAK,SAAS,IAAI,UAAU,MAAM,QAAQ,SAAS,KAAK,SAAS,IAAI,UAAU,GAAG,KAAK,MAAM,IAAI,UAAU,CAAC,GAAG;AAC9T,YAAI,UAAU;AAAA,MAChB,OAAO;AACL,YAAI,CAAC,UAAU,aAAa,IAAI,MAAM,WAAW,IAAI,QAAQ,OAAO,IAAI,MAAM,IAAI,IAAI,IAAI,KAAK,GAAG;AAChG,cAAI,CAAC,QAAQ,SAAS,IAAI,MAAM,IAAI,IAAI,IAAI,KAAK,EAAG,OAAM,KAAK,MAAM,OAAO,IAAI,MAAM,IAAI,IAAI,IAAI,KAAK,CAAC;AAAA,QAC1G;AACA,YAAI,KAAK;AAAA,MACX;AAAA,IACF;AACA,WAAO,QAAQ,YAAY,KAAK,UAAU,WAAW,CAAC,MAAM,SAAS,UAAS,QAAQ,KAAK,UAAU,OAAO;AAAA,MAC1G,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS;AACX,QAAI,QAAQ,SAAS,KAAK,QAAS,QAAO;AAC1C,QAAI,SAAS,CAAC,GACZ,WAAW,CAAC,GACZ,WAAW;AACb,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,UAAI,QAAQ,KAAK,SAAS,CAAC,GACzB,QAAQ,KAAK,MAAM,CAAC;AACtB,UAAI,QAAQ,QAAQ,aAAa,OAAO,QAAQ,MAAM,MAAM;AAC5D,UAAI,UAAU,OAAO;AACnB,mBAAW,KAAK,IAAI,UAAU,MAAM,QAAQ;AAC5C,eAAO,KAAK,KAAK;AACjB,iBAAS,KAAK,QAAQ,OAAO,KAAK,CAAC;AAAA,MACrC,WAAW,UAAU,MAAM;AACzB,YAAI;AAAA,UACF;AAAA,UACA;AAAA,QACF,IAAI,MAAM,IAAI,OAAO,OAAO;AAC5B,YAAI,QAAQ;AACV,qBAAW,KAAK,IAAI,UAAU,OAAO,QAAQ;AAC7C,iBAAO,KAAK,MAAM;AAClB,mBAAS,KAAK,GAAG;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,KAAK,UAAU,IAAI,OAAO;AACrC,WAAO,OAAO,UAAU,IAAI,OAAO,IAAI,UAAS,UAAU,QAAQ,QAAQ,UAAS,OAAO,QAAQ;AAAA,EACpG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,MAAM,IAAI,GAAG;AACnB,QAAI,KAAK,QAAS;AAClB,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,UAAI,QAAQ,KAAK,SAAS,CAAC,GACzB,QAAQ,KAAK,MAAM,CAAC;AACtB,UAAI,MAAM,SAAS,QAAQ,QAAQ,MAAM,UAAU,MAAM,QAAQ,OAAO,OAAO,OAAO,KAAK,OAAO,CAAC,MAAM,MAAO;AAAA,IAClH;AACA,SAAK,UAAU,QAAQ,MAAM,IAAI,CAAC;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,OAAO,GAAG;AACb,WAAO,WAAW,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,UAAU;AACZ,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,KAAK,MAAM,OAAO,GAAG;AAC1B,WAAO,WAAW,KAAK,IAAI,EAAE,KAAK,IAAI;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,QAAQ,SAAS,SAKxB,UAAU,YAKV,eAAe,IAAI;AACjB,QAAI,IAAI,QAAQ,OAAO,SAAO,IAAI,WAAW,KAAK,CAAC,IAAI,WAAW,IAAI,YAAY,YAAY;AAC9F,QAAI,IAAI,QAAQ,OAAO,SAAO,IAAI,WAAW,KAAK,CAAC,IAAI,WAAW,IAAI,YAAY,YAAY;AAC9F,QAAI,eAAe,iBAAiB,GAAG,GAAG,QAAQ;AAClD,QAAI,QAAQ,IAAI,WAAW,GAAG,cAAc,YAAY;AACxD,QAAI,QAAQ,IAAI,WAAW,GAAG,cAAc,YAAY;AACxD,aAAS,SAAS,CAAC,OAAO,OAAO,WAAW,QAAQ,OAAO,OAAO,OAAO,OAAO,QAAQ,UAAU,CAAC;AACnG,QAAI,SAAS,SAAS,SAAS,UAAU,EAAG,SAAQ,OAAO,GAAG,OAAO,GAAG,GAAG,UAAU;AAAA,EACvF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,GAAG,SAAS,SAAS,OAAO,GAAG,IAAI;AACxC,QAAI,MAAM,KAAM,MAAK,MAAyB;AAC9C,QAAI,IAAI,QAAQ,OAAO,SAAO,CAAC,IAAI,WAAW,QAAQ,QAAQ,GAAG,IAAI,CAAC;AACtE,QAAI,IAAI,QAAQ,OAAO,SAAO,CAAC,IAAI,WAAW,QAAQ,QAAQ,GAAG,IAAI,CAAC;AACtE,QAAI,EAAE,UAAU,EAAE,OAAQ,QAAO;AACjC,QAAI,CAAC,EAAE,OAAQ,QAAO;AACtB,QAAI,eAAe,iBAAiB,GAAG,CAAC;AACxC,QAAI,QAAQ,IAAI,WAAW,GAAG,cAAc,CAAC,EAAE,KAAK,IAAI,GACtD,QAAQ,IAAI,WAAW,GAAG,cAAc,CAAC,EAAE,KAAK,IAAI;AACtD,eAAS;AACP,UAAI,MAAM,MAAM,MAAM,MAAM,CAAC,WAAW,MAAM,QAAQ,MAAM,MAAM,KAAK,MAAM,UAAU,CAAC,MAAM,SAAS,CAAC,MAAM,MAAM,GAAG,MAAM,KAAK,GAAI,QAAO;AAC7I,UAAI,MAAM,KAAK,GAAI,QAAO;AAC1B,YAAM,KAAK;AACX,YAAM,KAAK;AAAA,IACb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,MAAM,MAAM,MAAM,IAAI,UAK7B,eAAe,IAAI;AACjB,QAAI,SAAS,IAAI,WAAW,MAAM,MAAM,YAAY,EAAE,KAAK,IAAI,GAC7D,MAAM;AACR,QAAI,aAAa,OAAO;AACxB,eAAS;AACP,UAAI,QAAQ,KAAK,IAAI,OAAO,IAAI,EAAE;AAClC,UAAI,OAAO,OAAO;AAChB,YAAI,SAAS,OAAO,eAAe,OAAO,EAAE;AAC5C,YAAI,YAAY,OAAO,YAAY,OAAO,OAAO,SAAS,IAAI,OAAO,MAAM,YAAY,IAAI,OAAO,SAAS,KAAK,IAAI,OAAO,QAAQ,UAAU;AAC7I,iBAAS,MAAM,KAAK,OAAO,OAAO,OAAO,QAAQ,WAAW,OAAO,SAAS;AAC5E,qBAAa,KAAK,IAAI,OAAO,QAAQ,KAAK,GAAG,OAAO,MAAM;AAAA,MAC5D,WAAW,QAAQ,KAAK;AACtB,iBAAS,KAAK,KAAK,OAAO,OAAO,QAAQ,UAAU;AACnD,qBAAa,OAAO,QAAQ,KAAK;AAAA,MACnC;AACA,UAAI,OAAO,KAAK,GAAI,QAAO,cAAc,OAAO,SAAS,OAAO,KAAK,KAAK,IAAI;AAC9E,YAAM,OAAO;AACb,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,GAAG,QAAQ,OAAO,OAAO;AAC9B,QAAI,QAAQ,IAAI,gBAAgB;AAChC,aAAS,SAAS,kBAAkB,QAAQ,CAAC,MAAM,IAAI,OAAO,SAAS,MAAM,IAAI,OAAQ,OAAM,IAAI,MAAM,MAAM,MAAM,IAAI,MAAM,KAAK;AACpI,WAAO,MAAM,OAAO;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,KAAK,MAAM;AAChB,QAAI,CAAC,KAAK,OAAQ,QAAO,UAAS;AAClC,QAAI,SAAS,KAAK,KAAK,SAAS,CAAC;AACjC,aAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,eAAS,QAAQ,KAAK,CAAC,GAAG,SAAS,UAAS,OAAO,QAAQ,MAAM,UAAW,UAAS,IAAI,UAAS,MAAM,UAAU,MAAM,OAAO,QAAQ,KAAK,IAAI,MAAM,UAAU,OAAO,QAAQ,CAAC;AAAA,IAClL;AACA,WAAO;AAAA,EACT;AACF;AAIA,SAAS,QAAqB,IAAI,SAAS,CAAC,GAAG,CAAC,GAAG,MAAM,EAAE;AAC3D,SAAS,SAAS,QAAQ;AACxB,MAAI,OAAO,SAAS,EAAG,UAAS,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAC/E,QAAI,MAAM,OAAO,CAAC;AAClB,QAAI,SAAS,MAAM,GAAG,IAAI,EAAG,QAAO,OAAO,MAAM,EAAE,KAAK,QAAQ;AAChE,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,MAAM,YAAY,SAAS;AAMpC,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,WAAW;AACrB,SAAK,OAAO,KAAK,IAAI,MAAM,KAAK,MAAM,KAAK,IAAI,KAAK,OAAO,KAAK,QAAQ,CAAC;AACzE,SAAK,SAAS,KAAK,KAAK,UAAU;AAClC,SAAK,aAAa;AAClB,SAAK,cAAc,KAAK,IAAI,KAAK,aAAa,KAAK,QAAQ;AAC3D,SAAK,WAAW;AAChB,QAAI,WAAW;AACb,WAAK,OAAO,CAAC;AACb,WAAK,KAAK,CAAC;AACX,WAAK,QAAQ,CAAC;AAAA,IAChB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,SAAK,SAAS,CAAC;AACf,SAAK,WAAW,CAAC;AACjB,SAAK,aAAa;AAClB,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,OAAO,CAAC;AACb,SAAK,KAAK,CAAC;AACX,SAAK,QAAQ,CAAC;AACd,SAAK,WAAW;AAChB,SAAK,cAAc;AACnB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,MAAM,IAAI,OAAO;AACnB,QAAI,CAAC,KAAK,SAAS,MAAM,IAAI,KAAK,EAAG,EAAC,KAAK,cAAc,KAAK,YAAY,IAAI,iBAAgB,IAAI,IAAI,MAAM,IAAI,KAAK;AAAA,EACvH;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,MAAM,IAAI,OAAO;AACxB,QAAI,OAAO,OAAO,KAAK,UAAU,MAAM,YAAY,KAAK,KAAK;AAC7D,QAAI,QAAQ,MAAM,OAAO,KAAK,YAAY,MAAM,YAAY,KAAK,KAAK,aAAa,EAAG,OAAM,IAAI,MAAM,gEAAgE;AACtK,QAAI,OAAO,EAAG,QAAO;AACrB,QAAI,KAAK,KAAK,UAAU,IAAuB,MAAK,YAAY,IAAI;AACpE,QAAI,KAAK,aAAa,EAAG,MAAK,aAAa;AAC3C,SAAK,KAAK,KAAK,OAAO,KAAK,UAAU;AACrC,SAAK,GAAG,KAAK,KAAK,KAAK,UAAU;AACjC,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,MAAM,KAAK,KAAK;AACrB,QAAI,MAAM,MAAO,MAAK,WAAW,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI;AAClE,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,MAAM,OAAO;AACpB,SAAK,OAAO,KAAK,UAAU,MAAM,MAAM,CAAC,EAAE,YAAY,KAAK,KAAK,WAAW,EAAG,QAAO;AACrF,QAAI,KAAK,KAAK,OAAQ,MAAK,YAAY,IAAI;AAC3C,SAAK,cAAc,KAAK,IAAI,KAAK,aAAa,MAAM,QAAQ;AAC5D,SAAK,OAAO,KAAK,KAAK;AACtB,SAAK,SAAS,KAAK,IAAI;AACvB,QAAI,OAAO,MAAM,MAAM,SAAS;AAChC,SAAK,OAAO,MAAM,MAAM,IAAI;AAC5B,SAAK,WAAW,MAAM,KAAK,IAAI,IAAI;AACnC,SAAK,SAAS,MAAM,GAAG,IAAI,IAAI;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,WAAO,KAAK,YAAY,SAAS,KAAK;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,MAAM;AAChB,QAAI,KAAK,KAAK,OAAQ,MAAK,YAAY,KAAK;AAC5C,QAAI,KAAK,OAAO,UAAU,EAAG,QAAO;AACpC,QAAI,SAAS,SAAS,OAAO,KAAK,UAAU,KAAK,QAAQ,KAAK,YAAY,KAAK,UAAU,YAAY,IAAI,IAAI,MAAM,KAAK,WAAW;AACnI,SAAK,OAAO;AACZ,WAAO;AAAA,EACT;AACF;AACA,SAAS,iBAAiB,GAAG,GAAG,UAAU;AACxC,MAAI,MAAM,oBAAI,IAAI;AAClB,WAAS,OAAO,EAAG,UAAS,IAAI,GAAG,IAAI,IAAI,MAAM,QAAQ,IAAK,KAAI,IAAI,MAAM,CAAC,EAAE,YAAY,EAAG,KAAI,IAAI,IAAI,MAAM,CAAC,GAAG,IAAI,SAAS,CAAC,CAAC;AACnI,MAAI,SAAS,oBAAI,IAAI;AACrB,WAAS,OAAO,EAAG,UAAS,IAAI,GAAG,IAAI,IAAI,MAAM,QAAQ,KAAK;AAC5D,QAAI,QAAQ,IAAI,IAAI,IAAI,MAAM,CAAC,CAAC;AAChC,QAAI,SAAS,SAAS,WAAW,SAAS,OAAO,KAAK,IAAI,UAAU,IAAI,SAAS,CAAC,KAAK,EAAE,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,aAAa,OAAO,QAAQ,IAAI,MAAM,CAAC,EAAE,MAAM,GAAI,QAAO,IAAI,IAAI,MAAM,CAAC,CAAC;AAAA,EAClO;AACA,SAAO;AACT;AACA,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,OAAO,MAAM,UAAU,OAAO,GAAG;AAC3C,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,QAAQ,KAAK,MAAM,YAAY;AAAA,EAC7C;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK,QAAQ,KAAK,MAAM,UAAU;AAAA,EAC3C;AAAA,EACA,KAAK,KAAK,OAAO,MAAyB;AACxC,SAAK,aAAa,KAAK,aAAa;AACpC,SAAK,UAAU,KAAK,MAAM,KAAK;AAC/B,WAAO;AAAA,EACT;AAAA,EACA,UAAU,KAAK,MAAM,SAAS;AAC5B,WAAO,KAAK,aAAa,KAAK,MAAM,MAAM,QAAQ;AAChD,UAAI,OAAO,KAAK,MAAM,MAAM,KAAK,UAAU;AAC3C,UAAI,EAAE,KAAK,QAAQ,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,MAAM,SAAS,KAAK,UAAU,IAAI,OAAO,KAAK,WAAW,KAAK,UAAW;AACxH,WAAK;AACL,gBAAU;AAAA,IACZ;AACA,QAAI,KAAK,aAAa,KAAK,MAAM,MAAM,QAAQ;AAC7C,UAAI,aAAa,KAAK,MAAM,MAAM,KAAK,UAAU,EAAE,UAAU,MAAM,KAAK,MAAM,SAAS,KAAK,UAAU,GAAG,MAAM,IAAI;AACnH,UAAI,CAAC,WAAW,KAAK,aAAa,WAAY,MAAK,cAAc,UAAU;AAAA,IAC7E;AACA,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,QAAQ,KAAK,MAAM;AACjB,SAAK,KAAK,KAAK,OAAO,KAAK,UAAU,QAAQ,EAAG,MAAK,UAAU,KAAK,MAAM,IAAI;AAAA,EAChF;AAAA,EACA,OAAO;AACL,eAAS;AACP,UAAI,KAAK,cAAc,KAAK,MAAM,MAAM,QAAQ;AAC9C,aAAK,OAAO,KAAK,KAAK;AACtB,aAAK,QAAQ;AACb;AAAA,MACF,OAAO;AACL,YAAI,WAAW,KAAK,MAAM,SAAS,KAAK,UAAU,GAChD,QAAQ,KAAK,MAAM,MAAM,KAAK,UAAU;AAC1C,YAAI,OAAO,WAAW,MAAM,KAAK,KAAK,UAAU;AAChD,aAAK,OAAO;AACZ,aAAK,KAAK,WAAW,MAAM,GAAG,KAAK,UAAU;AAC7C,aAAK,QAAQ,MAAM,MAAM,KAAK,UAAU;AACxC,aAAK,cAAc,KAAK,aAAa,CAAC;AACtC,YAAI,KAAK,WAAW,KAAK,KAAK,MAAM,SAAS,KAAK,KAAK,KAAK,QAAQ,KAAK,SAAU;AAAA,MACrF;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,SAAS,KAAK,MAAM,MAAM,KAAK,UAAU,EAAE,MAAM,QAAQ;AAC3D,WAAK;AACL,UAAI,KAAK,MAAM;AACb,eAAO,KAAK,aAAa,KAAK,MAAM,MAAM,UAAU,KAAK,KAAK,IAAI,KAAK,MAAM,MAAM,KAAK,UAAU,CAAC,EAAG,MAAK;AAAA,MAC7G;AACA,WAAK,aAAa;AAAA,IACpB,OAAO;AACL,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,YAAY;AACV,SAAK;AACL,SAAK,aAAa;AAClB,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,QAAQ,OAAO;AACb,WAAO,KAAK,OAAO,MAAM,QAAQ,KAAK,YAAY,MAAM,aAAa,KAAK,OAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,MAAM,KAAK,UAAU,MAAM;AAAA,EAC5I;AACF;AACA,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,YAAY,MAAM;AAChB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,OAAO,KAAK,MAAM,OAAO,MAAM,WAAW,IAAI;AAC5C,QAAI,OAAO,CAAC;AACZ,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,eAAS,MAAM,KAAK,CAAC,GAAG,CAAC,IAAI,SAAS,MAAM,IAAI,WAAW;AACzD,YAAI,IAAI,YAAY,SAAU,MAAK,KAAK,IAAI,YAAY,KAAK,MAAM,UAAU,CAAC,CAAC;AAAA,MACjF;AAAA,IACF;AACA,WAAO,KAAK,UAAU,IAAI,KAAK,CAAC,IAAI,IAAI,YAAW,IAAI;AAAA,EACzD;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,QAAQ,KAAK,MAAM,YAAY;AAAA,EAC7C;AAAA,EACA,KAAK,KAAK,OAAO,MAAyB;AACxC,aAAS,OAAO,KAAK,KAAM,KAAI,KAAK,KAAK,IAAI;AAC7C,aAAS,IAAI,KAAK,KAAK,UAAU,GAAG,KAAK,GAAG,IAAK,YAAW,KAAK,MAAM,CAAC;AACxE,SAAK,KAAK;AACV,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,KAAK,MAAM;AACjB,aAAS,OAAO,KAAK,KAAM,KAAI,QAAQ,KAAK,IAAI;AAChD,aAAS,IAAI,KAAK,KAAK,UAAU,GAAG,KAAK,GAAG,IAAK,YAAW,KAAK,MAAM,CAAC;AACxE,SAAK,KAAK,KAAK,OAAO,KAAK,MAAM,UAAU,QAAQ,EAAG,MAAK,KAAK;AAAA,EAClE;AAAA,EACA,OAAO;AACL,QAAI,KAAK,KAAK,UAAU,GAAG;AACzB,WAAK,OAAO,KAAK,KAAK;AACtB,WAAK,QAAQ;AACb,WAAK,OAAO;AAAA,IACd,OAAO;AACL,UAAI,MAAM,KAAK,KAAK,CAAC;AACrB,WAAK,OAAO,IAAI;AAChB,WAAK,KAAK,IAAI;AACd,WAAK,QAAQ,IAAI;AACjB,WAAK,OAAO,IAAI;AAChB,UAAI,IAAI,MAAO,KAAI,KAAK;AACxB,iBAAW,KAAK,MAAM,CAAC;AAAA,IACzB;AAAA,EACF;AACF;AACA,SAAS,WAAW,MAAM,OAAO;AAC/B,WAAS,MAAM,KAAK,KAAK,OAAK;AAC5B,QAAI,cAAc,SAAS,KAAK;AAChC,QAAI,cAAc,KAAK,OAAQ;AAC/B,QAAI,QAAQ,KAAK,UAAU;AAC3B,QAAI,aAAa,IAAI,KAAK,UAAU,MAAM,QAAQ,KAAK,aAAa,CAAC,CAAC,KAAK,GAAG;AAC5E,cAAQ,KAAK,aAAa,CAAC;AAC3B;AAAA,IACF;AACA,QAAI,IAAI,QAAQ,KAAK,IAAI,EAAG;AAC5B,SAAK,UAAU,IAAI;AACnB,SAAK,KAAK,IAAI;AACd,YAAQ;AAAA,EACV;AACF;AACA,IAAM,aAAN,MAAiB;AAAA,EACf,YAAY,MAAM,MAAM,UAAU;AAChC,SAAK,WAAW;AAChB,SAAK,SAAS,CAAC;AACf,SAAK,WAAW,CAAC;AACjB,SAAK,aAAa,CAAC;AACnB,SAAK,YAAY;AAEjB,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,KAAK;AACV,SAAK,UAAU;AAGf,SAAK,YAAY;AACjB,SAAK,SAAS,WAAW,KAAK,MAAM,MAAM,QAAQ;AAAA,EACpD;AAAA,EACA,KAAK,KAAK,OAAO,MAAyB;AACxC,SAAK,OAAO,KAAK,KAAK,IAAI;AAC1B,SAAK,OAAO,SAAS,KAAK,SAAS,SAAS,KAAK,WAAW,SAAS;AACrE,SAAK,YAAY;AACjB,SAAK,KAAK;AACV,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,KAAK;AACV,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,KAAK,MAAM;AACjB,WAAO,KAAK,YAAY,OAAO,KAAK,SAAS,KAAK,SAAS,IAAI,OAAO,KAAK,OAAO,KAAK,SAAS,EAAE,UAAU,QAAQ,EAAG,MAAK,aAAa,KAAK,SAAS;AACvJ,SAAK,OAAO,QAAQ,KAAK,IAAI;AAAA,EAC/B;AAAA,EACA,aAAa,OAAO;AAClB,WAAO,KAAK,QAAQ,KAAK;AACzB,WAAO,KAAK,UAAU,KAAK;AAC3B,WAAO,KAAK,YAAY,KAAK;AAC7B,SAAK,YAAY,aAAa,KAAK,QAAQ,KAAK,QAAQ;AAAA,EAC1D;AAAA,EACA,UAAU,WAAW;AACnB,QAAI,IAAI,GACN;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AAEX,WAAO,IAAI,KAAK,WAAW,WAAW,OAAO,KAAK,WAAW,CAAC,KAAK,KAAK,KAAK,SAAS,CAAC,KAAK,EAAG;AAC/F,WAAO,KAAK,QAAQ,GAAG,KAAK;AAC5B,WAAO,KAAK,UAAU,GAAG,EAAE;AAC3B,WAAO,KAAK,YAAY,GAAG,IAAI;AAC/B,QAAI,UAAW,QAAO,WAAW,GAAG,KAAK,OAAO,IAAI;AACpD,SAAK,YAAY,aAAa,KAAK,QAAQ,KAAK,QAAQ;AAAA,EAC1D;AAAA;AAAA;AAAA,EAGA,OAAO;AACL,QAAI,OAAO,KAAK,IACd,WAAW,KAAK;AAClB,SAAK,QAAQ;AACb,QAAI,YAAY,KAAK,YAAY,IAAI,CAAC,IAAI;AAC1C,eAAS;AACP,UAAI,IAAI,KAAK;AACb,UAAI,IAAI,OAAO,KAAK,SAAS,CAAC,IAAI,KAAK,OAAO,QAAQ,KAAK,OAAO,CAAC,EAAE,UAAU,KAAK,OAAO,aAAa,GAAG;AACzG,YAAI,KAAK,SAAS,CAAC,IAAI,MAAM;AAC3B,eAAK,KAAK,KAAK,SAAS,CAAC;AACzB,eAAK,UAAU,KAAK,OAAO,CAAC,EAAE;AAC9B;AAAA,QACF;AACA,aAAK,aAAa,CAAC;AACnB,YAAI,UAAW,QAAO,WAAW,CAAC;AAAA,MACpC,WAAW,CAAC,KAAK,OAAO,OAAO;AAC7B,aAAK,KAAK,KAAK,UAAU;AACzB;AAAA,MACF,WAAW,KAAK,OAAO,OAAO,MAAM;AAClC,aAAK,KAAK,KAAK,OAAO;AACtB,aAAK,UAAU,KAAK,OAAO;AAC3B;AAAA,MACF,OAAO;AACL,YAAI,UAAU,KAAK,OAAO;AAC1B,YAAI,CAAC,QAAQ,OAAO;AAElB,eAAK,UAAU,SAAS;AACxB,eAAK,OAAO,KAAK;AAAA,QACnB,WAAW,YAAY,KAAK,OAAO,MAAM,KAAK,MAAM,KAAK,OAAO,OAAO,KAAK,OAAO,IAAI;AAErF,eAAK,OAAO,KAAK;AAAA,QACnB,OAAO;AAEL,eAAK,QAAQ;AACb,eAAK,YAAY,KAAK,OAAO;AAC7B,eAAK,YAAY,KAAK,OAAO;AAC7B,eAAK,KAAK,KAAK,OAAO;AACtB,eAAK,UAAU,QAAQ;AACvB,eAAK,OAAO,KAAK;AACjB,eAAK,QAAQ,KAAK,IAAI,KAAK,OAAO;AAClC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,WAAW;AACb,WAAK,YAAY;AACjB,eAAS,IAAI,UAAU,SAAS,GAAG,KAAK,KAAK,UAAU,CAAC,IAAI,MAAM,IAAK,MAAK;AAAA,IAC9E;AAAA,EACF;AAAA,EACA,eAAe,IAAI;AACjB,QAAI,CAAC,KAAK,OAAO,OAAQ,QAAO,KAAK;AACrC,QAAI,SAAS,CAAC;AACd,aAAS,IAAI,KAAK,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAChD,UAAI,KAAK,WAAW,CAAC,IAAI,KAAK,UAAW;AACzC,UAAI,KAAK,SAAS,CAAC,IAAI,MAAM,KAAK,SAAS,CAAC,KAAK,MAAM,KAAK,OAAO,CAAC,EAAE,WAAW,KAAK,MAAM,QAAS,QAAO,KAAK,KAAK,OAAO,CAAC,CAAC;AAAA,IACjI;AACA,WAAO,OAAO,QAAQ;AAAA,EACxB;AAAA,EACA,QAAQ,IAAI;AACV,QAAI,OAAO;AACX,aAAS,IAAI,KAAK,SAAS,SAAS,GAAG,KAAK,KAAK,KAAK,SAAS,CAAC,IAAI,IAAI,IAAK;AAC7E,WAAO;AAAA,EACT;AACF;AACA,SAAS,QAAQ,GAAG,QAAQ,GAAG,QAAQ,QAAQ,YAAY;AACzD,IAAE,KAAK,MAAM;AACb,IAAE,KAAK,MAAM;AACb,MAAI,OAAO,SAAS;AACpB,MAAI,MAAM,QACR,OAAO,SAAS;AAClB,aAAS;AACP,QAAI,OAAO,EAAE,KAAK,OAAO,EAAE,IACzB,OAAO,QAAQ,EAAE,UAAU,EAAE;AAC/B,QAAI,MAAM,OAAO,IAAI,EAAE,KAAK,OAAO,EAAE,IACnC,UAAU,KAAK,IAAI,KAAK,IAAI;AAC9B,QAAI,EAAE,SAAS,EAAE,OAAO;AACtB,UAAI,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,GAAG,EAAE,KAAK,MAAM,WAAW,EAAE,eAAe,EAAE,EAAE,GAAG,EAAE,eAAe,EAAE,EAAE,CAAC,GAAI,YAAW,aAAa,KAAK,SAAS,EAAE,OAAO,EAAE,KAAK;AAAA,IAChM,OAAO;AACL,UAAI,UAAU,OAAO,CAAC,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAG,YAAW,aAAa,KAAK,SAAS,EAAE,QAAQ,EAAE,MAAM;AAAA,IAChH;AACA,QAAI,MAAM,KAAM;AAChB,SAAK,QAAQ,EAAE,WAAW,EAAE,YAAY,WAAW,YAAa,YAAW,YAAY,GAAG;AAC1F,UAAM;AACN,QAAI,QAAQ,EAAG,GAAE,KAAK;AACtB,QAAI,QAAQ,EAAG,GAAE,KAAK;AAAA,EACxB;AACF;AACA,SAAS,WAAW,GAAG,GAAG;AACxB,MAAI,EAAE,UAAU,EAAE,OAAQ,QAAO;AACjC,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAK,KAAI,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAG,QAAO;AAC9E,SAAO;AACT;AACA,SAAS,OAAO,OAAO,OAAO;AAC5B,WAAS,IAAI,OAAO,IAAI,MAAM,SAAS,GAAG,IAAI,GAAG,IAAK,OAAM,CAAC,IAAI,MAAM,IAAI,CAAC;AAC5E,QAAM,IAAI;AACZ;AACA,SAAS,OAAO,OAAO,OAAO,OAAO;AACnC,WAAS,IAAI,MAAM,SAAS,GAAG,KAAK,OAAO,IAAK,OAAM,IAAI,CAAC,IAAI,MAAM,CAAC;AACtE,QAAM,KAAK,IAAI;AACjB;AACA,SAAS,aAAa,OAAO,OAAO;AAClC,MAAI,QAAQ,IACV,WAAW;AACb,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAK,MAAK,MAAM,CAAC,IAAI,YAAY,MAAM,CAAC,EAAE,UAAU,MAAM,KAAK,EAAE,WAAW,GAAG;AAC/G,YAAQ;AACR,eAAW,MAAM,CAAC;AAAA,EACpB;AACA,SAAO;AACT;AAMA,SAAS,YAAY,QAAQ,SAAS,KAAK,OAAO,QAAQ;AACxD,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,MAAM,IAAI,OAAO,UAAS;AAC5C,QAAI,OAAO,WAAW,CAAC,KAAK,GAAG;AAC7B,WAAK,UAAU,IAAI;AACnB;AAAA,IACF,OAAO;AACL;AACA,UAAIA,kBAAiB,QAAQ,CAAC;AAAA,IAChC;AAAA,EACF;AACA,SAAO;AACT;AAQA,SAAS,WAAW,QAAQ,KAAK,SAAS,QAAQ;AAChD,WAAS,IAAI,GAAG,IAAI,OAAK;AACvB,QAAI,KAAK,IAAK,QAAO;AACrB,QAAI,KAAK,OAAO,OAAQ;AACxB,SAAK,OAAO,WAAW,CAAC,KAAK,IAAI,UAAU,IAAI,UAAU;AACzD,QAAIA,kBAAiB,QAAQ,CAAC;AAAA,EAChC;AACA,SAAO,WAAW,OAAO,KAAK,OAAO;AACvC;", "names": ["findClusterBreak", "surrogate<PERSON>ow", "surrogateHigh", "codePointAt", "codePointSize", "MapMode", "insert", "i", "compare", "prec", "CharCategor<PERSON>", "require"]}