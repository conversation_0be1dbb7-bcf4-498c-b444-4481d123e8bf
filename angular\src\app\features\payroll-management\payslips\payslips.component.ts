import { Component, DestroyRef, inject } from '@angular/core';
import { payslips } from './payslips.model';
import { PayslipService } from '@proxy/payroll/payslips';
import { requireAllOperator } from '@shared';
import { TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { MatDialog } from '@angular/material/dialog';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Subject } from 'rxjs';
import { PayslipsCreateDialogComponent } from './payslips-create-dialog/payslips-create-dialog.component';
import { PayslipItemsDialogComponent } from './payslip-items-dialog/payslip-items-dialog.component';

@Component({
  selector: 'app-payslips',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `<ttwr-grid class="actions-end" [config]="config"/>`,
})
export class PayslipsComponent {
  private payslip = inject(PayslipService);
  private dialog = inject(MatDialog);
  private destroyRef = inject(DestroyRef);

  private refreshSubject = new Subject<void>();

  protected config = payslips().exclude({
    employeeId: true,
  }).grid({
    title: '::Payslips',
    refreshSubject: this.refreshSubject,
    dataFunc: pagination => this.payslip.getList({
      maxResultCount: pagination.pageSize,
      skipCount: pagination.pageSize * pagination.pageIndex,
    }).pipe(requireAllOperator()),
    actions: [
      {
        matIcon: 'add',
        tooltip: 'Add',
        delegateFunc: () => {
          const ref = this.dialog.open(PayslipsCreateDialogComponent, {
            width: '500px'
          });

          ref.afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        }
      }
    ],
    fieldActions: [
      {
        label: '::PayslipItems',
        delegateFunc: ({ id }) => this.dialog.open(PayslipItemsDialogComponent, {
          maxWidth: '700px',
          width: '100%',
          data: id,
        })
      }
    ],
    fields: {
      employeeFullName: {
        columnName: '::GoldenOwl:Employee',
      },
      startDate: {
        columnName: '::GoldenOwl:StartDate',
      },
      endDate: {
        columnName: '::GoldenOwl:EndDate',
      },
    },
  });
}
