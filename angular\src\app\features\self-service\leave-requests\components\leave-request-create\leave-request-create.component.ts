import {Component, DestroyRef, inject, signal} from '@angular/core';
import {
  AlertService,
  dateToISO,
  LanguagePipe,
  LOADING,
  takeOptions,
  TtwrFormComponent
} from '@ttwr-framework/ngx-main-visuals';
import { finalize } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatCard, MatCardContent } from '@angular/material/card';
import {leaveRequestCreate, leaveRequests} from '../../leave-requests.model';
import { ActivatedRoute, Router } from '@angular/router';
import {LeaveRequestService, LeaveUnit} from '@proxy/hr/leave-requests';
import {CalculationDurationDto, CreateLeaveRequestDto} from '@proxy/hr/leave-requests/dtos';
import {LocalizationService} from '@abp/ng.core';
import {getLabelOfValue} from '@shared/functions/enum-labels';
import {FormGroup} from '@angular/forms';
import {DatePipe} from '@angular/common';

@Component({
  selector: 'app-leave-request-create',
  standalone: true,
  templateUrl: './leave-request-create.component.html',
  styleUrls: ['./leave-request-create.component.scss'],
  imports: [MatCard, MatCardContent, TtwrFormComponent, LanguagePipe],
})
export class LeaveRequestCreateComponent {
  private leaveRequestService = inject(LeaveRequestService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private loading = inject(LOADING);
  private localizationService = inject(LocalizationService);
  private datePipe = inject(DatePipe);

  protected config = leaveRequestCreate().form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);
        let obj : any = {
          leaveTypeId: body.leaveTypeId,
          leaveUnit: body.leaveUnit,
          createDailyLeaveRequestDto: body.leaveUnit == 0 ? {
            from: this.datePipe.transform(new Date(body.from), 'yyyy-MM-dd')!,
            to: this.datePipe.transform(new Date(body.to), 'yyyy-MM-dd')!
          } : null,
          createHourlyLeaveRequestDto: body.leaveUnit == 1 ? {
            fromTime: this.datePipe.transform(new Date(body.fromTime), 'HH:mm')!,
            toTime: this.datePipe.transform(new Date(body.toTime), 'HH:mm')!,
            fromDate: this.datePipe.transform(new Date(body.fromDate), 'yyyy-MM-dd')!,
            toDate: this.datePipe.transform(new Date(body.toDate), 'yyyy-MM-dd')!
          } : null
        };
        this.leaveRequestService.create(obj)
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::CreatedSuccessfully');
            this.router.navigate(['.'], { relativeTo: this.route.parent });
          });
      },
    },
    fields: {
      leaveTypeId: {
        label: '::GoldenOwl:LeaveType',
      },
      leaveUnit: {
        label: '::GoldenOwl:LeaveUnit',
        onChange: (value, indexChain, parentGroup) => {
          let fromTimeHiddenSignal:any = this.config.fields.fromTime.hiddenSignal;
          let toTimeHiddenSignal:any = this.config.fields.toTime.hiddenSignal;
          let fromDateHiddenSignal:any = this.config.fields.fromDate.hiddenSignal;
          let toDateHiddenSignal:any = this.config.fields.toDate.hiddenSignal;
          let fromHiddenSignal:any = this.config.fields.from.hiddenSignal;
          let toHiddenSignal:any = this.config.fields.to.hiddenSignal;

          if(value === LeaveUnit.Days)
          {
            fromTimeHiddenSignal.set(true);
            toTimeHiddenSignal.set(true)
            fromDateHiddenSignal.set(true)
            toDateHiddenSignal.set(true)
            fromHiddenSignal.set(false);
            toHiddenSignal.set(false);
          }
          if(value === LeaveUnit.Hours)
          {
            fromTimeHiddenSignal.set(false);
            toTimeHiddenSignal.set(false)
            fromDateHiddenSignal.set(false)
            toDateHiddenSignal.set(false)
            fromHiddenSignal.set(true);
            toHiddenSignal.set(true);
          }
        }
      },
      fromTime: {
        label: '::GoldenOwl:FromTime',
        pickerType: 'time',
        mode: 'portrait',
        twelveHour: true,
        hiddenSignal: signal(true),
        onChange: (value,  indexChain, parentGroup) => {
          if(value)
            this.updateDurationField(value, parentGroup.controls.toTime.value, parentGroup.controls.leaveUnit.value, parentGroup);
        }
      },
      toTime: {
        label: '::GoldenOwl:ToTime',
        pickerType: 'time',
        mode: 'portrait',
        twelveHour: true,
        hiddenSignal: signal(true),
        onChange: (value,  indexChain, parentGroup) => {
          if(value)
            this.updateDurationField(parentGroup.controls.fromTime.value, value, parentGroup.controls.leaveUnit.value, parentGroup);
        }
      },
      fromDate: {
        label: '::GoldenOwl:FromDate',
        hiddenSignal: signal(true)
      },
      toDate: {
        label: '::GoldenOwl:ToDate',
        hiddenSignal: signal(true)
      },
      from: {
        label: '::GoldenOwl:From',
        hiddenSignal: signal(true),
        onChange: (value,  indexChain, parentGroup) => {
          if(value)
            this.updateDurationField(value, parentGroup.controls.to.value, parentGroup.controls.leaveUnit.value, parentGroup);
        }
      },
      to: {
        label: '::GoldenOwl:To',
        hiddenSignal: signal(true),
        onChange: (value,  indexChain, parentGroup) => {
          if(value)
            this.updateDurationField(parentGroup.controls.from.value, value, parentGroup.controls.leaveUnit.value, parentGroup);
        }
      },
      duration: {
        label: '::GoldenOwl:Duration',
        readonlySignal: signal(true),
      },
    }
  });

  updateDurationField(fromDate: Date, toDate: Date, unit: number, formGroup: FormGroup){
    if(fromDate && toDate) {
      let calc: CalculationDurationDto = {
        from: dateToISO(fromDate),
        to: dateToISO(toDate),
        unit: unit
      }
      this.leaveRequestService.calcDuration(calc).subscribe((res) => {
        formGroup.controls['duration'].patchValue(res.toString() + " " + this.localizationService.instant('::'+ getLabelOfValue(LeaveUnit, unit)));
      })
    }
  }
}
