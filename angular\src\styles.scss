@use "@angular/material" as mat;
@use "@ttwr-framework/ngx-main-visuals" as ttwr;

@include mat.core();
@include ttwr.m3-support();
@include ttwr.ttwr-basic-table();

$font-family: "Noto Kufi Arabic";

$app-theme: mat.define-theme(
  (
    color: (
      primary: mat.$green-palette,
      tertiary: mat.$spring-green-palette,
    ),
    typography: (
      plain-family: $font-family,
      brand-family: $font-family,
    ),
  )
);

html {
  @include mat.all-component-themes($app-theme);
  @include mat.color-variants-backwards-compatibility($app-theme);
}

:root {
  --lpx-logo: url("/assets/images/tatweer.png");
  --ttwr-grid-text-cell-container-margin: 0;

  --ttwr-basic-table-normal: #{mat.get-theme-color(
      $app-theme,
      secondary-container
    )};
  --ttwr-basic-table-normal-text: #{mat.get-theme-color(
      $app-theme,
      on-secondary-container
    )};
  --ttwr-basic-table-light: #{mat.get-theme-color($app-theme, surface-container)};
  --ttwr-basic-table-light-text: #{mat.get-theme-color($app-theme, on-surface)};

  --app-primary-color: #{mat.get-theme-color($app-theme, primary)};
  --app-on-primary-color: #{mat.get-theme-color($app-theme, on-primary)};
  --app-secondary-container-color: #{mat.get-theme-color(
      $app-theme,
      secondary-container
    )};
}

body {
  font-family: $font-family, sans-serif;
}

* {
  letter-spacing: normal !important;
}

mat-sidenav {
  left: 0;

  [dir="rtl"] & {
    left: unset;
    right: 0;
  }
}

ttwr-form ttwr-form,
ttwr-form app-extra-fields-form {
  h1 {
    font-size: 1.25rem;
  }
}

ttwr-grid {
  &.actions-end {
    .top-section {
      justify-content: end;
    }
  }

  &.empty-grid {
    .responsive-container {
      display: none;
    }
  }
}

.mat-datepicker-content .mat-calendar {
  height: 366px !important;
}

.mdc-list-item--with-leading-icon .mdc-list-item__start {
  margin-left: 16px !important;
  margin-right: 16px !important;
}

.ttwr-download-link {
  color: var(--app-primary-color);
}
