import type { InvoiceType } from '../invoice-type.enum';
import type { PaymentMethod } from '../payment-method.enum';
import type { CreateEntryDto, UpdateEntryDto } from '../../entries/dto/models';
import type { FinancialClientType } from '../../financial-clients/financial-client-type.enum';

export interface CreateInvoiceDto {
  invoiceType: InvoiceType;
  financialClientId?: string;
  currencyCode?: string;
  warehouse?: string;
  date?: string;
  paymentMethod: PaymentMethod;
  description?: string;
  totalAmountInCurrency: number;
  totalAmountInLocal: number;
  exchangeRate: number;
  financialPeriodId?: string;
  entry: CreateEntryDto;
}

export interface InvoiceDto {
  id?: string;
  invoiceType: InvoiceType;
  invoiceNumber: number;
  financialClientId?: string;
  financialClientType: FinancialClientType;
  currencyCode?: string;
  warehouse?: string;
  date?: string;
  paymentMethod: PaymentMethod;
  description?: string;
  isPosted: boolean;
  postedDate?: string;
  totalAmount: number;
  financialPeriodId?: string;
  entryId?: string;
}

export interface UpdateInvoiceDto {
  invoiceType: InvoiceType;
  financialClientId?: string;
  currencyCode?: string;
  warehouse?: string;
  date?: string;
  paymentMethod: PaymentMethod;
  description?: string;
  totalAmountInCurrency: number;
  totalAmountInLocal: number;
  exchangeRate: number;
  financialPeriodId?: string;
  entry: UpdateEntryDto;
}
