import { model, fields, arrayMap } from '@ttwr-framework/ngx-main-visuals';
import { inject } from '@angular/core';
import { DepartmentService } from '@proxy/hr/departments';
import { requireAllOperator } from '@shared';
import { map } from 'rxjs';
import { JobTitleService } from '@proxy/hr/job-titles';

export const jobTitles = () => {
  const departments = inject(DepartmentService);
  const jobTitle = inject(JobTitleService);

  return model({
    id: fields.text(),
    name: fields.text(),
    departmentName: fields.text(),
    jobTitleName: fields.text(),
    baseSalary: fields.number(),
    departmentId: fields.selectFetch('single', () => departments.getGetAllDepartmentsByInput({
      maxResultCount: 999
    }).pipe(
      requireAllOperator(),
      map(res => res.items),
      arrayMap(item => ({
        label: item.name,
        value: item.id,
      })),
    )),
    parentJobTitleId: fields.selectFetch('single', () => jobTitle.getList({
      maxResultCount: 999
    }, '00000000-0000-0000-0000-000000000000').pipe(
      requireAllOperator(),
      map(res => res.items),
      arrayMap(item => ({
        label: item.name,
        value: item.id,
      })),
    )),
  })
};

