import type { CreateUpdateDepartmentDto, DepartmentDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class DepartmentService {
  apiName = 'Default';
  

  delete = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/department/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getDepartmentHasChildById = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, boolean>({
      method: 'GET',
      url: `/api/app/department/${id}/department-has-child`,
    },
    { apiName: this.apiName,...config });
  

  getGetAllDepartmentsByInput = (input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<DepartmentDto>>({
      method: 'GET',
      url: '/api/app/department/get-all-departments',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getGetAllRootDepartments = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, DepartmentDto[]>({
      method: 'GET',
      url: '/api/app/department/get-all-root-departments',
    },
    { apiName: this.apiName,...config });
  

  getGetDepartmentByIdById = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, DepartmentDto>({
      method: 'GET',
      url: `/api/app/department/${id}/get-department-by-id`,
    },
    { apiName: this.apiName,...config });
  

  getGetDepartmentChildrenById = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, DepartmentDto[]>({
      method: 'GET',
      url: `/api/app/department/${id}/get-department-children`,
    },
    { apiName: this.apiName,...config });
  

  postCreateDepartmentByDto = (dto: CreateUpdateDepartmentDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/department/create-department',
      body: dto,
    },
    { apiName: this.apiName,...config });
  

  putUpdateDepartmentByIdAndDto = (id: string, dto: CreateUpdateDepartmentDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'PUT',
      responseType: 'text',
      url: `/api/app/department/${id}/update-department`,
      body: dto,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
