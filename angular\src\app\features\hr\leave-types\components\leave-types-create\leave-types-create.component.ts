import { Component, DestroyRef, inject } from '@angular/core';
import { AlertService, LanguagePipe, LOADING, TtwrFormComponent } from '@ttwr-framework/ngx-main-visuals';
import { finalize } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatCard, MatCardContent } from '@angular/material/card';
import { leaveTypes } from '../../leave-types.model';
import { LeaveTypeService } from '@proxy/leave-types';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-leave-types-create',
  standalone: true,
  templateUrl: './leave-types-create.component.html',
  styleUrl: './leave-types-create.component.scss',
  imports: [MatCard, MatCardContent, TtwrFormComponent, LanguagePipe],
})
export class LeaveTypesCreateComponent {
  private leaveType = inject(LeaveTypeService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private loading = inject(LOADING);

  protected config = leaveTypes.form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        this.leaveType.create(body)
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::CreatedSuccessfully');
            this.router.navigate(['.'], { relativeTo: this.route.parent });
          })
      },
    },
    fields: {
      code: {
        label: '::GoldenOwl:Code',
      },
      kindOfTimeOff: {
        label: '::KindOfTimeOff',
      },
      requestUnit: {
        label: '::RequestUnit',
      },
      requiresAllocation: {
        label: '::RequiresAllocation',
      }
    },
  })
}
