using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoldenOwl.Authorization;
using GoldenOwl.HR.Employees;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.PermissionManagement;
using Volo.Abp.Users;

namespace GoldenOwl.GoTasks;

[Authorize]
public class GoTaskAppService : GoldenOwlAppService, IGoTaskAppService
{
    private readonly IGoTaskManager _goTaskManager;
    private readonly IRepository<GoTask, Guid> _goTaskRepository;
    private readonly ICurrentUser _currentUser;
    private readonly PermissionManager _permissionManager;


    public GoTaskAppService(IGoTaskManager goTaskManager, IRepository<GoTask, Guid> goTaskRepository,
        ICurrentUser currentUser, PermissionManager permissionManager)
    {
        _goTaskManager = goTaskManager;
        _goTaskRepository = goTaskRepository;
        _currentUser = currentUser;
        _permissionManager = permissionManager;
    }

    public async Task<PagedResultDto<GoTaskDto>> GetGoTasksAssignedForCurrentUserAsync(PagedResultRequestDto dto)
    {
        var userPermissions = await _permissionManager.GetAllForUserAsync(_currentUser.Id.Value);
        var userPermissionsNames = userPermissions.Select(p => p.Name).ToList();
        
        var query = await _goTaskRepository.GetQueryableAsync();
        query = query.Where(t => t.AssignedTo == _currentUser.Id)
            .OrderByDescending(ar => ar.CreationTime);

        var totalCount = await AsyncExecuter.CountAsync(query);
        query = query.PageBy(dto);
        List<GoTask> goTasks = await AsyncExecuter.ToListAsync(query);
        List<GoTaskDto> goTaskDtos = ObjectMapper.Map<List<GoTask>, List<GoTaskDto>>(goTasks);

        return new PagedResultDto<GoTaskDto>(totalCount, goTaskDtos);
    }

    public async Task<GoTaskDto> GetGoTaskAsync(Guid goTaskId)
    {
        var userPermissions = await _permissionManager.GetAllForUserAsync(_currentUser.Id.Value);
        var userPermissionsNames = userPermissions.Select(p => p.Name).ToList();

        var goTask = await _goTaskRepository.GetAsync(goTaskId);
        if (goTask.AssignedTo != _currentUser.Id)
        {
            throw new UserFriendlyException("Current user is not authorized for this task");
        }

        return ObjectMapper.Map<GoTask, GoTaskDto>(goTask);
    }

    public async Task ApproveGoTask(Guid goTaskId)
    {
        var userPermissions = await _permissionManager.GetAllForUserAsync(_currentUser.Id.Value);
        var userPermissionsNames = userPermissions.Select(p => p.Name).ToList();

        var goTask = await _goTaskRepository.GetAsync(t => t.Id == goTaskId);

        if (goTask.AssignedTo != _currentUser.Id)
        {
            throw new UserFriendlyException("Current user is not authorized for this task");
        }

        await _goTaskManager.ApproveTaskAsync(goTask.Id);
    }

    public async Task RejectGoTask(Guid goTaskId)
    {
        var userPermissions = await _permissionManager.GetAllForUserAsync(_currentUser.Id.Value);
        var userPermissionsNames = userPermissions.Select(p => p.Name).ToList();

        var goTask = await _goTaskRepository.GetAsync(t => t.Id == goTaskId);

        if (goTask.AssignedTo != _currentUser.Id)
        {
            throw new UserFriendlyException("Current user is not authorized for this task");
        }
        
        await _goTaskManager.RejectTaskAsync(goTask.Id);
    }
}