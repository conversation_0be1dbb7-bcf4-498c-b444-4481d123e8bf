import { Component, computed, DestroyRef, inject, signal, WritableSignal } from '@angular/core';
import { PermissionGrantInfoDto, PermissionsService } from '@abp/ng.permission-management/proxy';
import {
  MAT_DIALOG_DATA,
  MatDialogActions,
  MatDialogClose,
  MatDialogContent,
  MatDialogRef,
  MatDialogTitle
} from '@angular/material/dialog';
import { map, tap } from 'rxjs';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { LocalizationModule, LocalizationService } from '@abp/ng.core';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { MatListItem, MatListItemMeta, MatNavList } from '@angular/material/list';
import { MatDivider } from '@angular/material/divider';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { AlertService, LanguagePipe, LOADING } from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-permissions-dialog',
  standalone: true,
  imports: [
    MatDialogTitle,
    LocalizationModule,
    MatDialogContent,
    MatProgressSpinner,
    MatListItem,
    MatNavList,
    MatDivider,
    MatCheckbox,
    MatDialogActions,
    MatButton,
    MatIcon,
    LanguagePipe,
    MatListItemMeta,
    MatDialogClose,
  ],
  templateUrl: './permissions-dialog.component.html',
  styleUrl: './permissions-dialog.component.scss'
})
export class PermissionsDialogComponent {
  private permissions = inject(PermissionsService);
  private data = inject<PermissionsDialogData>(MAT_DIALOG_DATA);
  private ref = inject(MatDialogRef);
  private destroyRef = inject(DestroyRef);
  private localization = inject(LocalizationService);
  private alert = inject(AlertService);
  private loading = inject(LOADING);

  private permissionsData = signal<PermissionWithSignal[]>([]);

  protected displayName = this.data.displayName;
  protected permissionsGroups = toSignal(
    this.permissions
      .get(this.data.providerName, this.data.providerKey)
      .pipe(
        map(result => result.groups),
        map(groups => groups.map(group => ({
          ...group,
          permissions: group.permissions.map(permission => {
            return {
              ...permission,
              signal: signal(permission.isGranted),
            }
          })
        }))),
        tap(groups => {
          this.activeGroup.set(groups?.[0]?.name);
          this.permissionsData.set(
            groups
              .map(g => g.permissions)
              .reduce((pre, curr) => [...pre, ...curr], [])
          )
        }),
      ),
  )
  protected permissionsGrantsMap = computed(() => {
    const groups = this.permissionsGroups();
    const map = new Map<string, PermissionWithSignal[]>();

    groups?.forEach(group => {
      map.set(group.name!, group.permissions)
    })

    return map;
  })
  protected activeGroup = signal<string | undefined>(undefined);

  protected selectAllGrantsStatus = computed<'checked' | 'indeterminate' | 'none'>(() => {
    const values = this.permissionsData().map(permission => permission.signal());

    const allChecked = values.every(v => v);
    const someChecked = values.some(v => v);

    if (values.length === 0) return 'none';
    if (allChecked) return 'checked';
    if (someChecked) return 'indeterminate';

    return 'none';
  })

  protected allGrantsChange(checked: boolean) {
    this.permissionsData().forEach(permission => {
      permission.signal.set(checked)
    })
  }

  protected selectAllInTabStatus = computed<'checked' | 'indeterminate' | 'none'>(() => {
    const activeGroup = this.activeGroup();

    if (!activeGroup) return 'none';

    const permissions = this.permissionsGrantsMap().get(activeGroup)

    if (!permissions || permissions.length === 0) return 'none';

    const values = permissions.map(permission => permission.signal());

    const allChecked = values.every(v => v);
    const someChecked = values.some(v => v);

    if (allChecked) return 'checked';
    if (someChecked) return 'indeterminate';

    return 'none';
  })

  protected allInTabChange(checked: boolean) {
    const activeGroup = this.activeGroup();
    if (!activeGroup) return;

    this.permissionsGrantsMap().get(activeGroup)?.forEach(permission => {
      permission.signal.set(checked)
    })
  }

  protected save() {
    const permissions = this.permissionsData();
    if (permissions.length === 0) return;
    if (this.loading()) return;

    this.permissions.update(this.data.providerName, this.data.providerKey, {
      permissions: permissions.map(p => ({
        name: p.name!,
        isGranted: p.signal(),
      })),
    })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        error: (err) => {
          this.loading.set(false);

          const message = this.localization.instant(err.error?.error?.message);
          if (message) {
            this.alert.error(message);
          }
        },
        next: () => {
          this.ref.close();
        },
      })
  }

  protected getCheckedCount(group?: string) {
    return group
      ? this.permissionsGrantsMap().get(group)?.reduce((pre, curr) => pre + Number(curr.signal()), 0) ?? 0
      : 0;
  }

  handleCheckboxChange(grant: PermissionWithSignal, checked: boolean) {
    grant.signal.set(checked)

    this.permissionsData()
      .filter(p => p.parentName === grant.name)
      .forEach(p => p.signal.set(checked));

    if (grant.parentName) {
      this.permissionsData()
        .find(p => p.name === grant.parentName)
        ?.signal
        ?.set(
          this.permissionsData()
            .filter(p => p.parentName === grant.parentName)
            .map(p => p.signal())
            .some(v => v)
        )
    }
  }
}

export interface PermissionsDialogData {
  providerName: 'R' | 'U';
  providerKey: string;
  displayName: string;
}

interface PermissionWithSignal extends PermissionGrantInfoDto {
  signal: WritableSignal<boolean>;
}
