import { Component, inject } from '@angular/core';
import { <PERSON><PERSON><PERSON>, MatCardContent } from '@angular/material/card';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { ActivatedRoute, Router } from '@angular/router';
import { SalaryRuleFormComponent } from '../salary-rule-form/salary-rule-form.component';

@Component({
  selector: 'app-salary-rules-create',
  standalone: true,
  templateUrl: './salary-rules-create.component.html',
  imports: [
    Mat<PERSON><PERSON>,
    MatCardContent,
    SalaryRuleFormComponent,
    LanguagePipe,
  ],
  styles: `
    :host {
      --ttwr-form-grid-template-columns: 1fr 1fr;
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class SalaryRulesCreateComponent {
  private router = inject(Router);
  private route = inject(ActivatedRoute);

  onFinish() {
    this.router.navigate(['.'], { relativeTo: this.route.parent });
  }
}
