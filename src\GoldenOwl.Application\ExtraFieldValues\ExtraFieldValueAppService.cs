﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using GoldenOwl.ExtraFieldDefinitions;
using GoldenOwl.ExtraFields;
using GoldenOwl.ExtraFields.ExtraFieldDefinitions;
using GoldenOwl.ExtraFields.ExtraFieldValues;
using GoldenOwl.ExtraFieldValues.DTO;
using GoldenOwl.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;

namespace GoldenOwl.ExtraFieldValues;

[Authorize]
public class ExtraFieldValueAppService(
    IRepository<ExtraFieldValue, Guid> extraFieldValueRepository,
    IRepository<ExtraFieldDefinition, Guid> extraFieldDefinitionRepository,
    IExtraFieldValueManager extraFieldValueManager,
    IAuthorizationService authorizationService,
    ExtraFieldManager extraFieldManager
)
    : GoldenOwlAppService, IExtraFieldValueAppService
{
    public async Task<List<ExtraFieldDto>> GetListAsync(Guid entityId, EntityType entityType)
    {
        await CheckExtraFieldPermissionAsync(entityType);
        var entityExtraFields = await extraFieldManager.GetEntityExtraFields(entityId, entityType);
        var extraFieldDtos = entityExtraFields.Select(fieldValue => new ExtraFieldDto
            {
                ExtraFieldDefinitionId = fieldValue.Definition.Id,
                FieldType = fieldValue.Definition.FieldType,
                FieldName = fieldValue.Definition.FieldName,
                SelectList = fieldValue.Definition.SelectListValues,
                Value = (fieldValue.Value is IFormattable formattable)?formattable.ToString(null, CultureInfo.InvariantCulture):fieldValue.Value.ToString(),
            })
            .OrderBy(x=> x.FieldName).ToList();

        return extraFieldDtos;
    }
    
    public async Task SetAsync(List<ExtraFieldDto> extraFieldDtos, Guid bindEntityId)
    {
        if (bindEntityId == Guid.Empty)
        {
            throw new UserFriendlyException("bindEntityId can't be empty");
        }
        
        var definitionIds = extraFieldDtos.Select(x => x.ExtraFieldDefinitionId).ToList();
        var extraFieldDefinitionList = await extraFieldDefinitionRepository
            .GetListAsync(x => definitionIds.Contains(x.Id));
        
        var definitions = extraFieldDefinitionList.ToDictionary(x => x.Id);

        var extraFieldValue = new List<ExtraFieldValue>();
        foreach (var dto in extraFieldDtos)
        {
            if (!definitions.TryGetValue(dto.ExtraFieldDefinitionId, out var definition))
            {
                throw new UserFriendlyException($"Definition not found for ID {dto.ExtraFieldDefinitionId}");
            }
            await CheckExtraFieldPermissionAsync(definition.EntityType);
            extraFieldValue.Add(new ExtraFieldValue(bindEntityId, dto.ExtraFieldDefinitionId, dto.Value));
        }

        await extraFieldValueManager.SetAsync(extraFieldValue, bindEntityId);
    }
    
    private async Task CheckExtraFieldPermissionAsync(EntityType entityType)
    {
        var permissionName = entityType switch
        {
            EntityType.Employee => GoldenOwlPermissions.EmployeesCreateOrUpdate,
            EntityType.Contract => GoldenOwlPermissions.ContractManagement,
            _ => throw new UserFriendlyException("Invalid EntityType")
        };

        await authorizationService.CheckAsync(permissionName);
    }
}