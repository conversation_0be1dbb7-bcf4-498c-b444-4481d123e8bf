import { Component, DestroyRef, inject, input, OnInit } from '@angular/core';
import {
  FormFieldWithControlAndName,
  ICustomInputComponent,
  LanguagePipe,
  TextFieldType
} from '@ttwr-framework/ngx-main-visuals';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>orm<PERSON>ield, Mat<PERSON>abe<PERSON> } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { ReactiveFormsModule, Validators } from '@angular/forms';
import { ExtraFieldDefinitionService } from '@proxy/extra-field-definitions';
import { map, of, switchMap, timer } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-unique-code-custom-input',
  standalone: true,
  imports: [
    MatFormField,
    LanguagePipe,
    MatInput,
    MatLabel,
    MatError,
    ReactiveFormsModule
  ],
  template: `
    <mat-form-field>
      <mat-label>{{ (field().label ?? field().name) | i18n }}</mat-label>
      <input [formControl]="field().control" matInput/>
      <mat-error>
        @if (field().control.hasError('unique')) {
          {{ '::GoldenOwl:CodeMustBeUnique' | i18n }}
        } @else {
          {{ 'this field is required' | i18n }}
        }
      </mat-error>
    </mat-form-field>
  `,
  styles: `
    mat-form-field {
      width: 100%;
    }
  `,
})
export class UniqueCodeCustomInputComponent implements ICustomInputComponent<TextFieldType>, OnInit {
  private extraFieldDefinition = inject(ExtraFieldDefinitionService);
  private destroyRef = inject(DestroyRef);

  public field = input.required<FormFieldWithControlAndName<TextFieldType>>();
  public self = input<boolean>(false);

  _selfValue = '';

  ngOnInit() {
    if (this.self()) {
      this._selfValue = this.field().control.value;
    }

    this.field().control.addValidators(Validators.required);

    this.field().control.addAsyncValidators(
      (control) => {
        if (!control.value) return of(null);

        // this timer work like debounce time operator
        return timer(1000).pipe(
          switchMap(() => this.extraFieldDefinition.isKeyUnique(control.value, '00000000-0000-0000-0000-000000000000')),
          map(result => {
            if (result) return null;

            if (this._selfValue === control.value) return null;

            return {
              unique: true,
            }
          }),
          takeUntilDestroyed(this.destroyRef),
        )
      }
    )
  }
}
