<h1 mat-dialog-title>
  {{ 'AbpPermissionManagement::Permissions' | abpLocalization }} -
  {{ displayName }}
</h1>
<mat-dialog-content>
  @if (permissionsGroups(); as groups) {
    <mat-nav-list>
      <mat-checkbox
      [checked]="selectAllGrantsStatus() === 'checked'"
      [indeterminate]="selectAllGrantsStatus() === 'indeterminate'"
      (change)="allGrantsChange($event.checked)"
      >
        {{ 'AbpPermissionManagement::SelectAllInAllTabs' | abpLocalization }}
      </mat-checkbox>
      <mat-divider />
      @for (group of groups; track group.name) {
        <button
          (click)="activeGroup.set(group.name)"
          role="tab"
          mat-list-item
          [activated]="activeGroup() === group.name"
        >
          {{ group.displayName }}
          @if (getCheckedCount(group.name); as count) {
            <span matListItemMeta>({{ count }})</span>
          }
        </button>
      }
    </mat-nav-list>
    @if (permissionsGrantsMap().get(activeGroup() ?? ''); as grants) {
      <div>
        <mat-checkbox
          [checked]="selectAllInTabStatus() === 'checked'"
          [indeterminate]="selectAllInTabStatus() === 'indeterminate'"
          (change)="allInTabChange($event.checked)"
        >
          {{ 'AbpPermissionManagement::SelectAllInThisTab' | abpLocalization }}
        </mat-checkbox>
        <mat-divider />
        @for (grant of grants; track grant.name) {
          <mat-checkbox
            [class.child-checkbox]="!!grant.parentName"
            [checked]="grant.signal()"
            (change)="handleCheckboxChange(grant, $event.checked)"
          >
            {{ grant.displayName }}
          </mat-checkbox>
        }
      </div>
    }
  } @else {
    <mat-progress-spinner mode="indeterminate" />
  }
</mat-dialog-content>
<mat-dialog-actions>
  <button mat-dialog-close mat-raised-button>
    {{ 'Cancel' | i18n }}
  </button>
  <button (click)="save()" [disabled]="activeGroup() === null" mat-raised-button>
    <mat-icon>check</mat-icon>
    {{ 'Save' | i18n }}
  </button>
</mat-dialog-actions>
