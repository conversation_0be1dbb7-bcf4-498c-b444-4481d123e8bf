import { Component, DestroyRef, inject } from '@angular/core';
import {
  AlertService,
  fields,
  LanguagePipe,
  LOADING,
  model,
  TtwrFormComponent
} from '@ttwr-framework/ngx-main-visuals';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { ContractService } from '@proxy/hr/contracts';
import { finalize } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-contracts-cancel-dialog',
  standalone: true,
  imports: [
    LanguagePipe,
    MatDialogTitle,
    MatDialogContent,
    TtwrFormComponent
  ],
  template: `
    <h1 mat-dialog-title>{{ '::CancelContract' | i18n }}</h1>
    <mat-dialog-content>
      <ttwr-form [config]="config"/>
    </mat-dialog-content>
  `,
})
export class ContractsCancelDialogComponent {
  private contract = inject(ContractService);
  private destroyRef = inject(DestroyRef);
  private dialogRef = inject(MatDialogRef);
  private alert = inject(AlertService);
  private id = inject<string>(MAT_DIALOG_DATA);
  private loading = inject(LOADING);

  protected config = model({
    reason: fields.text(),
  }).form({
    initialRequired: true,
    submitAction: {
      onSubmit: body => {
        this.loading.set(true);

        this.contract.putCancelContract(this.id, body).pipe(
          finalize(() => this.loading.set(false)),
          takeUntilDestroyed(this.destroyRef),
        )
          .subscribe(() => {
            this.alert.success('::GoldenOwl:SuccessCancelled');
            this.dialogRef.close(true);
          });
      },
    },
    fields: {
      reason: {
        label: '::Contract:Reason',
        textInputType: 'textarea',
      },
    },
  });
}
