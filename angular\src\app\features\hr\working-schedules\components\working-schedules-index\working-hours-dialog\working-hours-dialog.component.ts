import { Component, inject } from '@angular/core';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogTitle } from '@angular/material/dialog';
import { WorkingHoursDto } from '@proxy/hr/working-schedules/dto';
import { DayOfWeek } from '../../../day-of-week.enum';
import { DatePipe } from '@angular/common';


@Component({
  selector: 'app-working-hours-dialog',
  standalone: true,
  imports: [
    LanguagePipe,
    MatDialogContent,
    MatDialogTitle,
    DatePipe,
  ],
  template: `
    <h1 mat-dialog-title>
      {{ '::GoldenOwl:WorkingHours' | i18n }}
    </h1>
    <mat-dialog-content>
      <table class="ttwr-basic-table">
        <thead>
        <tr>
          <th>{{ 'GoldenOwl:Day' | i18n }}</th>
          <th>{{ 'GoldenOwl:From' | i18n }}</th>
          <th>{{ 'GoldenOwl:To' | i18n }}</th>
          <th>{{ 'GoldenOwl:ExpectedAttendance' | i18n }}</th>
        </tr>
        </thead>
        <tbody>
          @for (item of data; track $index) {
            <tr>
              <td>{{ dayOfWeek[item.day] }}</td>
              <td>{{ item.from | date: 'hh:mm a' }}</td>
              <td>{{ item.to | date: 'hh:mm a' }}</td>
              <td>{{ item.expectedAttendance | date: 'hh:mm' }}</td>
            </tr>
          }
        </tbody>
      </table>
    </mat-dialog-content>
  `,
})
export class WorkingHoursDialogComponent {
  protected data = inject<WorkingHoursDto[]>(MAT_DIALOG_DATA);
  protected dayOfWeek = DayOfWeek;
}
