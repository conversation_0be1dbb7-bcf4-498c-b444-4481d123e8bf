import { Component, DestroyRef, inject } from '@angular/core';
import { AlertService, LanguagePipe, LOADING, TtwrFormComponent } from '@ttwr-framework/ngx-main-visuals';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { exchangeRate } from '../../currency.model';
import { ExchangeRateService } from '@proxy/fn/exchange-rates';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { finalize, of } from 'rxjs';
import {
  DateWithDisabledCellsCustomInputComponent
} from '@shared/components/date-with-disabled-cells-custom-input.component';
import { clearDate } from '@shared';

@Component({
  selector: 'app-exchange-rate-update-dialog',
  standalone: true,
  imports: [
    LanguagePipe,
    MatDialogTitle,
    MatDialogContent,
    TtwrFormComponent
  ],
  template: `
    <h1 mat-dialog-title>{{ '::ExchangeRate:Update' | i18n }}</h1>
    <mat-dialog-content>
      <ttwr-form [config]="config"/>
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class ExchangeRateUpdateDialogComponent {
  private exchangeRate = inject(ExchangeRateService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private dialogRef = inject(MatDialogRef);
  private data = inject<DialogData>(MAT_DIALOG_DATA);
  private loading = inject(LOADING);

  protected config = exchangeRate.form({
    initialRequired: true,
    submitAction: {
      onSubmit: value => {
        this.loading.set(true);

        this.exchangeRate.update({
          rate: value.rate,
          date: clearDate(value.date),
          currencyCode: this.data.currencyCode,
        }, this.data.id).pipe(
          finalize(() => this.loading.set(false)),
          takeUntilDestroyed(this.destroyRef),
        ).subscribe(() => {
          this.alert.success('::CreatedSuccessfully');
          this.dialogRef.close(true);
        });
      }
    },
    fields: {
      date: {
        label: '::ExchangeRate:Date',
        customInputComponent: DateWithDisabledCellsCustomInputComponent,
        customInputComponentExtraInputs: {
          dateFilter: (date: Date) => {
            const now = new Date();
            now.setHours(0, 0, 0, 0);

            return date <= now;
          }
        },
      },
      rate: {
        label: '::ExchangeRate:Rate'
      },
    },
    viewFunc: () => of(this.data)
  })
}

interface DialogData {
  id: string;
  date: string;
  rate: number;
  currencyCode: string;
}
