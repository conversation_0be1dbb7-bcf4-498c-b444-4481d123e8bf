using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoldenOwl.HR.EmployeeCustodies.DTO;
using GoldenOwl.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace GoldenOwl.HR.EmployeeCustodies;

public class EmployeeCustodyAppService(
    IRepository<EmployeeCustody, Guid> employeeRepository,
    IEmployeeCustodyManager employeeCustodyManager) : GoldenOwlAppService, IEmployeeCustodyAppService
{
    [Authorize(GoldenOwlPermissions.EmployeeCustodyIndex)]
    public async Task<EmployeeCustodyDto> GetGetEmployeeCustodyById(Guid id)
    {
        IQueryable<EmployeeCustody> query = await employeeRepository.WithDetailsAsync(e => e.Employee);
        EmployeeCustody employeeCustody = query.FirstOrDefault(c => c.Id == id) ??
                                          throw new EntityNotFoundException(typeof(EmployeeCustody), id);
        return ObjectMapper.Map<EmployeeCustody, EmployeeCustodyDto>(employeeCustody);
    }

    [Authorize(GoldenOwlPermissions.EmployeeCustodyManagement)]
    public virtual Task<Guid> PostCreateEmployeeCustody(CreateEmployeeCustodyDto dto)
    {
        return employeeCustodyManager.AddEmployeeCustodyAsync(dto.EmployeeId, dto.Description, dto.ReceiptDate);
    }

    [Authorize(GoldenOwlPermissions.EmployeeCustodyManagement)]
    public virtual Task PutReleaseEmployeeCustody(Guid id, ReleaseEmployeeCustodyDto dto)
    {
        return employeeCustodyManager.ReleaseEmployeeCustodyAsync(id, dto.ReleaseDate, dto.ReleaseReason);
    }

    [Authorize(GoldenOwlPermissions.EmployeeCustodyIndex)]
    public async Task<PagedResultDto<EmployeeCustodyDto>> GetGetAllEmployeeCustodies(GetEmployeeCustodiesRequestDto dto)
    {
        IQueryable<EmployeeCustody> query = await employeeRepository.WithDetailsAsync(e => e.Employee);

        if (dto.EmployeeId != null && dto.EmployeeId != Guid.Empty)
        {
            query = query.Where(c => c.EmployeeId == dto.EmployeeId);
        }

        int totalCount = await AsyncExecuter.CountAsync(query);
        List<EmployeeCustody> employeeCustodies = await AsyncExecuter.ToListAsync(query.PageBy(dto));
        List<EmployeeCustodyDto> employeeCustodiesDto =
            ObjectMapper.Map<List<EmployeeCustody>, List<EmployeeCustodyDto>>(employeeCustodies);

        return new PagedResultDto<EmployeeCustodyDto>(totalCount, employeeCustodiesDto);
    }
}