import { Component, DestroyRef, inject, signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { MatDialog } from '@angular/material/dialog';
import { map, startWith, Subject, switchMap } from 'rxjs';
import { AlertService, fields, LanguagePipe, LOADING, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { JobTitlesCreateDialogComponent } from './job-titles-create-dialog/job-titles-create-dialog.component';
import { JobTitlesUpdateDialogComponent } from './job-titles-update-dialog/job-titles-update-dialog.component';
import { OrgTreeComponent, OrgTreeData, requireAllOperator } from '@shared';
import { MatButtonToggle, MatButtonToggleGroup } from '@angular/material/button-toggle';
import { MatCard, MatCardContent } from '@angular/material/card';
import { MatButton } from '@angular/material/button';
import { jobTitles } from './job-titles.model';
import { JobTitleService } from '@proxy/hr/job-titles';

@Component({
  selector: 'app-job-titles-index',
  standalone: true,
  imports: [TtwrGridComponent, MatButtonToggleGroup, MatButtonToggle, LanguagePipe, MatCard, MatCardContent, OrgTreeComponent, MatButton],
  templateUrl: './job-titles.component.html',
  styleUrl: './job-titles.component.scss',
})
export class JobTitlesComponent {
  private jobTitle = inject(JobTitleService);
  private dialog = inject(MatDialog);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);

  private refreshSubject = new Subject<void>();

  protected activeSection = signal<'table' | 'tree'>('table');

  protected treeData = toSignal(
    this.refreshSubject.pipe(
      startWith(null),
      switchMap(() => this.jobTitle.getJobTitleTreeWithEmployees().pipe(
        map(res => res[0] as OrgTreeData),
      ))
    )
  )

  protected config = jobTitles().exclude({
    parentJobTitleId: true,
    baseSalary: true,
    departmentId: true,
  }).extend({
    departmentName: fields.text(),
  }).grid({
    hiddenPagination: true,
    elevationClass: 'mat-elevation-z0',
    refreshSubject: this.refreshSubject,
    dataFunc: () => this.jobTitle.getList({
      maxResultCount: 1000,
    }, '00000000-0000-0000-0000-000000000000').pipe(
      requireAllOperator(),
    ),
    fieldActions: [
      {
        matIcon: 'edit',
        tooltip: 'Edit',
        color: 'primary',
        delegateFunc: (obj) => {
          const ref = this.dialog.open(JobTitlesUpdateDialogComponent, {
            width: '500px',
            data: {
              id: obj.id
            },
          });

          ref.afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        }
      },
      {
        matIcon: 'delete',
        tooltip: 'Delete',
        color: 'warn',
        confirmation: {
          title: '::Warning',
          message: '::AreYouSureYouWantToDelete?',
        },
        delegateFunc: (obj) => {
          this.loading.set(true);

          this.jobTitle.delete(obj.id).pipe(
            takeUntilDestroyed(this.destroyRef)
          ).subscribe(() => {
            this.refreshSubject.next();
            this.alert.success('::SuccessDelete');
          })
        }
      }
    ],
    fields: {
      departmentName: {
        columnName: '::Department:Name',
      },
      jobTitleName: {
        columnName: '::GoldenOwl:ParentJobTitle'
      },
    },
  });

  protected openCreateDialog() {
    const ref = this.dialog.open(JobTitlesCreateDialogComponent, {
      width: '500px'
    });

    ref.afterClosed()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(res => {
        if (res) {
          this.refreshSubject.next();
        }
      });
  }
}
