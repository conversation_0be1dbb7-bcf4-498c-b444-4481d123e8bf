import { Component, DestroyRef, inject } from '@angular/core';
import { MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { AlertService, fields, LanguagePipe, LOADING, TtwrFormComponent } from '@ttwr-framework/ngx-main-visuals';
import { PayslipService } from '@proxy/payroll/payslips';
import { payslips } from '../payslips.model';
import { DateRangeCustomInputComponent, extractTwoDates } from '@shared';
import { finalize } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-payslips-create-dialog',
  standalone: true,
  imports: [
    MatDialogContent,
    MatDialogTitle,
    LanguagePipe,
    TtwrFormComponent
  ],
  template: `
    <h2 mat-dialog-title>
      {{ '::CreatePayslip' | i18n }}
    </h2>
    <mat-dialog-content>
      <ttwr-form [config]="config" />
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class PayslipsCreateDialogComponent {
  private payslip = inject(PayslipService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);
  private dialogRef = inject(MatDialogRef);

  protected config = payslips().exclude({
    startDate: true,
    endDate: true,
    employeeFullName: true,
  }).extend({
    dateRange: fields.text(),
  }).form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        const { start, end } = extractTwoDates(body.dateRange);

        this.payslip.createAndGenerateItems({
          employeeId: body.employeeId,
          startDate: start,
          endDate: end,
        })
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::CreatedSuccessfully');
            this.dialogRef.close(true);
          })
      }
    },
    fields: {
      employeeId: {
        search: true,
        label: '::GoldenOwl:Employee',
      },
      dateRange: {
        customInputComponent: DateRangeCustomInputComponent,
      },
    },
  })
}
