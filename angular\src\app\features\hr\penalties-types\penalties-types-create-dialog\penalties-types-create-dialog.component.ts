import { Component, DestroyRef, inject } from '@angular/core';
import {
  AlertService,
  LanguagePipe,
  LOADING,
  requiredValidator,
  TtwrFormComponent
} from '@ttwr-framework/ngx-main-visuals';
import { MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { penaltiesTypes } from '../penalties-types.model';
import { finalize } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { PenaltyTypeService } from '@proxy/hr/penalty-types';

@Component({
  selector: 'app-penalties-types-create-dialog',
  standalone: true,
  imports: [TtwrFormComponent, MatDialogTitle, MatDialogContent, LanguagePipe],
  template: `
    <h1 mat-dialog-title>{{ '::AddPenaltyType' | i18n }}</h1>
    <mat-dialog-content>
      <ttwr-form [config]="config"/>
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-grid-template-columns: 1fr 1fr;
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class PenaltiesTypesCreateDialogComponent {
  private penaltyType = inject(PenaltyTypeService);
  private alert = inject(AlertService);
  private dialogRef = inject(MatDialogRef);
  private destroyRef = inject(DestroyRef);
  private loading = inject(LOADING);

  protected config = penaltiesTypes.form({
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        this.penaltyType
          .postCreatePenaltyTypeByDto(body)
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::CreatedSuccessfully');
            this.dialogRef.close(true);
          })
      },
    },
    fields: {
      name: {
        validators: [requiredValidator],
      },
      code: {
        label: '::PenaltyType:Code',
        validators: [requiredValidator],
      },
      isTerminateContract: {
        label: '::PenaltyType:IsTerminateContract',
      },
      salaryEffectValue: {
        label: '::PenaltyType:SalaryEffectValue',
      },
      salaryEffectValueType: {
        label: '::PenaltyType:SalaryEffectValueType',
      },
    },
  })
}
