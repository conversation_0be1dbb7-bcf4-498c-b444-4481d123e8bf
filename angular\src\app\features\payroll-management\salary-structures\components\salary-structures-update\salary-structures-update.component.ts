import { Component, computed, DestroyRef, inject, signal } from '@angular/core';
import { AlertService, LanguagePipe, LOADING, TtwrFormComponent } from '@ttwr-framework/ngx-main-visuals';
import { finalize, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SalaryStructureService } from '@proxy/payroll/salary-structures';
import { salaryStructures } from '../../salary-structures.model';
import { requireAllOperator } from '@shared';
import { SalaryRulesListComponent } from '../salary-rules-list/salary-rules-list.component';
import { ActivatedRoute, Router } from '@angular/router';
import { MatCard, MatCardContent } from '@angular/material/card';

@Component({
  selector: 'app-salary-structures-update',
  standalone: true,
  imports: [
    LanguagePipe,
    TtwrFormComponent,
    SalaryRulesListComponent,
    MatCard,
    MatCardContent
  ],
  templateUrl: './salary-structures-update.component.html',
  styles: `
    ttwr-form {
      display: block;
      max-width: 800px;
    }
  `,
})
export class SalaryStructuresUpdateComponent {
  private salaryStructure = inject(SalaryStructureService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);
  private router = inject(Router);
  private route = inject(ActivatedRoute);

  protected selectedRules = signal<(string | null)[]>([]);
  private notNullRules = computed<string[]>(() => this.selectedRules().filter(Boolean) as string[]);

  protected config = salaryStructures.form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        const rules = this.notNullRules();

        if (rules.length <= 0) {
          this.alert.error('::YouMustAddOneNonEmptyRuleAtLeast');
          return;
        }

        this.salaryStructure.update(this.route.snapshot.params['id'], {
          ...body,
          structureRules: this.notNullRules().map((id, index) => ({
            ruleId: id,
            sequence: index + 1,
          })),
        })
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::UpdatedSuccessfully');
            this.router.navigate(['.'], { relativeTo: this.route.parent });
          })
      },
    },
    viewFunc: () => this.salaryStructure.get(this.route.snapshot.params['id']).pipe(
      requireAllOperator(),
      tap(res => {
        const rules = res.structureRules;

        rules.sort((a, b) => a.sequence - b.sequence);

        this.selectedRules.set(rules.map(rule => rule.ruleId))
      }),
    ),
    fields: {
      name: {
        inputSize: 'span 2',
      },
    },
  })
}
