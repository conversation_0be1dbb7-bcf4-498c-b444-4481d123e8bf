import { Component, DestroyRef, inject, signal, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';
import {
  AlertService,
  LanguagePipe, LanguageService,
  LOADING,
  requiredValidator,
  TtwrFormComponent
} from '@ttwr-framework/ngx-main-visuals';
import { finalize, map, switchMap, combineLatest, shareReplay, of } from 'rxjs';
import { contactInformationModel, emergencyContactInformationModel, employees } from '../../employees.model';
import { EmployeeService } from '@proxy/hr/employees';
import { clearDate, emailValidator, ExtraFieldsFormComponent, phoneNumberValidator } from '@shared';
import { MatCard, MatCardContent } from '@angular/material/card';
import { EntityType as ExtraFieldEntityType } from '@proxy/extra-field-definitions';
import { ExtraFieldValueService } from '@proxy/extra-field-values';
import { AttachmentGridFormComponent } from '@shared/components/attachment-grid-form/attachment-grid-form.component';
import { EntityType as AttachmentEntityType } from '@proxy/attachment-types';
import { AttachmentService } from '@proxy/attachments';
import {
  UsersCreateDialogComponent
} from '../../../../../abp-overrides/identity/user/users-create-dialog/users-create-dialog.component';
import { LocalizationService } from '@abp/ng.core';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-employees-update',
  standalone: true,
  imports: [
    TtwrFormComponent,
    LanguagePipe,
    MatCard,
    MatCardContent,
    ExtraFieldsFormComponent,
    AttachmentGridFormComponent
  ],
  templateUrl: './employees-update.component.html',
  styleUrl: './employees-update.component.scss',
})
export class EmployeesUpdateComponent {
  private employees = inject(EmployeeService);
  private extraFieldValue = inject(ExtraFieldValueService);
  private attachment = inject(AttachmentService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private language = inject(LanguageService);
  private loading = inject(LOADING);
  private localization = inject(LocalizationService);
  private dialog = inject(MatDialog);

  private attachmentGridForm = viewChild.required(AttachmentGridFormComponent);
  private extraFieldsForm = viewChild.required(ExtraFieldsFormComponent);

  private _phoneNumberHint = this.language.translate('Example') + ': 0999999999';

  protected extraValueEntityType = ExtraFieldEntityType.Employee;
  protected attachmentEntityType = AttachmentEntityType.Employee;
  protected entityId = this.route.snapshot.params['id'];

  private employee$ = this.employees.get(this.route.snapshot.params['id']).pipe(
    shareReplay({
      refCount: true,
      bufferSize: 1,
    }),
  );
  private contactInfo$ = this.employee$.pipe(
    map(employee => employee.contactInformation ?? {}),
  );
  private emergencyContactInfo$ = this.employee$.pipe(
    map(employee => employee.emergencyContactInformation ?? {}),
  );

  protected config = employees().exclude({
    jobTitleName: true,
    employeeStatus: true,
  }).form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        const contactInfoGroup = this.contactInfoConfig.formGroup;
        const emergencyContactInfoGroup = this.emergencyContactInfoConfig.formGroup;
        const extraFieldsGroup = this.extraFieldsForm().form;

        if (contactInfoGroup.invalid || emergencyContactInfoGroup.invalid || extraFieldsGroup.invalid) {
          contactInfoGroup.markAllAsTouched();
          emergencyContactInfoGroup.markAllAsTouched();
          extraFieldsGroup.markAllAsTouched();
          return;
        }

        this.loading.set(true);

        this.employees
          .update({
            ...body,
            dateOfBirth: body.dateOfBirth ? clearDate(body.dateOfBirth) : body.dateOfBirth,
            contactInformation: contactInfoGroup.value,
            emergencyContactInformation: emergencyContactInfoGroup.value,
          }, this.route.snapshot.params['id'])
          .pipe(
            switchMap(() => {
              const extraFields = this.extraFieldsForm().extraFieldsValue;
              const attachments = this.attachmentGridForm().attachmentGridValue(this.route.snapshot.params['id']);

              return combineLatest([
                extraFields.length > 0 ? this.extraFieldValue.set(extraFields, this.route.snapshot.params['id']) : of(null),
                attachments.length > 0 ? this.attachment.saveTemp(attachments) : of(null),
              ]);
            }),
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::UpdatedSuccessfully');
            this.router.navigate(['.'], { relativeTo: this.route.parent });
          })
      },
    },
    viewFunc: () => this.employee$,
    fields: {
      dateOfBirth: {
        validators: [],
        label: '::GoldenOwl:DateOfBirth'
      },
      managerId: {
        validators: [],
        label: '::GoldenOwl:Manager',
        search: true,
      },
      userId: {
        validators: [],
        label: '::GoldenOwl:User',
        formFieldActions: [
          {
            matIcon: 'person_add',
            tooltip: this.localization.instant('AbpIdentity::NewUser'),
            delegateFunc: () => {
              const ref = this.dialog.open(UsersCreateDialogComponent, {
                width: '600px',
              });

              ref.afterClosed().pipe(
                takeUntilDestroyed(this.destroyRef),
              ).subscribe(res => {
                if (res) {
                  this.config.fields.userId.options$Signal.set(this.config.fields.userId.options$);
                  this.config.fields.userId.control.setValue(res);
                }
              })
            },
          }
        ],
      },
      jobTitleId: {
        label: '::GoldenOwl:JobTitle',
        search: true,
      },

      fatherName: {
        label: '::GoldenOwl:FatherName',
      },
      motherName: {
        label: '::GoldenOwl:MotherName',
      },
      gender: {
        label: '::GoldenOwl:Gender',
      },
      maritalStatus: {
        label: '::GoldenOwl:MaritalStatus',
      },
      militaryServiceStatus: {
        label: '::GoldenOwl:MilitaryServiceStatus',
      },
      nationalNumber: {
        label: '::GoldenOwl:NationalNumber',
      },
    },
  });

  protected contactInfoConfig = contactInformationModel.form({
    title: '::GoldenOwl:ContactInformation',
    submitAction: {
      onSubmit: () => {},
      hiddenSignal: signal(true),
    },
    fields: {
      email: { validators: [emailValidator] },
      workEmail: {
        validators: [emailValidator],
        label: '::GoldenOwl:WorkEmail',
      },

      phoneNumber: {
        validators: [phoneNumberValidator],
        hint: this._phoneNumberHint,
      },
      workPhoneNumber: {
        validators: [phoneNumberValidator],
        hint: this._phoneNumberHint,
        label: '::GoldenOwl:WorkPhoneNumber',
      },
    },
    viewFunc: () => this.contactInfo$,
  });

  protected emergencyContactInfoConfig = emergencyContactInformationModel.form({
    title: '::GoldenOwl:EmergencyContactInformation',
    initialRequired: true,
    submitAction: {
      onSubmit: () => {},
      hiddenSignal: signal(true),
    },
    fields: {
      emergencyPhone: {
        validators: [requiredValidator, phoneNumberValidator],
        hint: this._phoneNumberHint,
        label: '::GoldenOwl:EmergencyPhone',
      },
      contactName: {
        label: '::GoldenOwl:ContactName',
      },
      relationShip: {
        label: '::GoldenOwl:RelationShip',
      },
    },
    viewFunc: () => this.emergencyContactInfo$,
  })
}
