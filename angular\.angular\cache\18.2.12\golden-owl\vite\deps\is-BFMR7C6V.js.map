{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/is.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    t = parseInt(val.toString().replace(/^[^.]*\\.?|0+$/g, ''), 10) || 0;\n  if (t === 0 && i % 10 === 1 && !(i % 100 === 11) || !(t === 0)) return 1;\n  return 5;\n}\nexport default [\"is\", [[\"f.\", \"e.\"], [\"f.h.\", \"e.h.\"], u], [[\"f.h.\", \"e.h.\"], u, u], [[\"S\", \"M\", \"Þ\", \"M\", \"F\", \"F\", \"L\"], [\"sun.\", \"mán.\", \"þri.\", \"mið.\", \"fim.\", \"fös.\", \"lau.\"], [\"sunnudagur\", \"mánudagur\", \"þriðjudagur\", \"miðvikudagur\", \"fimmtudagur\", \"föstudagur\", \"laugardagur\"], [\"su.\", \"má.\", \"þr.\", \"mi.\", \"fi.\", \"fö.\", \"la.\"]], u, [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"Á\", \"S\", \"O\", \"N\", \"D\"], [\"jan.\", \"feb.\", \"mar.\", \"apr.\", \"maí\", \"jún.\", \"júl.\", \"ágú.\", \"sep.\", \"okt.\", \"nóv.\", \"des.\"], [\"janúar\", \"febrúar\", \"mars\", \"apríl\", \"maí\", \"júní\", \"júlí\", \"ágúst\", \"september\", \"október\", \"nóvember\", \"desember\"]], u, [[\"f.k.\", \"e.k.\"], [\"f.Kr.\", \"e.Kr.\"], [\"fyrir Krist\", \"eftir Krist\"]], 1, [6, 0], [\"d.M.y\", \"d. MMM y\", \"d. MMMM y\", \"EEEE, d. MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1}, {0}\", u, \"{1} 'kl'. {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"ISK\", \"ISK\", \"íslensk króna\", {\n  \"AUD\": [u, \"$\"],\n  \"BRL\": [u, \"R$\"],\n  \"CAD\": [u, \"$\"],\n  \"EUR\": [u, \"€\"],\n  \"GBP\": [u, \"£\"],\n  \"INR\": [u, \"₹\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"KRW\": [u, \"₩\"],\n  \"MXN\": [u, \"$\"],\n  \"NZD\": [u, \"$\"],\n  \"PHP\": [u, \"₱\"],\n  \"TWD\": [u, \"NT$\"],\n  \"USD\": [u, \"$\"],\n  \"VND\": [u, \"₫\"]\n}, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACnB,QAAM,IAAI,KACR,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,GAC5B,IAAI,SAAS,IAAI,SAAS,EAAE,QAAQ,kBAAkB,EAAE,GAAG,EAAE,KAAK;AACpE,MAAI,MAAM,KAAK,IAAI,OAAO,KAAK,EAAE,IAAI,QAAQ,OAAO,EAAE,MAAM,GAAI,QAAO;AACvE,SAAO;AACT;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,IAAI,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,GAAG,CAAC,cAAc,aAAa,eAAe,gBAAgB,eAAe,cAAc,aAAa,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,GAAG,CAAC,UAAU,WAAW,QAAQ,SAAS,OAAO,QAAQ,QAAQ,SAAS,aAAa,WAAW,YAAY,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,MAAM,GAAG,CAAC,SAAS,OAAO,GAAG,CAAC,eAAe,aAAa,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,YAAY,aAAa,iBAAiB,GAAG,CAAC,SAAS,YAAY,cAAc,eAAe,GAAG,CAAC,YAAY,GAAG,iBAAiB,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,UAAU,cAAc,KAAK,GAAG,OAAO,OAAO,iBAAiB;AAAA,EACh+B,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAChB,GAAG,OAAO,MAAM;", "names": []}