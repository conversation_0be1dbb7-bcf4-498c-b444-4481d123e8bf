{"version": 3, "sources": ["../../../../../../node_modules/apextree/apextree.es.min.js"], "sourcesContent": ["function count(node) {\n  var sum = 0,\n    children = node.children,\n    i = children && children.length;\n  if (!i) sum = 1;else while (--i >= 0) sum += children[i].value;\n  node.value = sum;\n}\nfunction node_count() {\n  return this.eachAfter(count);\n}\nfunction node_each(callback) {\n  var node = this,\n    current,\n    next2 = [node],\n    children,\n    i,\n    n;\n  do {\n    current = next2.reverse(), next2 = [];\n    while (node = current.pop()) {\n      callback(node), children = node.children;\n      if (children) for (i = 0, n = children.length; i < n; ++i) {\n        next2.push(children[i]);\n      }\n    }\n  } while (next2.length);\n  return this;\n}\nfunction node_eachBefore(callback) {\n  var node = this,\n    nodes = [node],\n    children,\n    i;\n  while (node = nodes.pop()) {\n    callback(node), children = node.children;\n    if (children) for (i = children.length - 1; i >= 0; --i) {\n      nodes.push(children[i]);\n    }\n  }\n  return this;\n}\nfunction node_eachAfter(callback) {\n  var node = this,\n    nodes = [node],\n    next2 = [],\n    children,\n    i,\n    n;\n  while (node = nodes.pop()) {\n    next2.push(node), children = node.children;\n    if (children) for (i = 0, n = children.length; i < n; ++i) {\n      nodes.push(children[i]);\n    }\n  }\n  while (node = next2.pop()) {\n    callback(node);\n  }\n  return this;\n}\nfunction node_sum(value) {\n  return this.eachAfter(function (node) {\n    var sum = +value(node.data) || 0,\n      children = node.children,\n      i = children && children.length;\n    while (--i >= 0) sum += children[i].value;\n    node.value = sum;\n  });\n}\nfunction node_sort(compare) {\n  return this.eachBefore(function (node) {\n    if (node.children) {\n      node.children.sort(compare);\n    }\n  });\n}\nfunction node_path(end) {\n  var start = this,\n    ancestor = leastCommonAncestor(start, end),\n    nodes = [start];\n  while (start !== ancestor) {\n    start = start.parent;\n    nodes.push(start);\n  }\n  var k = nodes.length;\n  while (end !== ancestor) {\n    nodes.splice(k, 0, end);\n    end = end.parent;\n  }\n  return nodes;\n}\nfunction leastCommonAncestor(a, b) {\n  if (a === b) return a;\n  var aNodes = a.ancestors(),\n    bNodes = b.ancestors(),\n    c = null;\n  a = aNodes.pop();\n  b = bNodes.pop();\n  while (a === b) {\n    c = a;\n    a = aNodes.pop();\n    b = bNodes.pop();\n  }\n  return c;\n}\nfunction node_ancestors() {\n  var node = this,\n    nodes = [node];\n  while (node = node.parent) {\n    nodes.push(node);\n  }\n  return nodes;\n}\nfunction node_descendants() {\n  var nodes = [];\n  this.each(function (node) {\n    nodes.push(node);\n  });\n  return nodes;\n}\nfunction node_leaves() {\n  var leaves = [];\n  this.eachBefore(function (node) {\n    if (!node.children) {\n      leaves.push(node);\n    }\n  });\n  return leaves;\n}\nfunction node_links() {\n  var root2 = this,\n    links = [];\n  root2.each(function (node) {\n    if (node !== root2) {\n      links.push({\n        source: node.parent,\n        target: node\n      });\n    }\n  });\n  return links;\n}\nfunction hierarchy(data2, children) {\n  var root2 = new Node(data2),\n    valued = +data2.value && (root2.value = data2.value),\n    node,\n    nodes = [root2],\n    child,\n    childs,\n    i,\n    n;\n  if (children == null) children = defaultChildren;\n  while (node = nodes.pop()) {\n    if (valued) node.value = +node.data.value;\n    if ((childs = children(node.data)) && (n = childs.length)) {\n      node.children = new Array(n);\n      for (i = n - 1; i >= 0; --i) {\n        nodes.push(child = node.children[i] = new Node(childs[i]));\n        child.parent = node;\n        child.depth = node.depth + 1;\n      }\n    }\n  }\n  return root2.eachBefore(computeHeight);\n}\nfunction node_copy() {\n  return hierarchy(this).eachBefore(copyData);\n}\nfunction defaultChildren(d) {\n  return d.children;\n}\nfunction copyData(node) {\n  node.data = node.data.data;\n}\nfunction computeHeight(node) {\n  var height2 = 0;\n  do node.height = height2; while ((node = node.parent) && node.height < ++height2);\n}\nfunction Node(data2) {\n  this.data = data2;\n  this.depth = this.height = 0;\n  this.parent = null;\n}\nNode.prototype = hierarchy.prototype = {\n  constructor: Node,\n  count: node_count,\n  each: node_each,\n  eachAfter: node_eachAfter,\n  eachBefore: node_eachBefore,\n  sum: node_sum,\n  sort: node_sort,\n  path: node_path,\n  ancestors: node_ancestors,\n  descendants: node_descendants,\n  leaves: node_leaves,\n  links: node_links,\n  copy: node_copy\n};\nconst name = \"d3-flextree\";\nconst version$1 = \"2.1.2\";\nconst main = \"build/d3-flextree.js\";\nconst module = \"index\";\nconst author = {\n  name: \"Chris Maloney\",\n  url: \"http://chrismaloney.org\"\n};\nconst description = \"Flexible tree layout algorithm that allows for variable node sizes.\";\nconst keywords = [\"d3\", \"d3-module\", \"layout\", \"tree\", \"hierarchy\", \"d3-hierarchy\", \"plugin\", \"d3-plugin\", \"infovis\", \"visualization\", \"2d\"];\nconst homepage = \"https://github.com/klortho/d3-flextree\";\nconst license = \"WTFPL\";\nconst repository = {\n  type: \"git\",\n  url: \"https://github.com/klortho/d3-flextree.git\"\n};\nconst scripts = {\n  clean: \"rm -rf build demo test\",\n  \"build:demo\": \"rollup -c --environment BUILD:demo\",\n  \"build:dev\": \"rollup -c --environment BUILD:dev\",\n  \"build:prod\": \"rollup -c --environment BUILD:prod\",\n  \"build:test\": \"rollup -c --environment BUILD:test\",\n  build: \"rollup -c\",\n  lint: \"eslint index.js src\",\n  \"test:main\": \"node test/bundle.js\",\n  \"test:browser\": \"node test/browser-tests.js\",\n  test: \"npm-run-all test:*\",\n  prepare: \"npm-run-all clean build lint test\"\n};\nconst dependencies = {\n  \"d3-hierarchy\": \"^1.1.5\"\n};\nconst devDependencies = {\n  \"babel-plugin-external-helpers\": \"^6.22.0\",\n  \"babel-preset-es2015-rollup\": \"^3.0.0\",\n  d3: \"^4.13.0\",\n  \"d3-selection-multi\": \"^1.0.1\",\n  eslint: \"^4.19.1\",\n  jsdom: \"^11.6.2\",\n  \"npm-run-all\": \"^4.1.2\",\n  rollup: \"^0.55.3\",\n  \"rollup-plugin-babel\": \"^2.7.1\",\n  \"rollup-plugin-commonjs\": \"^8.0.2\",\n  \"rollup-plugin-copy\": \"^0.2.3\",\n  \"rollup-plugin-json\": \"^2.3.0\",\n  \"rollup-plugin-node-resolve\": \"^3.0.2\",\n  \"rollup-plugin-uglify\": \"^3.0.0\",\n  \"uglify-es\": \"^3.3.9\"\n};\nconst packageInfo = {\n  name,\n  version: version$1,\n  main,\n  module,\n  \"jsnext:main\": \"index\",\n  author,\n  description,\n  keywords,\n  homepage,\n  license,\n  repository,\n  scripts,\n  dependencies,\n  devDependencies\n};\nconst {\n  version\n} = packageInfo;\nconst defaults = Object.freeze({\n  children: data2 => data2.children,\n  nodeSize: node => node.data.size,\n  spacing: 0\n});\nfunction flextree(options) {\n  const opts = Object.assign({}, defaults, options);\n  function accessor(name2) {\n    const opt = opts[name2];\n    return typeof opt === \"function\" ? opt : () => opt;\n  }\n  function layout(tree) {\n    const wtree = wrap(getWrapper(), tree, node => node.children);\n    wtree.update();\n    return wtree.data;\n  }\n  function getFlexNode() {\n    const nodeSize = accessor(\"nodeSize\");\n    const spacing = accessor(\"spacing\");\n    return class FlexNode extends hierarchy.prototype.constructor {\n      constructor(data2) {\n        super(data2);\n      }\n      copy() {\n        const c = wrap(this.constructor, this, node => node.children);\n        c.each(node => node.data = node.data.data);\n        return c;\n      }\n      get size() {\n        return nodeSize(this);\n      }\n      spacing(oNode) {\n        return spacing(this, oNode);\n      }\n      get nodes() {\n        return this.descendants();\n      }\n      get xSize() {\n        return this.size[0];\n      }\n      get ySize() {\n        return this.size[1];\n      }\n      get top() {\n        return this.y;\n      }\n      get bottom() {\n        return this.y + this.ySize;\n      }\n      get left() {\n        return this.x - this.xSize / 2;\n      }\n      get right() {\n        return this.x + this.xSize / 2;\n      }\n      get root() {\n        const ancs = this.ancestors();\n        return ancs[ancs.length - 1];\n      }\n      get numChildren() {\n        return this.hasChildren ? this.children.length : 0;\n      }\n      get hasChildren() {\n        return !this.noChildren;\n      }\n      get noChildren() {\n        return this.children === null;\n      }\n      get firstChild() {\n        return this.hasChildren ? this.children[0] : null;\n      }\n      get lastChild() {\n        return this.hasChildren ? this.children[this.numChildren - 1] : null;\n      }\n      get extents() {\n        return (this.children || []).reduce((acc, kid) => FlexNode.maxExtents(acc, kid.extents), this.nodeExtents);\n      }\n      get nodeExtents() {\n        return {\n          top: this.top,\n          bottom: this.bottom,\n          left: this.left,\n          right: this.right\n        };\n      }\n      static maxExtents(e0, e1) {\n        return {\n          top: Math.min(e0.top, e1.top),\n          bottom: Math.max(e0.bottom, e1.bottom),\n          left: Math.min(e0.left, e1.left),\n          right: Math.max(e0.right, e1.right)\n        };\n      }\n    };\n  }\n  function getWrapper() {\n    const FlexNode = getFlexNode();\n    const nodeSize = accessor(\"nodeSize\");\n    const spacing = accessor(\"spacing\");\n    return class extends FlexNode {\n      constructor(data2) {\n        super(data2);\n        Object.assign(this, {\n          x: 0,\n          y: 0,\n          relX: 0,\n          prelim: 0,\n          shift: 0,\n          change: 0,\n          lExt: this,\n          lExtRelX: 0,\n          lThr: null,\n          rExt: this,\n          rExtRelX: 0,\n          rThr: null\n        });\n      }\n      get size() {\n        return nodeSize(this.data);\n      }\n      spacing(oNode) {\n        return spacing(this.data, oNode.data);\n      }\n      get x() {\n        return this.data.x;\n      }\n      set x(v) {\n        this.data.x = v;\n      }\n      get y() {\n        return this.data.y;\n      }\n      set y(v) {\n        this.data.y = v;\n      }\n      update() {\n        layoutChildren(this);\n        resolveX(this);\n        return this;\n      }\n    };\n  }\n  function wrap(FlexClass, treeData, children) {\n    const _wrap = (data2, parent) => {\n      const node = new FlexClass(data2);\n      Object.assign(node, {\n        parent,\n        depth: parent === null ? 0 : parent.depth + 1,\n        height: 0,\n        length: 1\n      });\n      const kidsData = children(data2) || [];\n      node.children = kidsData.length === 0 ? null : kidsData.map(kd => _wrap(kd, node));\n      if (node.children) {\n        Object.assign(node, node.children.reduce((hl, kid) => ({\n          height: Math.max(hl.height, kid.height + 1),\n          length: hl.length + kid.length\n        }), node));\n      }\n      return node;\n    };\n    return _wrap(treeData, null);\n  }\n  Object.assign(layout, {\n    nodeSize(arg) {\n      return arguments.length ? (opts.nodeSize = arg, layout) : opts.nodeSize;\n    },\n    spacing(arg) {\n      return arguments.length ? (opts.spacing = arg, layout) : opts.spacing;\n    },\n    children(arg) {\n      return arguments.length ? (opts.children = arg, layout) : opts.children;\n    },\n    hierarchy(treeData, children) {\n      const kids = typeof children === \"undefined\" ? opts.children : children;\n      return wrap(getFlexNode(), treeData, kids);\n    },\n    dump(tree) {\n      const nodeSize = accessor(\"nodeSize\");\n      const _dump = i0 => node => {\n        const i1 = i0 + \"  \";\n        const i2 = i0 + \"    \";\n        const {\n          x: x2,\n          y: y2\n        } = node;\n        const size2 = nodeSize(node);\n        const kids = node.children || [];\n        const kdumps = kids.length === 0 ? \" \" : `,${i1}children: [${i2}${kids.map(_dump(i2)).join(i2)}${i1}],${i0}`;\n        return `{ size: [${size2.join(\", \")}],${i1}x: ${x2}, y: ${y2}${kdumps}},`;\n      };\n      return _dump(\"\\n\")(tree);\n    }\n  });\n  return layout;\n}\nflextree.version = version;\nconst layoutChildren = (w, y2 = 0) => {\n  w.y = y2;\n  (w.children || []).reduce((acc, kid) => {\n    const [i, lastLows] = acc;\n    layoutChildren(kid, w.y + w.ySize);\n    const lowY = (i === 0 ? kid.lExt : kid.rExt).bottom;\n    if (i !== 0) separate(w, i, lastLows);\n    const lows = updateLows(lowY, i, lastLows);\n    return [i + 1, lows];\n  }, [0, null]);\n  shiftChange(w);\n  positionRoot(w);\n  return w;\n};\nconst resolveX = (w, prevSum, parentX) => {\n  if (typeof prevSum === \"undefined\") {\n    prevSum = -w.relX - w.prelim;\n    parentX = 0;\n  }\n  const sum = prevSum + w.relX;\n  w.relX = sum + w.prelim - parentX;\n  w.prelim = 0;\n  w.x = parentX + w.relX;\n  (w.children || []).forEach(k => resolveX(k, sum, w.x));\n  return w;\n};\nconst shiftChange = w => {\n  (w.children || []).reduce((acc, child) => {\n    const [lastShiftSum, lastChangeSum] = acc;\n    const shiftSum = lastShiftSum + child.shift;\n    const changeSum = lastChangeSum + shiftSum + child.change;\n    child.relX += changeSum;\n    return [shiftSum, changeSum];\n  }, [0, 0]);\n};\nconst separate = (w, i, lows) => {\n  const lSib = w.children[i - 1];\n  const curSubtree = w.children[i];\n  let rContour = lSib;\n  let rSumMods = lSib.relX;\n  let lContour = curSubtree;\n  let lSumMods = curSubtree.relX;\n  let isFirst = true;\n  while (rContour && lContour) {\n    if (rContour.bottom > lows.lowY) lows = lows.next;\n    const dist = rSumMods + rContour.prelim - (lSumMods + lContour.prelim) + rContour.xSize / 2 + lContour.xSize / 2 + rContour.spacing(lContour);\n    if (dist > 0 || dist < 0 && isFirst) {\n      lSumMods += dist;\n      moveSubtree(curSubtree, dist);\n      distributeExtra(w, i, lows.index, dist);\n    }\n    isFirst = false;\n    const rightBottom = rContour.bottom;\n    const leftBottom = lContour.bottom;\n    if (rightBottom <= leftBottom) {\n      rContour = nextRContour(rContour);\n      if (rContour) rSumMods += rContour.relX;\n    }\n    if (rightBottom >= leftBottom) {\n      lContour = nextLContour(lContour);\n      if (lContour) lSumMods += lContour.relX;\n    }\n  }\n  if (!rContour && lContour) setLThr(w, i, lContour, lSumMods);else if (rContour && !lContour) setRThr(w, i, rContour, rSumMods);\n};\nconst moveSubtree = (subtree, distance) => {\n  subtree.relX += distance;\n  subtree.lExtRelX += distance;\n  subtree.rExtRelX += distance;\n};\nconst distributeExtra = (w, curSubtreeI, leftSibI, dist) => {\n  const curSubtree = w.children[curSubtreeI];\n  const n = curSubtreeI - leftSibI;\n  if (n > 1) {\n    const delta = dist / n;\n    w.children[leftSibI + 1].shift += delta;\n    curSubtree.shift -= delta;\n    curSubtree.change -= dist - delta;\n  }\n};\nconst nextLContour = w => {\n  return w.hasChildren ? w.firstChild : w.lThr;\n};\nconst nextRContour = w => {\n  return w.hasChildren ? w.lastChild : w.rThr;\n};\nconst setLThr = (w, i, lContour, lSumMods) => {\n  const firstChild = w.firstChild;\n  const lExt = firstChild.lExt;\n  const curSubtree = w.children[i];\n  lExt.lThr = lContour;\n  const diff = lSumMods - lContour.relX - firstChild.lExtRelX;\n  lExt.relX += diff;\n  lExt.prelim -= diff;\n  firstChild.lExt = curSubtree.lExt;\n  firstChild.lExtRelX = curSubtree.lExtRelX;\n};\nconst setRThr = (w, i, rContour, rSumMods) => {\n  const curSubtree = w.children[i];\n  const rExt = curSubtree.rExt;\n  const lSib = w.children[i - 1];\n  rExt.rThr = rContour;\n  const diff = rSumMods - rContour.relX - curSubtree.rExtRelX;\n  rExt.relX += diff;\n  rExt.prelim -= diff;\n  curSubtree.rExt = lSib.rExt;\n  curSubtree.rExtRelX = lSib.rExtRelX;\n};\nconst positionRoot = w => {\n  if (w.hasChildren) {\n    const k0 = w.firstChild;\n    const kf = w.lastChild;\n    const prelim = (k0.prelim + k0.relX - k0.xSize / 2 + kf.relX + kf.prelim + kf.xSize / 2) / 2;\n    Object.assign(w, {\n      prelim,\n      lExt: k0.lExt,\n      lExtRelX: k0.lExtRelX,\n      rExt: kf.rExt,\n      rExtRelX: kf.rExtRelX\n    });\n  }\n};\nconst updateLows = (lowY, index, lastLows) => {\n  while (lastLows !== null && lowY >= lastLows.lowY) lastLows = lastLows.next;\n  return {\n    lowY,\n    index,\n    next: lastLows\n  };\n};\nconst curvedEdgesHorizontal = (s, t, m) => {\n  const x2 = s.x;\n  const y2 = s.y;\n  const ex = t.x;\n  const ey = t.y;\n  const mx = (m == null ? void 0 : m.x) ?? x2;\n  const my = (m == null ? void 0 : m.y) ?? y2;\n  const xrvs = ex - x2 < 0 ? -1 : 1;\n  const yrvs = ey - y2 < 0 ? -1 : 1;\n  const rdef = 35;\n  let r = Math.abs(ex - x2) / 2 < rdef ? Math.abs(ex - x2) / 2 : rdef;\n  r = Math.abs(ey - y2) / 2 < r ? Math.abs(ey - y2) / 2 : r;\n  const w = Math.abs(ex - x2) / 2 - r;\n  const pathArray = [`M ${mx} ${my}`, `L ${mx} ${y2}`, `L ${x2} ${y2}`, `L ${x2 + w * xrvs} ${y2}`, `C ${x2 + w * xrvs + r * xrvs} ${y2} ${x2 + w * xrvs + r * xrvs} ${y2} ${x2 + w * xrvs + r * xrvs} ${y2 + r * yrvs}`, `L ${x2 + w * xrvs + r * xrvs} ${ey - r * yrvs}`, `C ${x2 + w * xrvs + r * xrvs} ${ey} ${x2 + w * xrvs + r * xrvs} ${ey} ${ex - w * xrvs} ${ey}`, `L ${ex} ${ey}`];\n  return pathArray.join(\" \");\n};\nconst curvedEdgesVertical = (s, t, m, offsets = {\n  sy: 0\n}) => {\n  const x2 = s.x;\n  let y2 = s.y;\n  const ex = t.x;\n  const ey = t.y;\n  const mx = (m == null ? void 0 : m.x) ?? x2;\n  const my = (m == null ? void 0 : m.y) ?? y2;\n  const xrvs = ex - x2 < 0 ? -1 : 1;\n  const yrvs = ey - y2 < 0 ? -1 : 1;\n  y2 += offsets.sy;\n  const rdef = 35;\n  let r = Math.abs(ex - x2) / 2 < rdef ? Math.abs(ex - x2) / 2 : rdef;\n  r = Math.abs(ey - y2) / 2 < r ? Math.abs(ey - y2) / 2 : r;\n  const h = Math.abs(ey - y2) / 2 - r;\n  const w = Math.abs(ex - x2) - r * 2;\n  const pathArray = [`M ${mx} ${my}`, `L ${x2} ${my}`, `L ${x2} ${y2}`, `L ${x2} ${y2 + h * yrvs}`, `C  ${x2} ${y2 + h * yrvs + r * yrvs} ${x2} ${y2 + h * yrvs + r * yrvs} ${x2 + r * xrvs} ${y2 + h * yrvs + r * yrvs}`, `L ${x2 + w * xrvs + r * xrvs} ${y2 + h * yrvs + r * yrvs}`, `C  ${ex} ${y2 + h * yrvs + r * yrvs} ${ex} ${y2 + h * yrvs + r * yrvs} ${ex} ${ey - h * yrvs}`, `L ${ex} ${ey}`];\n  return pathArray.join(\" \");\n};\nconst DirectionConfig = {\n  top: {\n    containerX: ({\n      width: width2\n    }) => width2 / 2,\n    containerY: () => 0,\n    edgeX: ({\n      node,\n      nodeWidth\n    }) => node.x + nodeWidth / 2,\n    edgeY: ({\n      node\n    }) => node.y,\n    edgeMidX: ({\n      node,\n      nodeWidth\n    }) => node.x + nodeWidth / 2,\n    edgeMidY: ({\n      node\n    }) => node.y,\n    edgeParentX: ({\n      parent,\n      nodeWidth\n    }) => parent.x + nodeWidth / 2,\n    edgeParentY: ({\n      parent,\n      nodeHeight\n    }) => parent.y + nodeHeight,\n    nodeFlexSize: ({\n      nodeWidth,\n      nodeHeight,\n      siblingSpacing,\n      childrenSpacing\n    }) => {\n      return [nodeWidth + siblingSpacing, nodeHeight + childrenSpacing];\n    },\n    calculateEdge: curvedEdgesVertical,\n    swap: node => ({\n      x: node.left,\n      y: node.top\n    }),\n    viewBoxDimensions: ({\n      rootNode,\n      childrenSpacing,\n      siblingSpacing\n    }) => {\n      if (!rootNode) return {\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n      };\n      const {\n        left,\n        top,\n        right,\n        bottom\n      } = rootNode.extents;\n      const width2 = Math.abs(left) + Math.abs(right);\n      const height2 = Math.abs(top) + Math.abs(bottom);\n      const x2 = Math.abs(left) + siblingSpacing / 2;\n      const y2 = (rootNode.ySize - childrenSpacing) / 2;\n      return {\n        x: -x2,\n        y: -y2,\n        width: width2,\n        height: height2\n      };\n    }\n  },\n  bottom: {\n    containerX: ({\n      width: width2\n    }) => width2 / 2,\n    containerY: ({\n      height: height2,\n      nodeHeight\n    }) => height2 - nodeHeight - 10,\n    edgeX: ({\n      node,\n      nodeWidth\n    }) => node.x + nodeWidth / 2,\n    edgeY: ({\n      node,\n      nodeHeight\n    }) => node.y + nodeHeight,\n    edgeMidX: ({\n      node,\n      nodeWidth\n    }) => node.x + nodeWidth / 2,\n    edgeMidY: ({\n      node,\n      nodeHeight\n    }) => node.y + nodeHeight,\n    edgeParentX: ({\n      parent,\n      nodeWidth\n    }) => parent.x + nodeWidth / 2,\n    edgeParentY: ({\n      parent\n    }) => parent.y,\n    nodeFlexSize: ({\n      nodeWidth,\n      nodeHeight,\n      siblingSpacing,\n      childrenSpacing\n    }) => {\n      return [nodeWidth + siblingSpacing, nodeHeight + childrenSpacing];\n    },\n    calculateEdge: curvedEdgesVertical,\n    swap: node => ({\n      ...node,\n      y: -node.y\n    }),\n    viewBoxDimensions: ({\n      rootNode,\n      childrenSpacing,\n      siblingSpacing\n    }) => {\n      if (!rootNode) return {\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n      };\n      const {\n        left,\n        top,\n        right,\n        bottom\n      } = rootNode.extents;\n      const width2 = Math.abs(left) + Math.abs(right);\n      const height2 = Math.abs(top) + Math.abs(bottom);\n      const x2 = Math.abs(left) - (rootNode.xSize - siblingSpacing) / 2;\n      const y2 = height2 - rootNode.ySize + childrenSpacing / 2;\n      return {\n        x: -x2,\n        y: -y2,\n        width: width2,\n        height: height2\n      };\n    }\n  },\n  left: {\n    containerX: () => 10,\n    containerY: ({\n      height: height2\n    }) => height2 / 2,\n    edgeX: ({\n      node\n    }) => node.x,\n    edgeY: ({\n      node,\n      nodeHeight\n    }) => node.y + nodeHeight / 2,\n    edgeMidX: ({\n      node\n    }) => node.x,\n    edgeMidY: ({\n      node,\n      nodeHeight\n    }) => node.y + nodeHeight / 2,\n    edgeParentX: ({\n      parent,\n      nodeWidth\n    }) => parent.x + nodeWidth,\n    edgeParentY: ({\n      parent,\n      nodeHeight\n    }) => parent.y + nodeHeight / 2,\n    nodeFlexSize: ({\n      nodeWidth,\n      nodeHeight,\n      siblingSpacing,\n      childrenSpacing\n    }) => {\n      return [nodeHeight + siblingSpacing, nodeWidth + childrenSpacing];\n    },\n    calculateEdge: curvedEdgesHorizontal,\n    swap: node => ({\n      ...node,\n      x: node.y,\n      y: node.x\n    }),\n    viewBoxDimensions: ({\n      rootNode,\n      childrenSpacing,\n      siblingSpacing\n    }) => {\n      if (!rootNode) return {\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n      };\n      const {\n        left,\n        top,\n        right,\n        bottom\n      } = rootNode.extents;\n      const width2 = Math.abs(top) + Math.abs(bottom);\n      const height2 = Math.abs(left) + Math.abs(right);\n      const x2 = Math.abs(top) + childrenSpacing / 2;\n      const y2 = Math.abs(left) - siblingSpacing;\n      return {\n        x: -x2,\n        y: -y2,\n        width: width2,\n        height: height2\n      };\n    }\n  },\n  right: {\n    containerX: ({\n      width: width2,\n      nodeWidth\n    }) => width2 - nodeWidth - 10,\n    containerY: ({\n      height: height2\n    }) => height2 / 2,\n    edgeX: ({\n      node,\n      nodeWidth\n    }) => node.x + nodeWidth,\n    edgeY: ({\n      node,\n      nodeHeight\n    }) => node.y + nodeHeight / 2,\n    edgeMidX: ({\n      node,\n      nodeWidth\n    }) => node.x + nodeWidth,\n    edgeMidY: ({\n      node,\n      nodeHeight\n    }) => node.y + nodeHeight / 2,\n    edgeParentX: ({\n      parent\n    }) => parent.x,\n    edgeParentY: ({\n      parent,\n      nodeHeight\n    }) => parent.y + nodeHeight / 2,\n    nodeFlexSize: ({\n      nodeWidth,\n      nodeHeight,\n      siblingSpacing,\n      childrenSpacing\n    }) => {\n      return [nodeHeight + siblingSpacing, nodeWidth + childrenSpacing];\n    },\n    calculateEdge: curvedEdgesHorizontal,\n    swap: node => ({\n      ...node,\n      x: -node.y,\n      y: node.x\n    }),\n    viewBoxDimensions: ({\n      rootNode,\n      siblingSpacing,\n      childrenSpacing\n    }) => {\n      if (!rootNode) return {\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n      };\n      const {\n        left,\n        top,\n        right,\n        bottom\n      } = rootNode.extents;\n      const width2 = Math.abs(top) + Math.abs(bottom);\n      const height2 = Math.abs(left) + Math.abs(right);\n      const x2 = width2 - rootNode.ySize + childrenSpacing / 2;\n      const y2 = Math.abs(left) - siblingSpacing;\n      return {\n        x: -x2,\n        y: -y2,\n        width: width2,\n        height: height2\n      };\n    }\n  }\n};\nconst getEdge = (node, nodeWidth, nodeHeight, graphDirection) => {\n  if (!node || !node.parent) return null;\n  const {\n    edgeX,\n    edgeY,\n    edgeParentX,\n    edgeParentY,\n    edgeMidX,\n    edgeMidY,\n    calculateEdge,\n    swap\n  } = DirectionConfig[graphDirection];\n  const newNode = swap(node);\n  const newParent = swap(node.parent);\n  const child = {\n    x: edgeX({\n      node: newNode,\n      nodeWidth,\n      nodeHeight\n    }),\n    y: edgeY({\n      node: newNode,\n      nodeWidth,\n      nodeHeight\n    })\n  };\n  const parent = {\n    x: edgeParentX({\n      parent: newParent,\n      nodeWidth,\n      nodeHeight\n    }),\n    y: edgeParentY({\n      parent: newParent,\n      nodeWidth,\n      nodeHeight\n    })\n  };\n  const mid = {\n    x: edgeMidX({\n      node: newNode,\n      nodeWidth,\n      nodeHeight\n    }),\n    y: edgeMidY({\n      node: newNode,\n      nodeWidth,\n      nodeHeight\n    })\n  };\n  return calculateEdge(child, parent, mid, {\n    sy: 0\n  });\n};\nconst setAttributes = (element, attrs2 = {}) => {\n  for (const key in attrs2) {\n    element == null ? void 0 : element.setAttribute(key, attrs2[key]);\n  }\n};\nconst ExpandCollapseButtonSize = 14;\nconst highlightToPath = (nodes, selfNode, isHighlighted, options) => {\n  var _a;\n  const nodeOptions = selfNode == null ? void 0 : selfNode.data.options;\n  const nodeBorderWidth = (nodeOptions == null ? void 0 : nodeOptions.borderWidth) || options.borderWidth;\n  let borderColor = (nodeOptions == null ? void 0 : nodeOptions.borderColor) || options.borderColor;\n  let backgroundColor = (nodeOptions == null ? void 0 : nodeOptions.nodeBGColor) || options.nodeBGColor;\n  if (isHighlighted) {\n    borderColor = (nodeOptions == null ? void 0 : nodeOptions.borderColorHover) || options.borderColorHover;\n    backgroundColor = (nodeOptions == null ? void 0 : nodeOptions.nodeBGColorHover) || options.nodeBGColorHover;\n  }\n  const selfContentElement = document.querySelector(`[data-self='${selfNode.data.id}'] foreignObject div`);\n  if (selfContentElement) {\n    selfContentElement.style.borderWidth = `${nodeBorderWidth}px`;\n    selfContentElement.style.borderColor = borderColor;\n    selfContentElement.style.backgroundColor = backgroundColor;\n  }\n  if (selfNode.parent) {\n    const edge = document.getElementById(`${selfNode.data.id}-${(_a = selfNode.parent) == null ? void 0 : _a.data.id}`);\n    if (isHighlighted) {\n      setAttributes(edge, {\n        \"stroke-width\": options.edgeWidth + 1,\n        stroke: options.edgeColorHover\n      });\n    } else {\n      setAttributes(edge, {\n        \"stroke-width\": options.edgeWidth,\n        stroke: options.edgeColor\n      });\n    }\n    selfNode.parent && highlightToPath(nodes, selfNode.parent, isHighlighted, options);\n  }\n};\nconst getTooltipStyles = (x2, y2, maxWidth, borderColor, bgColor, addPadding) => {\n  const styles = [\"position: absolute;\", `left: ${x2 + 20}px;`, `top: ${y2 + 20}px;`, `border: 1px solid ${borderColor};`, `border-radius: 5px;`, `max-width: ${maxWidth}px;`, `background-color: ${bgColor};`];\n  if (addPadding) {\n    styles.push(\"padding: 10px;\");\n  }\n  return styles;\n};\nconst generateStyles = (styleObject = {}) => {\n  const styles = [];\n  for (const styleKey in styleObject) {\n    const styleString = `${camelToKebabCase(styleKey)}: ${styleObject[styleKey]};`;\n    styles.push(styleString);\n  }\n  return styles.join(\" \");\n};\nconst getTooltip = (tooltipId = \"apextree-tooltip-container\") => {\n  const tooltipElement = document.getElementById(tooltipId) || document.createElement(\"div\");\n  tooltipElement.id = tooltipId;\n  return tooltipElement;\n};\nconst updateTooltip = (id = \"\", styles, content = \"\") => {\n  const tooltipElement = document.getElementById(id);\n  if (styles) {\n    tooltipElement == null ? void 0 : tooltipElement.setAttribute(\"style\", styles);\n  } else {\n    tooltipElement == null ? void 0 : tooltipElement.removeAttribute(\"style\");\n  }\n  if ((tooltipElement == null ? void 0 : tooltipElement.innerHTML.replaceAll(\"'\", '\"')) !== content.replaceAll(\"'\", '\"')) {\n    tooltipElement && (tooltipElement.innerHTML = content);\n  }\n};\nconst camelToKebabCase = str => {\n  return str.replace(/[A-Z]+(?![a-z])|[A-Z]/g, ($, ofs) => (ofs ? \"-\" : \"\") + $.toLowerCase());\n};\nconst methods$1 = {};\nconst names = [];\nfunction registerMethods(name2, m) {\n  if (Array.isArray(name2)) {\n    for (const _name of name2) {\n      registerMethods(_name, m);\n    }\n    return;\n  }\n  if (typeof name2 === \"object\") {\n    for (const _name in name2) {\n      registerMethods(_name, name2[_name]);\n    }\n    return;\n  }\n  addMethodNames(Object.getOwnPropertyNames(m));\n  methods$1[name2] = Object.assign(methods$1[name2] || {}, m);\n}\nfunction getMethodsFor(name2) {\n  return methods$1[name2] || {};\n}\nfunction getMethodNames() {\n  return [...new Set(names)];\n}\nfunction addMethodNames(_names) {\n  names.push(..._names);\n}\nfunction map(array2, block) {\n  let i;\n  const il = array2.length;\n  const result = [];\n  for (i = 0; i < il; i++) {\n    result.push(block(array2[i]));\n  }\n  return result;\n}\nfunction filter(array2, block) {\n  let i;\n  const il = array2.length;\n  const result = [];\n  for (i = 0; i < il; i++) {\n    if (block(array2[i])) {\n      result.push(array2[i]);\n    }\n  }\n  return result;\n}\nfunction radians(d) {\n  return d % 360 * Math.PI / 180;\n}\nfunction unCamelCase(s) {\n  return s.replace(/([A-Z])/g, function (m, g) {\n    return \"-\" + g.toLowerCase();\n  });\n}\nfunction capitalize(s) {\n  return s.charAt(0).toUpperCase() + s.slice(1);\n}\nfunction proportionalSize(element, width2, height2, box) {\n  if (width2 == null || height2 == null) {\n    box = box || element.bbox();\n    if (width2 == null) {\n      width2 = box.width / box.height * height2;\n    } else if (height2 == null) {\n      height2 = box.height / box.width * width2;\n    }\n  }\n  return {\n    width: width2,\n    height: height2\n  };\n}\nfunction getOrigin(o, element) {\n  const origin = o.origin;\n  let ox = o.ox != null ? o.ox : o.originX != null ? o.originX : \"center\";\n  let oy = o.oy != null ? o.oy : o.originY != null ? o.originY : \"center\";\n  if (origin != null) {\n    [ox, oy] = Array.isArray(origin) ? origin : typeof origin === \"object\" ? [origin.x, origin.y] : [origin, origin];\n  }\n  const condX = typeof ox === \"string\";\n  const condY = typeof oy === \"string\";\n  if (condX || condY) {\n    const {\n      height: height2,\n      width: width2,\n      x: x2,\n      y: y2\n    } = element.bbox();\n    if (condX) {\n      ox = ox.includes(\"left\") ? x2 : ox.includes(\"right\") ? x2 + width2 : x2 + width2 / 2;\n    }\n    if (condY) {\n      oy = oy.includes(\"top\") ? y2 : oy.includes(\"bottom\") ? y2 + height2 : y2 + height2 / 2;\n    }\n  }\n  return [ox, oy];\n}\nconst descriptiveElements = /* @__PURE__ */new Set([\"desc\", \"metadata\", \"title\"]);\nconst isDescriptive = element => descriptiveElements.has(element.nodeName);\nconst writeDataToDom = (element, data2, defaults2 = {}) => {\n  const cloned = {\n    ...data2\n  };\n  for (const key in cloned) {\n    if (cloned[key].valueOf() === defaults2[key]) {\n      delete cloned[key];\n    }\n  }\n  if (Object.keys(cloned).length) {\n    element.node.setAttribute(\"data-svgjs\", JSON.stringify(cloned));\n  } else {\n    element.node.removeAttribute(\"data-svgjs\");\n    element.node.removeAttribute(\"svgjs:data\");\n  }\n};\nconst svg = \"http://www.w3.org/2000/svg\";\nconst html = \"http://www.w3.org/1999/xhtml\";\nconst xmlns = \"http://www.w3.org/2000/xmlns/\";\nconst xlink = \"http://www.w3.org/1999/xlink\";\nconst globals = {\n  window: typeof window === \"undefined\" ? null : window,\n  document: typeof document === \"undefined\" ? null : document\n};\nfunction getWindow() {\n  return globals.window;\n}\nclass Base {\n  // constructor (node/*, {extensions = []} */) {\n  //   // this.tags = []\n  //   //\n  //   // for (let extension of extensions) {\n  //   //   extension.setup.call(this, node)\n  //   //   this.tags.push(extension.name)\n  //   // }\n  // }\n}\nconst elements = {};\nconst root = \"___SYMBOL___ROOT___\";\nfunction create(name2, ns = svg) {\n  return globals.document.createElementNS(ns, name2);\n}\nfunction makeInstance(element, isHTML = false) {\n  if (element instanceof Base) return element;\n  if (typeof element === \"object\") {\n    return adopter(element);\n  }\n  if (element == null) {\n    return new elements[root]();\n  }\n  if (typeof element === \"string\" && element.charAt(0) !== \"<\") {\n    return adopter(globals.document.querySelector(element));\n  }\n  const wrapper = isHTML ? globals.document.createElement(\"div\") : create(\"svg\");\n  wrapper.innerHTML = element;\n  element = adopter(wrapper.firstChild);\n  wrapper.removeChild(wrapper.firstChild);\n  return element;\n}\nfunction nodeOrNew(name2, node) {\n  return node && (node instanceof globals.window.Node || node.ownerDocument && node instanceof node.ownerDocument.defaultView.Node) ? node : create(name2);\n}\nfunction adopt(node) {\n  if (!node) return null;\n  if (node.instance instanceof Base) return node.instance;\n  if (node.nodeName === \"#document-fragment\") {\n    return new elements.Fragment(node);\n  }\n  let className = capitalize(node.nodeName || \"Dom\");\n  if (className === \"LinearGradient\" || className === \"RadialGradient\") {\n    className = \"Gradient\";\n  } else if (!elements[className]) {\n    className = \"Dom\";\n  }\n  return new elements[className](node);\n}\nlet adopter = adopt;\nfunction register(element, name2 = element.name, asRoot = false) {\n  elements[name2] = element;\n  if (asRoot) elements[root] = element;\n  addMethodNames(Object.getOwnPropertyNames(element.prototype));\n  return element;\n}\nfunction getClass(name2) {\n  return elements[name2];\n}\nlet did = 1e3;\nfunction eid(name2) {\n  return \"Svgjs\" + capitalize(name2) + did++;\n}\nfunction assignNewId(node) {\n  for (let i = node.children.length - 1; i >= 0; i--) {\n    assignNewId(node.children[i]);\n  }\n  if (node.id) {\n    node.id = eid(node.nodeName);\n    return node;\n  }\n  return node;\n}\nfunction extend(modules, methods2) {\n  let key, i;\n  modules = Array.isArray(modules) ? modules : [modules];\n  for (i = modules.length - 1; i >= 0; i--) {\n    for (key in methods2) {\n      modules[i].prototype[key] = methods2[key];\n    }\n  }\n}\nfunction wrapWithAttrCheck(fn) {\n  return function (...args) {\n    const o = args[args.length - 1];\n    if (o && o.constructor === Object && !(o instanceof Array)) {\n      return fn.apply(this, args.slice(0, -1)).attr(o);\n    } else {\n      return fn.apply(this, args);\n    }\n  };\n}\nfunction siblings() {\n  return this.parent().children();\n}\nfunction position() {\n  return this.parent().index(this);\n}\nfunction next() {\n  return this.siblings()[this.position() + 1];\n}\nfunction prev() {\n  return this.siblings()[this.position() - 1];\n}\nfunction forward() {\n  const i = this.position();\n  const p = this.parent();\n  p.add(this.remove(), i + 1);\n  return this;\n}\nfunction backward() {\n  const i = this.position();\n  const p = this.parent();\n  p.add(this.remove(), i ? i - 1 : 0);\n  return this;\n}\nfunction front() {\n  const p = this.parent();\n  p.add(this.remove());\n  return this;\n}\nfunction back() {\n  const p = this.parent();\n  p.add(this.remove(), 0);\n  return this;\n}\nfunction before(element) {\n  element = makeInstance(element);\n  element.remove();\n  const i = this.position();\n  this.parent().add(element, i);\n  return this;\n}\nfunction after(element) {\n  element = makeInstance(element);\n  element.remove();\n  const i = this.position();\n  this.parent().add(element, i + 1);\n  return this;\n}\nfunction insertBefore(element) {\n  element = makeInstance(element);\n  element.before(this);\n  return this;\n}\nfunction insertAfter(element) {\n  element = makeInstance(element);\n  element.after(this);\n  return this;\n}\nregisterMethods(\"Dom\", {\n  siblings,\n  position,\n  next,\n  prev,\n  forward,\n  backward,\n  front,\n  back,\n  before,\n  after,\n  insertBefore,\n  insertAfter\n});\nconst numberAndUnit = /^([+-]?(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?)([a-z%]*)$/i;\nconst hex = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i;\nconst rgb = /rgb\\((\\d+),(\\d+),(\\d+)\\)/;\nconst reference = /(#[a-z_][a-z0-9\\-_]*)/i;\nconst transforms = /\\)\\s*,?\\s*/;\nconst whitespace = /\\s/g;\nconst isHex = /^#[a-f0-9]{3}$|^#[a-f0-9]{6}$/i;\nconst isRgb = /^rgb\\(/;\nconst isBlank = /^(\\s+)?$/;\nconst isNumber = /^[+-]?(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i;\nconst isImage = /\\.(jpg|jpeg|png|gif|svg)(\\?[^=]+.*)?/i;\nconst delimiter = /[\\s,]+/;\nconst isPathLetter = /[MLHVCSQTAZ]/i;\nfunction classes() {\n  const attr2 = this.attr(\"class\");\n  return attr2 == null ? [] : attr2.trim().split(delimiter);\n}\nfunction hasClass(name2) {\n  return this.classes().indexOf(name2) !== -1;\n}\nfunction addClass(name2) {\n  if (!this.hasClass(name2)) {\n    const array2 = this.classes();\n    array2.push(name2);\n    this.attr(\"class\", array2.join(\" \"));\n  }\n  return this;\n}\nfunction removeClass(name2) {\n  if (this.hasClass(name2)) {\n    this.attr(\"class\", this.classes().filter(function (c) {\n      return c !== name2;\n    }).join(\" \"));\n  }\n  return this;\n}\nfunction toggleClass(name2) {\n  return this.hasClass(name2) ? this.removeClass(name2) : this.addClass(name2);\n}\nregisterMethods(\"Dom\", {\n  classes,\n  hasClass,\n  addClass,\n  removeClass,\n  toggleClass\n});\nfunction css(style, val) {\n  const ret = {};\n  if (arguments.length === 0) {\n    this.node.style.cssText.split(/\\s*;\\s*/).filter(function (el) {\n      return !!el.length;\n    }).forEach(function (el) {\n      const t = el.split(/\\s*:\\s*/);\n      ret[t[0]] = t[1];\n    });\n    return ret;\n  }\n  if (arguments.length < 2) {\n    if (Array.isArray(style)) {\n      for (const name2 of style) {\n        const cased = name2;\n        ret[name2] = this.node.style.getPropertyValue(cased);\n      }\n      return ret;\n    }\n    if (typeof style === \"string\") {\n      return this.node.style.getPropertyValue(style);\n    }\n    if (typeof style === \"object\") {\n      for (const name2 in style) {\n        this.node.style.setProperty(name2, style[name2] == null || isBlank.test(style[name2]) ? \"\" : style[name2]);\n      }\n    }\n  }\n  if (arguments.length === 2) {\n    this.node.style.setProperty(style, val == null || isBlank.test(val) ? \"\" : val);\n  }\n  return this;\n}\nfunction show() {\n  return this.css(\"display\", \"\");\n}\nfunction hide() {\n  return this.css(\"display\", \"none\");\n}\nfunction visible() {\n  return this.css(\"display\") !== \"none\";\n}\nregisterMethods(\"Dom\", {\n  css,\n  show,\n  hide,\n  visible\n});\nfunction data(a, v, r) {\n  if (a == null) {\n    return this.data(map(filter(this.node.attributes, el => el.nodeName.indexOf(\"data-\") === 0), el => el.nodeName.slice(5)));\n  } else if (a instanceof Array) {\n    const data2 = {};\n    for (const key of a) {\n      data2[key] = this.data(key);\n    }\n    return data2;\n  } else if (typeof a === \"object\") {\n    for (v in a) {\n      this.data(v, a[v]);\n    }\n  } else if (arguments.length < 2) {\n    try {\n      return JSON.parse(this.attr(\"data-\" + a));\n    } catch (e) {\n      return this.attr(\"data-\" + a);\n    }\n  } else {\n    this.attr(\"data-\" + a, v === null ? null : r === true || typeof v === \"string\" || typeof v === \"number\" ? v : JSON.stringify(v));\n  }\n  return this;\n}\nregisterMethods(\"Dom\", {\n  data\n});\nfunction remember(k, v) {\n  if (typeof arguments[0] === \"object\") {\n    for (const key in k) {\n      this.remember(key, k[key]);\n    }\n  } else if (arguments.length === 1) {\n    return this.memory()[k];\n  } else {\n    this.memory()[k] = v;\n  }\n  return this;\n}\nfunction forget() {\n  if (arguments.length === 0) {\n    this._memory = {};\n  } else {\n    for (let i = arguments.length - 1; i >= 0; i--) {\n      delete this.memory()[arguments[i]];\n    }\n  }\n  return this;\n}\nfunction memory() {\n  return this._memory = this._memory || {};\n}\nregisterMethods(\"Dom\", {\n  remember,\n  forget,\n  memory\n});\nfunction sixDigitHex(hex2) {\n  return hex2.length === 4 ? [\"#\", hex2.substring(1, 2), hex2.substring(1, 2), hex2.substring(2, 3), hex2.substring(2, 3), hex2.substring(3, 4), hex2.substring(3, 4)].join(\"\") : hex2;\n}\nfunction componentHex(component) {\n  const integer = Math.round(component);\n  const bounded = Math.max(0, Math.min(255, integer));\n  const hex2 = bounded.toString(16);\n  return hex2.length === 1 ? \"0\" + hex2 : hex2;\n}\nfunction is(object, space) {\n  for (let i = space.length; i--;) {\n    if (object[space[i]] == null) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction getParameters(a, b) {\n  const params = is(a, \"rgb\") ? {\n    _a: a.r,\n    _b: a.g,\n    _c: a.b,\n    _d: 0,\n    space: \"rgb\"\n  } : is(a, \"xyz\") ? {\n    _a: a.x,\n    _b: a.y,\n    _c: a.z,\n    _d: 0,\n    space: \"xyz\"\n  } : is(a, \"hsl\") ? {\n    _a: a.h,\n    _b: a.s,\n    _c: a.l,\n    _d: 0,\n    space: \"hsl\"\n  } : is(a, \"lab\") ? {\n    _a: a.l,\n    _b: a.a,\n    _c: a.b,\n    _d: 0,\n    space: \"lab\"\n  } : is(a, \"lch\") ? {\n    _a: a.l,\n    _b: a.c,\n    _c: a.h,\n    _d: 0,\n    space: \"lch\"\n  } : is(a, \"cmyk\") ? {\n    _a: a.c,\n    _b: a.m,\n    _c: a.y,\n    _d: a.k,\n    space: \"cmyk\"\n  } : {\n    _a: 0,\n    _b: 0,\n    _c: 0,\n    space: \"rgb\"\n  };\n  params.space = b || params.space;\n  return params;\n}\nfunction cieSpace(space) {\n  if (space === \"lab\" || space === \"xyz\" || space === \"lch\") {\n    return true;\n  } else {\n    return false;\n  }\n}\nfunction hueToRgb(p, q, t) {\n  if (t < 0) t += 1;\n  if (t > 1) t -= 1;\n  if (t < 1 / 6) return p + (q - p) * 6 * t;\n  if (t < 1 / 2) return q;\n  if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;\n  return p;\n}\nclass Color {\n  constructor(...inputs) {\n    this.init(...inputs);\n  }\n  // Test if given value is a color\n  static isColor(color) {\n    return color && (color instanceof Color || this.isRgb(color) || this.test(color));\n  }\n  // Test if given value is an rgb object\n  static isRgb(color) {\n    return color && typeof color.r === \"number\" && typeof color.g === \"number\" && typeof color.b === \"number\";\n  }\n  /*\n  Generating random colors\n  */\n  static random(mode = \"vibrant\", t) {\n    const {\n      random,\n      round,\n      sin,\n      PI: pi\n    } = Math;\n    if (mode === \"vibrant\") {\n      const l = (81 - 57) * random() + 57;\n      const c = (83 - 45) * random() + 45;\n      const h = 360 * random();\n      const color = new Color(l, c, h, \"lch\");\n      return color;\n    } else if (mode === \"sine\") {\n      t = t == null ? random() : t;\n      const r = round(80 * sin(2 * pi * t / 0.5 + 0.01) + 150);\n      const g = round(50 * sin(2 * pi * t / 0.5 + 4.6) + 200);\n      const b = round(100 * sin(2 * pi * t / 0.5 + 2.3) + 150);\n      const color = new Color(r, g, b);\n      return color;\n    } else if (mode === \"pastel\") {\n      const l = (94 - 86) * random() + 86;\n      const c = (26 - 9) * random() + 9;\n      const h = 360 * random();\n      const color = new Color(l, c, h, \"lch\");\n      return color;\n    } else if (mode === \"dark\") {\n      const l = 10 + 10 * random();\n      const c = (125 - 75) * random() + 86;\n      const h = 360 * random();\n      const color = new Color(l, c, h, \"lch\");\n      return color;\n    } else if (mode === \"rgb\") {\n      const r = 255 * random();\n      const g = 255 * random();\n      const b = 255 * random();\n      const color = new Color(r, g, b);\n      return color;\n    } else if (mode === \"lab\") {\n      const l = 100 * random();\n      const a = 256 * random() - 128;\n      const b = 256 * random() - 128;\n      const color = new Color(l, a, b, \"lab\");\n      return color;\n    } else if (mode === \"grey\") {\n      const grey = 255 * random();\n      const color = new Color(grey, grey, grey);\n      return color;\n    } else {\n      throw new Error(\"Unsupported random color mode\");\n    }\n  }\n  // Test if given value is a color string\n  static test(color) {\n    return typeof color === \"string\" && (isHex.test(color) || isRgb.test(color));\n  }\n  cmyk() {\n    const {\n      _a,\n      _b,\n      _c\n    } = this.rgb();\n    const [r, g, b] = [_a, _b, _c].map(v => v / 255);\n    const k = Math.min(1 - r, 1 - g, 1 - b);\n    if (k === 1) {\n      return new Color(0, 0, 0, 1, \"cmyk\");\n    }\n    const c = (1 - r - k) / (1 - k);\n    const m = (1 - g - k) / (1 - k);\n    const y2 = (1 - b - k) / (1 - k);\n    const color = new Color(c, m, y2, k, \"cmyk\");\n    return color;\n  }\n  hsl() {\n    const {\n      _a,\n      _b,\n      _c\n    } = this.rgb();\n    const [r, g, b] = [_a, _b, _c].map(v => v / 255);\n    const max = Math.max(r, g, b);\n    const min = Math.min(r, g, b);\n    const l = (max + min) / 2;\n    const isGrey = max === min;\n    const delta = max - min;\n    const s = isGrey ? 0 : l > 0.5 ? delta / (2 - max - min) : delta / (max + min);\n    const h = isGrey ? 0 : max === r ? ((g - b) / delta + (g < b ? 6 : 0)) / 6 : max === g ? ((b - r) / delta + 2) / 6 : max === b ? ((r - g) / delta + 4) / 6 : 0;\n    const color = new Color(360 * h, 100 * s, 100 * l, \"hsl\");\n    return color;\n  }\n  init(a = 0, b = 0, c = 0, d = 0, space = \"rgb\") {\n    a = !a ? 0 : a;\n    if (this.space) {\n      for (const component in this.space) {\n        delete this[this.space[component]];\n      }\n    }\n    if (typeof a === \"number\") {\n      space = typeof d === \"string\" ? d : space;\n      d = typeof d === \"string\" ? 0 : d;\n      Object.assign(this, {\n        _a: a,\n        _b: b,\n        _c: c,\n        _d: d,\n        space\n      });\n    } else if (a instanceof Array) {\n      this.space = b || (typeof a[3] === \"string\" ? a[3] : a[4]) || \"rgb\";\n      Object.assign(this, {\n        _a: a[0],\n        _b: a[1],\n        _c: a[2],\n        _d: a[3] || 0\n      });\n    } else if (a instanceof Object) {\n      const values = getParameters(a, b);\n      Object.assign(this, values);\n    } else if (typeof a === \"string\") {\n      if (isRgb.test(a)) {\n        const noWhitespace = a.replace(whitespace, \"\");\n        const [_a2, _b2, _c2] = rgb.exec(noWhitespace).slice(1, 4).map(v => parseInt(v));\n        Object.assign(this, {\n          _a: _a2,\n          _b: _b2,\n          _c: _c2,\n          _d: 0,\n          space: \"rgb\"\n        });\n      } else if (isHex.test(a)) {\n        const hexParse = v => parseInt(v, 16);\n        const [, _a2, _b2, _c2] = hex.exec(sixDigitHex(a)).map(hexParse);\n        Object.assign(this, {\n          _a: _a2,\n          _b: _b2,\n          _c: _c2,\n          _d: 0,\n          space: \"rgb\"\n        });\n      } else throw Error(\"Unsupported string format, can't construct Color\");\n    }\n    const {\n      _a,\n      _b,\n      _c,\n      _d\n    } = this;\n    const components = this.space === \"rgb\" ? {\n      r: _a,\n      g: _b,\n      b: _c\n    } : this.space === \"xyz\" ? {\n      x: _a,\n      y: _b,\n      z: _c\n    } : this.space === \"hsl\" ? {\n      h: _a,\n      s: _b,\n      l: _c\n    } : this.space === \"lab\" ? {\n      l: _a,\n      a: _b,\n      b: _c\n    } : this.space === \"lch\" ? {\n      l: _a,\n      c: _b,\n      h: _c\n    } : this.space === \"cmyk\" ? {\n      c: _a,\n      m: _b,\n      y: _c,\n      k: _d\n    } : {};\n    Object.assign(this, components);\n  }\n  lab() {\n    const {\n      x: x2,\n      y: y2,\n      z\n    } = this.xyz();\n    const l = 116 * y2 - 16;\n    const a = 500 * (x2 - y2);\n    const b = 200 * (y2 - z);\n    const color = new Color(l, a, b, \"lab\");\n    return color;\n  }\n  lch() {\n    const {\n      l,\n      a,\n      b\n    } = this.lab();\n    const c = Math.sqrt(a ** 2 + b ** 2);\n    let h = 180 * Math.atan2(b, a) / Math.PI;\n    if (h < 0) {\n      h *= -1;\n      h = 360 - h;\n    }\n    const color = new Color(l, c, h, \"lch\");\n    return color;\n  }\n  /*\n  Conversion Methods\n  */\n  rgb() {\n    if (this.space === \"rgb\") {\n      return this;\n    } else if (cieSpace(this.space)) {\n      let {\n        x: x2,\n        y: y2,\n        z\n      } = this;\n      if (this.space === \"lab\" || this.space === \"lch\") {\n        let {\n          l,\n          a,\n          b: b2\n        } = this;\n        if (this.space === \"lch\") {\n          const {\n            c,\n            h\n          } = this;\n          const dToR = Math.PI / 180;\n          a = c * Math.cos(dToR * h);\n          b2 = c * Math.sin(dToR * h);\n        }\n        const yL = (l + 16) / 116;\n        const xL = a / 500 + yL;\n        const zL = yL - b2 / 200;\n        const ct = 16 / 116;\n        const mx = 8856e-6;\n        const nm = 7.787;\n        x2 = 0.95047 * (xL ** 3 > mx ? xL ** 3 : (xL - ct) / nm);\n        y2 = 1 * (yL ** 3 > mx ? yL ** 3 : (yL - ct) / nm);\n        z = 1.08883 * (zL ** 3 > mx ? zL ** 3 : (zL - ct) / nm);\n      }\n      const rU = x2 * 3.2406 + y2 * -1.5372 + z * -0.4986;\n      const gU = x2 * -0.9689 + y2 * 1.8758 + z * 0.0415;\n      const bU = x2 * 0.0557 + y2 * -0.204 + z * 1.057;\n      const pow = Math.pow;\n      const bd = 31308e-7;\n      const r = rU > bd ? 1.055 * pow(rU, 1 / 2.4) - 0.055 : 12.92 * rU;\n      const g = gU > bd ? 1.055 * pow(gU, 1 / 2.4) - 0.055 : 12.92 * gU;\n      const b = bU > bd ? 1.055 * pow(bU, 1 / 2.4) - 0.055 : 12.92 * bU;\n      const color = new Color(255 * r, 255 * g, 255 * b);\n      return color;\n    } else if (this.space === \"hsl\") {\n      let {\n        h,\n        s,\n        l\n      } = this;\n      h /= 360;\n      s /= 100;\n      l /= 100;\n      if (s === 0) {\n        l *= 255;\n        const color2 = new Color(l, l, l);\n        return color2;\n      }\n      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n      const p = 2 * l - q;\n      const r = 255 * hueToRgb(p, q, h + 1 / 3);\n      const g = 255 * hueToRgb(p, q, h);\n      const b = 255 * hueToRgb(p, q, h - 1 / 3);\n      const color = new Color(r, g, b);\n      return color;\n    } else if (this.space === \"cmyk\") {\n      const {\n        c,\n        m,\n        y: y2,\n        k\n      } = this;\n      const r = 255 * (1 - Math.min(1, c * (1 - k) + k));\n      const g = 255 * (1 - Math.min(1, m * (1 - k) + k));\n      const b = 255 * (1 - Math.min(1, y2 * (1 - k) + k));\n      const color = new Color(r, g, b);\n      return color;\n    } else {\n      return this;\n    }\n  }\n  toArray() {\n    const {\n      _a,\n      _b,\n      _c,\n      _d,\n      space\n    } = this;\n    return [_a, _b, _c, _d, space];\n  }\n  toHex() {\n    const [r, g, b] = this._clamped().map(componentHex);\n    return `#${r}${g}${b}`;\n  }\n  toRgb() {\n    const [rV, gV, bV] = this._clamped();\n    const string = `rgb(${rV},${gV},${bV})`;\n    return string;\n  }\n  toString() {\n    return this.toHex();\n  }\n  xyz() {\n    const {\n      _a: r255,\n      _b: g255,\n      _c: b255\n    } = this.rgb();\n    const [r, g, b] = [r255, g255, b255].map(v => v / 255);\n    const rL = r > 0.04045 ? Math.pow((r + 0.055) / 1.055, 2.4) : r / 12.92;\n    const gL = g > 0.04045 ? Math.pow((g + 0.055) / 1.055, 2.4) : g / 12.92;\n    const bL = b > 0.04045 ? Math.pow((b + 0.055) / 1.055, 2.4) : b / 12.92;\n    const xU = (rL * 0.4124 + gL * 0.3576 + bL * 0.1805) / 0.95047;\n    const yU = (rL * 0.2126 + gL * 0.7152 + bL * 0.0722) / 1;\n    const zU = (rL * 0.0193 + gL * 0.1192 + bL * 0.9505) / 1.08883;\n    const x2 = xU > 8856e-6 ? Math.pow(xU, 1 / 3) : 7.787 * xU + 16 / 116;\n    const y2 = yU > 8856e-6 ? Math.pow(yU, 1 / 3) : 7.787 * yU + 16 / 116;\n    const z = zU > 8856e-6 ? Math.pow(zU, 1 / 3) : 7.787 * zU + 16 / 116;\n    const color = new Color(x2, y2, z, \"xyz\");\n    return color;\n  }\n  /*\n  Input and Output methods\n  */\n  _clamped() {\n    const {\n      _a,\n      _b,\n      _c\n    } = this.rgb();\n    const {\n      max,\n      min,\n      round\n    } = Math;\n    const format = v => max(0, min(round(v), 255));\n    return [_a, _b, _c].map(format);\n  }\n  /*\n  Constructing colors\n  */\n}\nclass Point {\n  // Initialize\n  constructor(...args) {\n    this.init(...args);\n  }\n  // Clone point\n  clone() {\n    return new Point(this);\n  }\n  init(x2, y2) {\n    const base = {\n      x: 0,\n      y: 0\n    };\n    const source = Array.isArray(x2) ? {\n      x: x2[0],\n      y: x2[1]\n    } : typeof x2 === \"object\" ? {\n      x: x2.x,\n      y: x2.y\n    } : {\n      x: x2,\n      y: y2\n    };\n    this.x = source.x == null ? base.x : source.x;\n    this.y = source.y == null ? base.y : source.y;\n    return this;\n  }\n  toArray() {\n    return [this.x, this.y];\n  }\n  transform(m) {\n    return this.clone().transformO(m);\n  }\n  // Transform point with matrix\n  transformO(m) {\n    if (!Matrix.isMatrixLike(m)) {\n      m = new Matrix(m);\n    }\n    const {\n      x: x2,\n      y: y2\n    } = this;\n    this.x = m.a * x2 + m.c * y2 + m.e;\n    this.y = m.b * x2 + m.d * y2 + m.f;\n    return this;\n  }\n}\nfunction point(x2, y2) {\n  return new Point(x2, y2).transformO(this.screenCTM().inverseO());\n}\nfunction closeEnough(a, b, threshold) {\n  return Math.abs(b - a) < 1e-6;\n}\nclass Matrix {\n  constructor(...args) {\n    this.init(...args);\n  }\n  static formatTransforms(o) {\n    const flipBoth = o.flip === \"both\" || o.flip === true;\n    const flipX = o.flip && (flipBoth || o.flip === \"x\") ? -1 : 1;\n    const flipY = o.flip && (flipBoth || o.flip === \"y\") ? -1 : 1;\n    const skewX = o.skew && o.skew.length ? o.skew[0] : isFinite(o.skew) ? o.skew : isFinite(o.skewX) ? o.skewX : 0;\n    const skewY = o.skew && o.skew.length ? o.skew[1] : isFinite(o.skew) ? o.skew : isFinite(o.skewY) ? o.skewY : 0;\n    const scaleX = o.scale && o.scale.length ? o.scale[0] * flipX : isFinite(o.scale) ? o.scale * flipX : isFinite(o.scaleX) ? o.scaleX * flipX : flipX;\n    const scaleY = o.scale && o.scale.length ? o.scale[1] * flipY : isFinite(o.scale) ? o.scale * flipY : isFinite(o.scaleY) ? o.scaleY * flipY : flipY;\n    const shear = o.shear || 0;\n    const theta = o.rotate || o.theta || 0;\n    const origin = new Point(o.origin || o.around || o.ox || o.originX, o.oy || o.originY);\n    const ox = origin.x;\n    const oy = origin.y;\n    const position2 = new Point(o.position || o.px || o.positionX || NaN, o.py || o.positionY || NaN);\n    const px = position2.x;\n    const py = position2.y;\n    const translate = new Point(o.translate || o.tx || o.translateX, o.ty || o.translateY);\n    const tx = translate.x;\n    const ty = translate.y;\n    const relative = new Point(o.relative || o.rx || o.relativeX, o.ry || o.relativeY);\n    const rx2 = relative.x;\n    const ry2 = relative.y;\n    return {\n      scaleX,\n      scaleY,\n      skewX,\n      skewY,\n      shear,\n      theta,\n      rx: rx2,\n      ry: ry2,\n      tx,\n      ty,\n      ox,\n      oy,\n      px,\n      py\n    };\n  }\n  static fromArray(a) {\n    return {\n      a: a[0],\n      b: a[1],\n      c: a[2],\n      d: a[3],\n      e: a[4],\n      f: a[5]\n    };\n  }\n  static isMatrixLike(o) {\n    return o.a != null || o.b != null || o.c != null || o.d != null || o.e != null || o.f != null;\n  }\n  // left matrix, right matrix, target matrix which is overwritten\n  static matrixMultiply(l, r, o) {\n    const a = l.a * r.a + l.c * r.b;\n    const b = l.b * r.a + l.d * r.b;\n    const c = l.a * r.c + l.c * r.d;\n    const d = l.b * r.c + l.d * r.d;\n    const e = l.e + l.a * r.e + l.c * r.f;\n    const f = l.f + l.b * r.e + l.d * r.f;\n    o.a = a;\n    o.b = b;\n    o.c = c;\n    o.d = d;\n    o.e = e;\n    o.f = f;\n    return o;\n  }\n  around(cx2, cy2, matrix) {\n    return this.clone().aroundO(cx2, cy2, matrix);\n  }\n  // Transform around a center point\n  aroundO(cx2, cy2, matrix) {\n    const dx2 = cx2 || 0;\n    const dy2 = cy2 || 0;\n    return this.translateO(-dx2, -dy2).lmultiplyO(matrix).translateO(dx2, dy2);\n  }\n  // Clones this matrix\n  clone() {\n    return new Matrix(this);\n  }\n  // Decomposes this matrix into its affine parameters\n  decompose(cx2 = 0, cy2 = 0) {\n    const a = this.a;\n    const b = this.b;\n    const c = this.c;\n    const d = this.d;\n    const e = this.e;\n    const f = this.f;\n    const determinant = a * d - b * c;\n    const ccw = determinant > 0 ? 1 : -1;\n    const sx = ccw * Math.sqrt(a * a + b * b);\n    const thetaRad = Math.atan2(ccw * b, ccw * a);\n    const theta = 180 / Math.PI * thetaRad;\n    const ct = Math.cos(thetaRad);\n    const st = Math.sin(thetaRad);\n    const lam = (a * c + b * d) / determinant;\n    const sy = c * sx / (lam * a - b) || d * sx / (lam * b + a);\n    const tx = e - cx2 + cx2 * ct * sx + cy2 * (lam * ct * sx - st * sy);\n    const ty = f - cy2 + cx2 * st * sx + cy2 * (lam * st * sx + ct * sy);\n    return {\n      // Return the affine parameters\n      scaleX: sx,\n      scaleY: sy,\n      shear: lam,\n      rotate: theta,\n      translateX: tx,\n      translateY: ty,\n      originX: cx2,\n      originY: cy2,\n      // Return the matrix parameters\n      a: this.a,\n      b: this.b,\n      c: this.c,\n      d: this.d,\n      e: this.e,\n      f: this.f\n    };\n  }\n  // Check if two matrices are equal\n  equals(other) {\n    if (other === this) return true;\n    const comp = new Matrix(other);\n    return closeEnough(this.a, comp.a) && closeEnough(this.b, comp.b) && closeEnough(this.c, comp.c) && closeEnough(this.d, comp.d) && closeEnough(this.e, comp.e) && closeEnough(this.f, comp.f);\n  }\n  // Flip matrix on x or y, at a given offset\n  flip(axis, around) {\n    return this.clone().flipO(axis, around);\n  }\n  flipO(axis, around) {\n    return axis === \"x\" ? this.scaleO(-1, 1, around, 0) : axis === \"y\" ? this.scaleO(1, -1, 0, around) : this.scaleO(-1, -1, axis, around || axis);\n  }\n  // Initialize\n  init(source) {\n    const base = Matrix.fromArray([1, 0, 0, 1, 0, 0]);\n    source = source instanceof Element ? source.matrixify() : typeof source === \"string\" ? Matrix.fromArray(source.split(delimiter).map(parseFloat)) : Array.isArray(source) ? Matrix.fromArray(source) : typeof source === \"object\" && Matrix.isMatrixLike(source) ? source : typeof source === \"object\" ? new Matrix().transform(source) : arguments.length === 6 ? Matrix.fromArray([].slice.call(arguments)) : base;\n    this.a = source.a != null ? source.a : base.a;\n    this.b = source.b != null ? source.b : base.b;\n    this.c = source.c != null ? source.c : base.c;\n    this.d = source.d != null ? source.d : base.d;\n    this.e = source.e != null ? source.e : base.e;\n    this.f = source.f != null ? source.f : base.f;\n    return this;\n  }\n  inverse() {\n    return this.clone().inverseO();\n  }\n  // Inverses matrix\n  inverseO() {\n    const a = this.a;\n    const b = this.b;\n    const c = this.c;\n    const d = this.d;\n    const e = this.e;\n    const f = this.f;\n    const det = a * d - b * c;\n    if (!det) throw new Error(\"Cannot invert \" + this);\n    const na = d / det;\n    const nb = -b / det;\n    const nc = -c / det;\n    const nd = a / det;\n    const ne = -(na * e + nc * f);\n    const nf = -(nb * e + nd * f);\n    this.a = na;\n    this.b = nb;\n    this.c = nc;\n    this.d = nd;\n    this.e = ne;\n    this.f = nf;\n    return this;\n  }\n  lmultiply(matrix) {\n    return this.clone().lmultiplyO(matrix);\n  }\n  lmultiplyO(matrix) {\n    const r = this;\n    const l = matrix instanceof Matrix ? matrix : new Matrix(matrix);\n    return Matrix.matrixMultiply(l, r, this);\n  }\n  // Left multiplies by the given matrix\n  multiply(matrix) {\n    return this.clone().multiplyO(matrix);\n  }\n  multiplyO(matrix) {\n    const l = this;\n    const r = matrix instanceof Matrix ? matrix : new Matrix(matrix);\n    return Matrix.matrixMultiply(l, r, this);\n  }\n  // Rotate matrix\n  rotate(r, cx2, cy2) {\n    return this.clone().rotateO(r, cx2, cy2);\n  }\n  rotateO(r, cx2 = 0, cy2 = 0) {\n    r = radians(r);\n    const cos = Math.cos(r);\n    const sin = Math.sin(r);\n    const {\n      a,\n      b,\n      c,\n      d,\n      e,\n      f\n    } = this;\n    this.a = a * cos - b * sin;\n    this.b = b * cos + a * sin;\n    this.c = c * cos - d * sin;\n    this.d = d * cos + c * sin;\n    this.e = e * cos - f * sin + cy2 * sin - cx2 * cos + cx2;\n    this.f = f * cos + e * sin - cx2 * sin - cy2 * cos + cy2;\n    return this;\n  }\n  // Scale matrix\n  scale() {\n    return this.clone().scaleO(...arguments);\n  }\n  scaleO(x2, y2 = x2, cx2 = 0, cy2 = 0) {\n    if (arguments.length === 3) {\n      cy2 = cx2;\n      cx2 = y2;\n      y2 = x2;\n    }\n    const {\n      a,\n      b,\n      c,\n      d,\n      e,\n      f\n    } = this;\n    this.a = a * x2;\n    this.b = b * y2;\n    this.c = c * x2;\n    this.d = d * y2;\n    this.e = e * x2 - cx2 * x2 + cx2;\n    this.f = f * y2 - cy2 * y2 + cy2;\n    return this;\n  }\n  // Shear matrix\n  shear(a, cx2, cy2) {\n    return this.clone().shearO(a, cx2, cy2);\n  }\n  // eslint-disable-next-line no-unused-vars\n  shearO(lx, cx2 = 0, cy2 = 0) {\n    const {\n      a,\n      b,\n      c,\n      d,\n      e,\n      f\n    } = this;\n    this.a = a + b * lx;\n    this.c = c + d * lx;\n    this.e = e + f * lx - cy2 * lx;\n    return this;\n  }\n  // Skew Matrix\n  skew() {\n    return this.clone().skewO(...arguments);\n  }\n  skewO(x2, y2 = x2, cx2 = 0, cy2 = 0) {\n    if (arguments.length === 3) {\n      cy2 = cx2;\n      cx2 = y2;\n      y2 = x2;\n    }\n    x2 = radians(x2);\n    y2 = radians(y2);\n    const lx = Math.tan(x2);\n    const ly = Math.tan(y2);\n    const {\n      a,\n      b,\n      c,\n      d,\n      e,\n      f\n    } = this;\n    this.a = a + b * lx;\n    this.b = b + a * ly;\n    this.c = c + d * lx;\n    this.d = d + c * ly;\n    this.e = e + f * lx - cy2 * lx;\n    this.f = f + e * ly - cx2 * ly;\n    return this;\n  }\n  // SkewX\n  skewX(x2, cx2, cy2) {\n    return this.skew(x2, 0, cx2, cy2);\n  }\n  // SkewY\n  skewY(y2, cx2, cy2) {\n    return this.skew(0, y2, cx2, cy2);\n  }\n  toArray() {\n    return [this.a, this.b, this.c, this.d, this.e, this.f];\n  }\n  // Convert matrix to string\n  toString() {\n    return \"matrix(\" + this.a + \",\" + this.b + \",\" + this.c + \",\" + this.d + \",\" + this.e + \",\" + this.f + \")\";\n  }\n  // Transform a matrix into another matrix by manipulating the space\n  transform(o) {\n    if (Matrix.isMatrixLike(o)) {\n      const matrix = new Matrix(o);\n      return matrix.multiplyO(this);\n    }\n    const t = Matrix.formatTransforms(o);\n    const current = this;\n    const {\n      x: ox,\n      y: oy\n    } = new Point(t.ox, t.oy).transform(current);\n    const transformer = new Matrix().translateO(t.rx, t.ry).lmultiplyO(current).translateO(-ox, -oy).scaleO(t.scaleX, t.scaleY).skewO(t.skewX, t.skewY).shearO(t.shear).rotateO(t.theta).translateO(ox, oy);\n    if (isFinite(t.px) || isFinite(t.py)) {\n      const origin = new Point(ox, oy).transform(transformer);\n      const dx2 = isFinite(t.px) ? t.px - origin.x : 0;\n      const dy2 = isFinite(t.py) ? t.py - origin.y : 0;\n      transformer.translateO(dx2, dy2);\n    }\n    transformer.translateO(t.tx, t.ty);\n    return transformer;\n  }\n  // Translate matrix\n  translate(x2, y2) {\n    return this.clone().translateO(x2, y2);\n  }\n  translateO(x2, y2) {\n    this.e += x2 || 0;\n    this.f += y2 || 0;\n    return this;\n  }\n  valueOf() {\n    return {\n      a: this.a,\n      b: this.b,\n      c: this.c,\n      d: this.d,\n      e: this.e,\n      f: this.f\n    };\n  }\n}\nfunction ctm() {\n  return new Matrix(this.node.getCTM());\n}\nfunction screenCTM() {\n  try {\n    if (typeof this.isRoot === \"function\" && !this.isRoot()) {\n      const rect = this.rect(1, 1);\n      const m = rect.node.getScreenCTM();\n      rect.remove();\n      return new Matrix(m);\n    }\n    return new Matrix(this.node.getScreenCTM());\n  } catch (e) {\n    console.warn(`Cannot get CTM from SVG node ${this.node.nodeName}. Is the element rendered?`);\n    return new Matrix();\n  }\n}\nregister(Matrix, \"Matrix\");\nfunction parser() {\n  if (!parser.nodes) {\n    const svg2 = makeInstance().size(2, 0);\n    svg2.node.style.cssText = [\"opacity: 0\", \"position: absolute\", \"left: -100%\", \"top: -100%\", \"overflow: hidden\"].join(\";\");\n    svg2.attr(\"focusable\", \"false\");\n    svg2.attr(\"aria-hidden\", \"true\");\n    const path = svg2.path().node;\n    parser.nodes = {\n      svg: svg2,\n      path\n    };\n  }\n  if (!parser.nodes.svg.node.parentNode) {\n    const b = globals.document.body || globals.document.documentElement;\n    parser.nodes.svg.addTo(b);\n  }\n  return parser.nodes;\n}\nfunction isNulledBox(box) {\n  return !box.width && !box.height && !box.x && !box.y;\n}\nfunction domContains(node) {\n  return node === globals.document || (globals.document.documentElement.contains || function (node2) {\n    while (node2.parentNode) {\n      node2 = node2.parentNode;\n    }\n    return node2 === globals.document;\n  }).call(globals.document.documentElement, node);\n}\nclass Box {\n  constructor(...args) {\n    this.init(...args);\n  }\n  addOffset() {\n    this.x += globals.window.pageXOffset;\n    this.y += globals.window.pageYOffset;\n    return new Box(this);\n  }\n  init(source) {\n    const base = [0, 0, 0, 0];\n    source = typeof source === \"string\" ? source.split(delimiter).map(parseFloat) : Array.isArray(source) ? source : typeof source === \"object\" ? [source.left != null ? source.left : source.x, source.top != null ? source.top : source.y, source.width, source.height] : arguments.length === 4 ? [].slice.call(arguments) : base;\n    this.x = source[0] || 0;\n    this.y = source[1] || 0;\n    this.width = this.w = source[2] || 0;\n    this.height = this.h = source[3] || 0;\n    this.x2 = this.x + this.w;\n    this.y2 = this.y + this.h;\n    this.cx = this.x + this.w / 2;\n    this.cy = this.y + this.h / 2;\n    return this;\n  }\n  isNulled() {\n    return isNulledBox(this);\n  }\n  // Merge rect box with another, return a new instance\n  merge(box) {\n    const x2 = Math.min(this.x, box.x);\n    const y2 = Math.min(this.y, box.y);\n    const width2 = Math.max(this.x + this.width, box.x + box.width) - x2;\n    const height2 = Math.max(this.y + this.height, box.y + box.height) - y2;\n    return new Box(x2, y2, width2, height2);\n  }\n  toArray() {\n    return [this.x, this.y, this.width, this.height];\n  }\n  toString() {\n    return this.x + \" \" + this.y + \" \" + this.width + \" \" + this.height;\n  }\n  transform(m) {\n    if (!(m instanceof Matrix)) {\n      m = new Matrix(m);\n    }\n    let xMin = Infinity;\n    let xMax = -Infinity;\n    let yMin = Infinity;\n    let yMax = -Infinity;\n    const pts = [new Point(this.x, this.y), new Point(this.x2, this.y), new Point(this.x, this.y2), new Point(this.x2, this.y2)];\n    pts.forEach(function (p) {\n      p = p.transform(m);\n      xMin = Math.min(xMin, p.x);\n      xMax = Math.max(xMax, p.x);\n      yMin = Math.min(yMin, p.y);\n      yMax = Math.max(yMax, p.y);\n    });\n    return new Box(xMin, yMin, xMax - xMin, yMax - yMin);\n  }\n}\nfunction getBox(el, getBBoxFn, retry) {\n  let box;\n  try {\n    box = getBBoxFn(el.node);\n    if (isNulledBox(box) && !domContains(el.node)) {\n      throw new Error(\"Element not in the dom\");\n    }\n  } catch (e) {\n    box = retry(el);\n  }\n  return box;\n}\nfunction bbox() {\n  const getBBox = node => node.getBBox();\n  const retry = el => {\n    try {\n      const clone = el.clone().addTo(parser().svg).show();\n      const box2 = clone.node.getBBox();\n      clone.remove();\n      return box2;\n    } catch (e) {\n      throw new Error(`Getting bbox of element \"${el.node.nodeName}\" is not possible: ${e.toString()}`);\n    }\n  };\n  const box = getBox(this, getBBox, retry);\n  const bbox2 = new Box(box);\n  return bbox2;\n}\nfunction rbox(el) {\n  const getRBox = node => node.getBoundingClientRect();\n  const retry = el2 => {\n    throw new Error(`Getting rbox of element \"${el2.node.nodeName}\" is not possible`);\n  };\n  const box = getBox(this, getRBox, retry);\n  const rbox2 = new Box(box);\n  if (el) {\n    return rbox2.transform(el.screenCTM().inverseO());\n  }\n  return rbox2.addOffset();\n}\nfunction inside(x2, y2) {\n  const box = this.bbox();\n  return x2 > box.x && y2 > box.y && x2 < box.x + box.width && y2 < box.y + box.height;\n}\nregisterMethods({\n  viewbox: {\n    viewbox(x2, y2, width2, height2) {\n      if (x2 == null) return new Box(this.attr(\"viewBox\"));\n      return this.attr(\"viewBox\", new Box(x2, y2, width2, height2));\n    },\n    zoom(level, point2) {\n      let {\n        width: width2,\n        height: height2\n      } = this.attr([\"width\", \"height\"]);\n      if (!width2 && !height2 || typeof width2 === \"string\" || typeof height2 === \"string\") {\n        width2 = this.node.clientWidth;\n        height2 = this.node.clientHeight;\n      }\n      if (!width2 || !height2) {\n        throw new Error(\"Impossible to get absolute width and height. Please provide an absolute width and height attribute on the zooming element\");\n      }\n      const v = this.viewbox();\n      const zoomX = width2 / v.width;\n      const zoomY = height2 / v.height;\n      const zoom = Math.min(zoomX, zoomY);\n      if (level == null) {\n        return zoom;\n      }\n      let zoomAmount = zoom / level;\n      if (zoomAmount === Infinity) zoomAmount = Number.MAX_SAFE_INTEGER / 100;\n      point2 = point2 || new Point(width2 / 2 / zoomX + v.x, height2 / 2 / zoomY + v.y);\n      const box = new Box(v).transform(new Matrix({\n        scale: zoomAmount,\n        origin: point2\n      }));\n      return this.viewbox(box);\n    }\n  }\n});\nregister(Box, \"Box\");\nclass List extends Array {\n  constructor(arr = [], ...args) {\n    super(arr, ...args);\n    if (typeof arr === \"number\") return this;\n    this.length = 0;\n    this.push(...arr);\n  }\n}\nextend([List], {\n  each(fnOrMethodName, ...args) {\n    if (typeof fnOrMethodName === \"function\") {\n      return this.map((el, i, arr) => {\n        return fnOrMethodName.call(el, el, i, arr);\n      });\n    } else {\n      return this.map(el => {\n        return el[fnOrMethodName](...args);\n      });\n    }\n  },\n  toArray() {\n    return Array.prototype.concat.apply([], this);\n  }\n});\nconst reserved = [\"toArray\", \"constructor\", \"each\"];\nList.extend = function (methods2) {\n  methods2 = methods2.reduce((obj, name2) => {\n    if (reserved.includes(name2)) return obj;\n    if (name2[0] === \"_\") return obj;\n    if (name2 in Array.prototype) {\n      obj[\"$\" + name2] = Array.prototype[name2];\n    }\n    obj[name2] = function (...attrs2) {\n      return this.each(name2, ...attrs2);\n    };\n    return obj;\n  }, {});\n  extend([List], methods2);\n};\nfunction baseFind(query, parent) {\n  return new List(map((parent || globals.document).querySelectorAll(query), function (node) {\n    return adopt(node);\n  }));\n}\nfunction find(query) {\n  return baseFind(query, this.node);\n}\nfunction findOne(query) {\n  return adopt(this.node.querySelector(query));\n}\nlet listenerId = 0;\nconst windowEvents = {};\nfunction getEvents(instance) {\n  let n = instance.getEventHolder();\n  if (n === globals.window) n = windowEvents;\n  if (!n.events) n.events = {};\n  return n.events;\n}\nfunction getEventTarget(instance) {\n  return instance.getEventTarget();\n}\nfunction clearEvents(instance) {\n  let n = instance.getEventHolder();\n  if (n === globals.window) n = windowEvents;\n  if (n.events) n.events = {};\n}\nfunction on(node, events, listener, binding, options) {\n  const l = listener.bind(binding || node);\n  const instance = makeInstance(node);\n  const bag = getEvents(instance);\n  const n = getEventTarget(instance);\n  events = Array.isArray(events) ? events : events.split(delimiter);\n  if (!listener._svgjsListenerId) {\n    listener._svgjsListenerId = ++listenerId;\n  }\n  events.forEach(function (event) {\n    const ev = event.split(\".\")[0];\n    const ns = event.split(\".\")[1] || \"*\";\n    bag[ev] = bag[ev] || {};\n    bag[ev][ns] = bag[ev][ns] || {};\n    bag[ev][ns][listener._svgjsListenerId] = l;\n    n.addEventListener(ev, l, options || false);\n  });\n}\nfunction off(node, events, listener, options) {\n  const instance = makeInstance(node);\n  const bag = getEvents(instance);\n  const n = getEventTarget(instance);\n  if (typeof listener === \"function\") {\n    listener = listener._svgjsListenerId;\n    if (!listener) return;\n  }\n  events = Array.isArray(events) ? events : (events || \"\").split(delimiter);\n  events.forEach(function (event) {\n    const ev = event && event.split(\".\")[0];\n    const ns = event && event.split(\".\")[1];\n    let namespace, l;\n    if (listener) {\n      if (bag[ev] && bag[ev][ns || \"*\"]) {\n        n.removeEventListener(ev, bag[ev][ns || \"*\"][listener], options || false);\n        delete bag[ev][ns || \"*\"][listener];\n      }\n    } else if (ev && ns) {\n      if (bag[ev] && bag[ev][ns]) {\n        for (l in bag[ev][ns]) {\n          off(n, [ev, ns].join(\".\"), l);\n        }\n        delete bag[ev][ns];\n      }\n    } else if (ns) {\n      for (event in bag) {\n        for (namespace in bag[event]) {\n          if (ns === namespace) {\n            off(n, [event, ns].join(\".\"));\n          }\n        }\n      }\n    } else if (ev) {\n      if (bag[ev]) {\n        for (namespace in bag[ev]) {\n          off(n, [ev, namespace].join(\".\"));\n        }\n        delete bag[ev];\n      }\n    } else {\n      for (event in bag) {\n        off(n, event);\n      }\n      clearEvents(instance);\n    }\n  });\n}\nfunction dispatch(node, event, data2, options) {\n  const n = getEventTarget(node);\n  if (event instanceof globals.window.Event) {\n    n.dispatchEvent(event);\n  } else {\n    event = new globals.window.CustomEvent(event, {\n      detail: data2,\n      cancelable: true,\n      ...options\n    });\n    n.dispatchEvent(event);\n  }\n  return event;\n}\nclass EventTarget extends Base {\n  addEventListener() {}\n  dispatch(event, data2, options) {\n    return dispatch(this, event, data2, options);\n  }\n  dispatchEvent(event) {\n    const bag = this.getEventHolder().events;\n    if (!bag) return true;\n    const events = bag[event.type];\n    for (const i in events) {\n      for (const j in events[i]) {\n        events[i][j](event);\n      }\n    }\n    return !event.defaultPrevented;\n  }\n  // Fire given event\n  fire(event, data2, options) {\n    this.dispatch(event, data2, options);\n    return this;\n  }\n  getEventHolder() {\n    return this;\n  }\n  getEventTarget() {\n    return this;\n  }\n  // Unbind event from listener\n  off(event, listener, options) {\n    off(this, event, listener, options);\n    return this;\n  }\n  // Bind given event to listener\n  on(event, listener, binding, options) {\n    on(this, event, listener, binding, options);\n    return this;\n  }\n  removeEventListener() {}\n}\nregister(EventTarget, \"EventTarget\");\nfunction noop() {}\nconst timeline = {\n  duration: 400,\n  ease: \">\",\n  delay: 0\n};\nconst attrs = {\n  // fill and stroke\n  \"fill-opacity\": 1,\n  \"stroke-opacity\": 1,\n  \"stroke-width\": 0,\n  \"stroke-linejoin\": \"miter\",\n  \"stroke-linecap\": \"butt\",\n  fill: \"#000000\",\n  stroke: \"#000000\",\n  opacity: 1,\n  // position\n  x: 0,\n  y: 0,\n  cx: 0,\n  cy: 0,\n  // size\n  width: 0,\n  height: 0,\n  // radius\n  r: 0,\n  rx: 0,\n  ry: 0,\n  // gradient\n  offset: 0,\n  \"stop-opacity\": 1,\n  \"stop-color\": \"#000000\",\n  // text\n  \"text-anchor\": \"start\"\n};\nclass SVGArray extends Array {\n  constructor(...args) {\n    super(...args);\n    this.init(...args);\n  }\n  clone() {\n    return new this.constructor(this);\n  }\n  init(arr) {\n    if (typeof arr === \"number\") return this;\n    this.length = 0;\n    this.push(...this.parse(arr));\n    return this;\n  }\n  // Parse whitespace separated string\n  parse(array2 = []) {\n    if (array2 instanceof Array) return array2;\n    return array2.trim().split(delimiter).map(parseFloat);\n  }\n  toArray() {\n    return Array.prototype.concat.apply([], this);\n  }\n  toSet() {\n    return new Set(this);\n  }\n  toString() {\n    return this.join(\" \");\n  }\n  // Flattens the array if needed\n  valueOf() {\n    const ret = [];\n    ret.push(...this);\n    return ret;\n  }\n}\nclass SVGNumber {\n  // Initialize\n  constructor(...args) {\n    this.init(...args);\n  }\n  convert(unit) {\n    return new SVGNumber(this.value, unit);\n  }\n  // Divide number\n  divide(number) {\n    number = new SVGNumber(number);\n    return new SVGNumber(this / number, this.unit || number.unit);\n  }\n  init(value, unit) {\n    unit = Array.isArray(value) ? value[1] : unit;\n    value = Array.isArray(value) ? value[0] : value;\n    this.value = 0;\n    this.unit = unit || \"\";\n    if (typeof value === \"number\") {\n      this.value = isNaN(value) ? 0 : !isFinite(value) ? value < 0 ? -34e37 : 34e37 : value;\n    } else if (typeof value === \"string\") {\n      unit = value.match(numberAndUnit);\n      if (unit) {\n        this.value = parseFloat(unit[1]);\n        if (unit[5] === \"%\") {\n          this.value /= 100;\n        } else if (unit[5] === \"s\") {\n          this.value *= 1e3;\n        }\n        this.unit = unit[5];\n      }\n    } else {\n      if (value instanceof SVGNumber) {\n        this.value = value.valueOf();\n        this.unit = value.unit;\n      }\n    }\n    return this;\n  }\n  // Subtract number\n  minus(number) {\n    number = new SVGNumber(number);\n    return new SVGNumber(this - number, this.unit || number.unit);\n  }\n  // Add number\n  plus(number) {\n    number = new SVGNumber(number);\n    return new SVGNumber(this + number, this.unit || number.unit);\n  }\n  // Multiply number\n  times(number) {\n    number = new SVGNumber(number);\n    return new SVGNumber(this * number, this.unit || number.unit);\n  }\n  toArray() {\n    return [this.value, this.unit];\n  }\n  toJSON() {\n    return this.toString();\n  }\n  toString() {\n    return (this.unit === \"%\" ? ~~(this.value * 1e8) / 1e6 : this.unit === \"s\" ? this.value / 1e3 : this.value) + this.unit;\n  }\n  valueOf() {\n    return this.value;\n  }\n}\nconst colorAttributes = /* @__PURE__ */new Set([\"fill\", \"stroke\", \"color\", \"bgcolor\", \"stop-color\", \"flood-color\", \"lighting-color\"]);\nconst hooks = [];\nfunction registerAttrHook(fn) {\n  hooks.push(fn);\n}\nfunction attr(attr2, val, ns) {\n  if (attr2 == null) {\n    attr2 = {};\n    val = this.node.attributes;\n    for (const node of val) {\n      attr2[node.nodeName] = isNumber.test(node.nodeValue) ? parseFloat(node.nodeValue) : node.nodeValue;\n    }\n    return attr2;\n  } else if (attr2 instanceof Array) {\n    return attr2.reduce((last, curr) => {\n      last[curr] = this.attr(curr);\n      return last;\n    }, {});\n  } else if (typeof attr2 === \"object\" && attr2.constructor === Object) {\n    for (val in attr2) this.attr(val, attr2[val]);\n  } else if (val === null) {\n    this.node.removeAttribute(attr2);\n  } else if (val == null) {\n    val = this.node.getAttribute(attr2);\n    return val == null ? attrs[attr2] : isNumber.test(val) ? parseFloat(val) : val;\n  } else {\n    val = hooks.reduce((_val, hook) => {\n      return hook(attr2, _val, this);\n    }, val);\n    if (typeof val === \"number\") {\n      val = new SVGNumber(val);\n    } else if (colorAttributes.has(attr2) && Color.isColor(val)) {\n      val = new Color(val);\n    } else if (val.constructor === Array) {\n      val = new SVGArray(val);\n    }\n    if (attr2 === \"leading\") {\n      if (this.leading) {\n        this.leading(val);\n      }\n    } else {\n      typeof ns === \"string\" ? this.node.setAttributeNS(ns, attr2, val.toString()) : this.node.setAttribute(attr2, val.toString());\n    }\n    if (this.rebuild && (attr2 === \"font-size\" || attr2 === \"x\")) {\n      this.rebuild();\n    }\n  }\n  return this;\n}\nclass Dom extends EventTarget {\n  constructor(node, attrs2) {\n    super();\n    this.node = node;\n    this.type = node.nodeName;\n    if (attrs2 && node !== attrs2) {\n      this.attr(attrs2);\n    }\n  }\n  // Add given element at a position\n  add(element, i) {\n    element = makeInstance(element);\n    if (element.removeNamespace && this.node instanceof globals.window.SVGElement) {\n      element.removeNamespace();\n    }\n    if (i == null) {\n      this.node.appendChild(element.node);\n    } else if (element.node !== this.node.childNodes[i]) {\n      this.node.insertBefore(element.node, this.node.childNodes[i]);\n    }\n    return this;\n  }\n  // Add element to given container and return self\n  addTo(parent, i) {\n    return makeInstance(parent).put(this, i);\n  }\n  // Returns all child elements\n  children() {\n    return new List(map(this.node.children, function (node) {\n      return adopt(node);\n    }));\n  }\n  // Remove all elements in this container\n  clear() {\n    while (this.node.hasChildNodes()) {\n      this.node.removeChild(this.node.lastChild);\n    }\n    return this;\n  }\n  // Clone element\n  clone(deep = true, assignNewIds = true) {\n    this.writeDataToDom();\n    let nodeClone = this.node.cloneNode(deep);\n    if (assignNewIds) {\n      nodeClone = assignNewId(nodeClone);\n    }\n    return new this.constructor(nodeClone);\n  }\n  // Iterates over all children and invokes a given block\n  each(block, deep) {\n    const children = this.children();\n    let i, il;\n    for (i = 0, il = children.length; i < il; i++) {\n      block.apply(children[i], [i, children]);\n      if (deep) {\n        children[i].each(block, deep);\n      }\n    }\n    return this;\n  }\n  element(nodeName, attrs2) {\n    return this.put(new Dom(create(nodeName), attrs2));\n  }\n  // Get first child\n  first() {\n    return adopt(this.node.firstChild);\n  }\n  // Get a element at the given index\n  get(i) {\n    return adopt(this.node.childNodes[i]);\n  }\n  getEventHolder() {\n    return this.node;\n  }\n  getEventTarget() {\n    return this.node;\n  }\n  // Checks if the given element is a child\n  has(element) {\n    return this.index(element) >= 0;\n  }\n  html(htmlOrFn, outerHTML) {\n    return this.xml(htmlOrFn, outerHTML, html);\n  }\n  // Get / set id\n  id(id) {\n    if (typeof id === \"undefined\" && !this.node.id) {\n      this.node.id = eid(this.type);\n    }\n    return this.attr(\"id\", id);\n  }\n  // Gets index of given element\n  index(element) {\n    return [].slice.call(this.node.childNodes).indexOf(element.node);\n  }\n  // Get the last child\n  last() {\n    return adopt(this.node.lastChild);\n  }\n  // matches the element vs a css selector\n  matches(selector) {\n    const el = this.node;\n    const matcher = el.matches || el.matchesSelector || el.msMatchesSelector || el.mozMatchesSelector || el.webkitMatchesSelector || el.oMatchesSelector || null;\n    return matcher && matcher.call(el, selector);\n  }\n  // Returns the parent element instance\n  parent(type) {\n    let parent = this;\n    if (!parent.node.parentNode) return null;\n    parent = adopt(parent.node.parentNode);\n    if (!type) return parent;\n    do {\n      if (typeof type === \"string\" ? parent.matches(type) : parent instanceof type) return parent;\n    } while (parent = adopt(parent.node.parentNode));\n    return parent;\n  }\n  // Basically does the same as `add()` but returns the added element instead\n  put(element, i) {\n    element = makeInstance(element);\n    this.add(element, i);\n    return element;\n  }\n  // Add element to given container and return container\n  putIn(parent, i) {\n    return makeInstance(parent).add(this, i);\n  }\n  // Remove element\n  remove() {\n    if (this.parent()) {\n      this.parent().removeElement(this);\n    }\n    return this;\n  }\n  // Remove a given child\n  removeElement(element) {\n    this.node.removeChild(element.node);\n    return this;\n  }\n  // Replace this with element\n  replace(element) {\n    element = makeInstance(element);\n    if (this.node.parentNode) {\n      this.node.parentNode.replaceChild(element.node, this.node);\n    }\n    return element;\n  }\n  round(precision = 2, map2 = null) {\n    const factor = 10 ** precision;\n    const attrs2 = this.attr(map2);\n    for (const i in attrs2) {\n      if (typeof attrs2[i] === \"number\") {\n        attrs2[i] = Math.round(attrs2[i] * factor) / factor;\n      }\n    }\n    this.attr(attrs2);\n    return this;\n  }\n  // Import / Export raw svg\n  svg(svgOrFn, outerSVG) {\n    return this.xml(svgOrFn, outerSVG, svg);\n  }\n  // Return id on string conversion\n  toString() {\n    return this.id();\n  }\n  words(text) {\n    this.node.textContent = text;\n    return this;\n  }\n  wrap(node) {\n    const parent = this.parent();\n    if (!parent) {\n      return this.addTo(node);\n    }\n    const position2 = parent.index(this);\n    return parent.put(node, position2).put(this);\n  }\n  // write svgjs data to the dom\n  writeDataToDom() {\n    this.each(function () {\n      this.writeDataToDom();\n    });\n    return this;\n  }\n  // Import / Export raw svg\n  xml(xmlOrFn, outerXML, ns) {\n    if (typeof xmlOrFn === \"boolean\") {\n      ns = outerXML;\n      outerXML = xmlOrFn;\n      xmlOrFn = null;\n    }\n    if (xmlOrFn == null || typeof xmlOrFn === \"function\") {\n      outerXML = outerXML == null ? true : outerXML;\n      this.writeDataToDom();\n      let current = this;\n      if (xmlOrFn != null) {\n        current = adopt(current.node.cloneNode(true));\n        if (outerXML) {\n          const result = xmlOrFn(current);\n          current = result || current;\n          if (result === false) return \"\";\n        }\n        current.each(function () {\n          const result = xmlOrFn(this);\n          const _this = result || this;\n          if (result === false) {\n            this.remove();\n          } else if (result && this !== _this) {\n            this.replace(_this);\n          }\n        }, true);\n      }\n      return outerXML ? current.node.outerHTML : current.node.innerHTML;\n    }\n    outerXML = outerXML == null ? false : outerXML;\n    const well = create(\"wrapper\", ns);\n    const fragment = globals.document.createDocumentFragment();\n    well.innerHTML = xmlOrFn;\n    for (let len = well.children.length; len--;) {\n      fragment.appendChild(well.firstElementChild);\n    }\n    const parent = this.parent();\n    return outerXML ? this.replace(fragment) && parent : this.add(fragment);\n  }\n}\nextend(Dom, {\n  attr,\n  find,\n  findOne\n});\nregister(Dom, \"Dom\");\nclass Element extends Dom {\n  constructor(node, attrs2) {\n    super(node, attrs2);\n    this.dom = {};\n    this.node.instance = this;\n    if (node.hasAttribute(\"data-svgjs\") || node.hasAttribute(\"svgjs:data\")) {\n      this.setData(JSON.parse(node.getAttribute(\"data-svgjs\")) ?? JSON.parse(node.getAttribute(\"svgjs:data\")) ?? {});\n    }\n  }\n  // Move element by its center\n  center(x2, y2) {\n    return this.cx(x2).cy(y2);\n  }\n  // Move by center over x-axis\n  cx(x2) {\n    return x2 == null ? this.x() + this.width() / 2 : this.x(x2 - this.width() / 2);\n  }\n  // Move by center over y-axis\n  cy(y2) {\n    return y2 == null ? this.y() + this.height() / 2 : this.y(y2 - this.height() / 2);\n  }\n  // Get defs\n  defs() {\n    const root2 = this.root();\n    return root2 && root2.defs();\n  }\n  // Relative move over x and y axes\n  dmove(x2, y2) {\n    return this.dx(x2).dy(y2);\n  }\n  // Relative move over x axis\n  dx(x2 = 0) {\n    return this.x(new SVGNumber(x2).plus(this.x()));\n  }\n  // Relative move over y axis\n  dy(y2 = 0) {\n    return this.y(new SVGNumber(y2).plus(this.y()));\n  }\n  getEventHolder() {\n    return this;\n  }\n  // Set height of element\n  height(height2) {\n    return this.attr(\"height\", height2);\n  }\n  // Move element to given x and y values\n  move(x2, y2) {\n    return this.x(x2).y(y2);\n  }\n  // return array of all ancestors of given type up to the root svg\n  parents(until = this.root()) {\n    const isSelector = typeof until === \"string\";\n    if (!isSelector) {\n      until = makeInstance(until);\n    }\n    const parents = new List();\n    let parent = this;\n    while ((parent = parent.parent()) && parent.node !== globals.document && parent.nodeName !== \"#document-fragment\") {\n      parents.push(parent);\n      if (!isSelector && parent.node === until.node) {\n        break;\n      }\n      if (isSelector && parent.matches(until)) {\n        break;\n      }\n      if (parent.node === this.root().node) {\n        return null;\n      }\n    }\n    return parents;\n  }\n  // Get referenced element form attribute value\n  reference(attr2) {\n    attr2 = this.attr(attr2);\n    if (!attr2) return null;\n    const m = (attr2 + \"\").match(reference);\n    return m ? makeInstance(m[1]) : null;\n  }\n  // Get parent document\n  root() {\n    const p = this.parent(getClass(root));\n    return p && p.root();\n  }\n  // set given data to the elements data property\n  setData(o) {\n    this.dom = o;\n    return this;\n  }\n  // Set element size to given width and height\n  size(width2, height2) {\n    const p = proportionalSize(this, width2, height2);\n    return this.width(new SVGNumber(p.width)).height(new SVGNumber(p.height));\n  }\n  // Set width of element\n  width(width2) {\n    return this.attr(\"width\", width2);\n  }\n  // write svgjs data to the dom\n  writeDataToDom() {\n    writeDataToDom(this, this.dom);\n    return super.writeDataToDom();\n  }\n  // Move over x-axis\n  x(x2) {\n    return this.attr(\"x\", x2);\n  }\n  // Move over y-axis\n  y(y2) {\n    return this.attr(\"y\", y2);\n  }\n}\nextend(Element, {\n  bbox,\n  rbox,\n  inside,\n  point,\n  ctm,\n  screenCTM\n});\nregister(Element, \"Element\");\nconst sugar = {\n  stroke: [\"color\", \"width\", \"opacity\", \"linecap\", \"linejoin\", \"miterlimit\", \"dasharray\", \"dashoffset\"],\n  fill: [\"color\", \"opacity\", \"rule\"],\n  prefix: function (t, a) {\n    return a === \"color\" ? t : t + \"-\" + a;\n  }\n};\n[\"fill\", \"stroke\"].forEach(function (m) {\n  const extension = {};\n  let i;\n  extension[m] = function (o) {\n    if (typeof o === \"undefined\") {\n      return this.attr(m);\n    }\n    if (typeof o === \"string\" || o instanceof Color || Color.isRgb(o) || o instanceof Element) {\n      this.attr(m, o);\n    } else {\n      for (i = sugar[m].length - 1; i >= 0; i--) {\n        if (o[sugar[m][i]] != null) {\n          this.attr(sugar.prefix(m, sugar[m][i]), o[sugar[m][i]]);\n        }\n      }\n    }\n    return this;\n  };\n  registerMethods([\"Element\", \"Runner\"], extension);\n});\nregisterMethods([\"Element\", \"Runner\"], {\n  // Let the user set the matrix directly\n  matrix: function (mat, b, c, d, e, f) {\n    if (mat == null) {\n      return new Matrix(this);\n    }\n    return this.attr(\"transform\", new Matrix(mat, b, c, d, e, f));\n  },\n  // Map rotation to transform\n  rotate: function (angle, cx2, cy2) {\n    return this.transform({\n      rotate: angle,\n      ox: cx2,\n      oy: cy2\n    }, true);\n  },\n  // Map skew to transform\n  skew: function (x2, y2, cx2, cy2) {\n    return arguments.length === 1 || arguments.length === 3 ? this.transform({\n      skew: x2,\n      ox: y2,\n      oy: cx2\n    }, true) : this.transform({\n      skew: [x2, y2],\n      ox: cx2,\n      oy: cy2\n    }, true);\n  },\n  shear: function (lam, cx2, cy2) {\n    return this.transform({\n      shear: lam,\n      ox: cx2,\n      oy: cy2\n    }, true);\n  },\n  // Map scale to transform\n  scale: function (x2, y2, cx2, cy2) {\n    return arguments.length === 1 || arguments.length === 3 ? this.transform({\n      scale: x2,\n      ox: y2,\n      oy: cx2\n    }, true) : this.transform({\n      scale: [x2, y2],\n      ox: cx2,\n      oy: cy2\n    }, true);\n  },\n  // Map translate to transform\n  translate: function (x2, y2) {\n    return this.transform({\n      translate: [x2, y2]\n    }, true);\n  },\n  // Map relative translations to transform\n  relative: function (x2, y2) {\n    return this.transform({\n      relative: [x2, y2]\n    }, true);\n  },\n  // Map flip to transform\n  flip: function (direction = \"both\", origin = \"center\") {\n    if (\"xybothtrue\".indexOf(direction) === -1) {\n      origin = direction;\n      direction = \"both\";\n    }\n    return this.transform({\n      flip: direction,\n      origin\n    }, true);\n  },\n  // Opacity\n  opacity: function (value) {\n    return this.attr(\"opacity\", value);\n  }\n});\nregisterMethods(\"radius\", {\n  // Add x and y radius\n  radius: function (x2, y2 = x2) {\n    const type = (this._element || this).type;\n    return type === \"radialGradient\" ? this.attr(\"r\", new SVGNumber(x2)) : this.rx(x2).ry(y2);\n  }\n});\nregisterMethods(\"Path\", {\n  // Get path length\n  length: function () {\n    return this.node.getTotalLength();\n  },\n  // Get point at length\n  pointAt: function (length2) {\n    return new Point(this.node.getPointAtLength(length2));\n  }\n});\nregisterMethods([\"Element\", \"Runner\"], {\n  // Set font\n  font: function (a, v) {\n    if (typeof a === \"object\") {\n      for (v in a) this.font(v, a[v]);\n      return this;\n    }\n    return a === \"leading\" ? this.leading(v) : a === \"anchor\" ? this.attr(\"text-anchor\", v) : a === \"size\" || a === \"family\" || a === \"weight\" || a === \"stretch\" || a === \"variant\" || a === \"style\" ? this.attr(\"font-\" + a, v) : this.attr(a, v);\n  }\n});\nconst methods = [\"click\", \"dblclick\", \"mousedown\", \"mouseup\", \"mouseover\", \"mouseout\", \"mousemove\", \"mouseenter\", \"mouseleave\", \"touchstart\", \"touchmove\", \"touchleave\", \"touchend\", \"touchcancel\", \"contextmenu\", \"wheel\", \"pointerdown\", \"pointermove\", \"pointerup\", \"pointerleave\", \"pointercancel\"].reduce(function (last, event) {\n  const fn = function (f) {\n    if (f === null) {\n      this.off(event);\n    } else {\n      this.on(event, f);\n    }\n    return this;\n  };\n  last[event] = fn;\n  return last;\n}, {});\nregisterMethods(\"Element\", methods);\nfunction untransform() {\n  return this.attr(\"transform\", null);\n}\nfunction matrixify() {\n  const matrix = (this.attr(\"transform\") || \"\").split(transforms).slice(0, -1).map(function (str) {\n    const kv = str.trim().split(\"(\");\n    return [kv[0], kv[1].split(delimiter).map(function (str2) {\n      return parseFloat(str2);\n    })];\n  }).reverse().reduce(function (matrix2, transform2) {\n    if (transform2[0] === \"matrix\") {\n      return matrix2.lmultiply(Matrix.fromArray(transform2[1]));\n    }\n    return matrix2[transform2[0]].apply(matrix2, transform2[1]);\n  }, new Matrix());\n  return matrix;\n}\nfunction toParent(parent, i) {\n  if (this === parent) return this;\n  if (isDescriptive(this.node)) return this.addTo(parent, i);\n  const ctm2 = this.screenCTM();\n  const pCtm = parent.screenCTM().inverse();\n  this.addTo(parent, i).untransform().transform(pCtm.multiply(ctm2));\n  return this;\n}\nfunction toRoot(i) {\n  return this.toParent(this.root(), i);\n}\nfunction transform(o, relative) {\n  if (o == null || typeof o === \"string\") {\n    const decomposed = new Matrix(this).decompose();\n    return o == null ? decomposed : decomposed[o];\n  }\n  if (!Matrix.isMatrixLike(o)) {\n    o = {\n      ...o,\n      origin: getOrigin(o, this)\n    };\n  }\n  const cleanRelative = relative === true ? this : relative || false;\n  const result = new Matrix(cleanRelative).transform(o);\n  return this.attr(\"transform\", result);\n}\nregisterMethods(\"Element\", {\n  untransform,\n  matrixify,\n  toParent,\n  toRoot,\n  transform\n});\nclass Container extends Element {\n  flatten() {\n    this.each(function () {\n      if (this instanceof Container) {\n        return this.flatten().ungroup();\n      }\n    });\n    return this;\n  }\n  ungroup(parent = this.parent(), index = parent.index(this)) {\n    index = index === -1 ? parent.children().length : index;\n    this.each(function (i, children) {\n      return children[children.length - i - 1].toParent(parent, index);\n    });\n    return this.remove();\n  }\n}\nregister(Container, \"Container\");\nclass Defs extends Container {\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"defs\", node), attrs2);\n  }\n  flatten() {\n    return this;\n  }\n  ungroup() {\n    return this;\n  }\n}\nregister(Defs, \"Defs\");\nclass Shape extends Element {}\nregister(Shape, \"Shape\");\nfunction rx(rx2) {\n  return this.attr(\"rx\", rx2);\n}\nfunction ry(ry2) {\n  return this.attr(\"ry\", ry2);\n}\nfunction x$3(x2) {\n  return x2 == null ? this.cx() - this.rx() : this.cx(x2 + this.rx());\n}\nfunction y$3(y2) {\n  return y2 == null ? this.cy() - this.ry() : this.cy(y2 + this.ry());\n}\nfunction cx$1(x2) {\n  return this.attr(\"cx\", x2);\n}\nfunction cy$1(y2) {\n  return this.attr(\"cy\", y2);\n}\nfunction width$2(width2) {\n  return width2 == null ? this.rx() * 2 : this.rx(new SVGNumber(width2).divide(2));\n}\nfunction height$2(height2) {\n  return height2 == null ? this.ry() * 2 : this.ry(new SVGNumber(height2).divide(2));\n}\nconst circled = /* @__PURE__ */Object.freeze(/* @__PURE__ */Object.defineProperty({\n  __proto__: null,\n  cx: cx$1,\n  cy: cy$1,\n  height: height$2,\n  rx,\n  ry,\n  width: width$2,\n  x: x$3,\n  y: y$3\n}, Symbol.toStringTag, {\n  value: \"Module\"\n}));\nclass Ellipse extends Shape {\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"ellipse\", node), attrs2);\n  }\n  size(width2, height2) {\n    const p = proportionalSize(this, width2, height2);\n    return this.rx(new SVGNumber(p.width).divide(2)).ry(new SVGNumber(p.height).divide(2));\n  }\n}\nextend(Ellipse, circled);\nregisterMethods(\"Container\", {\n  // Create an ellipse\n  ellipse: wrapWithAttrCheck(function (width2 = 0, height2 = width2) {\n    return this.put(new Ellipse()).size(width2, height2).move(0, 0);\n  })\n});\nregister(Ellipse, \"Ellipse\");\nclass Fragment extends Dom {\n  constructor(node = globals.document.createDocumentFragment()) {\n    super(node);\n  }\n  // Import / Export raw xml\n  xml(xmlOrFn, outerXML, ns) {\n    if (typeof xmlOrFn === \"boolean\") {\n      ns = outerXML;\n      outerXML = xmlOrFn;\n      xmlOrFn = null;\n    }\n    if (xmlOrFn == null || typeof xmlOrFn === \"function\") {\n      const wrapper = new Dom(create(\"wrapper\", ns));\n      wrapper.add(this.node.cloneNode(true));\n      return wrapper.xml(false, ns);\n    }\n    return super.xml(xmlOrFn, false, ns);\n  }\n}\nregister(Fragment, \"Fragment\");\nfunction from(x2, y2) {\n  return (this._element || this).type === \"radialGradient\" ? this.attr({\n    fx: new SVGNumber(x2),\n    fy: new SVGNumber(y2)\n  }) : this.attr({\n    x1: new SVGNumber(x2),\n    y1: new SVGNumber(y2)\n  });\n}\nfunction to(x2, y2) {\n  return (this._element || this).type === \"radialGradient\" ? this.attr({\n    cx: new SVGNumber(x2),\n    cy: new SVGNumber(y2)\n  }) : this.attr({\n    x2: new SVGNumber(x2),\n    y2: new SVGNumber(y2)\n  });\n}\nconst gradiented = /* @__PURE__ */Object.freeze(/* @__PURE__ */Object.defineProperty({\n  __proto__: null,\n  from,\n  to\n}, Symbol.toStringTag, {\n  value: \"Module\"\n}));\nclass Gradient extends Container {\n  constructor(type, attrs2) {\n    super(nodeOrNew(type + \"Gradient\", typeof type === \"string\" ? null : type), attrs2);\n  }\n  // custom attr to handle transform\n  attr(a, b, c) {\n    if (a === \"transform\") a = \"gradientTransform\";\n    return super.attr(a, b, c);\n  }\n  bbox() {\n    return new Box();\n  }\n  targets() {\n    return baseFind(\"svg [fill*=\" + this.id() + \"]\");\n  }\n  // Alias string conversion to fill\n  toString() {\n    return this.url();\n  }\n  // Update gradient\n  update(block) {\n    this.clear();\n    if (typeof block === \"function\") {\n      block.call(this, this);\n    }\n    return this;\n  }\n  // Return the fill id\n  url() {\n    return \"url(#\" + this.id() + \")\";\n  }\n}\nextend(Gradient, gradiented);\nregisterMethods({\n  Container: {\n    // Create gradient element in defs\n    gradient(...args) {\n      return this.defs().gradient(...args);\n    }\n  },\n  // define gradient\n  Defs: {\n    gradient: wrapWithAttrCheck(function (type, block) {\n      return this.put(new Gradient(type)).update(block);\n    })\n  }\n});\nregister(Gradient, \"Gradient\");\nclass Pattern extends Container {\n  // Initialize node\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"pattern\", node), attrs2);\n  }\n  // custom attr to handle transform\n  attr(a, b, c) {\n    if (a === \"transform\") a = \"patternTransform\";\n    return super.attr(a, b, c);\n  }\n  bbox() {\n    return new Box();\n  }\n  targets() {\n    return baseFind(\"svg [fill*=\" + this.id() + \"]\");\n  }\n  // Alias string conversion to fill\n  toString() {\n    return this.url();\n  }\n  // Update pattern by rebuilding\n  update(block) {\n    this.clear();\n    if (typeof block === \"function\") {\n      block.call(this, this);\n    }\n    return this;\n  }\n  // Return the fill id\n  url() {\n    return \"url(#\" + this.id() + \")\";\n  }\n}\nregisterMethods({\n  Container: {\n    // Create pattern element in defs\n    pattern(...args) {\n      return this.defs().pattern(...args);\n    }\n  },\n  Defs: {\n    pattern: wrapWithAttrCheck(function (width2, height2, block) {\n      return this.put(new Pattern()).update(block).attr({\n        x: 0,\n        y: 0,\n        width: width2,\n        height: height2,\n        patternUnits: \"userSpaceOnUse\"\n      });\n    })\n  }\n});\nregister(Pattern, \"Pattern\");\nlet Image$1 = class Image2 extends Shape {\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"image\", node), attrs2);\n  }\n  // (re)load image\n  load(url, callback) {\n    if (!url) return this;\n    const img = new globals.window.Image();\n    on(img, \"load\", function (e) {\n      const p = this.parent(Pattern);\n      if (this.width() === 0 && this.height() === 0) {\n        this.size(img.width, img.height);\n      }\n      if (p instanceof Pattern) {\n        if (p.width() === 0 && p.height() === 0) {\n          p.size(this.width(), this.height());\n        }\n      }\n      if (typeof callback === \"function\") {\n        callback.call(this, e);\n      }\n    }, this);\n    on(img, \"load error\", function () {\n      off(img);\n    });\n    return this.attr(\"href\", img.src = url, xlink);\n  }\n};\nregisterAttrHook(function (attr2, val, _this) {\n  if (attr2 === \"fill\" || attr2 === \"stroke\") {\n    if (isImage.test(val)) {\n      val = _this.root().defs().image(val);\n    }\n  }\n  if (val instanceof Image$1) {\n    val = _this.root().defs().pattern(0, 0, pattern => {\n      pattern.add(val);\n    });\n  }\n  return val;\n});\nregisterMethods({\n  Container: {\n    // create image element, load image and set its size\n    image: wrapWithAttrCheck(function (source, callback) {\n      return this.put(new Image$1()).size(0, 0).load(source, callback);\n    })\n  }\n});\nregister(Image$1, \"Image\");\nclass PointArray extends SVGArray {\n  // Get bounding box of points\n  bbox() {\n    let maxX = -Infinity;\n    let maxY = -Infinity;\n    let minX = Infinity;\n    let minY = Infinity;\n    this.forEach(function (el) {\n      maxX = Math.max(el[0], maxX);\n      maxY = Math.max(el[1], maxY);\n      minX = Math.min(el[0], minX);\n      minY = Math.min(el[1], minY);\n    });\n    return new Box(minX, minY, maxX - minX, maxY - minY);\n  }\n  // Move point string\n  move(x2, y2) {\n    const box = this.bbox();\n    x2 -= box.x;\n    y2 -= box.y;\n    if (!isNaN(x2) && !isNaN(y2)) {\n      for (let i = this.length - 1; i >= 0; i--) {\n        this[i] = [this[i][0] + x2, this[i][1] + y2];\n      }\n    }\n    return this;\n  }\n  // Parse point string and flat array\n  parse(array2 = [0, 0]) {\n    const points = [];\n    if (array2 instanceof Array) {\n      array2 = Array.prototype.concat.apply([], array2);\n    } else {\n      array2 = array2.trim().split(delimiter).map(parseFloat);\n    }\n    if (array2.length % 2 !== 0) array2.pop();\n    for (let i = 0, len = array2.length; i < len; i = i + 2) {\n      points.push([array2[i], array2[i + 1]]);\n    }\n    return points;\n  }\n  // Resize poly string\n  size(width2, height2) {\n    let i;\n    const box = this.bbox();\n    for (i = this.length - 1; i >= 0; i--) {\n      if (box.width) this[i][0] = (this[i][0] - box.x) * width2 / box.width + box.x;\n      if (box.height) this[i][1] = (this[i][1] - box.y) * height2 / box.height + box.y;\n    }\n    return this;\n  }\n  // Convert array to line object\n  toLine() {\n    return {\n      x1: this[0][0],\n      y1: this[0][1],\n      x2: this[1][0],\n      y2: this[1][1]\n    };\n  }\n  // Convert array to string\n  toString() {\n    const array2 = [];\n    for (let i = 0, il = this.length; i < il; i++) {\n      array2.push(this[i].join(\",\"));\n    }\n    return array2.join(\" \");\n  }\n  transform(m) {\n    return this.clone().transformO(m);\n  }\n  // transform points with matrix (similar to Point.transform)\n  transformO(m) {\n    if (!Matrix.isMatrixLike(m)) {\n      m = new Matrix(m);\n    }\n    for (let i = this.length; i--;) {\n      const [x2, y2] = this[i];\n      this[i][0] = m.a * x2 + m.c * y2 + m.e;\n      this[i][1] = m.b * x2 + m.d * y2 + m.f;\n    }\n    return this;\n  }\n}\nconst MorphArray = PointArray;\nfunction x$2(x2) {\n  return x2 == null ? this.bbox().x : this.move(x2, this.bbox().y);\n}\nfunction y$2(y2) {\n  return y2 == null ? this.bbox().y : this.move(this.bbox().x, y2);\n}\nfunction width$1(width2) {\n  const b = this.bbox();\n  return width2 == null ? b.width : this.size(width2, b.height);\n}\nfunction height$1(height2) {\n  const b = this.bbox();\n  return height2 == null ? b.height : this.size(b.width, height2);\n}\nconst pointed = /* @__PURE__ */Object.freeze(/* @__PURE__ */Object.defineProperty({\n  __proto__: null,\n  MorphArray,\n  height: height$1,\n  width: width$1,\n  x: x$2,\n  y: y$2\n}, Symbol.toStringTag, {\n  value: \"Module\"\n}));\nclass Line extends Shape {\n  // Initialize node\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"line\", node), attrs2);\n  }\n  // Get array\n  array() {\n    return new PointArray([[this.attr(\"x1\"), this.attr(\"y1\")], [this.attr(\"x2\"), this.attr(\"y2\")]]);\n  }\n  // Move by left top corner\n  move(x2, y2) {\n    return this.attr(this.array().move(x2, y2).toLine());\n  }\n  // Overwrite native plot() method\n  plot(x1, y1, x2, y2) {\n    if (x1 == null) {\n      return this.array();\n    } else if (typeof y1 !== \"undefined\") {\n      x1 = {\n        x1,\n        y1,\n        x2,\n        y2\n      };\n    } else {\n      x1 = new PointArray(x1).toLine();\n    }\n    return this.attr(x1);\n  }\n  // Set element size to given width and height\n  size(width2, height2) {\n    const p = proportionalSize(this, width2, height2);\n    return this.attr(this.array().size(p.width, p.height).toLine());\n  }\n}\nextend(Line, pointed);\nregisterMethods({\n  Container: {\n    // Create a line element\n    line: wrapWithAttrCheck(function (...args) {\n      return Line.prototype.plot.apply(this.put(new Line()), args[0] != null ? args : [0, 0, 0, 0]);\n    })\n  }\n});\nregister(Line, \"Line\");\nclass Marker extends Container {\n  // Initialize node\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"marker\", node), attrs2);\n  }\n  // Set height of element\n  height(height2) {\n    return this.attr(\"markerHeight\", height2);\n  }\n  orient(orient) {\n    return this.attr(\"orient\", orient);\n  }\n  // Set marker refX and refY\n  ref(x2, y2) {\n    return this.attr(\"refX\", x2).attr(\"refY\", y2);\n  }\n  // Return the fill id\n  toString() {\n    return \"url(#\" + this.id() + \")\";\n  }\n  // Update marker\n  update(block) {\n    this.clear();\n    if (typeof block === \"function\") {\n      block.call(this, this);\n    }\n    return this;\n  }\n  // Set width of element\n  width(width2) {\n    return this.attr(\"markerWidth\", width2);\n  }\n}\nregisterMethods({\n  Container: {\n    marker(...args) {\n      return this.defs().marker(...args);\n    }\n  },\n  Defs: {\n    // Create marker\n    marker: wrapWithAttrCheck(function (width2, height2, block) {\n      return this.put(new Marker()).size(width2, height2).ref(width2 / 2, height2 / 2).viewbox(0, 0, width2, height2).attr(\"orient\", \"auto\").update(block);\n    })\n  },\n  marker: {\n    // Create and attach markers\n    marker(marker, width2, height2, block) {\n      let attr2 = [\"marker\"];\n      if (marker !== \"all\") attr2.push(marker);\n      attr2 = attr2.join(\"-\");\n      marker = arguments[1] instanceof Marker ? arguments[1] : this.defs().marker(width2, height2, block);\n      return this.attr(attr2, marker);\n    }\n  }\n});\nregister(Marker, \"Marker\");\nfunction makeSetterGetter(k, f) {\n  return function (v) {\n    if (v == null) return this[k];\n    this[k] = v;\n    if (f) f.call(this);\n    return this;\n  };\n}\nconst easing = {\n  \"-\": function (pos) {\n    return pos;\n  },\n  \"<>\": function (pos) {\n    return -Math.cos(pos * Math.PI) / 2 + 0.5;\n  },\n  \">\": function (pos) {\n    return Math.sin(pos * Math.PI / 2);\n  },\n  \"<\": function (pos) {\n    return -Math.cos(pos * Math.PI / 2) + 1;\n  },\n  bezier: function (x1, y1, x2, y2) {\n    return function (t) {\n      if (t < 0) {\n        if (x1 > 0) {\n          return y1 / x1 * t;\n        } else if (x2 > 0) {\n          return y2 / x2 * t;\n        } else {\n          return 0;\n        }\n      } else if (t > 1) {\n        if (x2 < 1) {\n          return (1 - y2) / (1 - x2) * t + (y2 - x2) / (1 - x2);\n        } else if (x1 < 1) {\n          return (1 - y1) / (1 - x1) * t + (y1 - x1) / (1 - x1);\n        } else {\n          return 1;\n        }\n      } else {\n        return 3 * t * (1 - t) ** 2 * y1 + 3 * t ** 2 * (1 - t) * y2 + t ** 3;\n      }\n    };\n  },\n  // see https://www.w3.org/TR/css-easing-1/#step-timing-function-algo\n  steps: function (steps, stepPosition = \"end\") {\n    stepPosition = stepPosition.split(\"-\").reverse()[0];\n    let jumps = steps;\n    if (stepPosition === \"none\") {\n      --jumps;\n    } else if (stepPosition === \"both\") {\n      ++jumps;\n    }\n    return (t, beforeFlag = false) => {\n      let step = Math.floor(t * steps);\n      const jumping = t * step % 1 === 0;\n      if (stepPosition === \"start\" || stepPosition === \"both\") {\n        ++step;\n      }\n      if (beforeFlag && jumping) {\n        --step;\n      }\n      if (t >= 0 && step < 0) {\n        step = 0;\n      }\n      if (t <= 1 && step > jumps) {\n        step = jumps;\n      }\n      return step / jumps;\n    };\n  }\n};\nclass Stepper {\n  done() {\n    return false;\n  }\n}\nclass Ease extends Stepper {\n  constructor(fn = timeline.ease) {\n    super();\n    this.ease = easing[fn] || fn;\n  }\n  step(from2, to2, pos) {\n    if (typeof from2 !== \"number\") {\n      return pos < 1 ? from2 : to2;\n    }\n    return from2 + (to2 - from2) * this.ease(pos);\n  }\n}\nclass Controller extends Stepper {\n  constructor(fn) {\n    super();\n    this.stepper = fn;\n  }\n  done(c) {\n    return c.done;\n  }\n  step(current, target, dt, c) {\n    return this.stepper(current, target, dt, c);\n  }\n}\nfunction recalculate() {\n  const duration = (this._duration || 500) / 1e3;\n  const overshoot = this._overshoot || 0;\n  const eps = 1e-10;\n  const pi = Math.PI;\n  const os = Math.log(overshoot / 100 + eps);\n  const zeta = -os / Math.sqrt(pi * pi + os * os);\n  const wn = 3.9 / (zeta * duration);\n  this.d = 2 * zeta * wn;\n  this.k = wn * wn;\n}\nclass Spring extends Controller {\n  constructor(duration = 500, overshoot = 0) {\n    super();\n    this.duration(duration).overshoot(overshoot);\n  }\n  step(current, target, dt, c) {\n    if (typeof current === \"string\") return current;\n    c.done = dt === Infinity;\n    if (dt === Infinity) return target;\n    if (dt === 0) return current;\n    if (dt > 100) dt = 16;\n    dt /= 1e3;\n    const velocity = c.velocity || 0;\n    const acceleration = -this.d * velocity - this.k * (current - target);\n    const newPosition = current + velocity * dt + acceleration * dt * dt / 2;\n    c.velocity = velocity + acceleration * dt;\n    c.done = Math.abs(target - newPosition) + Math.abs(velocity) < 2e-3;\n    return c.done ? target : newPosition;\n  }\n}\nextend(Spring, {\n  duration: makeSetterGetter(\"_duration\", recalculate),\n  overshoot: makeSetterGetter(\"_overshoot\", recalculate)\n});\nclass PID extends Controller {\n  constructor(p = 0.1, i = 0.01, d = 0, windup = 1e3) {\n    super();\n    this.p(p).i(i).d(d).windup(windup);\n  }\n  step(current, target, dt, c) {\n    if (typeof current === \"string\") return current;\n    c.done = dt === Infinity;\n    if (dt === Infinity) return target;\n    if (dt === 0) return current;\n    const p = target - current;\n    let i = (c.integral || 0) + p * dt;\n    const d = (p - (c.error || 0)) / dt;\n    const windup = this._windup;\n    if (windup !== false) {\n      i = Math.max(-windup, Math.min(i, windup));\n    }\n    c.error = p;\n    c.integral = i;\n    c.done = Math.abs(p) < 1e-3;\n    return c.done ? target : current + (this.P * p + this.I * i + this.D * d);\n  }\n}\nextend(PID, {\n  windup: makeSetterGetter(\"_windup\"),\n  p: makeSetterGetter(\"P\"),\n  i: makeSetterGetter(\"I\"),\n  d: makeSetterGetter(\"D\")\n});\nconst segmentParameters = {\n  M: 2,\n  L: 2,\n  H: 1,\n  V: 1,\n  C: 6,\n  S: 4,\n  Q: 4,\n  T: 2,\n  A: 7,\n  Z: 0\n};\nconst pathHandlers = {\n  M: function (c, p, p0) {\n    p.x = p0.x = c[0];\n    p.y = p0.y = c[1];\n    return [\"M\", p.x, p.y];\n  },\n  L: function (c, p) {\n    p.x = c[0];\n    p.y = c[1];\n    return [\"L\", c[0], c[1]];\n  },\n  H: function (c, p) {\n    p.x = c[0];\n    return [\"H\", c[0]];\n  },\n  V: function (c, p) {\n    p.y = c[0];\n    return [\"V\", c[0]];\n  },\n  C: function (c, p) {\n    p.x = c[4];\n    p.y = c[5];\n    return [\"C\", c[0], c[1], c[2], c[3], c[4], c[5]];\n  },\n  S: function (c, p) {\n    p.x = c[2];\n    p.y = c[3];\n    return [\"S\", c[0], c[1], c[2], c[3]];\n  },\n  Q: function (c, p) {\n    p.x = c[2];\n    p.y = c[3];\n    return [\"Q\", c[0], c[1], c[2], c[3]];\n  },\n  T: function (c, p) {\n    p.x = c[0];\n    p.y = c[1];\n    return [\"T\", c[0], c[1]];\n  },\n  Z: function (c, p, p0) {\n    p.x = p0.x;\n    p.y = p0.y;\n    return [\"Z\"];\n  },\n  A: function (c, p) {\n    p.x = c[5];\n    p.y = c[6];\n    return [\"A\", c[0], c[1], c[2], c[3], c[4], c[5], c[6]];\n  }\n};\nconst mlhvqtcsaz = \"mlhvqtcsaz\".split(\"\");\nfor (let i = 0, il = mlhvqtcsaz.length; i < il; ++i) {\n  pathHandlers[mlhvqtcsaz[i]] = /* @__PURE__ */function (i2) {\n    return function (c, p, p0) {\n      if (i2 === \"H\") c[0] = c[0] + p.x;else if (i2 === \"V\") c[0] = c[0] + p.y;else if (i2 === \"A\") {\n        c[5] = c[5] + p.x;\n        c[6] = c[6] + p.y;\n      } else {\n        for (let j = 0, jl = c.length; j < jl; ++j) {\n          c[j] = c[j] + (j % 2 ? p.y : p.x);\n        }\n      }\n      return pathHandlers[i2](c, p, p0);\n    };\n  }(mlhvqtcsaz[i].toUpperCase());\n}\nfunction makeAbsolut(parser2) {\n  const command = parser2.segment[0];\n  return pathHandlers[command](parser2.segment.slice(1), parser2.p, parser2.p0);\n}\nfunction segmentComplete(parser2) {\n  return parser2.segment.length && parser2.segment.length - 1 === segmentParameters[parser2.segment[0].toUpperCase()];\n}\nfunction startNewSegment(parser2, token) {\n  parser2.inNumber && finalizeNumber(parser2, false);\n  const pathLetter = isPathLetter.test(token);\n  if (pathLetter) {\n    parser2.segment = [token];\n  } else {\n    const lastCommand = parser2.lastCommand;\n    const small = lastCommand.toLowerCase();\n    const isSmall = lastCommand === small;\n    parser2.segment = [small === \"m\" ? isSmall ? \"l\" : \"L\" : lastCommand];\n  }\n  parser2.inSegment = true;\n  parser2.lastCommand = parser2.segment[0];\n  return pathLetter;\n}\nfunction finalizeNumber(parser2, inNumber) {\n  if (!parser2.inNumber) throw new Error(\"Parser Error\");\n  parser2.number && parser2.segment.push(parseFloat(parser2.number));\n  parser2.inNumber = inNumber;\n  parser2.number = \"\";\n  parser2.pointSeen = false;\n  parser2.hasExponent = false;\n  if (segmentComplete(parser2)) {\n    finalizeSegment(parser2);\n  }\n}\nfunction finalizeSegment(parser2) {\n  parser2.inSegment = false;\n  if (parser2.absolute) {\n    parser2.segment = makeAbsolut(parser2);\n  }\n  parser2.segments.push(parser2.segment);\n}\nfunction isArcFlag(parser2) {\n  if (!parser2.segment.length) return false;\n  const isArc = parser2.segment[0].toUpperCase() === \"A\";\n  const length2 = parser2.segment.length;\n  return isArc && (length2 === 4 || length2 === 5);\n}\nfunction isExponential(parser2) {\n  return parser2.lastToken.toUpperCase() === \"E\";\n}\nconst pathDelimiters = /* @__PURE__ */new Set([\" \", \",\", \"\t\", \"\\n\", \"\\r\", \"\\f\"]);\nfunction pathParser(d, toAbsolute = true) {\n  let index = 0;\n  let token = \"\";\n  const parser2 = {\n    segment: [],\n    inNumber: false,\n    number: \"\",\n    lastToken: \"\",\n    inSegment: false,\n    segments: [],\n    pointSeen: false,\n    hasExponent: false,\n    absolute: toAbsolute,\n    p0: new Point(),\n    p: new Point()\n  };\n  while (parser2.lastToken = token, token = d.charAt(index++)) {\n    if (!parser2.inSegment) {\n      if (startNewSegment(parser2, token)) {\n        continue;\n      }\n    }\n    if (token === \".\") {\n      if (parser2.pointSeen || parser2.hasExponent) {\n        finalizeNumber(parser2, false);\n        --index;\n        continue;\n      }\n      parser2.inNumber = true;\n      parser2.pointSeen = true;\n      parser2.number += token;\n      continue;\n    }\n    if (!isNaN(parseInt(token))) {\n      if (parser2.number === \"0\" || isArcFlag(parser2)) {\n        parser2.inNumber = true;\n        parser2.number = token;\n        finalizeNumber(parser2, true);\n        continue;\n      }\n      parser2.inNumber = true;\n      parser2.number += token;\n      continue;\n    }\n    if (pathDelimiters.has(token)) {\n      if (parser2.inNumber) {\n        finalizeNumber(parser2, false);\n      }\n      continue;\n    }\n    if (token === \"-\" || token === \"+\") {\n      if (parser2.inNumber && !isExponential(parser2)) {\n        finalizeNumber(parser2, false);\n        --index;\n        continue;\n      }\n      parser2.number += token;\n      parser2.inNumber = true;\n      continue;\n    }\n    if (token.toUpperCase() === \"E\") {\n      parser2.number += token;\n      parser2.hasExponent = true;\n      continue;\n    }\n    if (isPathLetter.test(token)) {\n      if (parser2.inNumber) {\n        finalizeNumber(parser2, false);\n      } else if (!segmentComplete(parser2)) {\n        throw new Error(\"parser Error\");\n      } else {\n        finalizeSegment(parser2);\n      }\n      --index;\n    }\n  }\n  if (parser2.inNumber) {\n    finalizeNumber(parser2, false);\n  }\n  if (parser2.inSegment && segmentComplete(parser2)) {\n    finalizeSegment(parser2);\n  }\n  return parser2.segments;\n}\nfunction arrayToString(a) {\n  let s = \"\";\n  for (let i = 0, il = a.length; i < il; i++) {\n    s += a[i][0];\n    if (a[i][1] != null) {\n      s += a[i][1];\n      if (a[i][2] != null) {\n        s += \" \";\n        s += a[i][2];\n        if (a[i][3] != null) {\n          s += \" \";\n          s += a[i][3];\n          s += \" \";\n          s += a[i][4];\n          if (a[i][5] != null) {\n            s += \" \";\n            s += a[i][5];\n            s += \" \";\n            s += a[i][6];\n            if (a[i][7] != null) {\n              s += \" \";\n              s += a[i][7];\n            }\n          }\n        }\n      }\n    }\n  }\n  return s + \" \";\n}\nclass PathArray extends SVGArray {\n  // Get bounding box of path\n  bbox() {\n    parser().path.setAttribute(\"d\", this.toString());\n    return new Box(parser.nodes.path.getBBox());\n  }\n  // Move path string\n  move(x2, y2) {\n    const box = this.bbox();\n    x2 -= box.x;\n    y2 -= box.y;\n    if (!isNaN(x2) && !isNaN(y2)) {\n      for (let l, i = this.length - 1; i >= 0; i--) {\n        l = this[i][0];\n        if (l === \"M\" || l === \"L\" || l === \"T\") {\n          this[i][1] += x2;\n          this[i][2] += y2;\n        } else if (l === \"H\") {\n          this[i][1] += x2;\n        } else if (l === \"V\") {\n          this[i][1] += y2;\n        } else if (l === \"C\" || l === \"S\" || l === \"Q\") {\n          this[i][1] += x2;\n          this[i][2] += y2;\n          this[i][3] += x2;\n          this[i][4] += y2;\n          if (l === \"C\") {\n            this[i][5] += x2;\n            this[i][6] += y2;\n          }\n        } else if (l === \"A\") {\n          this[i][6] += x2;\n          this[i][7] += y2;\n        }\n      }\n    }\n    return this;\n  }\n  // Absolutize and parse path to array\n  parse(d = \"M0 0\") {\n    if (Array.isArray(d)) {\n      d = Array.prototype.concat.apply([], d).toString();\n    }\n    return pathParser(d);\n  }\n  // Resize path string\n  size(width2, height2) {\n    const box = this.bbox();\n    let i, l;\n    box.width = box.width === 0 ? 1 : box.width;\n    box.height = box.height === 0 ? 1 : box.height;\n    for (i = this.length - 1; i >= 0; i--) {\n      l = this[i][0];\n      if (l === \"M\" || l === \"L\" || l === \"T\") {\n        this[i][1] = (this[i][1] - box.x) * width2 / box.width + box.x;\n        this[i][2] = (this[i][2] - box.y) * height2 / box.height + box.y;\n      } else if (l === \"H\") {\n        this[i][1] = (this[i][1] - box.x) * width2 / box.width + box.x;\n      } else if (l === \"V\") {\n        this[i][1] = (this[i][1] - box.y) * height2 / box.height + box.y;\n      } else if (l === \"C\" || l === \"S\" || l === \"Q\") {\n        this[i][1] = (this[i][1] - box.x) * width2 / box.width + box.x;\n        this[i][2] = (this[i][2] - box.y) * height2 / box.height + box.y;\n        this[i][3] = (this[i][3] - box.x) * width2 / box.width + box.x;\n        this[i][4] = (this[i][4] - box.y) * height2 / box.height + box.y;\n        if (l === \"C\") {\n          this[i][5] = (this[i][5] - box.x) * width2 / box.width + box.x;\n          this[i][6] = (this[i][6] - box.y) * height2 / box.height + box.y;\n        }\n      } else if (l === \"A\") {\n        this[i][1] = this[i][1] * width2 / box.width;\n        this[i][2] = this[i][2] * height2 / box.height;\n        this[i][6] = (this[i][6] - box.x) * width2 / box.width + box.x;\n        this[i][7] = (this[i][7] - box.y) * height2 / box.height + box.y;\n      }\n    }\n    return this;\n  }\n  // Convert array to string\n  toString() {\n    return arrayToString(this);\n  }\n}\nconst getClassForType = value => {\n  const type = typeof value;\n  if (type === \"number\") {\n    return SVGNumber;\n  } else if (type === \"string\") {\n    if (Color.isColor(value)) {\n      return Color;\n    } else if (delimiter.test(value)) {\n      return isPathLetter.test(value) ? PathArray : SVGArray;\n    } else if (numberAndUnit.test(value)) {\n      return SVGNumber;\n    } else {\n      return NonMorphable;\n    }\n  } else if (morphableTypes.indexOf(value.constructor) > -1) {\n    return value.constructor;\n  } else if (Array.isArray(value)) {\n    return SVGArray;\n  } else if (type === \"object\") {\n    return ObjectBag;\n  } else {\n    return NonMorphable;\n  }\n};\nclass Morphable {\n  constructor(stepper) {\n    this._stepper = stepper || new Ease(\"-\");\n    this._from = null;\n    this._to = null;\n    this._type = null;\n    this._context = null;\n    this._morphObj = null;\n  }\n  at(pos) {\n    return this._morphObj.morph(this._from, this._to, pos, this._stepper, this._context);\n  }\n  done() {\n    const complete = this._context.map(this._stepper.done).reduce(function (last, curr) {\n      return last && curr;\n    }, true);\n    return complete;\n  }\n  from(val) {\n    if (val == null) {\n      return this._from;\n    }\n    this._from = this._set(val);\n    return this;\n  }\n  stepper(stepper) {\n    if (stepper == null) return this._stepper;\n    this._stepper = stepper;\n    return this;\n  }\n  to(val) {\n    if (val == null) {\n      return this._to;\n    }\n    this._to = this._set(val);\n    return this;\n  }\n  type(type) {\n    if (type == null) {\n      return this._type;\n    }\n    this._type = type;\n    return this;\n  }\n  _set(value) {\n    if (!this._type) {\n      this.type(getClassForType(value));\n    }\n    let result = new this._type(value);\n    if (this._type === Color) {\n      result = this._to ? result[this._to[4]]() : this._from ? result[this._from[4]]() : result;\n    }\n    if (this._type === ObjectBag) {\n      result = this._to ? result.align(this._to) : this._from ? result.align(this._from) : result;\n    }\n    result = result.toConsumable();\n    this._morphObj = this._morphObj || new this._type();\n    this._context = this._context || Array.apply(null, Array(result.length)).map(Object).map(function (o) {\n      o.done = true;\n      return o;\n    });\n    return result;\n  }\n}\nclass NonMorphable {\n  constructor(...args) {\n    this.init(...args);\n  }\n  init(val) {\n    val = Array.isArray(val) ? val[0] : val;\n    this.value = val;\n    return this;\n  }\n  toArray() {\n    return [this.value];\n  }\n  valueOf() {\n    return this.value;\n  }\n}\nclass TransformBag {\n  constructor(...args) {\n    this.init(...args);\n  }\n  init(obj) {\n    if (Array.isArray(obj)) {\n      obj = {\n        scaleX: obj[0],\n        scaleY: obj[1],\n        shear: obj[2],\n        rotate: obj[3],\n        translateX: obj[4],\n        translateY: obj[5],\n        originX: obj[6],\n        originY: obj[7]\n      };\n    }\n    Object.assign(this, TransformBag.defaults, obj);\n    return this;\n  }\n  toArray() {\n    const v = this;\n    return [v.scaleX, v.scaleY, v.shear, v.rotate, v.translateX, v.translateY, v.originX, v.originY];\n  }\n}\nTransformBag.defaults = {\n  scaleX: 1,\n  scaleY: 1,\n  shear: 0,\n  rotate: 0,\n  translateX: 0,\n  translateY: 0,\n  originX: 0,\n  originY: 0\n};\nconst sortByKey = (a, b) => {\n  return a[0] < b[0] ? -1 : a[0] > b[0] ? 1 : 0;\n};\nclass ObjectBag {\n  constructor(...args) {\n    this.init(...args);\n  }\n  align(other) {\n    const values = this.values;\n    for (let i = 0, il = values.length; i < il; ++i) {\n      if (values[i + 1] === other[i + 1]) {\n        if (values[i + 1] === Color && other[i + 7] !== values[i + 7]) {\n          const space = other[i + 7];\n          const color = new Color(this.values.splice(i + 3, 5))[space]().toArray();\n          this.values.splice(i + 3, 0, ...color);\n        }\n        i += values[i + 2] + 2;\n        continue;\n      }\n      if (!other[i + 1]) {\n        return this;\n      }\n      const defaultObject = new other[i + 1]().toArray();\n      const toDelete = values[i + 2] + 3;\n      values.splice(i, toDelete, other[i], other[i + 1], other[i + 2], ...defaultObject);\n      i += values[i + 2] + 2;\n    }\n    return this;\n  }\n  init(objOrArr) {\n    this.values = [];\n    if (Array.isArray(objOrArr)) {\n      this.values = objOrArr.slice();\n      return;\n    }\n    objOrArr = objOrArr || {};\n    const entries = [];\n    for (const i in objOrArr) {\n      const Type = getClassForType(objOrArr[i]);\n      const val = new Type(objOrArr[i]).toArray();\n      entries.push([i, Type, val.length, ...val]);\n    }\n    entries.sort(sortByKey);\n    this.values = entries.reduce((last, curr) => last.concat(curr), []);\n    return this;\n  }\n  toArray() {\n    return this.values;\n  }\n  valueOf() {\n    const obj = {};\n    const arr = this.values;\n    while (arr.length) {\n      const key = arr.shift();\n      const Type = arr.shift();\n      const num = arr.shift();\n      const values = arr.splice(0, num);\n      obj[key] = new Type(values);\n    }\n    return obj;\n  }\n}\nconst morphableTypes = [NonMorphable, TransformBag, ObjectBag];\nfunction registerMorphableType(type = []) {\n  morphableTypes.push(...[].concat(type));\n}\nfunction makeMorphable() {\n  extend(morphableTypes, {\n    to(val) {\n      return new Morphable().type(this.constructor).from(this.toArray()).to(val);\n    },\n    fromArray(arr) {\n      this.init(arr);\n      return this;\n    },\n    toConsumable() {\n      return this.toArray();\n    },\n    morph(from2, to2, pos, stepper, context) {\n      const mapper = function (i, index) {\n        return stepper.step(i, to2[index], pos, context[index], context);\n      };\n      return this.fromArray(from2.map(mapper));\n    }\n  });\n}\nclass Path extends Shape {\n  // Initialize node\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"path\", node), attrs2);\n  }\n  // Get array\n  array() {\n    return this._array || (this._array = new PathArray(this.attr(\"d\")));\n  }\n  // Clear array cache\n  clear() {\n    delete this._array;\n    return this;\n  }\n  // Set height of element\n  height(height2) {\n    return height2 == null ? this.bbox().height : this.size(this.bbox().width, height2);\n  }\n  // Move by left top corner\n  move(x2, y2) {\n    return this.attr(\"d\", this.array().move(x2, y2));\n  }\n  // Plot new path\n  plot(d) {\n    return d == null ? this.array() : this.clear().attr(\"d\", typeof d === \"string\" ? d : this._array = new PathArray(d));\n  }\n  // Set element size to given width and height\n  size(width2, height2) {\n    const p = proportionalSize(this, width2, height2);\n    return this.attr(\"d\", this.array().size(p.width, p.height));\n  }\n  // Set width of element\n  width(width2) {\n    return width2 == null ? this.bbox().width : this.size(width2, this.bbox().height);\n  }\n  // Move by left top corner over x-axis\n  x(x2) {\n    return x2 == null ? this.bbox().x : this.move(x2, this.bbox().y);\n  }\n  // Move by left top corner over y-axis\n  y(y2) {\n    return y2 == null ? this.bbox().y : this.move(this.bbox().x, y2);\n  }\n}\nPath.prototype.MorphArray = PathArray;\nregisterMethods({\n  Container: {\n    // Create a wrapped path element\n    path: wrapWithAttrCheck(function (d) {\n      return this.put(new Path()).plot(d || new PathArray());\n    })\n  }\n});\nregister(Path, \"Path\");\nfunction array() {\n  return this._array || (this._array = new PointArray(this.attr(\"points\")));\n}\nfunction clear() {\n  delete this._array;\n  return this;\n}\nfunction move$2(x2, y2) {\n  return this.attr(\"points\", this.array().move(x2, y2));\n}\nfunction plot(p) {\n  return p == null ? this.array() : this.clear().attr(\"points\", typeof p === \"string\" ? p : this._array = new PointArray(p));\n}\nfunction size$1(width2, height2) {\n  const p = proportionalSize(this, width2, height2);\n  return this.attr(\"points\", this.array().size(p.width, p.height));\n}\nconst poly = /* @__PURE__ */Object.freeze(/* @__PURE__ */Object.defineProperty({\n  __proto__: null,\n  array,\n  clear,\n  move: move$2,\n  plot,\n  size: size$1\n}, Symbol.toStringTag, {\n  value: \"Module\"\n}));\nclass Polygon extends Shape {\n  // Initialize node\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"polygon\", node), attrs2);\n  }\n}\nregisterMethods({\n  Container: {\n    // Create a wrapped polygon element\n    polygon: wrapWithAttrCheck(function (p) {\n      return this.put(new Polygon()).plot(p || new PointArray());\n    })\n  }\n});\nextend(Polygon, pointed);\nextend(Polygon, poly);\nregister(Polygon, \"Polygon\");\nclass Polyline extends Shape {\n  // Initialize node\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"polyline\", node), attrs2);\n  }\n}\nregisterMethods({\n  Container: {\n    // Create a wrapped polygon element\n    polyline: wrapWithAttrCheck(function (p) {\n      return this.put(new Polyline()).plot(p || new PointArray());\n    })\n  }\n});\nextend(Polyline, pointed);\nextend(Polyline, poly);\nregister(Polyline, \"Polyline\");\nclass Rect extends Shape {\n  // Initialize node\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"rect\", node), attrs2);\n  }\n}\nextend(Rect, {\n  rx,\n  ry\n});\nregisterMethods({\n  Container: {\n    // Create a rect element\n    rect: wrapWithAttrCheck(function (width2, height2) {\n      return this.put(new Rect()).size(width2, height2);\n    })\n  }\n});\nregister(Rect, \"Rect\");\nclass Queue {\n  constructor() {\n    this._first = null;\n    this._last = null;\n  }\n  // Shows us the first item in the list\n  first() {\n    return this._first && this._first.value;\n  }\n  // Shows us the last item in the list\n  last() {\n    return this._last && this._last.value;\n  }\n  push(value) {\n    const item = typeof value.next !== \"undefined\" ? value : {\n      value,\n      next: null,\n      prev: null\n    };\n    if (this._last) {\n      item.prev = this._last;\n      this._last.next = item;\n      this._last = item;\n    } else {\n      this._last = item;\n      this._first = item;\n    }\n    return item;\n  }\n  // Removes the item that was returned from the push\n  remove(item) {\n    if (item.prev) item.prev.next = item.next;\n    if (item.next) item.next.prev = item.prev;\n    if (item === this._last) this._last = item.prev;\n    if (item === this._first) this._first = item.next;\n    item.prev = null;\n    item.next = null;\n  }\n  shift() {\n    const remove = this._first;\n    if (!remove) return null;\n    this._first = remove.next;\n    if (this._first) this._first.prev = null;\n    this._last = this._first ? this._last : null;\n    return remove.value;\n  }\n}\nconst Animator = {\n  nextDraw: null,\n  frames: new Queue(),\n  timeouts: new Queue(),\n  immediates: new Queue(),\n  timer: () => globals.window.performance || globals.window.Date,\n  transforms: [],\n  frame(fn) {\n    const node = Animator.frames.push({\n      run: fn\n    });\n    if (Animator.nextDraw === null) {\n      Animator.nextDraw = globals.window.requestAnimationFrame(Animator._draw);\n    }\n    return node;\n  },\n  timeout(fn, delay) {\n    delay = delay || 0;\n    const time = Animator.timer().now() + delay;\n    const node = Animator.timeouts.push({\n      run: fn,\n      time\n    });\n    if (Animator.nextDraw === null) {\n      Animator.nextDraw = globals.window.requestAnimationFrame(Animator._draw);\n    }\n    return node;\n  },\n  immediate(fn) {\n    const node = Animator.immediates.push(fn);\n    if (Animator.nextDraw === null) {\n      Animator.nextDraw = globals.window.requestAnimationFrame(Animator._draw);\n    }\n    return node;\n  },\n  cancelFrame(node) {\n    node != null && Animator.frames.remove(node);\n  },\n  clearTimeout(node) {\n    node != null && Animator.timeouts.remove(node);\n  },\n  cancelImmediate(node) {\n    node != null && Animator.immediates.remove(node);\n  },\n  _draw(now) {\n    let nextTimeout = null;\n    const lastTimeout = Animator.timeouts.last();\n    while (nextTimeout = Animator.timeouts.shift()) {\n      if (now >= nextTimeout.time) {\n        nextTimeout.run();\n      } else {\n        Animator.timeouts.push(nextTimeout);\n      }\n      if (nextTimeout === lastTimeout) break;\n    }\n    let nextFrame = null;\n    const lastFrame = Animator.frames.last();\n    while (nextFrame !== lastFrame && (nextFrame = Animator.frames.shift())) {\n      nextFrame.run(now);\n    }\n    let nextImmediate = null;\n    while (nextImmediate = Animator.immediates.shift()) {\n      nextImmediate();\n    }\n    Animator.nextDraw = Animator.timeouts.first() || Animator.frames.first() ? globals.window.requestAnimationFrame(Animator._draw) : null;\n  }\n};\nconst makeSchedule = function (runnerInfo) {\n  const start = runnerInfo.start;\n  const duration = runnerInfo.runner.duration();\n  const end = start + duration;\n  return {\n    start,\n    duration,\n    end,\n    runner: runnerInfo.runner\n  };\n};\nconst defaultSource = function () {\n  const w = globals.window;\n  return (w.performance || w.Date).now();\n};\nclass Timeline extends EventTarget {\n  // Construct a new timeline on the given element\n  constructor(timeSource = defaultSource) {\n    super();\n    this._timeSource = timeSource;\n    this.terminate();\n  }\n  active() {\n    return !!this._nextFrame;\n  }\n  finish() {\n    this.time(this.getEndTimeOfTimeline() + 1);\n    return this.pause();\n  }\n  // Calculates the end of the timeline\n  getEndTime() {\n    const lastRunnerInfo = this.getLastRunnerInfo();\n    const lastDuration = lastRunnerInfo ? lastRunnerInfo.runner.duration() : 0;\n    const lastStartTime = lastRunnerInfo ? lastRunnerInfo.start : this._time;\n    return lastStartTime + lastDuration;\n  }\n  getEndTimeOfTimeline() {\n    const endTimes = this._runners.map(i => i.start + i.runner.duration());\n    return Math.max(0, ...endTimes);\n  }\n  getLastRunnerInfo() {\n    return this.getRunnerInfoById(this._lastRunnerId);\n  }\n  getRunnerInfoById(id) {\n    return this._runners[this._runnerIds.indexOf(id)] || null;\n  }\n  pause() {\n    this._paused = true;\n    return this._continue();\n  }\n  persist(dtOrForever) {\n    if (dtOrForever == null) return this._persist;\n    this._persist = dtOrForever;\n    return this;\n  }\n  play() {\n    this._paused = false;\n    return this.updateTime()._continue();\n  }\n  reverse(yes) {\n    const currentSpeed = this.speed();\n    if (yes == null) return this.speed(-currentSpeed);\n    const positive = Math.abs(currentSpeed);\n    return this.speed(yes ? -positive : positive);\n  }\n  // schedules a runner on the timeline\n  schedule(runner, delay, when) {\n    if (runner == null) {\n      return this._runners.map(makeSchedule);\n    }\n    let absoluteStartTime = 0;\n    const endTime = this.getEndTime();\n    delay = delay || 0;\n    if (when == null || when === \"last\" || when === \"after\") {\n      absoluteStartTime = endTime;\n    } else if (when === \"absolute\" || when === \"start\") {\n      absoluteStartTime = delay;\n      delay = 0;\n    } else if (when === \"now\") {\n      absoluteStartTime = this._time;\n    } else if (when === \"relative\") {\n      const runnerInfo2 = this.getRunnerInfoById(runner.id);\n      if (runnerInfo2) {\n        absoluteStartTime = runnerInfo2.start + delay;\n        delay = 0;\n      }\n    } else if (when === \"with-last\") {\n      const lastRunnerInfo = this.getLastRunnerInfo();\n      const lastStartTime = lastRunnerInfo ? lastRunnerInfo.start : this._time;\n      absoluteStartTime = lastStartTime;\n    } else {\n      throw new Error('Invalid value for the \"when\" parameter');\n    }\n    runner.unschedule();\n    runner.timeline(this);\n    const persist = runner.persist();\n    const runnerInfo = {\n      persist: persist === null ? this._persist : persist,\n      start: absoluteStartTime + delay,\n      runner\n    };\n    this._lastRunnerId = runner.id;\n    this._runners.push(runnerInfo);\n    this._runners.sort((a, b) => a.start - b.start);\n    this._runnerIds = this._runners.map(info => info.runner.id);\n    this.updateTime()._continue();\n    return this;\n  }\n  seek(dt) {\n    return this.time(this._time + dt);\n  }\n  source(fn) {\n    if (fn == null) return this._timeSource;\n    this._timeSource = fn;\n    return this;\n  }\n  speed(speed) {\n    if (speed == null) return this._speed;\n    this._speed = speed;\n    return this;\n  }\n  stop() {\n    this.time(0);\n    return this.pause();\n  }\n  time(time) {\n    if (time == null) return this._time;\n    this._time = time;\n    return this._continue(true);\n  }\n  // Remove the runner from this timeline\n  unschedule(runner) {\n    const index = this._runnerIds.indexOf(runner.id);\n    if (index < 0) return this;\n    this._runners.splice(index, 1);\n    this._runnerIds.splice(index, 1);\n    runner.timeline(null);\n    return this;\n  }\n  // Makes sure, that after pausing the time doesn't jump\n  updateTime() {\n    if (!this.active()) {\n      this._lastSourceTime = this._timeSource();\n    }\n    return this;\n  }\n  // Checks if we are running and continues the animation\n  _continue(immediateStep = false) {\n    Animator.cancelFrame(this._nextFrame);\n    this._nextFrame = null;\n    if (immediateStep) return this._stepImmediate();\n    if (this._paused) return this;\n    this._nextFrame = Animator.frame(this._step);\n    return this;\n  }\n  _stepFn(immediateStep = false) {\n    const time = this._timeSource();\n    let dtSource = time - this._lastSourceTime;\n    if (immediateStep) dtSource = 0;\n    const dtTime = this._speed * dtSource + (this._time - this._lastStepTime);\n    this._lastSourceTime = time;\n    if (!immediateStep) {\n      this._time += dtTime;\n      this._time = this._time < 0 ? 0 : this._time;\n    }\n    this._lastStepTime = this._time;\n    this.fire(\"time\", this._time);\n    for (let k = this._runners.length; k--;) {\n      const runnerInfo = this._runners[k];\n      const runner = runnerInfo.runner;\n      const dtToStart = this._time - runnerInfo.start;\n      if (dtToStart <= 0) {\n        runner.reset();\n      }\n    }\n    let runnersLeft = false;\n    for (let i = 0, len = this._runners.length; i < len; i++) {\n      const runnerInfo = this._runners[i];\n      const runner = runnerInfo.runner;\n      let dt = dtTime;\n      const dtToStart = this._time - runnerInfo.start;\n      if (dtToStart <= 0) {\n        runnersLeft = true;\n        continue;\n      } else if (dtToStart < dt) {\n        dt = dtToStart;\n      }\n      if (!runner.active()) continue;\n      const finished = runner.step(dt).done;\n      if (!finished) {\n        runnersLeft = true;\n      } else if (runnerInfo.persist !== true) {\n        const endTime = runner.duration() - runner.time() + this._time;\n        if (endTime + runnerInfo.persist < this._time) {\n          runner.unschedule();\n          --i;\n          --len;\n        }\n      }\n    }\n    if (runnersLeft && !(this._speed < 0 && this._time === 0) || this._runnerIds.length && this._speed < 0 && this._time > 0) {\n      this._continue();\n    } else {\n      this.pause();\n      this.fire(\"finished\");\n    }\n    return this;\n  }\n  terminate() {\n    this._startTime = 0;\n    this._speed = 1;\n    this._persist = 0;\n    this._nextFrame = null;\n    this._paused = true;\n    this._runners = [];\n    this._runnerIds = [];\n    this._lastRunnerId = -1;\n    this._time = 0;\n    this._lastSourceTime = 0;\n    this._lastStepTime = 0;\n    this._step = this._stepFn.bind(this, false);\n    this._stepImmediate = this._stepFn.bind(this, true);\n  }\n}\nregisterMethods({\n  Element: {\n    timeline: function (timeline2) {\n      if (timeline2 == null) {\n        this._timeline = this._timeline || new Timeline();\n        return this._timeline;\n      } else {\n        this._timeline = timeline2;\n        return this;\n      }\n    }\n  }\n});\nclass Runner extends EventTarget {\n  constructor(options) {\n    super();\n    this.id = Runner.id++;\n    options = options == null ? timeline.duration : options;\n    options = typeof options === \"function\" ? new Controller(options) : options;\n    this._element = null;\n    this._timeline = null;\n    this.done = false;\n    this._queue = [];\n    this._duration = typeof options === \"number\" && options;\n    this._isDeclarative = options instanceof Controller;\n    this._stepper = this._isDeclarative ? options : new Ease();\n    this._history = {};\n    this.enabled = true;\n    this._time = 0;\n    this._lastTime = 0;\n    this._reseted = true;\n    this.transforms = new Matrix();\n    this.transformId = 1;\n    this._haveReversed = false;\n    this._reverse = false;\n    this._loopsDone = 0;\n    this._swing = false;\n    this._wait = 0;\n    this._times = 1;\n    this._frameId = null;\n    this._persist = this._isDeclarative ? true : null;\n  }\n  static sanitise(duration, delay, when) {\n    let times = 1;\n    let swing = false;\n    let wait = 0;\n    duration = duration ?? timeline.duration;\n    delay = delay ?? timeline.delay;\n    when = when || \"last\";\n    if (typeof duration === \"object\" && !(duration instanceof Stepper)) {\n      delay = duration.delay ?? delay;\n      when = duration.when ?? when;\n      swing = duration.swing || swing;\n      times = duration.times ?? times;\n      wait = duration.wait ?? wait;\n      duration = duration.duration ?? timeline.duration;\n    }\n    return {\n      duration,\n      delay,\n      swing,\n      times,\n      wait,\n      when\n    };\n  }\n  active(enabled) {\n    if (enabled == null) return this.enabled;\n    this.enabled = enabled;\n    return this;\n  }\n  /*\n  Private Methods\n  ===============\n  Methods that shouldn't be used externally\n  */\n  addTransform(transform2) {\n    this.transforms.lmultiplyO(transform2);\n    return this;\n  }\n  after(fn) {\n    return this.on(\"finished\", fn);\n  }\n  animate(duration, delay, when) {\n    const o = Runner.sanitise(duration, delay, when);\n    const runner = new Runner(o.duration);\n    if (this._timeline) runner.timeline(this._timeline);\n    if (this._element) runner.element(this._element);\n    return runner.loop(o).schedule(o.delay, o.when);\n  }\n  clearTransform() {\n    this.transforms = new Matrix();\n    return this;\n  }\n  // TODO: Keep track of all transformations so that deletion is faster\n  clearTransformsFromQueue() {\n    if (!this.done || !this._timeline || !this._timeline._runnerIds.includes(this.id)) {\n      this._queue = this._queue.filter(item => {\n        return !item.isTransform;\n      });\n    }\n  }\n  delay(delay) {\n    return this.animate(0, delay);\n  }\n  duration() {\n    return this._times * (this._wait + this._duration) - this._wait;\n  }\n  during(fn) {\n    return this.queue(null, fn);\n  }\n  ease(fn) {\n    this._stepper = new Ease(fn);\n    return this;\n  }\n  /*\n  Runner Definitions\n  ==================\n  These methods help us define the runtime behaviour of the Runner or they\n  help us make new runners from the current runner\n  */\n  element(element) {\n    if (element == null) return this._element;\n    this._element = element;\n    element._prepareRunner();\n    return this;\n  }\n  finish() {\n    return this.step(Infinity);\n  }\n  loop(times, swing, wait) {\n    if (typeof times === \"object\") {\n      swing = times.swing;\n      wait = times.wait;\n      times = times.times;\n    }\n    this._times = times || Infinity;\n    this._swing = swing || false;\n    this._wait = wait || 0;\n    if (this._times === true) {\n      this._times = Infinity;\n    }\n    return this;\n  }\n  loops(p) {\n    const loopDuration = this._duration + this._wait;\n    if (p == null) {\n      const loopsDone = Math.floor(this._time / loopDuration);\n      const relativeTime = this._time - loopsDone * loopDuration;\n      const position2 = relativeTime / this._duration;\n      return Math.min(loopsDone + position2, this._times);\n    }\n    const whole = Math.floor(p);\n    const partial = p % 1;\n    const time = loopDuration * whole + this._duration * partial;\n    return this.time(time);\n  }\n  persist(dtOrForever) {\n    if (dtOrForever == null) return this._persist;\n    this._persist = dtOrForever;\n    return this;\n  }\n  position(p) {\n    const x2 = this._time;\n    const d = this._duration;\n    const w = this._wait;\n    const t = this._times;\n    const s = this._swing;\n    const r = this._reverse;\n    let position2;\n    if (p == null) {\n      const f = function (x3) {\n        const swinging = s * Math.floor(x3 % (2 * (w + d)) / (w + d));\n        const backwards = swinging && !r || !swinging && r;\n        const uncliped = Math.pow(-1, backwards) * (x3 % (w + d)) / d + backwards;\n        const clipped = Math.max(Math.min(uncliped, 1), 0);\n        return clipped;\n      };\n      const endTime = t * (w + d) - w;\n      position2 = x2 <= 0 ? Math.round(f(1e-5)) : x2 < endTime ? f(x2) : Math.round(f(endTime - 1e-5));\n      return position2;\n    }\n    const loopsDone = Math.floor(this.loops());\n    const swingForward = s && loopsDone % 2 === 0;\n    const forwards = swingForward && !r || r && swingForward;\n    position2 = loopsDone + (forwards ? p : 1 - p);\n    return this.loops(position2);\n  }\n  progress(p) {\n    if (p == null) {\n      return Math.min(1, this._time / this.duration());\n    }\n    return this.time(p * this.duration());\n  }\n  /*\n  Basic Functionality\n  ===================\n  These methods allow us to attach basic functions to the runner directly\n  */\n  queue(initFn, runFn, retargetFn, isTransform) {\n    this._queue.push({\n      initialiser: initFn || noop,\n      runner: runFn || noop,\n      retarget: retargetFn,\n      isTransform,\n      initialised: false,\n      finished: false\n    });\n    const timeline2 = this.timeline();\n    timeline2 && this.timeline()._continue();\n    return this;\n  }\n  reset() {\n    if (this._reseted) return this;\n    this.time(0);\n    this._reseted = true;\n    return this;\n  }\n  reverse(reverse) {\n    this._reverse = reverse == null ? !this._reverse : reverse;\n    return this;\n  }\n  schedule(timeline2, delay, when) {\n    if (!(timeline2 instanceof Timeline)) {\n      when = delay;\n      delay = timeline2;\n      timeline2 = this.timeline();\n    }\n    if (!timeline2) {\n      throw Error(\"Runner cannot be scheduled without timeline\");\n    }\n    timeline2.schedule(this, delay, when);\n    return this;\n  }\n  step(dt) {\n    if (!this.enabled) return this;\n    dt = dt == null ? 16 : dt;\n    this._time += dt;\n    const position2 = this.position();\n    const running = this._lastPosition !== position2 && this._time >= 0;\n    this._lastPosition = position2;\n    const duration = this.duration();\n    const justStarted = this._lastTime <= 0 && this._time > 0;\n    const justFinished = this._lastTime < duration && this._time >= duration;\n    this._lastTime = this._time;\n    if (justStarted) {\n      this.fire(\"start\", this);\n    }\n    const declarative = this._isDeclarative;\n    this.done = !declarative && !justFinished && this._time >= duration;\n    this._reseted = false;\n    let converged = false;\n    if (running || declarative) {\n      this._initialise(running);\n      this.transforms = new Matrix();\n      converged = this._run(declarative ? dt : position2);\n      this.fire(\"step\", this);\n    }\n    this.done = this.done || converged && declarative;\n    if (justFinished) {\n      this.fire(\"finished\", this);\n    }\n    return this;\n  }\n  /*\n  Runner animation methods\n  ========================\n  Control how the animation plays\n  */\n  time(time) {\n    if (time == null) {\n      return this._time;\n    }\n    const dt = time - this._time;\n    this.step(dt);\n    return this;\n  }\n  timeline(timeline2) {\n    if (typeof timeline2 === \"undefined\") return this._timeline;\n    this._timeline = timeline2;\n    return this;\n  }\n  unschedule() {\n    const timeline2 = this.timeline();\n    timeline2 && timeline2.unschedule(this);\n    return this;\n  }\n  // Run each initialise function in the runner if required\n  _initialise(running) {\n    if (!running && !this._isDeclarative) return;\n    for (let i = 0, len = this._queue.length; i < len; ++i) {\n      const current = this._queue[i];\n      const needsIt = this._isDeclarative || !current.initialised && running;\n      running = !current.finished;\n      if (needsIt && running) {\n        current.initialiser.call(this);\n        current.initialised = true;\n      }\n    }\n  }\n  // Save a morpher to the morpher list so that we can retarget it later\n  _rememberMorpher(method, morpher) {\n    this._history[method] = {\n      morpher,\n      caller: this._queue[this._queue.length - 1]\n    };\n    if (this._isDeclarative) {\n      const timeline2 = this.timeline();\n      timeline2 && timeline2.play();\n    }\n  }\n  // Try to set the target for a morpher if the morpher exists, otherwise\n  // Run each run function for the position or dt given\n  _run(positionOrDt) {\n    let allfinished = true;\n    for (let i = 0, len = this._queue.length; i < len; ++i) {\n      const current = this._queue[i];\n      const converged = current.runner.call(this, positionOrDt);\n      current.finished = current.finished || converged === true;\n      allfinished = allfinished && current.finished;\n    }\n    return allfinished;\n  }\n  // do nothing and return false\n  _tryRetarget(method, target, extra) {\n    if (this._history[method]) {\n      if (!this._history[method].caller.initialised) {\n        const index = this._queue.indexOf(this._history[method].caller);\n        this._queue.splice(index, 1);\n        return false;\n      }\n      if (this._history[method].caller.retarget) {\n        this._history[method].caller.retarget.call(this, target, extra);\n      } else {\n        this._history[method].morpher.to(target);\n      }\n      this._history[method].caller.finished = false;\n      const timeline2 = this.timeline();\n      timeline2 && timeline2.play();\n      return true;\n    }\n    return false;\n  }\n}\nRunner.id = 0;\nclass FakeRunner {\n  constructor(transforms2 = new Matrix(), id = -1, done = true) {\n    this.transforms = transforms2;\n    this.id = id;\n    this.done = done;\n  }\n  clearTransformsFromQueue() {}\n}\nextend([Runner, FakeRunner], {\n  mergeWith(runner) {\n    return new FakeRunner(runner.transforms.lmultiply(this.transforms), runner.id);\n  }\n});\nconst lmultiply = (last, curr) => last.lmultiplyO(curr);\nconst getRunnerTransform = runner => runner.transforms;\nfunction mergeTransforms() {\n  const runners = this._transformationRunners.runners;\n  const netTransform = runners.map(getRunnerTransform).reduce(lmultiply, new Matrix());\n  this.transform(netTransform);\n  this._transformationRunners.merge();\n  if (this._transformationRunners.length() === 1) {\n    this._frameId = null;\n  }\n}\nclass RunnerArray {\n  constructor() {\n    this.runners = [];\n    this.ids = [];\n  }\n  add(runner) {\n    if (this.runners.includes(runner)) return;\n    const id = runner.id + 1;\n    this.runners.push(runner);\n    this.ids.push(id);\n    return this;\n  }\n  clearBefore(id) {\n    const deleteCnt = this.ids.indexOf(id + 1) || 1;\n    this.ids.splice(0, deleteCnt, 0);\n    this.runners.splice(0, deleteCnt, new FakeRunner()).forEach(r => r.clearTransformsFromQueue());\n    return this;\n  }\n  edit(id, newRunner) {\n    const index = this.ids.indexOf(id + 1);\n    this.ids.splice(index, 1, id + 1);\n    this.runners.splice(index, 1, newRunner);\n    return this;\n  }\n  getByID(id) {\n    return this.runners[this.ids.indexOf(id + 1)];\n  }\n  length() {\n    return this.ids.length;\n  }\n  merge() {\n    let lastRunner = null;\n    for (let i = 0; i < this.runners.length; ++i) {\n      const runner = this.runners[i];\n      const condition = lastRunner && runner.done && lastRunner.done && (\n      // don't merge runner when persisted on timeline\n      !runner._timeline || !runner._timeline._runnerIds.includes(runner.id)) && (!lastRunner._timeline || !lastRunner._timeline._runnerIds.includes(lastRunner.id));\n      if (condition) {\n        this.remove(runner.id);\n        const newRunner = runner.mergeWith(lastRunner);\n        this.edit(lastRunner.id, newRunner);\n        lastRunner = newRunner;\n        --i;\n      } else {\n        lastRunner = runner;\n      }\n    }\n    return this;\n  }\n  remove(id) {\n    const index = this.ids.indexOf(id + 1);\n    this.ids.splice(index, 1);\n    this.runners.splice(index, 1);\n    return this;\n  }\n}\nregisterMethods({\n  Element: {\n    animate(duration, delay, when) {\n      const o = Runner.sanitise(duration, delay, when);\n      const timeline2 = this.timeline();\n      return new Runner(o.duration).loop(o).element(this).timeline(timeline2.play()).schedule(o.delay, o.when);\n    },\n    delay(by, when) {\n      return this.animate(0, by, when);\n    },\n    // this function searches for all runners on the element and deletes the ones\n    // which run before the current one. This is because absolute transformations\n    // overwrite anything anyway so there is no need to waste time computing\n    // other runners\n    _clearTransformRunnersBefore(currentRunner) {\n      this._transformationRunners.clearBefore(currentRunner.id);\n    },\n    _currentTransform(current) {\n      return this._transformationRunners.runners.filter(runner => runner.id <= current.id).map(getRunnerTransform).reduce(lmultiply, new Matrix());\n    },\n    _addRunner(runner) {\n      this._transformationRunners.add(runner);\n      Animator.cancelImmediate(this._frameId);\n      this._frameId = Animator.immediate(mergeTransforms.bind(this));\n    },\n    _prepareRunner() {\n      if (this._frameId == null) {\n        this._transformationRunners = new RunnerArray().add(new FakeRunner(new Matrix(this)));\n      }\n    }\n  }\n});\nconst difference = (a, b) => a.filter(x2 => !b.includes(x2));\nextend(Runner, {\n  attr(a, v) {\n    return this.styleAttr(\"attr\", a, v);\n  },\n  // Add animatable styles\n  css(s, v) {\n    return this.styleAttr(\"css\", s, v);\n  },\n  styleAttr(type, nameOrAttrs, val) {\n    if (typeof nameOrAttrs === \"string\") {\n      return this.styleAttr(type, {\n        [nameOrAttrs]: val\n      });\n    }\n    let attrs2 = nameOrAttrs;\n    if (this._tryRetarget(type, attrs2)) return this;\n    let morpher = new Morphable(this._stepper).to(attrs2);\n    let keys = Object.keys(attrs2);\n    this.queue(function () {\n      morpher = morpher.from(this.element()[type](keys));\n    }, function (pos) {\n      this.element()[type](morpher.at(pos).valueOf());\n      return morpher.done();\n    }, function (newToAttrs) {\n      const newKeys = Object.keys(newToAttrs);\n      const differences = difference(newKeys, keys);\n      if (differences.length) {\n        const addedFromAttrs = this.element()[type](differences);\n        const oldFromAttrs = new ObjectBag(morpher.from()).valueOf();\n        Object.assign(oldFromAttrs, addedFromAttrs);\n        morpher.from(oldFromAttrs);\n      }\n      const oldToAttrs = new ObjectBag(morpher.to()).valueOf();\n      Object.assign(oldToAttrs, newToAttrs);\n      morpher.to(oldToAttrs);\n      keys = newKeys;\n      attrs2 = newToAttrs;\n    });\n    this._rememberMorpher(type, morpher);\n    return this;\n  },\n  zoom(level, point2) {\n    if (this._tryRetarget(\"zoom\", level, point2)) return this;\n    let morpher = new Morphable(this._stepper).to(new SVGNumber(level));\n    this.queue(function () {\n      morpher = morpher.from(this.element().zoom());\n    }, function (pos) {\n      this.element().zoom(morpher.at(pos), point2);\n      return morpher.done();\n    }, function (newLevel, newPoint) {\n      point2 = newPoint;\n      morpher.to(newLevel);\n    });\n    this._rememberMorpher(\"zoom\", morpher);\n    return this;\n  },\n  /**\n   ** absolute transformations\n   **/\n  //\n  // M v -----|-----(D M v = F v)------|----->  T v\n  //\n  // 1. define the final state (T) and decompose it (once)\n  //    t = [tx, ty, the, lam, sy, sx]\n  // 2. on every frame: pull the current state of all previous transforms\n  //    (M - m can change)\n  //   and then write this as m = [tx0, ty0, the0, lam0, sy0, sx0]\n  // 3. Find the interpolated matrix F(pos) = m + pos * (t - m)\n  //   - Note F(0) = M\n  //   - Note F(1) = T\n  // 4. Now you get the delta matrix as a result: D = F * inv(M)\n  transform(transforms2, relative, affine) {\n    relative = transforms2.relative || relative;\n    if (this._isDeclarative && !relative && this._tryRetarget(\"transform\", transforms2)) {\n      return this;\n    }\n    const isMatrix = Matrix.isMatrixLike(transforms2);\n    affine = transforms2.affine != null ? transforms2.affine : affine != null ? affine : !isMatrix;\n    const morpher = new Morphable(this._stepper).type(affine ? TransformBag : Matrix);\n    let origin;\n    let element;\n    let current;\n    let currentAngle;\n    let startTransform;\n    function setup() {\n      element = element || this.element();\n      origin = origin || getOrigin(transforms2, element);\n      startTransform = new Matrix(relative ? void 0 : element);\n      element._addRunner(this);\n      if (!relative) {\n        element._clearTransformRunnersBefore(this);\n      }\n    }\n    function run(pos) {\n      if (!relative) this.clearTransform();\n      const {\n        x: x2,\n        y: y2\n      } = new Point(origin).transform(element._currentTransform(this));\n      let target = new Matrix({\n        ...transforms2,\n        origin: [x2, y2]\n      });\n      let start = this._isDeclarative && current ? current : startTransform;\n      if (affine) {\n        target = target.decompose(x2, y2);\n        start = start.decompose(x2, y2);\n        const rTarget = target.rotate;\n        const rCurrent = start.rotate;\n        const possibilities = [rTarget - 360, rTarget, rTarget + 360];\n        const distances = possibilities.map(a => Math.abs(a - rCurrent));\n        const shortest = Math.min(...distances);\n        const index = distances.indexOf(shortest);\n        target.rotate = possibilities[index];\n      }\n      if (relative) {\n        if (!isMatrix) {\n          target.rotate = transforms2.rotate || 0;\n        }\n        if (this._isDeclarative && currentAngle) {\n          start.rotate = currentAngle;\n        }\n      }\n      morpher.from(start);\n      morpher.to(target);\n      const affineParameters = morpher.at(pos);\n      currentAngle = affineParameters.rotate;\n      current = new Matrix(affineParameters);\n      this.addTransform(current);\n      element._addRunner(this);\n      return morpher.done();\n    }\n    function retarget(newTransforms) {\n      if ((newTransforms.origin || \"center\").toString() !== (transforms2.origin || \"center\").toString()) {\n        origin = getOrigin(newTransforms, element);\n      }\n      transforms2 = {\n        ...newTransforms,\n        origin\n      };\n    }\n    this.queue(setup, run, retarget, true);\n    this._isDeclarative && this._rememberMorpher(\"transform\", morpher);\n    return this;\n  },\n  // Animatable x-axis\n  x(x2) {\n    return this._queueNumber(\"x\", x2);\n  },\n  // Animatable y-axis\n  y(y2) {\n    return this._queueNumber(\"y\", y2);\n  },\n  ax(x2) {\n    return this._queueNumber(\"ax\", x2);\n  },\n  ay(y2) {\n    return this._queueNumber(\"ay\", y2);\n  },\n  dx(x2 = 0) {\n    return this._queueNumberDelta(\"x\", x2);\n  },\n  dy(y2 = 0) {\n    return this._queueNumberDelta(\"y\", y2);\n  },\n  dmove(x2, y2) {\n    return this.dx(x2).dy(y2);\n  },\n  _queueNumberDelta(method, to2) {\n    to2 = new SVGNumber(to2);\n    if (this._tryRetarget(method, to2)) return this;\n    const morpher = new Morphable(this._stepper).to(to2);\n    let from2 = null;\n    this.queue(function () {\n      from2 = this.element()[method]();\n      morpher.from(from2);\n      morpher.to(from2 + to2);\n    }, function (pos) {\n      this.element()[method](morpher.at(pos));\n      return morpher.done();\n    }, function (newTo) {\n      morpher.to(from2 + new SVGNumber(newTo));\n    });\n    this._rememberMorpher(method, morpher);\n    return this;\n  },\n  _queueObject(method, to2) {\n    if (this._tryRetarget(method, to2)) return this;\n    const morpher = new Morphable(this._stepper).to(to2);\n    this.queue(function () {\n      morpher.from(this.element()[method]());\n    }, function (pos) {\n      this.element()[method](morpher.at(pos));\n      return morpher.done();\n    });\n    this._rememberMorpher(method, morpher);\n    return this;\n  },\n  _queueNumber(method, value) {\n    return this._queueObject(method, new SVGNumber(value));\n  },\n  // Animatable center x-axis\n  cx(x2) {\n    return this._queueNumber(\"cx\", x2);\n  },\n  // Animatable center y-axis\n  cy(y2) {\n    return this._queueNumber(\"cy\", y2);\n  },\n  // Add animatable move\n  move(x2, y2) {\n    return this.x(x2).y(y2);\n  },\n  amove(x2, y2) {\n    return this.ax(x2).ay(y2);\n  },\n  // Add animatable center\n  center(x2, y2) {\n    return this.cx(x2).cy(y2);\n  },\n  // Add animatable size\n  size(width2, height2) {\n    let box;\n    if (!width2 || !height2) {\n      box = this._element.bbox();\n    }\n    if (!width2) {\n      width2 = box.width / box.height * height2;\n    }\n    if (!height2) {\n      height2 = box.height / box.width * width2;\n    }\n    return this.width(width2).height(height2);\n  },\n  // Add animatable width\n  width(width2) {\n    return this._queueNumber(\"width\", width2);\n  },\n  // Add animatable height\n  height(height2) {\n    return this._queueNumber(\"height\", height2);\n  },\n  // Add animatable plot\n  plot(a, b, c, d) {\n    if (arguments.length === 4) {\n      return this.plot([a, b, c, d]);\n    }\n    if (this._tryRetarget(\"plot\", a)) return this;\n    const morpher = new Morphable(this._stepper).type(this._element.MorphArray).to(a);\n    this.queue(function () {\n      morpher.from(this._element.array());\n    }, function (pos) {\n      this._element.plot(morpher.at(pos));\n      return morpher.done();\n    });\n    this._rememberMorpher(\"plot\", morpher);\n    return this;\n  },\n  // Add leading method\n  leading(value) {\n    return this._queueNumber(\"leading\", value);\n  },\n  // Add animatable viewbox\n  viewbox(x2, y2, width2, height2) {\n    return this._queueObject(\"viewbox\", new Box(x2, y2, width2, height2));\n  },\n  update(o) {\n    if (typeof o !== \"object\") {\n      return this.update({\n        offset: arguments[0],\n        color: arguments[1],\n        opacity: arguments[2]\n      });\n    }\n    if (o.opacity != null) this.attr(\"stop-opacity\", o.opacity);\n    if (o.color != null) this.attr(\"stop-color\", o.color);\n    if (o.offset != null) this.attr(\"offset\", o.offset);\n    return this;\n  }\n});\nextend(Runner, {\n  rx,\n  ry,\n  from,\n  to\n});\nregister(Runner, \"Runner\");\nclass Svg extends Container {\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"svg\", node), attrs2);\n    this.namespace();\n  }\n  // Creates and returns defs element\n  defs() {\n    if (!this.isRoot()) return this.root().defs();\n    return adopt(this.node.querySelector(\"defs\")) || this.put(new Defs());\n  }\n  isRoot() {\n    return !this.node.parentNode || !(this.node.parentNode instanceof globals.window.SVGElement) && this.node.parentNode.nodeName !== \"#document-fragment\";\n  }\n  // Add namespaces\n  namespace() {\n    if (!this.isRoot()) return this.root().namespace();\n    return this.attr({\n      xmlns: svg,\n      version: \"1.1\"\n    }).attr(\"xmlns:xlink\", xlink, xmlns);\n  }\n  removeNamespace() {\n    return this.attr({\n      xmlns: null,\n      version: null\n    }).attr(\"xmlns:xlink\", null, xmlns).attr(\"xmlns:svgjs\", null, xmlns);\n  }\n  // Check if this is a root svg\n  // If not, call root() from this element\n  root() {\n    if (this.isRoot()) return this;\n    return super.root();\n  }\n}\nregisterMethods({\n  Container: {\n    // Create nested svg document\n    nested: wrapWithAttrCheck(function () {\n      return this.put(new Svg());\n    })\n  }\n});\nregister(Svg, \"Svg\", true);\nlet Symbol$1 = class Symbol2 extends Container {\n  // Initialize node\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"symbol\", node), attrs2);\n  }\n};\nregisterMethods({\n  Container: {\n    symbol: wrapWithAttrCheck(function () {\n      return this.put(new Symbol$1());\n    })\n  }\n});\nregister(Symbol$1, \"Symbol\");\nfunction plain(text) {\n  if (this._build === false) {\n    this.clear();\n  }\n  this.node.appendChild(globals.document.createTextNode(text));\n  return this;\n}\nfunction length() {\n  return this.node.getComputedTextLength();\n}\nfunction x$1(x2, box = this.bbox()) {\n  if (x2 == null) {\n    return box.x;\n  }\n  return this.attr(\"x\", this.attr(\"x\") + x2 - box.x);\n}\nfunction y$1(y2, box = this.bbox()) {\n  if (y2 == null) {\n    return box.y;\n  }\n  return this.attr(\"y\", this.attr(\"y\") + y2 - box.y);\n}\nfunction move$1(x2, y2, box = this.bbox()) {\n  return this.x(x2, box).y(y2, box);\n}\nfunction cx(x2, box = this.bbox()) {\n  if (x2 == null) {\n    return box.cx;\n  }\n  return this.attr(\"x\", this.attr(\"x\") + x2 - box.cx);\n}\nfunction cy(y2, box = this.bbox()) {\n  if (y2 == null) {\n    return box.cy;\n  }\n  return this.attr(\"y\", this.attr(\"y\") + y2 - box.cy);\n}\nfunction center(x2, y2, box = this.bbox()) {\n  return this.cx(x2, box).cy(y2, box);\n}\nfunction ax(x2) {\n  return this.attr(\"x\", x2);\n}\nfunction ay(y2) {\n  return this.attr(\"y\", y2);\n}\nfunction amove(x2, y2) {\n  return this.ax(x2).ay(y2);\n}\nfunction build(build2) {\n  this._build = !!build2;\n  return this;\n}\nconst textable = /* @__PURE__ */Object.freeze(/* @__PURE__ */Object.defineProperty({\n  __proto__: null,\n  amove,\n  ax,\n  ay,\n  build,\n  center,\n  cx,\n  cy,\n  length,\n  move: move$1,\n  plain,\n  x: x$1,\n  y: y$1\n}, Symbol.toStringTag, {\n  value: \"Module\"\n}));\nclass Text extends Shape {\n  // Initialize node\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"text\", node), attrs2);\n    this.dom.leading = this.dom.leading ?? new SVGNumber(1.3);\n    this._rebuild = true;\n    this._build = false;\n  }\n  // Set / get leading\n  leading(value) {\n    if (value == null) {\n      return this.dom.leading;\n    }\n    this.dom.leading = new SVGNumber(value);\n    return this.rebuild();\n  }\n  // Rebuild appearance type\n  rebuild(rebuild) {\n    if (typeof rebuild === \"boolean\") {\n      this._rebuild = rebuild;\n    }\n    if (this._rebuild) {\n      const self = this;\n      let blankLineOffset = 0;\n      const leading = this.dom.leading;\n      this.each(function (i) {\n        if (isDescriptive(this.node)) return;\n        const fontSize = globals.window.getComputedStyle(this.node).getPropertyValue(\"font-size\");\n        const dy2 = leading * new SVGNumber(fontSize);\n        if (this.dom.newLined) {\n          this.attr(\"x\", self.attr(\"x\"));\n          if (this.text() === \"\\n\") {\n            blankLineOffset += dy2;\n          } else {\n            this.attr(\"dy\", i ? dy2 + blankLineOffset : 0);\n            blankLineOffset = 0;\n          }\n        }\n      });\n      this.fire(\"rebuild\");\n    }\n    return this;\n  }\n  // overwrite method from parent to set data properly\n  setData(o) {\n    this.dom = o;\n    this.dom.leading = new SVGNumber(o.leading || 1.3);\n    return this;\n  }\n  writeDataToDom() {\n    writeDataToDom(this, this.dom, {\n      leading: 1.3\n    });\n    return this;\n  }\n  // Set the text content\n  text(text) {\n    if (text === void 0) {\n      const children = this.node.childNodes;\n      let firstLine = 0;\n      text = \"\";\n      for (let i = 0, len = children.length; i < len; ++i) {\n        if (children[i].nodeName === \"textPath\" || isDescriptive(children[i])) {\n          if (i === 0) firstLine = i + 1;\n          continue;\n        }\n        if (i !== firstLine && children[i].nodeType !== 3 && adopt(children[i]).dom.newLined === true) {\n          text += \"\\n\";\n        }\n        text += children[i].textContent;\n      }\n      return text;\n    }\n    this.clear().build(true);\n    if (typeof text === \"function\") {\n      text.call(this, this);\n    } else {\n      text = (text + \"\").split(\"\\n\");\n      for (let j = 0, jl = text.length; j < jl; j++) {\n        this.newLine(text[j]);\n      }\n    }\n    return this.build(false).rebuild();\n  }\n}\nextend(Text, textable);\nregisterMethods({\n  Container: {\n    // Create text element\n    text: wrapWithAttrCheck(function (text = \"\") {\n      return this.put(new Text()).text(text);\n    }),\n    // Create plain text element\n    plain: wrapWithAttrCheck(function (text = \"\") {\n      return this.put(new Text()).plain(text);\n    })\n  }\n});\nregister(Text, \"Text\");\nclass Tspan extends Shape {\n  // Initialize node\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"tspan\", node), attrs2);\n    this._build = false;\n  }\n  // Shortcut dx\n  dx(dx2) {\n    return this.attr(\"dx\", dx2);\n  }\n  // Shortcut dy\n  dy(dy2) {\n    return this.attr(\"dy\", dy2);\n  }\n  // Create new line\n  newLine() {\n    this.dom.newLined = true;\n    const text = this.parent();\n    if (!(text instanceof Text)) {\n      return this;\n    }\n    const i = text.index(this);\n    const fontSize = globals.window.getComputedStyle(this.node).getPropertyValue(\"font-size\");\n    const dy2 = text.dom.leading * new SVGNumber(fontSize);\n    return this.dy(i ? dy2 : 0).attr(\"x\", text.x());\n  }\n  // Set text content\n  text(text) {\n    if (text == null) return this.node.textContent + (this.dom.newLined ? \"\\n\" : \"\");\n    if (typeof text === \"function\") {\n      this.clear().build(true);\n      text.call(this, this);\n      this.build(false);\n    } else {\n      this.plain(text);\n    }\n    return this;\n  }\n}\nextend(Tspan, textable);\nregisterMethods({\n  Tspan: {\n    tspan: wrapWithAttrCheck(function (text = \"\") {\n      const tspan = new Tspan();\n      if (!this._build) {\n        this.clear();\n      }\n      return this.put(tspan).text(text);\n    })\n  },\n  Text: {\n    newLine: function (text = \"\") {\n      return this.tspan(text).newLine();\n    }\n  }\n});\nregister(Tspan, \"Tspan\");\nclass Circle extends Shape {\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"circle\", node), attrs2);\n  }\n  radius(r) {\n    return this.attr(\"r\", r);\n  }\n  // Radius x value\n  rx(rx2) {\n    return this.attr(\"r\", rx2);\n  }\n  // Alias radius x value\n  ry(ry2) {\n    return this.rx(ry2);\n  }\n  size(size2) {\n    return this.radius(new SVGNumber(size2).divide(2));\n  }\n}\nextend(Circle, {\n  x: x$3,\n  y: y$3,\n  cx: cx$1,\n  cy: cy$1,\n  width: width$2,\n  height: height$2\n});\nregisterMethods({\n  Container: {\n    // Create circle element\n    circle: wrapWithAttrCheck(function (size2 = 0) {\n      return this.put(new Circle()).size(size2).move(0, 0);\n    })\n  }\n});\nregister(Circle, \"Circle\");\nclass ClipPath extends Container {\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"clipPath\", node), attrs2);\n  }\n  // Unclip all clipped elements and remove itself\n  remove() {\n    this.targets().forEach(function (el) {\n      el.unclip();\n    });\n    return super.remove();\n  }\n  targets() {\n    return baseFind(\"svg [clip-path*=\" + this.id() + \"]\");\n  }\n}\nregisterMethods({\n  Container: {\n    // Create clipping element\n    clip: wrapWithAttrCheck(function () {\n      return this.defs().put(new ClipPath());\n    })\n  },\n  Element: {\n    // Distribute clipPath to svg element\n    clipper() {\n      return this.reference(\"clip-path\");\n    },\n    clipWith(element) {\n      const clipper = element instanceof ClipPath ? element : this.parent().clip().add(element);\n      return this.attr(\"clip-path\", \"url(#\" + clipper.id() + \")\");\n    },\n    // Unclip element\n    unclip() {\n      return this.attr(\"clip-path\", null);\n    }\n  }\n});\nregister(ClipPath, \"ClipPath\");\nclass ForeignObject extends Element {\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"foreignObject\", node), attrs2);\n  }\n}\nregisterMethods({\n  Container: {\n    foreignObject: wrapWithAttrCheck(function (width2, height2) {\n      return this.put(new ForeignObject()).size(width2, height2);\n    })\n  }\n});\nregister(ForeignObject, \"ForeignObject\");\nfunction dmove(dx2, dy2) {\n  this.children().forEach(child => {\n    let bbox2;\n    try {\n      bbox2 = child.node instanceof getWindow().SVGSVGElement ? new Box(child.attr([\"x\", \"y\", \"width\", \"height\"])) : child.bbox();\n    } catch (e) {\n      return;\n    }\n    const m = new Matrix(child);\n    const matrix = m.translate(dx2, dy2).transform(m.inverse());\n    const p = new Point(bbox2.x, bbox2.y).transform(matrix);\n    child.move(p.x, p.y);\n  });\n  return this;\n}\nfunction dx(dx2) {\n  return this.dmove(dx2, 0);\n}\nfunction dy(dy2) {\n  return this.dmove(0, dy2);\n}\nfunction height(height2, box = this.bbox()) {\n  if (height2 == null) return box.height;\n  return this.size(box.width, height2, box);\n}\nfunction move(x2 = 0, y2 = 0, box = this.bbox()) {\n  const dx2 = x2 - box.x;\n  const dy2 = y2 - box.y;\n  return this.dmove(dx2, dy2);\n}\nfunction size(width2, height2, box = this.bbox()) {\n  const p = proportionalSize(this, width2, height2, box);\n  const scaleX = p.width / box.width;\n  const scaleY = p.height / box.height;\n  this.children().forEach(child => {\n    const o = new Point(box).transform(new Matrix(child).inverse());\n    child.scale(scaleX, scaleY, o.x, o.y);\n  });\n  return this;\n}\nfunction width(width2, box = this.bbox()) {\n  if (width2 == null) return box.width;\n  return this.size(width2, box.height, box);\n}\nfunction x(x2, box = this.bbox()) {\n  if (x2 == null) return box.x;\n  return this.move(x2, box.y, box);\n}\nfunction y(y2, box = this.bbox()) {\n  if (y2 == null) return box.y;\n  return this.move(box.x, y2, box);\n}\nconst containerGeometry = /* @__PURE__ */Object.freeze(/* @__PURE__ */Object.defineProperty({\n  __proto__: null,\n  dmove,\n  dx,\n  dy,\n  height,\n  move,\n  size,\n  width,\n  x,\n  y\n}, Symbol.toStringTag, {\n  value: \"Module\"\n}));\nclass G extends Container {\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"g\", node), attrs2);\n  }\n}\nextend(G, containerGeometry);\nregisterMethods({\n  Container: {\n    // Create a group element\n    group: wrapWithAttrCheck(function () {\n      return this.put(new G());\n    })\n  }\n});\nregister(G, \"G\");\nclass A extends Container {\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"a\", node), attrs2);\n  }\n  // Link target attribute\n  target(target) {\n    return this.attr(\"target\", target);\n  }\n  // Link url\n  to(url) {\n    return this.attr(\"href\", url, xlink);\n  }\n}\nextend(A, containerGeometry);\nregisterMethods({\n  Container: {\n    // Create a hyperlink element\n    link: wrapWithAttrCheck(function (url) {\n      return this.put(new A()).to(url);\n    })\n  },\n  Element: {\n    unlink() {\n      const link = this.linker();\n      if (!link) return this;\n      const parent = link.parent();\n      if (!parent) {\n        return this.remove();\n      }\n      const index = parent.index(link);\n      parent.add(this, index);\n      link.remove();\n      return this;\n    },\n    linkTo(url) {\n      let link = this.linker();\n      if (!link) {\n        link = new A();\n        this.wrap(link);\n      }\n      if (typeof url === \"function\") {\n        url.call(link, link);\n      } else {\n        link.to(url);\n      }\n      return this;\n    },\n    linker() {\n      const link = this.parent();\n      if (link && link.node.nodeName.toLowerCase() === \"a\") {\n        return link;\n      }\n      return null;\n    }\n  }\n});\nregister(A, \"A\");\nclass Mask extends Container {\n  // Initialize node\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"mask\", node), attrs2);\n  }\n  // Unmask all masked elements and remove itself\n  remove() {\n    this.targets().forEach(function (el) {\n      el.unmask();\n    });\n    return super.remove();\n  }\n  targets() {\n    return baseFind(\"svg [mask*=\" + this.id() + \"]\");\n  }\n}\nregisterMethods({\n  Container: {\n    mask: wrapWithAttrCheck(function () {\n      return this.defs().put(new Mask());\n    })\n  },\n  Element: {\n    // Distribute mask to svg element\n    masker() {\n      return this.reference(\"mask\");\n    },\n    maskWith(element) {\n      const masker = element instanceof Mask ? element : this.parent().mask().add(element);\n      return this.attr(\"mask\", \"url(#\" + masker.id() + \")\");\n    },\n    // Unmask element\n    unmask() {\n      return this.attr(\"mask\", null);\n    }\n  }\n});\nregister(Mask, \"Mask\");\nclass Stop extends Element {\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"stop\", node), attrs2);\n  }\n  // add color stops\n  update(o) {\n    if (typeof o === \"number\" || o instanceof SVGNumber) {\n      o = {\n        offset: arguments[0],\n        color: arguments[1],\n        opacity: arguments[2]\n      };\n    }\n    if (o.opacity != null) this.attr(\"stop-opacity\", o.opacity);\n    if (o.color != null) this.attr(\"stop-color\", o.color);\n    if (o.offset != null) this.attr(\"offset\", new SVGNumber(o.offset));\n    return this;\n  }\n}\nregisterMethods({\n  Gradient: {\n    // Add a color stop\n    stop: function (offset, color, opacity) {\n      return this.put(new Stop()).update(offset, color, opacity);\n    }\n  }\n});\nregister(Stop, \"Stop\");\nfunction cssRule(selector, rule) {\n  if (!selector) return \"\";\n  if (!rule) return selector;\n  let ret = selector + \"{\";\n  for (const i in rule) {\n    ret += unCamelCase(i) + \":\" + rule[i] + \";\";\n  }\n  ret += \"}\";\n  return ret;\n}\nclass Style extends Element {\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"style\", node), attrs2);\n  }\n  addText(w = \"\") {\n    this.node.textContent += w;\n    return this;\n  }\n  font(name2, src, params = {}) {\n    return this.rule(\"@font-face\", {\n      fontFamily: name2,\n      src,\n      ...params\n    });\n  }\n  rule(selector, obj) {\n    return this.addText(cssRule(selector, obj));\n  }\n}\nregisterMethods(\"Dom\", {\n  style(selector, obj) {\n    return this.put(new Style()).rule(selector, obj);\n  },\n  fontface(name2, src, params) {\n    return this.put(new Style()).font(name2, src, params);\n  }\n});\nregister(Style, \"Style\");\nclass TextPath extends Text {\n  // Initialize node\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"textPath\", node), attrs2);\n  }\n  // return the array of the path track element\n  array() {\n    const track = this.track();\n    return track ? track.array() : null;\n  }\n  // Plot path if any\n  plot(d) {\n    const track = this.track();\n    let pathArray = null;\n    if (track) {\n      pathArray = track.plot(d);\n    }\n    return d == null ? pathArray : this;\n  }\n  // Get the path element\n  track() {\n    return this.reference(\"href\");\n  }\n}\nregisterMethods({\n  Container: {\n    textPath: wrapWithAttrCheck(function (text, path) {\n      if (!(text instanceof Text)) {\n        text = this.text(text);\n      }\n      return text.path(path);\n    })\n  },\n  Text: {\n    // Create path for text to run on\n    path: wrapWithAttrCheck(function (track, importNodes = true) {\n      const textPath = new TextPath();\n      if (!(track instanceof Path)) {\n        track = this.defs().path(track);\n      }\n      textPath.attr(\"href\", \"#\" + track, xlink);\n      let node;\n      if (importNodes) {\n        while (node = this.node.firstChild) {\n          textPath.node.appendChild(node);\n        }\n      }\n      return this.put(textPath);\n    }),\n    // Get the textPath children\n    textPath() {\n      return this.findOne(\"textPath\");\n    }\n  },\n  Path: {\n    // creates a textPath from this path\n    text: wrapWithAttrCheck(function (text) {\n      if (!(text instanceof Text)) {\n        text = new Text().addTo(this.parent()).text(text);\n      }\n      return text.path(this);\n    }),\n    targets() {\n      return baseFind(\"svg textPath\").filter(node => {\n        return (node.attr(\"href\") || \"\").includes(this.id());\n      });\n    }\n  }\n});\nTextPath.prototype.MorphArray = PathArray;\nregister(TextPath, \"TextPath\");\nclass Use extends Shape {\n  constructor(node, attrs2 = node) {\n    super(nodeOrNew(\"use\", node), attrs2);\n  }\n  // Use element as a reference\n  use(element, file) {\n    return this.attr(\"href\", (file || \"\") + \"#\" + element, xlink);\n  }\n}\nregisterMethods({\n  Container: {\n    // Create a use element\n    use: wrapWithAttrCheck(function (element, file) {\n      return this.put(new Use()).use(element, file);\n    })\n  }\n});\nregister(Use, \"Use\");\nconst SVG = makeInstance;\nextend([Svg, Symbol$1, Image$1, Pattern, Marker], getMethodsFor(\"viewbox\"));\nextend([Line, Polyline, Polygon, Path], getMethodsFor(\"marker\"));\nextend(Text, getMethodsFor(\"Text\"));\nextend(Path, getMethodsFor(\"Path\"));\nextend(Defs, getMethodsFor(\"Defs\"));\nextend([Text, Tspan], getMethodsFor(\"Tspan\"));\nextend([Rect, Ellipse, Gradient, Runner], getMethodsFor(\"radius\"));\nextend(EventTarget, getMethodsFor(\"EventTarget\"));\nextend(Dom, getMethodsFor(\"Dom\"));\nextend(Element, getMethodsFor(\"Element\"));\nextend(Shape, getMethodsFor(\"Shape\"));\nextend([Container, Fragment], getMethodsFor(\"Container\"));\nextend(Gradient, getMethodsFor(\"Gradient\"));\nextend(Runner, getMethodsFor(\"Runner\"));\nList.extend(getMethodNames());\nregisterMorphableType([SVGNumber, Color, Box, Matrix, SVGArray, PointArray, PathArray, Point]);\nmakeMorphable();\nconst defaultNodeTemplate = content => {\n  return `<div style='display: flex;justify-content: center;align-items: center; text-align: center; height: 100%;'>${content}</div>`;\n};\nconst DefaultOptions = {\n  width: 400,\n  height: 400,\n  contentKey: \"name\",\n  nodeWidth: 50,\n  nodeHeight: 30,\n  nodeTemplate: defaultNodeTemplate,\n  nodeBGColor: \"#FFFFFF\",\n  nodeBGColorHover: \"#FFFFFF\",\n  nodeStyle: \"\",\n  nodeClassName: \"apextree-node\",\n  borderWidth: 1,\n  borderStyle: \"solid\",\n  borderRadius: \"5px\",\n  borderColor: \"#BCBCBC\",\n  borderColorHover: \"#5C6BC0\",\n  enableExpandCollapse: false,\n  siblingSpacing: 50,\n  childrenSpacing: 50,\n  direction: \"top\",\n  highlightOnHover: true,\n  containerClassName: \"root\",\n  enableTooltip: false,\n  tooltipId: \"apextree-tooltip-container\",\n  tooltipMaxWidth: 100,\n  tooltipBorderColor: \"#BCBCBC\",\n  tooltipBGColor: \"#FFFFFF\",\n  fontSize: \"14px\",\n  fontFamily: \"\",\n  fontWeight: 400,\n  fontColor: \"#000000\",\n  canvasStyle: \"\",\n  enableToolbar: false,\n  edgeWidth: 1,\n  edgeColor: \"#BCBCBC\",\n  edgeColorHover: \"#5C6BC0\"\n};\nvar normalizeEvent = function normalizeEvent2(ev) {\n  return ev.touches || [{\n    clientX: ev.clientX,\n    clientY: ev.clientY\n  }];\n};\nextend(Svg, {\n  panZoom: function panZoom(options) {\n    var _options,\n      _options$zoomFactor,\n      _options$zoomMin,\n      _options$zoomMax,\n      _options$wheelZoom,\n      _options$pinchZoom,\n      _options$panning,\n      _options$panButton,\n      _options$oneFingerPan,\n      _options$margins,\n      _options$wheelZoomDel,\n      _options$wheelZoomDel2,\n      _this = this;\n    this.off(\".panZoom\");\n    if (options === false) return this;\n    options = (_options = options) != null ? _options : {};\n    var zoomFactor = (_options$zoomFactor = options.zoomFactor) != null ? _options$zoomFactor : 2;\n    var zoomMin = (_options$zoomMin = options.zoomMin) != null ? _options$zoomMin : Number.MIN_VALUE;\n    var zoomMax = (_options$zoomMax = options.zoomMax) != null ? _options$zoomMax : Number.MAX_VALUE;\n    var doWheelZoom = (_options$wheelZoom = options.wheelZoom) != null ? _options$wheelZoom : true;\n    var doPinchZoom = (_options$pinchZoom = options.pinchZoom) != null ? _options$pinchZoom : true;\n    var doPanning = (_options$panning = options.panning) != null ? _options$panning : true;\n    var panButton = (_options$panButton = options.panButton) != null ? _options$panButton : 0;\n    var oneFingerPan = (_options$oneFingerPan = options.oneFingerPan) != null ? _options$oneFingerPan : false;\n    var margins = (_options$margins = options.margins) != null ? _options$margins : false;\n    var wheelZoomDeltaModeLinePixels = (_options$wheelZoomDel = options.wheelZoomDeltaModeLinePixels) != null ? _options$wheelZoomDel : 17;\n    var wheelZoomDeltaModeScreenPixels = (_options$wheelZoomDel2 = options.wheelZoomDeltaModeScreenPixels) != null ? _options$wheelZoomDel2 : 53;\n    var lastP;\n    var lastTouches;\n    var zoomInProgress = false;\n    var viewbox = this.viewbox();\n    var restrictToMargins = function restrictToMargins2(box) {\n      if (!margins) return box;\n      var top = margins.top,\n        left = margins.left,\n        bottom = margins.bottom,\n        right = margins.right;\n      var _this$attr = _this.attr([\"width\", \"height\"]),\n        width2 = _this$attr.width,\n        height2 = _this$attr.height;\n      var preserveAspectRatio = _this.node.preserveAspectRatio.baseVal;\n      var viewportLeftOffset = 0;\n      var viewportRightOffset = 0;\n      var viewportTopOffset = 0;\n      var viewportBottomOffset = 0;\n      if (preserveAspectRatio.align !== preserveAspectRatio.SVG_PRESERVEASPECTRATIO_NONE) {\n        var svgAspectRatio = width2 / height2;\n        var viewboxAspectRatio = viewbox.width / viewbox.height;\n        if (viewboxAspectRatio !== svgAspectRatio) {\n          var isMeet = preserveAspectRatio.meetOrSlice !== preserveAspectRatio.SVG_MEETORSLICE_SLICE;\n          var changedAxis = svgAspectRatio > viewboxAspectRatio ? \"width\" : \"height\";\n          var isWidth = changedAxis === \"width\";\n          var changeHorizontal = isMeet && isWidth || !isMeet && !isWidth;\n          var ratio = changeHorizontal ? svgAspectRatio / viewboxAspectRatio : viewboxAspectRatio / svgAspectRatio;\n          var offset = box[changedAxis] - box[changedAxis] * ratio;\n          if (changeHorizontal) {\n            if (preserveAspectRatio.align === preserveAspectRatio.SVG_PRESERVEASPECTRATIO_XMIDYMIN || preserveAspectRatio.align === preserveAspectRatio.SVG_PRESERVEASPECTRATIO_XMIDYMID || preserveAspectRatio.align === preserveAspectRatio.SVG_PRESERVEASPECTRATIO_XMIDYMAX) {\n              viewportLeftOffset = offset / 2;\n              viewportRightOffset = -offset / 2;\n            } else if (preserveAspectRatio.align === preserveAspectRatio.SVG_PRESERVEASPECTRATIO_XMINYMIN || preserveAspectRatio.align === preserveAspectRatio.SVG_PRESERVEASPECTRATIO_XMINYMID || preserveAspectRatio.align === preserveAspectRatio.SVG_PRESERVEASPECTRATIO_XMINYMAX) {\n              viewportRightOffset = -offset;\n            } else if (preserveAspectRatio.align === preserveAspectRatio.SVG_PRESERVEASPECTRATIO_XMAXYMIN || preserveAspectRatio.align === preserveAspectRatio.SVG_PRESERVEASPECTRATIO_XMAXYMID || preserveAspectRatio.align === preserveAspectRatio.SVG_PRESERVEASPECTRATIO_XMAXYMAX) {\n              viewportLeftOffset = offset;\n            }\n          } else {\n            if (preserveAspectRatio.align === preserveAspectRatio.SVG_PRESERVEASPECTRATIO_XMINYMID || preserveAspectRatio.align === preserveAspectRatio.SVG_PRESERVEASPECTRATIO_XMIDYMID || preserveAspectRatio.align === preserveAspectRatio.SVG_PRESERVEASPECTRATIO_XMAXYMID) {\n              viewportTopOffset = offset / 2;\n              viewportBottomOffset = -offset / 2;\n            } else if (preserveAspectRatio.align === preserveAspectRatio.SVG_PRESERVEASPECTRATIO_XMINYMIN || preserveAspectRatio.align === preserveAspectRatio.SVG_PRESERVEASPECTRATIO_XMIDYMIN || preserveAspectRatio.align === preserveAspectRatio.SVG_PRESERVEASPECTRATIO_XMAXYMIN) {\n              viewportBottomOffset = -offset;\n            } else if (preserveAspectRatio.align === preserveAspectRatio.SVG_PRESERVEASPECTRATIO_XMINYMAX || preserveAspectRatio.align === preserveAspectRatio.SVG_PRESERVEASPECTRATIO_XMIDYMAX || preserveAspectRatio.align === preserveAspectRatio.SVG_PRESERVEASPECTRATIO_XMAXYMAX) {\n              viewportTopOffset = offset;\n            }\n          }\n        }\n      }\n      var leftLimit = viewbox.width + viewbox.x - left - viewportLeftOffset;\n      var rightLimit = viewbox.x + right - box.width - viewportRightOffset;\n      var topLimit = viewbox.height + viewbox.y - top - viewportTopOffset;\n      var bottomLimit = viewbox.y + bottom - box.height - viewportBottomOffset;\n      box.x = Math.min(leftLimit, Math.max(rightLimit, box.x));\n      box.y = Math.min(topLimit, Math.max(bottomLimit, box.y));\n      return box;\n    };\n    var wheelZoom = function wheelZoom2(ev) {\n      ev.preventDefault();\n      var normalizedPixelDeltaY;\n      switch (ev.deltaMode) {\n        case 1:\n          normalizedPixelDeltaY = ev.deltaY * wheelZoomDeltaModeLinePixels;\n          break;\n        case 2:\n          normalizedPixelDeltaY = ev.deltaY * wheelZoomDeltaModeScreenPixels;\n          break;\n        default:\n          normalizedPixelDeltaY = ev.deltaY;\n          break;\n      }\n      var lvl = Math.pow(1 + zoomFactor, -1 * normalizedPixelDeltaY / 100) * this.zoom();\n      var p = this.point(ev.clientX, ev.clientY);\n      if (lvl > zoomMax) {\n        lvl = zoomMax;\n      }\n      if (lvl < zoomMin) {\n        lvl = zoomMin;\n      }\n      if (this.dispatch(\"zoom\", {\n        level: lvl,\n        focus: p\n      }).defaultPrevented) {\n        return this;\n      }\n      this.zoom(lvl, p);\n      if (margins) {\n        var box = restrictToMargins(this.viewbox());\n        this.viewbox(box);\n      }\n    };\n    var pinchZoomStart = function pinchZoomStart2(ev) {\n      lastTouches = normalizeEvent(ev);\n      if (lastTouches.length < 2) {\n        if (doPanning && oneFingerPan) {\n          panStart.call(this, ev);\n        }\n        return;\n      }\n      if (doPanning && oneFingerPan) {\n        panStop.call(this, ev);\n      }\n      ev.preventDefault();\n      if (this.dispatch(\"pinchZoomStart\", {\n        event: ev\n      }).defaultPrevented) {\n        return;\n      }\n      this.off(\"touchstart.panZoom\", pinchZoomStart2);\n      zoomInProgress = true;\n      on(document, \"touchmove.panZoom\", pinchZoom, this, {\n        passive: false\n      });\n      on(document, \"touchend.panZoom\", pinchZoomStop, this, {\n        passive: false\n      });\n    };\n    var pinchZoomStop = function pinchZoomStop2(ev) {\n      ev.preventDefault();\n      var currentTouches = normalizeEvent(ev);\n      if (currentTouches.length > 1) {\n        return;\n      }\n      zoomInProgress = false;\n      this.dispatch(\"pinchZoomEnd\", {\n        event: ev\n      });\n      off(document, \"touchmove.panZoom\", pinchZoom);\n      off(document, \"touchend.panZoom\", pinchZoomStop2);\n      this.on(\"touchstart.panZoom\", pinchZoomStart);\n      if (currentTouches.length && doPanning && oneFingerPan) {\n        panStart.call(this, ev);\n      }\n    };\n    var pinchZoom = function pinchZoom2(ev) {\n      ev.preventDefault();\n      var currentTouches = normalizeEvent(ev);\n      var zoom = this.zoom();\n      var lastDelta = Math.sqrt(Math.pow(lastTouches[0].clientX - lastTouches[1].clientX, 2) + Math.pow(lastTouches[0].clientY - lastTouches[1].clientY, 2));\n      var currentDelta = Math.sqrt(Math.pow(currentTouches[0].clientX - currentTouches[1].clientX, 2) + Math.pow(currentTouches[0].clientY - currentTouches[1].clientY, 2));\n      var zoomAmount = lastDelta / currentDelta;\n      if (zoom < zoomMin && zoomAmount > 1 || zoom > zoomMax && zoomAmount < 1) {\n        zoomAmount = 1;\n      }\n      var currentFocus = {\n        x: currentTouches[0].clientX + 0.5 * (currentTouches[1].clientX - currentTouches[0].clientX),\n        y: currentTouches[0].clientY + 0.5 * (currentTouches[1].clientY - currentTouches[0].clientY)\n      };\n      var lastFocus = {\n        x: lastTouches[0].clientX + 0.5 * (lastTouches[1].clientX - lastTouches[0].clientX),\n        y: lastTouches[0].clientY + 0.5 * (lastTouches[1].clientY - lastTouches[0].clientY)\n      };\n      var p = this.point(currentFocus.x, currentFocus.y);\n      var focusP = this.point(2 * currentFocus.x - lastFocus.x, 2 * currentFocus.y - lastFocus.y);\n      var box = new Box(this.viewbox()).transform(new Matrix().translate(-focusP.x, -focusP.y).scale(zoomAmount, 0, 0).translate(p.x, p.y));\n      restrictToMargins(box);\n      this.viewbox(box);\n      lastTouches = currentTouches;\n      this.dispatch(\"zoom\", {\n        box,\n        focus: focusP\n      });\n    };\n    var panStart = function panStart2(ev) {\n      var isMouse = ev.type.indexOf(\"mouse\") > -1;\n      if (isMouse && ev.button !== panButton && ev.which !== panButton + 1) {\n        return;\n      }\n      ev.preventDefault();\n      this.off(\"mousedown.panZoom\", panStart2);\n      lastTouches = normalizeEvent(ev);\n      if (zoomInProgress) return;\n      this.dispatch(\"panStart\", {\n        event: ev\n      });\n      lastP = {\n        x: lastTouches[0].clientX,\n        y: lastTouches[0].clientY\n      };\n      on(document, \"touchmove.panZoom mousemove.panZoom\", panning, this, {\n        passive: false\n      });\n      on(document, \"touchend.panZoom mouseup.panZoom\", panStop, this, {\n        passive: false\n      });\n    };\n    var panStop = function panStop2(ev) {\n      ev.preventDefault();\n      off(document, \"touchmove.panZoom mousemove.panZoom\", panning);\n      off(document, \"touchend.panZoom mouseup.panZoom\", panStop2);\n      this.on(\"mousedown.panZoom\", panStart);\n      this.dispatch(\"panEnd\", {\n        event: ev\n      });\n    };\n    var panning = function panning2(ev) {\n      ev.preventDefault();\n      var currentTouches = normalizeEvent(ev);\n      var currentP = {\n        x: currentTouches[0].clientX,\n        y: currentTouches[0].clientY\n      };\n      var p1 = this.point(currentP.x, currentP.y);\n      var p2 = this.point(lastP.x, lastP.y);\n      var deltaP = [p2.x - p1.x, p2.y - p1.y];\n      if (!deltaP[0] && !deltaP[1]) {\n        return;\n      }\n      var box = new Box(this.viewbox()).transform(new Matrix().translate(deltaP[0], deltaP[1]));\n      lastP = currentP;\n      restrictToMargins(box);\n      if (this.dispatch(\"panning\", {\n        box,\n        event: ev\n      }).defaultPrevented) {\n        return;\n      }\n      this.viewbox(box);\n    };\n    if (doWheelZoom) {\n      this.on(\"wheel.panZoom\", wheelZoom, this, {\n        passive: false\n      });\n    }\n    if (doPinchZoom) {\n      this.on(\"touchstart.panZoom\", pinchZoomStart, this, {\n        passive: false\n      });\n    }\n    if (doPanning) {\n      this.on(\"mousedown.panZoom\", panStart, this, {\n        passive: false\n      });\n    }\n    return this;\n  }\n});\nclass Paper {\n  constructor(element, width2, height2, canvasStyle) {\n    this.width = width2;\n    this.height = height2;\n    this.canvas = SVG().addTo(element).size(width2, height2).viewbox(`0 0 ${width2} ${height2}`).panZoom({\n      zoomFactor: 0.2,\n      zoomMin: 0.1\n    }).attr({\n      style: canvasStyle\n    });\n  }\n  add(element) {\n    this.canvas.add(element);\n  }\n  resetViewBox() {\n    this.canvas.viewbox(`0 0 ${this.width} ${this.height}`);\n  }\n  updateViewBox(x2, y2, width2, height2) {\n    this.canvas.viewbox(`${x2} ${y2} ${width2} ${height2}`);\n  }\n  zoom(zoomFactor) {\n    const newZoomVal = this.canvas.zoom() + zoomFactor;\n    if (newZoomVal >= 0.1) {\n      this.canvas.zoom(newZoomVal);\n    }\n  }\n  clear() {\n    this.canvas.clear().viewbox(`0 0 ${this.width} ${this.height}`);\n  }\n  static drawRect({\n    x1 = void 0,\n    y1 = void 0,\n    width: width2 = 0,\n    height: height2 = 0,\n    radius = 0,\n    color = \"#fefefe\",\n    opacity = 1\n  } = {}) {\n    const rect = new Rect();\n    rect.attr({\n      x: x1 ?? void 0,\n      y: y1 ?? void 0,\n      width: width2,\n      height: height2,\n      rx: radius,\n      ry: radius,\n      opacity\n    });\n    rect.fill(color);\n    return rect;\n  }\n  static drawCircle(attributes = {}) {\n    const circle = new Circle();\n    circle.attr(attributes);\n    return circle;\n  }\n  static drawText(text = \"\", {\n    x: x2,\n    y: y2,\n    dx: dx2,\n    dy: dy2\n  }) {\n    const textSvg = new Text();\n    textSvg.font({\n      fill: \"#f06\"\n    });\n    textSvg.tspan(text);\n    if (x2 !== void 0 && y2 !== void 0) {\n      textSvg.move(x2, y2);\n    }\n    if (dx2 !== void 0 && dy2 !== void 0) {\n      textSvg.attr({\n        dx: dx2,\n        dy: dy2\n      });\n    }\n    return textSvg;\n  }\n  static drawTemplate(template, {\n    nodeWidth,\n    nodeHeight\n  } = {}) {\n    const object = new ForeignObject({\n      width: nodeWidth,\n      height: nodeHeight\n    });\n    const element = document.createElement(\"div\");\n    element.innerHTML = template;\n    element.setAttribute(\"xmlns\", \"http://www.w3.org/1999/xhtml\");\n    object.add(element);\n    return object;\n  }\n  static drawGroup(x2 = 0, y2 = 0, id, parent) {\n    const group = new G();\n    group.attr({\n      transform: `translate(${x2}, ${y2})`,\n      \"data-self\": id,\n      \"data-parent\": parent\n    });\n    return group;\n  }\n  static drawPath(pathString, {\n    id = \"\",\n    borderColor = DefaultOptions.borderColor\n  } = {}) {\n    const path = new Path({\n      d: pathString\n    });\n    path.id(id);\n    path.fill(\"none\").stroke({\n      color: borderColor,\n      width: 1\n    });\n    return path;\n  }\n}\nconst addSvg = '<svg height=\"14\" viewBox=\"0 0 24 24\" width=\"14\" xmlns=\"http://www.w3.org/2000/svg\">\\n  <path d=\"m12 0a12 12 0 1 0 12 12 12.013 12.013 0 0 0 -12-12zm0 22a10 10 0 1 1 10-10 10.011 10.011 0 0 1 -10 10zm5-10a1 1 0 0 1 -1 1h-3v3a1 1 0 0 1 -2 0v-3h-3a1 1 0 0 1 0-2h3v-3a1 1 0 0 1 2 0v3h3a1 1 0 0 1 1 1z\"/>\\n</svg>';\nconst minusSvg = '<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"14\" height=\"14\">\\n  <path d=\"m12,0C5.383,0,0,5.383,0,12s5.383,12,12,12,12-5.383,12-12S18.617,0,12,0Zm0,22c-5.514,0-10-4.486-10-10S6.486,2,12,2s10,4.486,10,10-4.486,10-10,10Zm5-10c0,.552-.448,1-1,1h-8c-.552,0-1-.448-1-1s.448-1,1-1h8c.552,0,1,.448,1,1Z\"/>\\n</svg>\\n';\nclass Graph extends Paper {\n  constructor(element, options) {\n    super(element, options.width, options.height, options.canvasStyle);\n    this.element = element;\n    this.options = options;\n  }\n  construct(data2) {\n    const {\n      nodeWidth,\n      nodeHeight,\n      siblingSpacing,\n      childrenSpacing\n    } = this.options;\n    const flexLayout = flextree({\n      nodeSize: () => {\n        return DirectionConfig[this.options.direction].nodeFlexSize({\n          nodeWidth,\n          nodeHeight,\n          siblingSpacing,\n          childrenSpacing\n        });\n      },\n      spacing: 0\n    });\n    const tree = flexLayout.hierarchy(data2);\n    this.rootNode = flexLayout(tree);\n  }\n  renderNode(node, mainGroup) {\n    var _a, _b;\n    const options = this.options;\n    const {\n      nodeWidth,\n      nodeHeight,\n      nodeTemplate,\n      highlightOnHover,\n      borderRadius,\n      enableTooltip,\n      tooltipTemplate,\n      enableExpandCollapse\n    } = options;\n    const {\n      tooltipId,\n      tooltipMaxWidth,\n      tooltipBGColor,\n      tooltipBorderColor,\n      fontSize,\n      fontWeight,\n      fontFamily,\n      fontColor,\n      borderWidth,\n      borderStyle,\n      borderColor,\n      nodeBGColor,\n      nodeStyle,\n      nodeClassName\n    } = {\n      ...options,\n      ...node.data.options\n    };\n    const {\n      x: x2,\n      y: y2\n    } = DirectionConfig[options.direction].swap(node);\n    const graphInstance = this;\n    const group = Paper.drawGroup(x2, y2, node.data.id, (_a = node.parent) == null ? void 0 : _a.data.id);\n    const nodeContent = nodeTemplate(node.data[options.contentKey]);\n    const object = Paper.drawTemplate(nodeContent, {\n      nodeWidth,\n      nodeHeight\n    });\n    const groupStyle = generateStyles({\n      fontSize,\n      fontWeight,\n      fontFamily,\n      color: fontColor\n    });\n    const containerStyles = generateStyles({\n      borderColor,\n      borderStyle,\n      borderWidth: `${borderWidth}px`,\n      borderRadius,\n      backgroundColor: nodeBGColor,\n      height: \"100%\",\n      boxSizing: \"border-box\"\n    });\n    object.first().attr(\"style\", containerStyles.concat(nodeStyle));\n    object.attr(\"class\", nodeClassName);\n    group.attr(\"style\", groupStyle);\n    group.add(object);\n    const nodes = ((_b = this.rootNode) == null ? void 0 : _b.nodes) || [];\n    if (highlightOnHover) {\n      group.on(\"mouseover\", function () {\n        const self = this.node.dataset.self;\n        const selfNode = nodes.find(n => n.data.id === self);\n        selfNode && highlightToPath(nodes, selfNode, true, options);\n      });\n      group.on(\"mouseout\", function () {\n        const self = this.node.dataset.self;\n        const selfNode = nodes.find(n => n.data.id === self);\n        selfNode && highlightToPath(nodes, selfNode, false, options);\n      });\n    }\n    if (enableTooltip) {\n      const tooltipContent = tooltipTemplate ? tooltipTemplate(node.data[this.options.contentKey]) : nodeContent;\n      group.on(\"mousemove\", function (e) {\n        const styles = getTooltipStyles(e.pageX, e.pageY, tooltipMaxWidth, tooltipBorderColor, tooltipBGColor, !tooltipTemplate);\n        updateTooltip(tooltipId, styles.join(\" \"), tooltipContent);\n      });\n      group.on(\"mouseout\", function (e) {\n        if (e.relatedTarget.tagName === \"svg\") {\n          updateTooltip(tooltipId);\n        }\n      });\n    }\n    mainGroup.add(group);\n    if (!node.children && !node.hiddenChildren) {\n      return;\n    }\n    if (enableExpandCollapse) {\n      const expandButtonRadius = ExpandCollapseButtonSize / 2;\n      const buttonGroup = Paper.drawGroup(x2 + nodeWidth / 2 - expandButtonRadius, y2 + nodeHeight - expandButtonRadius, node.data.id);\n      const buttonClickArea = Paper.drawCircle({\n        cx: expandButtonRadius,\n        cy: expandButtonRadius,\n        r: expandButtonRadius,\n        style: \"fill: #FFF; cursor: pointer;\"\n      });\n      buttonGroup.data(\"expanded\", false);\n      buttonGroup.add(buttonClickArea);\n      if (node.hiddenChildren) {\n        buttonGroup.add(addSvg);\n      } else {\n        buttonGroup.add(minusSvg);\n      }\n      buttonGroup.on(\"click\", function () {\n        if (node.hiddenChildren) {\n          graphInstance.expand(this.node.dataset.self);\n        } else {\n          graphInstance.collapse(this.node.dataset.self);\n        }\n      });\n      mainGroup.add(buttonGroup);\n    }\n  }\n  renderEdge(node, group) {\n    var _a;\n    const {\n      nodeWidth,\n      nodeHeight\n    } = this.options;\n    const edge = getEdge(node, nodeWidth, nodeHeight, this.options.direction);\n    if (!edge) return;\n    const path = Paper.drawPath(edge, {\n      id: `${node.data.id}-${(_a = node.parent) == null ? void 0 : _a.data.id}`\n    });\n    node.edge = path;\n    group.add(path);\n  }\n  collapse(nodeId) {\n    var _a;\n    const nodes = ((_a = this.rootNode) == null ? void 0 : _a.descendants()) || [];\n    const node = nodes.find(n => n.data.id === nodeId);\n    if (node == null ? void 0 : node.children) {\n      node.hiddenChildren = node.children;\n      node.hiddenChildren.forEach(child => this.collapse(child));\n      node.children = void 0;\n      this.render({\n        keepOldPosition: true\n      });\n    }\n  }\n  expand(nodeId) {\n    var _a;\n    const nodes = ((_a = this.rootNode) == null ? void 0 : _a.descendants()) || [];\n    const node = nodes.find(n => n.data.id === nodeId);\n    if (node == null ? void 0 : node.hiddenChildren) {\n      node.children = node.hiddenChildren;\n      node.children.forEach(child => this.expand(child));\n      node.hiddenChildren = void 0;\n      this.render({\n        keepOldPosition: true\n      });\n    }\n  }\n  changeLayout(direction = \"top\") {\n    this.options = {\n      ...this.options,\n      direction\n    };\n    this.render({\n      keepOldPosition: false\n    });\n  }\n  fitScreen() {\n    const {\n      childrenSpacing,\n      siblingSpacing\n    } = this.options;\n    const {\n      viewBoxDimensions\n    } = DirectionConfig[this.options.direction];\n    const {\n      x: x2,\n      y: y2,\n      width: vWidth,\n      height: vHeight\n    } = viewBoxDimensions({\n      rootNode: this.rootNode,\n      childrenSpacing,\n      siblingSpacing\n    });\n    this.updateViewBox(x2, y2, vWidth, vHeight);\n  }\n  render({\n    keepOldPosition = false\n  } = {}) {\n    var _a;\n    const oldViewbox = this.canvas.viewbox();\n    this.clear();\n    const {\n      containerClassName,\n      enableTooltip,\n      tooltipId,\n      fontSize,\n      fontWeight,\n      fontFamily,\n      fontColor\n    } = this.options;\n    const globalStyle = generateStyles({\n      fontSize,\n      fontWeight,\n      fontFamily,\n      color: fontColor\n    });\n    const mainGroup = Paper.drawGroup(0, 0, containerClassName);\n    mainGroup.attr(\"style\", globalStyle);\n    mainGroup.id(containerClassName);\n    const nodes = ((_a = this.rootNode) == null ? void 0 : _a.nodes) || [];\n    nodes.forEach(node => {\n      this.renderEdge(node, mainGroup);\n    });\n    nodes.forEach(node => {\n      this.renderNode(node, mainGroup);\n    });\n    this.add(mainGroup);\n    this.fitScreen();\n    if (keepOldPosition) {\n      this.updateViewBox(oldViewbox.x, oldViewbox.y, oldViewbox.width, oldViewbox.height);\n    }\n    if (enableTooltip) {\n      const tooltipElement = getTooltip(tooltipId);\n      const body = document.body || document.getElementsByTagName(\"body\")[0];\n      body.append(tooltipElement);\n    }\n  }\n}\nconst ZoomInIcon = \"data:image/svg+xml,%3csvg%20width='20px'%20height='20px'%20viewBox='0%200%2032%2032'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20xmlns:sketch='http://www.bohemiancoding.com/sketch/ns'%3e%3cg%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20transform='translate(-308.000000,%20-1139.000000)'%20fill='%23000000'%3e%3cpath%20d='M321.46,1163.45%20C315.17,1163.45%20310.07,1158.44%20310.07,1152.25%20C310.07,1146.06%20315.17,1141.04%20321.46,1141.04%20C327.75,1141.04%20332.85,1146.06%20332.85,1152.25%20C332.85,1158.44%20327.75,1163.45%20321.46,1163.45%20L321.46,1163.45%20Z%20M339.688,1169.25%20L331.429,1161.12%20C333.592,1158.77%20334.92,1155.67%20334.92,1152.25%20C334.92,1144.93%20328.894,1139%20321.46,1139%20C314.026,1139%20308,1144.93%20308,1152.25%20C308,1159.56%20314.026,1165.49%20321.46,1165.49%20C324.672,1165.49%20327.618,1164.38%20329.932,1162.53%20L338.225,1170.69%20C338.629,1171.09%20339.284,1171.09%20339.688,1170.69%20C340.093,1170.3%20340.093,1169.65%20339.688,1169.25%20L339.688,1169.25%20Z%20M326.519,1151.41%20L322.522,1151.41%20L322.522,1147.41%20C322.522,1146.85%20322.075,1146.41%20321.523,1146.41%20C320.972,1146.41%20320.524,1146.85%20320.524,1147.41%20L320.524,1151.41%20L316.529,1151.41%20C315.978,1151.41%20315.53,1151.59%20315.53,1152.14%20C315.53,1152.7%20315.978,1153.41%20316.529,1153.41%20L320.524,1153.41%20L320.524,1157.41%20C320.524,1157.97%20320.972,1158.41%20321.523,1158.41%20C322.075,1158.41%20322.522,1157.97%20322.522,1157.41%20L322.522,1153.41%20L326.519,1153.41%20C327.07,1153.41%20327.518,1152.96%20327.518,1152.41%20C327.518,1151.86%20327.07,1151.41%20326.519,1151.41%20L326.519,1151.41%20Z'%20/%3e%3c/g%3e%3c/g%3e%3c/svg%3e\";\nconst ZoomOutIcon = \"data:image/svg+xml,%3csvg%20width='20px'%20height='20px'%20viewBox='0%200%2032%2032'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3cg%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%20%3e%3cg%20transform='translate(-360.000000,%20-1139.000000)'%20fill='%23000000'%3e%3cpath%20d='M373.46,1163.45%20C367.17,1163.45%20362.071,1158.44%20362.071,1152.25%20C362.071,1146.06%20367.17,1141.04%20373.46,1141.04%20C379.75,1141.04%20384.85,1146.06%20384.85,1152.25%20C384.85,1158.44%20379.75,1163.45%20373.46,1163.45%20L373.46,1163.45%20Z%20M391.688,1169.25%20L383.429,1161.12%20C385.592,1158.77%20386.92,1155.67%20386.92,1152.25%20C386.92,1144.93%20380.894,1139%20373.46,1139%20C366.026,1139%20360,1144.93%20360,1152.25%20C360,1159.56%20366.026,1165.49%20373.46,1165.49%20C376.672,1165.49%20379.618,1164.38%20381.932,1162.53%20L390.225,1170.69%20C390.629,1171.09%20391.284,1171.09%20391.688,1170.69%20C392.093,1170.3%20392.093,1169.65%20391.688,1169.25%20L391.688,1169.25%20Z%20M378.689,1151.41%20L368.643,1151.41%20C368.102,1151.41%20367.663,1151.84%20367.663,1152.37%20C367.663,1152.9%20368.102,1153.33%20368.643,1153.33%20L378.689,1153.33%20C379.23,1153.33%20379.669,1152.9%20379.669,1152.37%20C379.669,1151.84%20379.23,1151.41%20378.689,1151.41%20L378.689,1151.41%20Z'%20/%3e%3c/g%3e%3c/g%3e%3c/svg%3e\";\nconst FitScreenIcon = \"data:image/svg+xml,%3csvg%20width='20px'%20height='20px'%20viewBox='0%200%2032%2032'%20id='icon'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpolygon%20points='8%202%202%202%202%208%204%208%204%204%208%204%208%202'/%3e%3cpolygon%20points='24%202%2030%202%2030%208%2028%208%2028%204%2024%204%2024%202'/%3e%3cpolygon%20points='8%2030%202%2030%202%2024%204%2024%204%2028%208%2028%208%2030'/%3e%3cpolygon%20points='24%2030%2030%2030%2030%2024%2028%2024%2028%2028%2024%2028%2024%2030'/%3e%3cpath%20d='M24,24H8a2.0023,2.0023,0,0,1-2-2V10A2.0023,2.0023,0,0,1,8,8H24a2.0023,2.0023,0,0,1,2,2V22A2.0023,2.0023,0,0,1,24,24ZM8,10V22H24V10Z'/%3e%3crect%20fill='none'%20width='32'%20height='32'/%3e%3c/svg%3e\";\nconst ExportIcon = \"data:image/svg+xml,%3csvg%20fill='%23000000'%20width='20px'%20height='20px'%20viewBox='0%200%2024%2024'%20id='export-2'%20xmlns='http://www.w3.org/2000/svg'%20class='icon%20line'%3e%3cpolyline%20id='primary'%20points='15%203%2021%203%2021%209'%20style='fill:%20none;%20stroke:%20rgb(0,%200,%200);%20stroke-linecap:%20round;%20stroke-linejoin:%20round;%20stroke-width:%201.5;'%3e%3c/polyline%3e%3cpath%20id='primary-2'%20data-name='primary'%20d='M21,13v7a1,1,0,0,1-1,1H4a1,1,0,0,1-1-1V4A1,1,0,0,1,4,3h7'%20style='fill:%20none;%20stroke:%20rgb(0,%200,%200);%20stroke-linecap:%20round;%20stroke-linejoin:%20round;%20stroke-width:%201.5;'%3e%3c/path%3e%3cline%20id='primary-3'%20data-name='primary'%20x1='11'%20y1='13'%20x2='21'%20y2='3'%20style='fill:%20none;%20stroke:%20rgb(0,%200,%200);%20stroke-linecap:%20round;%20stroke-linejoin:%20round;%20stroke-width:%201.5;'%3e%3c/line%3e%3c/svg%3e\";\nclass Export {\n  constructor(graph) {\n    this.graph = graph;\n  }\n  getSvgString() {\n    const svgString = this.graph.canvas.svg();\n    return svgString.replace(/(<img [\\w\\W]+?)(>)/g, \"$1 />\").replace(/(<br)(>)/g, \"$1 />\").replace(/(<hr)(>)/g, \"$1 />\");\n  }\n  svgUrl() {\n    const svgData = this.getSvgString();\n    const svgBlob = new Blob([svgData], {\n      type: \"image/svg+xml;charset=utf-8\"\n    });\n    return URL.createObjectURL(svgBlob);\n  }\n  triggerDownload(href, filename) {\n    const downloadLink = document.createElement(\"a\");\n    downloadLink.href = href;\n    downloadLink.download = filename;\n    document.body.appendChild(downloadLink);\n    downloadLink.click();\n    document.body.removeChild(downloadLink);\n  }\n  exportToSVG() {\n    this.triggerDownload(this.svgUrl(), `apex-tree-${(/* @__PURE__ */new Date()).getTime()}.svg`);\n  }\n}\nconst ToolBarIcons = {\n  [\"zoom-in\"\n  /* ZoomIn */]: ZoomInIcon,\n  [\"zoom-out\"\n  /* ZoomOut */]: ZoomOutIcon,\n  [\"fit-screen\"\n  /* FitScreen */]: FitScreenIcon,\n  [\"export\"\n  /* Export */]: ExportIcon\n};\nconst ZoomChangeFactor = 0.1;\nclass Toolbar {\n  constructor(element, graph) {\n    this.element = element;\n    this.graph = graph;\n    this.export = new Export(graph);\n  }\n  render() {\n    var _a;\n    const container = document.createElement(\"div\");\n    container.id = \"toolbar\";\n    const containerStyles = generateStyles({\n      display: \"flex\",\n      gap: \"5px\",\n      position: \"absolute\",\n      right: \"20px\",\n      top: \"20px\"\n    });\n    container.setAttribute(\"style\", containerStyles);\n    const btnZoomIn = this.createToolbarItem(\"zoom-in\", ToolBarIcons[\"zoom-in\"\n    /* ZoomIn */]);\n    const btnZoomOut = this.createToolbarItem(\"zoom-out\", ToolBarIcons[\"zoom-out\"\n    /* ZoomOut */]);\n    const btnFitScreen = this.createToolbarItem(\"fit-screen\", ToolBarIcons[\"fit-screen\"\n    /* FitScreen */]);\n    const btnExport = this.createToolbarItem(\"export\", ToolBarIcons[\"export\"\n    /* Export */]);\n    btnZoomIn.addEventListener(\"click\", () => {\n      this.graph.zoom(ZoomChangeFactor);\n    });\n    btnZoomOut.addEventListener(\"click\", () => {\n      this.graph.zoom(-ZoomChangeFactor);\n    });\n    btnFitScreen.addEventListener(\"click\", () => {\n      this.graph.fitScreen();\n    });\n    btnExport.addEventListener(\"click\", () => {\n      this.export.exportToSVG();\n    });\n    container.append(btnZoomIn, btnZoomOut, btnFitScreen, btnExport);\n    (_a = this.element) == null ? void 0 : _a.append(container);\n  }\n  createToolbarItem(itemName, icon) {\n    const itemContainer = document.createElement(\"div\");\n    const image = new Image();\n    image.src = icon;\n    itemContainer.id = itemName;\n    itemContainer.append(image);\n    const containerStyles = generateStyles({\n      width: \"30px\",\n      height: \"30px\",\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      border: \"1px solid #BCBCBC\",\n      backgroundColor: \"#FFFFFF\",\n      cursor: \"pointer\"\n    });\n    itemContainer.setAttribute(\"style\", containerStyles);\n    return itemContainer;\n  }\n}\nclass ApexTree {\n  constructor(element, options) {\n    this.element = element;\n    this.options = {\n      ...DefaultOptions,\n      ...options\n    };\n    const treeWrapper = document.createElement(\"div\");\n    treeWrapper.id = \"apexTreeWrapper\";\n    treeWrapper.style.position = \"relative\";\n    this.graph = new Graph(treeWrapper, this.options);\n    this.element.append(treeWrapper);\n  }\n  render(data2) {\n    if (!this.element) {\n      throw new Error(\"Element not found\");\n    }\n    this.graph.construct(data2);\n    this.graph.render();\n    if (this.options.enableToolbar) {\n      const toolbar = new Toolbar(document.getElementById(\"apexTreeWrapper\"), this.graph);\n      toolbar.render();\n    }\n    return this.graph;\n  }\n}\nexport { ApexTree as default };"], "mappings": ";;;;;;AAAA,SAAS,MAAM,MAAM;AACnB,MAAI,MAAM,GACR,WAAW,KAAK,UAChB,IAAI,YAAY,SAAS;AAC3B,MAAI,CAAC,EAAG,OAAM;AAAA,MAAO,QAAO,EAAE,KAAK,EAAG,QAAO,SAAS,CAAC,EAAE;AACzD,OAAK,QAAQ;AACf;AACA,SAAS,aAAa;AACpB,SAAO,KAAK,UAAU,KAAK;AAC7B;AACA,SAAS,UAAU,UAAU;AAC3B,MAAI,OAAO,MACT,SACA,QAAQ,CAAC,IAAI,GACb,UACA,GACA;AACF,KAAG;AACD,cAAU,MAAM,QAAQ,GAAG,QAAQ,CAAC;AACpC,WAAO,OAAO,QAAQ,IAAI,GAAG;AAC3B,eAAS,IAAI,GAAG,WAAW,KAAK;AAChC,UAAI,SAAU,MAAK,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,EAAE,GAAG;AACzD,cAAM,KAAK,SAAS,CAAC,CAAC;AAAA,MACxB;AAAA,IACF;AAAA,EACF,SAAS,MAAM;AACf,SAAO;AACT;AACA,SAAS,gBAAgB,UAAU;AACjC,MAAI,OAAO,MACT,QAAQ,CAAC,IAAI,GACb,UACA;AACF,SAAO,OAAO,MAAM,IAAI,GAAG;AACzB,aAAS,IAAI,GAAG,WAAW,KAAK;AAChC,QAAI,SAAU,MAAK,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACvD,YAAM,KAAK,SAAS,CAAC,CAAC;AAAA,IACxB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,eAAe,UAAU;AAChC,MAAI,OAAO,MACT,QAAQ,CAAC,IAAI,GACb,QAAQ,CAAC,GACT,UACA,GACA;AACF,SAAO,OAAO,MAAM,IAAI,GAAG;AACzB,UAAM,KAAK,IAAI,GAAG,WAAW,KAAK;AAClC,QAAI,SAAU,MAAK,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,EAAE,GAAG;AACzD,YAAM,KAAK,SAAS,CAAC,CAAC;AAAA,IACxB;AAAA,EACF;AACA,SAAO,OAAO,MAAM,IAAI,GAAG;AACzB,aAAS,IAAI;AAAA,EACf;AACA,SAAO;AACT;AACA,SAAS,SAAS,OAAO;AACvB,SAAO,KAAK,UAAU,SAAU,MAAM;AACpC,QAAI,MAAM,CAAC,MAAM,KAAK,IAAI,KAAK,GAC7B,WAAW,KAAK,UAChB,IAAI,YAAY,SAAS;AAC3B,WAAO,EAAE,KAAK,EAAG,QAAO,SAAS,CAAC,EAAE;AACpC,SAAK,QAAQ;AAAA,EACf,CAAC;AACH;AACA,SAAS,UAAU,SAAS;AAC1B,SAAO,KAAK,WAAW,SAAU,MAAM;AACrC,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,KAAK,OAAO;AAAA,IAC5B;AAAA,EACF,CAAC;AACH;AACA,SAAS,UAAU,KAAK;AACtB,MAAI,QAAQ,MACV,WAAW,oBAAoB,OAAO,GAAG,GACzC,QAAQ,CAAC,KAAK;AAChB,SAAO,UAAU,UAAU;AACzB,YAAQ,MAAM;AACd,UAAM,KAAK,KAAK;AAAA,EAClB;AACA,MAAI,IAAI,MAAM;AACd,SAAO,QAAQ,UAAU;AACvB,UAAM,OAAO,GAAG,GAAG,GAAG;AACtB,UAAM,IAAI;AAAA,EACZ;AACA,SAAO;AACT;AACA,SAAS,oBAAoB,GAAG,GAAG;AACjC,MAAI,MAAM,EAAG,QAAO;AACpB,MAAI,SAAS,EAAE,UAAU,GACvB,SAAS,EAAE,UAAU,GACrB,IAAI;AACN,MAAI,OAAO,IAAI;AACf,MAAI,OAAO,IAAI;AACf,SAAO,MAAM,GAAG;AACd,QAAI;AACJ,QAAI,OAAO,IAAI;AACf,QAAI,OAAO,IAAI;AAAA,EACjB;AACA,SAAO;AACT;AACA,SAAS,iBAAiB;AACxB,MAAI,OAAO,MACT,QAAQ,CAAC,IAAI;AACf,SAAO,OAAO,KAAK,QAAQ;AACzB,UAAM,KAAK,IAAI;AAAA,EACjB;AACA,SAAO;AACT;AACA,SAAS,mBAAmB;AAC1B,MAAI,QAAQ,CAAC;AACb,OAAK,KAAK,SAAU,MAAM;AACxB,UAAM,KAAK,IAAI;AAAA,EACjB,CAAC;AACD,SAAO;AACT;AACA,SAAS,cAAc;AACrB,MAAI,SAAS,CAAC;AACd,OAAK,WAAW,SAAU,MAAM;AAC9B,QAAI,CAAC,KAAK,UAAU;AAClB,aAAO,KAAK,IAAI;AAAA,IAClB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,aAAa;AACpB,MAAI,QAAQ,MACV,QAAQ,CAAC;AACX,QAAM,KAAK,SAAU,MAAM;AACzB,QAAI,SAAS,OAAO;AAClB,YAAM,KAAK;AAAA,QACT,QAAQ,KAAK;AAAA,QACb,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,UAAU,OAAO,UAAU;AAClC,MAAI,QAAQ,IAAI,KAAK,KAAK,GACxB,SAAS,CAAC,MAAM,UAAU,MAAM,QAAQ,MAAM,QAC9C,MACA,QAAQ,CAAC,KAAK,GACd,OACA,QACA,GACA;AACF,MAAI,YAAY,KAAM,YAAW;AACjC,SAAO,OAAO,MAAM,IAAI,GAAG;AACzB,QAAI,OAAQ,MAAK,QAAQ,CAAC,KAAK,KAAK;AACpC,SAAK,SAAS,SAAS,KAAK,IAAI,OAAO,IAAI,OAAO,SAAS;AACzD,WAAK,WAAW,IAAI,MAAM,CAAC;AAC3B,WAAK,IAAI,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,cAAM,KAAK,QAAQ,KAAK,SAAS,CAAC,IAAI,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC;AACzD,cAAM,SAAS;AACf,cAAM,QAAQ,KAAK,QAAQ;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AACA,SAAO,MAAM,WAAW,aAAa;AACvC;AACA,SAAS,YAAY;AACnB,SAAO,UAAU,IAAI,EAAE,WAAW,QAAQ;AAC5C;AACA,SAAS,gBAAgB,GAAG;AAC1B,SAAO,EAAE;AACX;AACA,SAAS,SAAS,MAAM;AACtB,OAAK,OAAO,KAAK,KAAK;AACxB;AACA,SAAS,cAAc,MAAM;AAC3B,MAAI,UAAU;AACd;AAAG,SAAK,SAAS;AAAA,UAAiB,OAAO,KAAK,WAAW,KAAK,SAAS,EAAE;AAC3E;AACA,SAAS,KAAK,OAAO;AACnB,OAAK,OAAO;AACZ,OAAK,QAAQ,KAAK,SAAS;AAC3B,OAAK,SAAS;AAChB;AACA,KAAK,YAAY,UAAU,YAAY;AAAA,EACrC,aAAa;AAAA,EACb,OAAO;AAAA,EACP,MAAM;AAAA,EACN,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,KAAK;AAAA,EACL,MAAM;AAAA,EACN,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AACR;AACA,IAAM,OAAO;AACb,IAAM,YAAY;AAClB,IAAM,OAAO;AACb,IAAM,SAAS;AACf,IAAM,SAAS;AAAA,EACb,MAAM;AAAA,EACN,KAAK;AACP;AACA,IAAM,cAAc;AACpB,IAAM,WAAW,CAAC,MAAM,aAAa,UAAU,QAAQ,aAAa,gBAAgB,UAAU,aAAa,WAAW,iBAAiB,IAAI;AAC3I,IAAM,WAAW;AACjB,IAAM,UAAU;AAChB,IAAM,aAAa;AAAA,EACjB,MAAM;AAAA,EACN,KAAK;AACP;AACA,IAAM,UAAU;AAAA,EACd,OAAO;AAAA,EACP,cAAc;AAAA,EACd,aAAa;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,OAAO;AAAA,EACP,MAAM;AAAA,EACN,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,eAAe;AAAA,EACnB,gBAAgB;AAClB;AACA,IAAM,kBAAkB;AAAA,EACtB,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,IAAI;AAAA,EACJ,sBAAsB;AAAA,EACtB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,aAAa;AACf;AACA,IAAM,cAAc;AAAA,EAClB;AAAA,EACA,SAAS;AAAA,EACT;AAAA,EACA;AAAA,EACA,eAAe;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM;AAAA,EACJ;AACF,IAAI;AACJ,IAAM,WAAW,OAAO,OAAO;AAAA,EAC7B,UAAU,WAAS,MAAM;AAAA,EACzB,UAAU,UAAQ,KAAK,KAAK;AAAA,EAC5B,SAAS;AACX,CAAC;AACD,SAAS,SAAS,SAAS;AACzB,QAAM,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,OAAO;AAChD,WAAS,SAAS,OAAO;AACvB,UAAM,MAAM,KAAK,KAAK;AACtB,WAAO,OAAO,QAAQ,aAAa,MAAM,MAAM;AAAA,EACjD;AACA,WAAS,OAAO,MAAM;AACpB,UAAM,QAAQ,KAAK,WAAW,GAAG,MAAM,UAAQ,KAAK,QAAQ;AAC5D,UAAM,OAAO;AACb,WAAO,MAAM;AAAA,EACf;AACA,WAAS,cAAc;AACrB,UAAM,WAAW,SAAS,UAAU;AACpC,UAAM,UAAU,SAAS,SAAS;AAClC,WAAO,MAAM,iBAAiB,UAAU,UAAU,YAAY;AAAA,MAC5D,YAAY,OAAO;AACjB,cAAM,KAAK;AAAA,MACb;AAAA,MACA,OAAO;AACL,cAAM,IAAI,KAAK,KAAK,aAAa,MAAM,UAAQ,KAAK,QAAQ;AAC5D,UAAE,KAAK,UAAQ,KAAK,OAAO,KAAK,KAAK,IAAI;AACzC,eAAO;AAAA,MACT;AAAA,MACA,IAAI,OAAO;AACT,eAAO,SAAS,IAAI;AAAA,MACtB;AAAA,MACA,QAAQ,OAAO;AACb,eAAO,QAAQ,MAAM,KAAK;AAAA,MAC5B;AAAA,MACA,IAAI,QAAQ;AACV,eAAO,KAAK,YAAY;AAAA,MAC1B;AAAA,MACA,IAAI,QAAQ;AACV,eAAO,KAAK,KAAK,CAAC;AAAA,MACpB;AAAA,MACA,IAAI,QAAQ;AACV,eAAO,KAAK,KAAK,CAAC;AAAA,MACpB;AAAA,MACA,IAAI,MAAM;AACR,eAAO,KAAK;AAAA,MACd;AAAA,MACA,IAAI,SAAS;AACX,eAAO,KAAK,IAAI,KAAK;AAAA,MACvB;AAAA,MACA,IAAI,OAAO;AACT,eAAO,KAAK,IAAI,KAAK,QAAQ;AAAA,MAC/B;AAAA,MACA,IAAI,QAAQ;AACV,eAAO,KAAK,IAAI,KAAK,QAAQ;AAAA,MAC/B;AAAA,MACA,IAAI,OAAO;AACT,cAAM,OAAO,KAAK,UAAU;AAC5B,eAAO,KAAK,KAAK,SAAS,CAAC;AAAA,MAC7B;AAAA,MACA,IAAI,cAAc;AAChB,eAAO,KAAK,cAAc,KAAK,SAAS,SAAS;AAAA,MACnD;AAAA,MACA,IAAI,cAAc;AAChB,eAAO,CAAC,KAAK;AAAA,MACf;AAAA,MACA,IAAI,aAAa;AACf,eAAO,KAAK,aAAa;AAAA,MAC3B;AAAA,MACA,IAAI,aAAa;AACf,eAAO,KAAK,cAAc,KAAK,SAAS,CAAC,IAAI;AAAA,MAC/C;AAAA,MACA,IAAI,YAAY;AACd,eAAO,KAAK,cAAc,KAAK,SAAS,KAAK,cAAc,CAAC,IAAI;AAAA,MAClE;AAAA,MACA,IAAI,UAAU;AACZ,gBAAQ,KAAK,YAAY,CAAC,GAAG,OAAO,CAAC,KAAK,QAAQ,SAAS,WAAW,KAAK,IAAI,OAAO,GAAG,KAAK,WAAW;AAAA,MAC3G;AAAA,MACA,IAAI,cAAc;AAChB,eAAO;AAAA,UACL,KAAK,KAAK;AAAA,UACV,QAAQ,KAAK;AAAA,UACb,MAAM,KAAK;AAAA,UACX,OAAO,KAAK;AAAA,QACd;AAAA,MACF;AAAA,MACA,OAAO,WAAW,IAAI,IAAI;AACxB,eAAO;AAAA,UACL,KAAK,KAAK,IAAI,GAAG,KAAK,GAAG,GAAG;AAAA,UAC5B,QAAQ,KAAK,IAAI,GAAG,QAAQ,GAAG,MAAM;AAAA,UACrC,MAAM,KAAK,IAAI,GAAG,MAAM,GAAG,IAAI;AAAA,UAC/B,OAAO,KAAK,IAAI,GAAG,OAAO,GAAG,KAAK;AAAA,QACpC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,WAAS,aAAa;AACpB,UAAM,WAAW,YAAY;AAC7B,UAAM,WAAW,SAAS,UAAU;AACpC,UAAM,UAAU,SAAS,SAAS;AAClC,WAAO,cAAc,SAAS;AAAA,MAC5B,YAAY,OAAO;AACjB,cAAM,KAAK;AACX,eAAO,OAAO,MAAM;AAAA,UAClB,GAAG;AAAA,UACH,GAAG;AAAA,UACH,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,UAAU;AAAA,UACV,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,MACA,IAAI,OAAO;AACT,eAAO,SAAS,KAAK,IAAI;AAAA,MAC3B;AAAA,MACA,QAAQ,OAAO;AACb,eAAO,QAAQ,KAAK,MAAM,MAAM,IAAI;AAAA,MACtC;AAAA,MACA,IAAI,IAAI;AACN,eAAO,KAAK,KAAK;AAAA,MACnB;AAAA,MACA,IAAI,EAAE,GAAG;AACP,aAAK,KAAK,IAAI;AAAA,MAChB;AAAA,MACA,IAAI,IAAI;AACN,eAAO,KAAK,KAAK;AAAA,MACnB;AAAA,MACA,IAAI,EAAE,GAAG;AACP,aAAK,KAAK,IAAI;AAAA,MAChB;AAAA,MACA,SAAS;AACP,uBAAe,IAAI;AACnB,iBAAS,IAAI;AACb,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,WAAS,KAAK,WAAW,UAAU,UAAU;AAC3C,UAAM,QAAQ,CAAC,OAAO,WAAW;AAC/B,YAAM,OAAO,IAAI,UAAU,KAAK;AAChC,aAAO,OAAO,MAAM;AAAA,QAClB;AAAA,QACA,OAAO,WAAW,OAAO,IAAI,OAAO,QAAQ;AAAA,QAC5C,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,CAAC;AACD,YAAM,WAAW,SAAS,KAAK,KAAK,CAAC;AACrC,WAAK,WAAW,SAAS,WAAW,IAAI,OAAO,SAAS,IAAI,QAAM,MAAM,IAAI,IAAI,CAAC;AACjF,UAAI,KAAK,UAAU;AACjB,eAAO,OAAO,MAAM,KAAK,SAAS,OAAO,CAAC,IAAI,SAAS;AAAA,UACrD,QAAQ,KAAK,IAAI,GAAG,QAAQ,IAAI,SAAS,CAAC;AAAA,UAC1C,QAAQ,GAAG,SAAS,IAAI;AAAA,QAC1B,IAAI,IAAI,CAAC;AAAA,MACX;AACA,aAAO;AAAA,IACT;AACA,WAAO,MAAM,UAAU,IAAI;AAAA,EAC7B;AACA,SAAO,OAAO,QAAQ;AAAA,IACpB,SAAS,KAAK;AACZ,aAAO,UAAU,UAAU,KAAK,WAAW,KAAK,UAAU,KAAK;AAAA,IACjE;AAAA,IACA,QAAQ,KAAK;AACX,aAAO,UAAU,UAAU,KAAK,UAAU,KAAK,UAAU,KAAK;AAAA,IAChE;AAAA,IACA,SAAS,KAAK;AACZ,aAAO,UAAU,UAAU,KAAK,WAAW,KAAK,UAAU,KAAK;AAAA,IACjE;AAAA,IACA,UAAU,UAAU,UAAU;AAC5B,YAAM,OAAO,OAAO,aAAa,cAAc,KAAK,WAAW;AAC/D,aAAO,KAAK,YAAY,GAAG,UAAU,IAAI;AAAA,IAC3C;AAAA,IACA,KAAK,MAAM;AACT,YAAM,WAAW,SAAS,UAAU;AACpC,YAAM,QAAQ,QAAM,UAAQ;AAC1B,cAAM,KAAK,KAAK;AAChB,cAAM,KAAK,KAAK;AAChB,cAAM;AAAA,UACJ,GAAG;AAAA,UACH,GAAG;AAAA,QACL,IAAI;AACJ,cAAM,QAAQ,SAAS,IAAI;AAC3B,cAAM,OAAO,KAAK,YAAY,CAAC;AAC/B,cAAM,SAAS,KAAK,WAAW,IAAI,MAAM,IAAI,EAAE,cAAc,EAAE,GAAG,KAAK,IAAI,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE;AAC1G,eAAO,YAAY,MAAM,KAAK,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM;AAAA,MACvE;AACA,aAAO,MAAM,IAAI,EAAE,IAAI;AAAA,IACzB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,UAAU;AACnB,IAAM,iBAAiB,CAAC,GAAG,KAAK,MAAM;AACpC,IAAE,IAAI;AACN,GAAC,EAAE,YAAY,CAAC,GAAG,OAAO,CAAC,KAAK,QAAQ;AACtC,UAAM,CAAC,GAAG,QAAQ,IAAI;AACtB,mBAAe,KAAK,EAAE,IAAI,EAAE,KAAK;AACjC,UAAM,QAAQ,MAAM,IAAI,IAAI,OAAO,IAAI,MAAM;AAC7C,QAAI,MAAM,EAAG,UAAS,GAAG,GAAG,QAAQ;AACpC,UAAM,OAAO,WAAW,MAAM,GAAG,QAAQ;AACzC,WAAO,CAAC,IAAI,GAAG,IAAI;AAAA,EACrB,GAAG,CAAC,GAAG,IAAI,CAAC;AACZ,cAAY,CAAC;AACb,eAAa,CAAC;AACd,SAAO;AACT;AACA,IAAM,WAAW,CAAC,GAAG,SAAS,YAAY;AACxC,MAAI,OAAO,YAAY,aAAa;AAClC,cAAU,CAAC,EAAE,OAAO,EAAE;AACtB,cAAU;AAAA,EACZ;AACA,QAAM,MAAM,UAAU,EAAE;AACxB,IAAE,OAAO,MAAM,EAAE,SAAS;AAC1B,IAAE,SAAS;AACX,IAAE,IAAI,UAAU,EAAE;AAClB,GAAC,EAAE,YAAY,CAAC,GAAG,QAAQ,OAAK,SAAS,GAAG,KAAK,EAAE,CAAC,CAAC;AACrD,SAAO;AACT;AACA,IAAM,cAAc,OAAK;AACvB,GAAC,EAAE,YAAY,CAAC,GAAG,OAAO,CAAC,KAAK,UAAU;AACxC,UAAM,CAAC,cAAc,aAAa,IAAI;AACtC,UAAM,WAAW,eAAe,MAAM;AACtC,UAAM,YAAY,gBAAgB,WAAW,MAAM;AACnD,UAAM,QAAQ;AACd,WAAO,CAAC,UAAU,SAAS;AAAA,EAC7B,GAAG,CAAC,GAAG,CAAC,CAAC;AACX;AACA,IAAM,WAAW,CAAC,GAAG,GAAG,SAAS;AAC/B,QAAM,OAAO,EAAE,SAAS,IAAI,CAAC;AAC7B,QAAM,aAAa,EAAE,SAAS,CAAC;AAC/B,MAAI,WAAW;AACf,MAAI,WAAW,KAAK;AACpB,MAAI,WAAW;AACf,MAAI,WAAW,WAAW;AAC1B,MAAI,UAAU;AACd,SAAO,YAAY,UAAU;AAC3B,QAAI,SAAS,SAAS,KAAK,KAAM,QAAO,KAAK;AAC7C,UAAM,OAAO,WAAW,SAAS,UAAU,WAAW,SAAS,UAAU,SAAS,QAAQ,IAAI,SAAS,QAAQ,IAAI,SAAS,QAAQ,QAAQ;AAC5I,QAAI,OAAO,KAAK,OAAO,KAAK,SAAS;AACnC,kBAAY;AACZ,kBAAY,YAAY,IAAI;AAC5B,sBAAgB,GAAG,GAAG,KAAK,OAAO,IAAI;AAAA,IACxC;AACA,cAAU;AACV,UAAM,cAAc,SAAS;AAC7B,UAAM,aAAa,SAAS;AAC5B,QAAI,eAAe,YAAY;AAC7B,iBAAW,aAAa,QAAQ;AAChC,UAAI,SAAU,aAAY,SAAS;AAAA,IACrC;AACA,QAAI,eAAe,YAAY;AAC7B,iBAAW,aAAa,QAAQ;AAChC,UAAI,SAAU,aAAY,SAAS;AAAA,IACrC;AAAA,EACF;AACA,MAAI,CAAC,YAAY,SAAU,SAAQ,GAAG,GAAG,UAAU,QAAQ;AAAA,WAAW,YAAY,CAAC,SAAU,SAAQ,GAAG,GAAG,UAAU,QAAQ;AAC/H;AACA,IAAM,cAAc,CAAC,SAAS,aAAa;AACzC,UAAQ,QAAQ;AAChB,UAAQ,YAAY;AACpB,UAAQ,YAAY;AACtB;AACA,IAAM,kBAAkB,CAAC,GAAG,aAAa,UAAU,SAAS;AAC1D,QAAM,aAAa,EAAE,SAAS,WAAW;AACzC,QAAM,IAAI,cAAc;AACxB,MAAI,IAAI,GAAG;AACT,UAAM,QAAQ,OAAO;AACrB,MAAE,SAAS,WAAW,CAAC,EAAE,SAAS;AAClC,eAAW,SAAS;AACpB,eAAW,UAAU,OAAO;AAAA,EAC9B;AACF;AACA,IAAM,eAAe,OAAK;AACxB,SAAO,EAAE,cAAc,EAAE,aAAa,EAAE;AAC1C;AACA,IAAM,eAAe,OAAK;AACxB,SAAO,EAAE,cAAc,EAAE,YAAY,EAAE;AACzC;AACA,IAAM,UAAU,CAAC,GAAG,GAAG,UAAU,aAAa;AAC5C,QAAM,aAAa,EAAE;AACrB,QAAM,OAAO,WAAW;AACxB,QAAM,aAAa,EAAE,SAAS,CAAC;AAC/B,OAAK,OAAO;AACZ,QAAM,OAAO,WAAW,SAAS,OAAO,WAAW;AACnD,OAAK,QAAQ;AACb,OAAK,UAAU;AACf,aAAW,OAAO,WAAW;AAC7B,aAAW,WAAW,WAAW;AACnC;AACA,IAAM,UAAU,CAAC,GAAG,GAAG,UAAU,aAAa;AAC5C,QAAM,aAAa,EAAE,SAAS,CAAC;AAC/B,QAAM,OAAO,WAAW;AACxB,QAAM,OAAO,EAAE,SAAS,IAAI,CAAC;AAC7B,OAAK,OAAO;AACZ,QAAM,OAAO,WAAW,SAAS,OAAO,WAAW;AACnD,OAAK,QAAQ;AACb,OAAK,UAAU;AACf,aAAW,OAAO,KAAK;AACvB,aAAW,WAAW,KAAK;AAC7B;AACA,IAAM,eAAe,OAAK;AACxB,MAAI,EAAE,aAAa;AACjB,UAAM,KAAK,EAAE;AACb,UAAM,KAAK,EAAE;AACb,UAAM,UAAU,GAAG,SAAS,GAAG,OAAO,GAAG,QAAQ,IAAI,GAAG,OAAO,GAAG,SAAS,GAAG,QAAQ,KAAK;AAC3F,WAAO,OAAO,GAAG;AAAA,MACf;AAAA,MACA,MAAM,GAAG;AAAA,MACT,UAAU,GAAG;AAAA,MACb,MAAM,GAAG;AAAA,MACT,UAAU,GAAG;AAAA,IACf,CAAC;AAAA,EACH;AACF;AACA,IAAM,aAAa,CAAC,MAAM,OAAO,aAAa;AAC5C,SAAO,aAAa,QAAQ,QAAQ,SAAS,KAAM,YAAW,SAAS;AACvE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACR;AACF;AACA,IAAM,wBAAwB,CAAC,GAAG,GAAG,MAAM;AACzC,QAAM,KAAK,EAAE;AACb,QAAM,KAAK,EAAE;AACb,QAAM,KAAK,EAAE;AACb,QAAM,KAAK,EAAE;AACb,QAAM,MAAM,KAAK,OAAO,SAAS,EAAE,MAAM;AACzC,QAAM,MAAM,KAAK,OAAO,SAAS,EAAE,MAAM;AACzC,QAAM,OAAO,KAAK,KAAK,IAAI,KAAK;AAChC,QAAM,OAAO,KAAK,KAAK,IAAI,KAAK;AAChC,QAAM,OAAO;AACb,MAAI,IAAI,KAAK,IAAI,KAAK,EAAE,IAAI,IAAI,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,IAAI;AAC/D,MAAI,KAAK,IAAI,KAAK,EAAE,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,EAAE,IAAI,IAAI;AACxD,QAAM,IAAI,KAAK,IAAI,KAAK,EAAE,IAAI,IAAI;AAClC,QAAM,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,EAAE,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,EAAE,IAAI,KAAK,KAAK,IAAI,OAAO,IAAI,IAAI,IAAI,EAAE,IAAI,KAAK,IAAI,OAAO,IAAI,IAAI,IAAI,EAAE,IAAI,KAAK,IAAI,OAAO,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,IAAI,OAAO,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,IAAI,OAAO,IAAI,IAAI,IAAI,EAAE,IAAI,KAAK,IAAI,OAAO,IAAI,IAAI,IAAI,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,EAAE,EAAE;AACzX,SAAO,UAAU,KAAK,GAAG;AAC3B;AACA,IAAM,sBAAsB,CAAC,GAAG,GAAG,GAAG,UAAU;AAAA,EAC9C,IAAI;AACN,MAAM;AACJ,QAAM,KAAK,EAAE;AACb,MAAI,KAAK,EAAE;AACX,QAAM,KAAK,EAAE;AACb,QAAM,KAAK,EAAE;AACb,QAAM,MAAM,KAAK,OAAO,SAAS,EAAE,MAAM;AACzC,QAAM,MAAM,KAAK,OAAO,SAAS,EAAE,MAAM;AACzC,QAAM,OAAO,KAAK,KAAK,IAAI,KAAK;AAChC,QAAM,OAAO,KAAK,KAAK,IAAI,KAAK;AAChC,QAAM,QAAQ;AACd,QAAM,OAAO;AACb,MAAI,IAAI,KAAK,IAAI,KAAK,EAAE,IAAI,IAAI,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,IAAI;AAC/D,MAAI,KAAK,IAAI,KAAK,EAAE,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,EAAE,IAAI,IAAI;AACxD,QAAM,IAAI,KAAK,IAAI,KAAK,EAAE,IAAI,IAAI;AAClC,QAAM,IAAI,KAAK,IAAI,KAAK,EAAE,IAAI,IAAI;AAClC,QAAM,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,MAAM,EAAE,IAAI,KAAK,IAAI,OAAO,IAAI,IAAI,IAAI,EAAE,IAAI,KAAK,IAAI,OAAO,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,OAAO,IAAI,IAAI,IAAI,KAAK,KAAK,IAAI,OAAO,IAAI,IAAI,IAAI,KAAK,IAAI,OAAO,IAAI,IAAI,IAAI,MAAM,EAAE,IAAI,KAAK,IAAI,OAAO,IAAI,IAAI,IAAI,EAAE,IAAI,KAAK,IAAI,OAAO,IAAI,IAAI,IAAI,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,EAAE,EAAE;AACtY,SAAO,UAAU,KAAK,GAAG;AAC3B;AACA,IAAM,kBAAkB;AAAA,EACtB,KAAK;AAAA,IACH,YAAY,CAAC;AAAA,MACX,OAAO;AAAA,IACT,MAAM,SAAS;AAAA,IACf,YAAY,MAAM;AAAA,IAClB,OAAO,CAAC;AAAA,MACN;AAAA,MACA;AAAA,IACF,MAAM,KAAK,IAAI,YAAY;AAAA,IAC3B,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,KAAK;AAAA,IACX,UAAU,CAAC;AAAA,MACT;AAAA,MACA;AAAA,IACF,MAAM,KAAK,IAAI,YAAY;AAAA,IAC3B,UAAU,CAAC;AAAA,MACT;AAAA,IACF,MAAM,KAAK;AAAA,IACX,aAAa,CAAC;AAAA,MACZ;AAAA,MACA;AAAA,IACF,MAAM,OAAO,IAAI,YAAY;AAAA,IAC7B,aAAa,CAAC;AAAA,MACZ;AAAA,MACA;AAAA,IACF,MAAM,OAAO,IAAI;AAAA,IACjB,cAAc,CAAC;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,MAAM;AACJ,aAAO,CAAC,YAAY,gBAAgB,aAAa,eAAe;AAAA,IAClE;AAAA,IACA,eAAe;AAAA,IACf,MAAM,WAAS;AAAA,MACb,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,IACV;AAAA,IACA,mBAAmB,CAAC;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,IACF,MAAM;AACJ,UAAI,CAAC,SAAU,QAAO;AAAA,QACpB,GAAG;AAAA,QACH,GAAG;AAAA,QACH,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AACA,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,SAAS;AACb,YAAM,SAAS,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK;AAC9C,YAAM,UAAU,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,MAAM;AAC/C,YAAM,KAAK,KAAK,IAAI,IAAI,IAAI,iBAAiB;AAC7C,YAAM,MAAM,SAAS,QAAQ,mBAAmB;AAChD,aAAO;AAAA,QACL,GAAG,CAAC;AAAA,QACJ,GAAG,CAAC;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,YAAY,CAAC;AAAA,MACX,OAAO;AAAA,IACT,MAAM,SAAS;AAAA,IACf,YAAY,CAAC;AAAA,MACX,QAAQ;AAAA,MACR;AAAA,IACF,MAAM,UAAU,aAAa;AAAA,IAC7B,OAAO,CAAC;AAAA,MACN;AAAA,MACA;AAAA,IACF,MAAM,KAAK,IAAI,YAAY;AAAA,IAC3B,OAAO,CAAC;AAAA,MACN;AAAA,MACA;AAAA,IACF,MAAM,KAAK,IAAI;AAAA,IACf,UAAU,CAAC;AAAA,MACT;AAAA,MACA;AAAA,IACF,MAAM,KAAK,IAAI,YAAY;AAAA,IAC3B,UAAU,CAAC;AAAA,MACT;AAAA,MACA;AAAA,IACF,MAAM,KAAK,IAAI;AAAA,IACf,aAAa,CAAC;AAAA,MACZ;AAAA,MACA;AAAA,IACF,MAAM,OAAO,IAAI,YAAY;AAAA,IAC7B,aAAa,CAAC;AAAA,MACZ;AAAA,IACF,MAAM,OAAO;AAAA,IACb,cAAc,CAAC;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,MAAM;AACJ,aAAO,CAAC,YAAY,gBAAgB,aAAa,eAAe;AAAA,IAClE;AAAA,IACA,eAAe;AAAA,IACf,MAAM,UAAS,iCACV,OADU;AAAA,MAEb,GAAG,CAAC,KAAK;AAAA,IACX;AAAA,IACA,mBAAmB,CAAC;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,IACF,MAAM;AACJ,UAAI,CAAC,SAAU,QAAO;AAAA,QACpB,GAAG;AAAA,QACH,GAAG;AAAA,QACH,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AACA,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,SAAS;AACb,YAAM,SAAS,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK;AAC9C,YAAM,UAAU,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,MAAM;AAC/C,YAAM,KAAK,KAAK,IAAI,IAAI,KAAK,SAAS,QAAQ,kBAAkB;AAChE,YAAM,KAAK,UAAU,SAAS,QAAQ,kBAAkB;AACxD,aAAO;AAAA,QACL,GAAG,CAAC;AAAA,QACJ,GAAG,CAAC;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM;AAAA,IACJ,YAAY,MAAM;AAAA,IAClB,YAAY,CAAC;AAAA,MACX,QAAQ;AAAA,IACV,MAAM,UAAU;AAAA,IAChB,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,KAAK;AAAA,IACX,OAAO,CAAC;AAAA,MACN;AAAA,MACA;AAAA,IACF,MAAM,KAAK,IAAI,aAAa;AAAA,IAC5B,UAAU,CAAC;AAAA,MACT;AAAA,IACF,MAAM,KAAK;AAAA,IACX,UAAU,CAAC;AAAA,MACT;AAAA,MACA;AAAA,IACF,MAAM,KAAK,IAAI,aAAa;AAAA,IAC5B,aAAa,CAAC;AAAA,MACZ;AAAA,MACA;AAAA,IACF,MAAM,OAAO,IAAI;AAAA,IACjB,aAAa,CAAC;AAAA,MACZ;AAAA,MACA;AAAA,IACF,MAAM,OAAO,IAAI,aAAa;AAAA,IAC9B,cAAc,CAAC;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,MAAM;AACJ,aAAO,CAAC,aAAa,gBAAgB,YAAY,eAAe;AAAA,IAClE;AAAA,IACA,eAAe;AAAA,IACf,MAAM,UAAS,iCACV,OADU;AAAA,MAEb,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,IACV;AAAA,IACA,mBAAmB,CAAC;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,IACF,MAAM;AACJ,UAAI,CAAC,SAAU,QAAO;AAAA,QACpB,GAAG;AAAA,QACH,GAAG;AAAA,QACH,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AACA,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,SAAS;AACb,YAAM,SAAS,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,MAAM;AAC9C,YAAM,UAAU,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK;AAC/C,YAAM,KAAK,KAAK,IAAI,GAAG,IAAI,kBAAkB;AAC7C,YAAM,KAAK,KAAK,IAAI,IAAI,IAAI;AAC5B,aAAO;AAAA,QACL,GAAG,CAAC;AAAA,QACJ,GAAG,CAAC;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,YAAY,CAAC;AAAA,MACX,OAAO;AAAA,MACP;AAAA,IACF,MAAM,SAAS,YAAY;AAAA,IAC3B,YAAY,CAAC;AAAA,MACX,QAAQ;AAAA,IACV,MAAM,UAAU;AAAA,IAChB,OAAO,CAAC;AAAA,MACN;AAAA,MACA;AAAA,IACF,MAAM,KAAK,IAAI;AAAA,IACf,OAAO,CAAC;AAAA,MACN;AAAA,MACA;AAAA,IACF,MAAM,KAAK,IAAI,aAAa;AAAA,IAC5B,UAAU,CAAC;AAAA,MACT;AAAA,MACA;AAAA,IACF,MAAM,KAAK,IAAI;AAAA,IACf,UAAU,CAAC;AAAA,MACT;AAAA,MACA;AAAA,IACF,MAAM,KAAK,IAAI,aAAa;AAAA,IAC5B,aAAa,CAAC;AAAA,MACZ;AAAA,IACF,MAAM,OAAO;AAAA,IACb,aAAa,CAAC;AAAA,MACZ;AAAA,MACA;AAAA,IACF,MAAM,OAAO,IAAI,aAAa;AAAA,IAC9B,cAAc,CAAC;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,MAAM;AACJ,aAAO,CAAC,aAAa,gBAAgB,YAAY,eAAe;AAAA,IAClE;AAAA,IACA,eAAe;AAAA,IACf,MAAM,UAAS,iCACV,OADU;AAAA,MAEb,GAAG,CAAC,KAAK;AAAA,MACT,GAAG,KAAK;AAAA,IACV;AAAA,IACA,mBAAmB,CAAC;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,IACF,MAAM;AACJ,UAAI,CAAC,SAAU,QAAO;AAAA,QACpB,GAAG;AAAA,QACH,GAAG;AAAA,QACH,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AACA,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,SAAS;AACb,YAAM,SAAS,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,MAAM;AAC9C,YAAM,UAAU,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK;AAC/C,YAAM,KAAK,SAAS,SAAS,QAAQ,kBAAkB;AACvD,YAAM,KAAK,KAAK,IAAI,IAAI,IAAI;AAC5B,aAAO;AAAA,QACL,GAAG,CAAC;AAAA,QACJ,GAAG,CAAC;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,UAAU,CAAC,MAAM,WAAW,YAAY,mBAAmB;AAC/D,MAAI,CAAC,QAAQ,CAAC,KAAK,OAAQ,QAAO;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,gBAAgB,cAAc;AAClC,QAAM,UAAU,KAAK,IAAI;AACzB,QAAM,YAAY,KAAK,KAAK,MAAM;AAClC,QAAM,QAAQ;AAAA,IACZ,GAAG,MAAM;AAAA,MACP,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF,CAAC;AAAA,IACD,GAAG,MAAM;AAAA,MACP,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,SAAS;AAAA,IACb,GAAG,YAAY;AAAA,MACb,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,IACF,CAAC;AAAA,IACD,GAAG,YAAY;AAAA,MACb,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,MAAM;AAAA,IACV,GAAG,SAAS;AAAA,MACV,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF,CAAC;AAAA,IACD,GAAG,SAAS;AAAA,MACV,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO,cAAc,OAAO,QAAQ,KAAK;AAAA,IACvC,IAAI;AAAA,EACN,CAAC;AACH;AACA,IAAM,gBAAgB,CAAC,SAAS,SAAS,CAAC,MAAM;AAC9C,aAAW,OAAO,QAAQ;AACxB,eAAW,OAAO,SAAS,QAAQ,aAAa,KAAK,OAAO,GAAG,CAAC;AAAA,EAClE;AACF;AACA,IAAM,2BAA2B;AACjC,IAAM,kBAAkB,CAAC,OAAO,UAAU,eAAe,YAAY;AACnE,MAAI;AACJ,QAAM,cAAc,YAAY,OAAO,SAAS,SAAS,KAAK;AAC9D,QAAM,mBAAmB,eAAe,OAAO,SAAS,YAAY,gBAAgB,QAAQ;AAC5F,MAAI,eAAe,eAAe,OAAO,SAAS,YAAY,gBAAgB,QAAQ;AACtF,MAAI,mBAAmB,eAAe,OAAO,SAAS,YAAY,gBAAgB,QAAQ;AAC1F,MAAI,eAAe;AACjB,mBAAe,eAAe,OAAO,SAAS,YAAY,qBAAqB,QAAQ;AACvF,uBAAmB,eAAe,OAAO,SAAS,YAAY,qBAAqB,QAAQ;AAAA,EAC7F;AACA,QAAM,qBAAqB,SAAS,cAAc,eAAe,SAAS,KAAK,EAAE,sBAAsB;AACvG,MAAI,oBAAoB;AACtB,uBAAmB,MAAM,cAAc,GAAG,eAAe;AACzD,uBAAmB,MAAM,cAAc;AACvC,uBAAmB,MAAM,kBAAkB;AAAA,EAC7C;AACA,MAAI,SAAS,QAAQ;AACnB,UAAM,OAAO,SAAS,eAAe,GAAG,SAAS,KAAK,EAAE,KAAK,KAAK,SAAS,WAAW,OAAO,SAAS,GAAG,KAAK,EAAE,EAAE;AAClH,QAAI,eAAe;AACjB,oBAAc,MAAM;AAAA,QAClB,gBAAgB,QAAQ,YAAY;AAAA,QACpC,QAAQ,QAAQ;AAAA,MAClB,CAAC;AAAA,IACH,OAAO;AACL,oBAAc,MAAM;AAAA,QAClB,gBAAgB,QAAQ;AAAA,QACxB,QAAQ,QAAQ;AAAA,MAClB,CAAC;AAAA,IACH;AACA,aAAS,UAAU,gBAAgB,OAAO,SAAS,QAAQ,eAAe,OAAO;AAAA,EACnF;AACF;AACA,IAAM,mBAAmB,CAAC,IAAI,IAAI,UAAU,aAAa,SAAS,eAAe;AAC/E,QAAM,SAAS,CAAC,uBAAuB,SAAS,KAAK,EAAE,OAAO,QAAQ,KAAK,EAAE,OAAO,qBAAqB,WAAW,KAAK,uBAAuB,cAAc,QAAQ,OAAO,qBAAqB,OAAO,GAAG;AAC5M,MAAI,YAAY;AACd,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AACA,SAAO;AACT;AACA,IAAM,iBAAiB,CAAC,cAAc,CAAC,MAAM;AAC3C,QAAM,SAAS,CAAC;AAChB,aAAW,YAAY,aAAa;AAClC,UAAM,cAAc,GAAG,iBAAiB,QAAQ,CAAC,KAAK,YAAY,QAAQ,CAAC;AAC3E,WAAO,KAAK,WAAW;AAAA,EACzB;AACA,SAAO,OAAO,KAAK,GAAG;AACxB;AACA,IAAM,aAAa,CAAC,YAAY,iCAAiC;AAC/D,QAAM,iBAAiB,SAAS,eAAe,SAAS,KAAK,SAAS,cAAc,KAAK;AACzF,iBAAe,KAAK;AACpB,SAAO;AACT;AACA,IAAM,gBAAgB,CAAC,KAAK,IAAI,QAAQ,UAAU,OAAO;AACvD,QAAM,iBAAiB,SAAS,eAAe,EAAE;AACjD,MAAI,QAAQ;AACV,sBAAkB,OAAO,SAAS,eAAe,aAAa,SAAS,MAAM;AAAA,EAC/E,OAAO;AACL,sBAAkB,OAAO,SAAS,eAAe,gBAAgB,OAAO;AAAA,EAC1E;AACA,OAAK,kBAAkB,OAAO,SAAS,eAAe,UAAU,WAAW,KAAK,GAAG,OAAO,QAAQ,WAAW,KAAK,GAAG,GAAG;AACtH,uBAAmB,eAAe,YAAY;AAAA,EAChD;AACF;AACA,IAAM,mBAAmB,SAAO;AAC9B,SAAO,IAAI,QAAQ,0BAA0B,CAAC,GAAG,SAAS,MAAM,MAAM,MAAM,EAAE,YAAY,CAAC;AAC7F;AACA,IAAM,YAAY,CAAC;AACnB,IAAM,QAAQ,CAAC;AACf,SAAS,gBAAgB,OAAO,GAAG;AACjC,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,eAAW,SAAS,OAAO;AACzB,sBAAgB,OAAO,CAAC;AAAA,IAC1B;AACA;AAAA,EACF;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,eAAW,SAAS,OAAO;AACzB,sBAAgB,OAAO,MAAM,KAAK,CAAC;AAAA,IACrC;AACA;AAAA,EACF;AACA,iBAAe,OAAO,oBAAoB,CAAC,CAAC;AAC5C,YAAU,KAAK,IAAI,OAAO,OAAO,UAAU,KAAK,KAAK,CAAC,GAAG,CAAC;AAC5D;AACA,SAAS,cAAc,OAAO;AAC5B,SAAO,UAAU,KAAK,KAAK,CAAC;AAC9B;AACA,SAAS,iBAAiB;AACxB,SAAO,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;AAC3B;AACA,SAAS,eAAe,QAAQ;AAC9B,QAAM,KAAK,GAAG,MAAM;AACtB;AACA,SAAS,IAAI,QAAQ,OAAO;AAC1B,MAAI;AACJ,QAAM,KAAK,OAAO;AAClB,QAAM,SAAS,CAAC;AAChB,OAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACvB,WAAO,KAAK,MAAM,OAAO,CAAC,CAAC,CAAC;AAAA,EAC9B;AACA,SAAO;AACT;AACA,SAAS,OAAO,QAAQ,OAAO;AAC7B,MAAI;AACJ,QAAM,KAAK,OAAO;AAClB,QAAM,SAAS,CAAC;AAChB,OAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACvB,QAAI,MAAM,OAAO,CAAC,CAAC,GAAG;AACpB,aAAO,KAAK,OAAO,CAAC,CAAC;AAAA,IACvB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,QAAQ,GAAG;AAClB,SAAO,IAAI,MAAM,KAAK,KAAK;AAC7B;AACA,SAAS,YAAY,GAAG;AACtB,SAAO,EAAE,QAAQ,YAAY,SAAU,GAAG,GAAG;AAC3C,WAAO,MAAM,EAAE,YAAY;AAAA,EAC7B,CAAC;AACH;AACA,SAAS,WAAW,GAAG;AACrB,SAAO,EAAE,OAAO,CAAC,EAAE,YAAY,IAAI,EAAE,MAAM,CAAC;AAC9C;AACA,SAAS,iBAAiB,SAAS,QAAQ,SAAS,KAAK;AACvD,MAAI,UAAU,QAAQ,WAAW,MAAM;AACrC,UAAM,OAAO,QAAQ,KAAK;AAC1B,QAAI,UAAU,MAAM;AAClB,eAAS,IAAI,QAAQ,IAAI,SAAS;AAAA,IACpC,WAAW,WAAW,MAAM;AAC1B,gBAAU,IAAI,SAAS,IAAI,QAAQ;AAAA,IACrC;AAAA,EACF;AACA,SAAO;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AACF;AACA,SAAS,UAAU,GAAG,SAAS;AAC7B,QAAM,SAAS,EAAE;AACjB,MAAI,KAAK,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,WAAW,OAAO,EAAE,UAAU;AAC/D,MAAI,KAAK,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,WAAW,OAAO,EAAE,UAAU;AAC/D,MAAI,UAAU,MAAM;AAClB,KAAC,IAAI,EAAE,IAAI,MAAM,QAAQ,MAAM,IAAI,SAAS,OAAO,WAAW,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,MAAM;AAAA,EACjH;AACA,QAAM,QAAQ,OAAO,OAAO;AAC5B,QAAM,QAAQ,OAAO,OAAO;AAC5B,MAAI,SAAS,OAAO;AAClB,UAAM;AAAA,MACJ,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI,QAAQ,KAAK;AACjB,QAAI,OAAO;AACT,WAAK,GAAG,SAAS,MAAM,IAAI,KAAK,GAAG,SAAS,OAAO,IAAI,KAAK,SAAS,KAAK,SAAS;AAAA,IACrF;AACA,QAAI,OAAO;AACT,WAAK,GAAG,SAAS,KAAK,IAAI,KAAK,GAAG,SAAS,QAAQ,IAAI,KAAK,UAAU,KAAK,UAAU;AAAA,IACvF;AAAA,EACF;AACA,SAAO,CAAC,IAAI,EAAE;AAChB;AACA,IAAM,sBAAqC,oBAAI,IAAI,CAAC,QAAQ,YAAY,OAAO,CAAC;AAChF,IAAM,gBAAgB,aAAW,oBAAoB,IAAI,QAAQ,QAAQ;AACzE,IAAM,iBAAiB,CAAC,SAAS,OAAO,YAAY,CAAC,MAAM;AACzD,QAAM,SAAS,mBACV;AAEL,aAAW,OAAO,QAAQ;AACxB,QAAI,OAAO,GAAG,EAAE,QAAQ,MAAM,UAAU,GAAG,GAAG;AAC5C,aAAO,OAAO,GAAG;AAAA,IACnB;AAAA,EACF;AACA,MAAI,OAAO,KAAK,MAAM,EAAE,QAAQ;AAC9B,YAAQ,KAAK,aAAa,cAAc,KAAK,UAAU,MAAM,CAAC;AAAA,EAChE,OAAO;AACL,YAAQ,KAAK,gBAAgB,YAAY;AACzC,YAAQ,KAAK,gBAAgB,YAAY;AAAA,EAC3C;AACF;AACA,IAAM,MAAM;AACZ,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,UAAU;AAAA,EACd,QAAQ,OAAO,WAAW,cAAc,OAAO;AAAA,EAC/C,UAAU,OAAO,aAAa,cAAc,OAAO;AACrD;AACA,SAAS,YAAY;AACnB,SAAO,QAAQ;AACjB;AACA,IAAM,OAAN,MAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASX;AACA,IAAM,WAAW,CAAC;AAClB,IAAM,OAAO;AACb,SAAS,OAAO,OAAO,KAAK,KAAK;AAC/B,SAAO,QAAQ,SAAS,gBAAgB,IAAI,KAAK;AACnD;AACA,SAAS,aAAa,SAAS,SAAS,OAAO;AAC7C,MAAI,mBAAmB,KAAM,QAAO;AACpC,MAAI,OAAO,YAAY,UAAU;AAC/B,WAAO,QAAQ,OAAO;AAAA,EACxB;AACA,MAAI,WAAW,MAAM;AACnB,WAAO,IAAI,SAAS,IAAI,EAAE;AAAA,EAC5B;AACA,MAAI,OAAO,YAAY,YAAY,QAAQ,OAAO,CAAC,MAAM,KAAK;AAC5D,WAAO,QAAQ,QAAQ,SAAS,cAAc,OAAO,CAAC;AAAA,EACxD;AACA,QAAM,UAAU,SAAS,QAAQ,SAAS,cAAc,KAAK,IAAI,OAAO,KAAK;AAC7E,UAAQ,YAAY;AACpB,YAAU,QAAQ,QAAQ,UAAU;AACpC,UAAQ,YAAY,QAAQ,UAAU;AACtC,SAAO;AACT;AACA,SAAS,UAAU,OAAO,MAAM;AAC9B,SAAO,SAAS,gBAAgB,QAAQ,OAAO,QAAQ,KAAK,iBAAiB,gBAAgB,KAAK,cAAc,YAAY,QAAQ,OAAO,OAAO,KAAK;AACzJ;AACA,SAAS,MAAM,MAAM;AACnB,MAAI,CAAC,KAAM,QAAO;AAClB,MAAI,KAAK,oBAAoB,KAAM,QAAO,KAAK;AAC/C,MAAI,KAAK,aAAa,sBAAsB;AAC1C,WAAO,IAAI,SAAS,SAAS,IAAI;AAAA,EACnC;AACA,MAAI,YAAY,WAAW,KAAK,YAAY,KAAK;AACjD,MAAI,cAAc,oBAAoB,cAAc,kBAAkB;AACpE,gBAAY;AAAA,EACd,WAAW,CAAC,SAAS,SAAS,GAAG;AAC/B,gBAAY;AAAA,EACd;AACA,SAAO,IAAI,SAAS,SAAS,EAAE,IAAI;AACrC;AACA,IAAI,UAAU;AACd,SAAS,SAAS,SAAS,QAAQ,QAAQ,MAAM,SAAS,OAAO;AAC/D,WAAS,KAAK,IAAI;AAClB,MAAI,OAAQ,UAAS,IAAI,IAAI;AAC7B,iBAAe,OAAO,oBAAoB,QAAQ,SAAS,CAAC;AAC5D,SAAO;AACT;AACA,SAAS,SAAS,OAAO;AACvB,SAAO,SAAS,KAAK;AACvB;AACA,IAAI,MAAM;AACV,SAAS,IAAI,OAAO;AAClB,SAAO,UAAU,WAAW,KAAK,IAAI;AACvC;AACA,SAAS,YAAY,MAAM;AACzB,WAAS,IAAI,KAAK,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAClD,gBAAY,KAAK,SAAS,CAAC,CAAC;AAAA,EAC9B;AACA,MAAI,KAAK,IAAI;AACX,SAAK,KAAK,IAAI,KAAK,QAAQ;AAC3B,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,OAAO,SAAS,UAAU;AACjC,MAAI,KAAK;AACT,YAAU,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AACrD,OAAK,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AACxC,SAAK,OAAO,UAAU;AACpB,cAAQ,CAAC,EAAE,UAAU,GAAG,IAAI,SAAS,GAAG;AAAA,IAC1C;AAAA,EACF;AACF;AACA,SAAS,kBAAkB,IAAI;AAC7B,SAAO,YAAa,MAAM;AACxB,UAAM,IAAI,KAAK,KAAK,SAAS,CAAC;AAC9B,QAAI,KAAK,EAAE,gBAAgB,UAAU,EAAE,aAAa,QAAQ;AAC1D,aAAO,GAAG,MAAM,MAAM,KAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC;AAAA,IACjD,OAAO;AACL,aAAO,GAAG,MAAM,MAAM,IAAI;AAAA,IAC5B;AAAA,EACF;AACF;AACA,SAAS,WAAW;AAClB,SAAO,KAAK,OAAO,EAAE,SAAS;AAChC;AACA,SAAS,WAAW;AAClB,SAAO,KAAK,OAAO,EAAE,MAAM,IAAI;AACjC;AACA,SAAS,OAAO;AACd,SAAO,KAAK,SAAS,EAAE,KAAK,SAAS,IAAI,CAAC;AAC5C;AACA,SAAS,OAAO;AACd,SAAO,KAAK,SAAS,EAAE,KAAK,SAAS,IAAI,CAAC;AAC5C;AACA,SAAS,UAAU;AACjB,QAAM,IAAI,KAAK,SAAS;AACxB,QAAM,IAAI,KAAK,OAAO;AACtB,IAAE,IAAI,KAAK,OAAO,GAAG,IAAI,CAAC;AAC1B,SAAO;AACT;AACA,SAAS,WAAW;AAClB,QAAM,IAAI,KAAK,SAAS;AACxB,QAAM,IAAI,KAAK,OAAO;AACtB,IAAE,IAAI,KAAK,OAAO,GAAG,IAAI,IAAI,IAAI,CAAC;AAClC,SAAO;AACT;AACA,SAAS,QAAQ;AACf,QAAM,IAAI,KAAK,OAAO;AACtB,IAAE,IAAI,KAAK,OAAO,CAAC;AACnB,SAAO;AACT;AACA,SAAS,OAAO;AACd,QAAM,IAAI,KAAK,OAAO;AACtB,IAAE,IAAI,KAAK,OAAO,GAAG,CAAC;AACtB,SAAO;AACT;AACA,SAAS,OAAO,SAAS;AACvB,YAAU,aAAa,OAAO;AAC9B,UAAQ,OAAO;AACf,QAAM,IAAI,KAAK,SAAS;AACxB,OAAK,OAAO,EAAE,IAAI,SAAS,CAAC;AAC5B,SAAO;AACT;AACA,SAAS,MAAM,SAAS;AACtB,YAAU,aAAa,OAAO;AAC9B,UAAQ,OAAO;AACf,QAAM,IAAI,KAAK,SAAS;AACxB,OAAK,OAAO,EAAE,IAAI,SAAS,IAAI,CAAC;AAChC,SAAO;AACT;AACA,SAAS,aAAa,SAAS;AAC7B,YAAU,aAAa,OAAO;AAC9B,UAAQ,OAAO,IAAI;AACnB,SAAO;AACT;AACA,SAAS,YAAY,SAAS;AAC5B,YAAU,aAAa,OAAO;AAC9B,UAAQ,MAAM,IAAI;AAClB,SAAO;AACT;AACA,gBAAgB,OAAO;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAM,gBAAgB;AACtB,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,YAAY;AAClB,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,WAAW;AACjB,IAAM,UAAU;AAChB,IAAM,YAAY;AAClB,IAAM,eAAe;AACrB,SAAS,UAAU;AACjB,QAAM,QAAQ,KAAK,KAAK,OAAO;AAC/B,SAAO,SAAS,OAAO,CAAC,IAAI,MAAM,KAAK,EAAE,MAAM,SAAS;AAC1D;AACA,SAAS,SAAS,OAAO;AACvB,SAAO,KAAK,QAAQ,EAAE,QAAQ,KAAK,MAAM;AAC3C;AACA,SAAS,SAAS,OAAO;AACvB,MAAI,CAAC,KAAK,SAAS,KAAK,GAAG;AACzB,UAAM,SAAS,KAAK,QAAQ;AAC5B,WAAO,KAAK,KAAK;AACjB,SAAK,KAAK,SAAS,OAAO,KAAK,GAAG,CAAC;AAAA,EACrC;AACA,SAAO;AACT;AACA,SAAS,YAAY,OAAO;AAC1B,MAAI,KAAK,SAAS,KAAK,GAAG;AACxB,SAAK,KAAK,SAAS,KAAK,QAAQ,EAAE,OAAO,SAAU,GAAG;AACpD,aAAO,MAAM;AAAA,IACf,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,EACd;AACA,SAAO;AACT;AACA,SAAS,YAAY,OAAO;AAC1B,SAAO,KAAK,SAAS,KAAK,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,SAAS,KAAK;AAC7E;AACA,gBAAgB,OAAO;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,SAAS,IAAI,OAAO,KAAK;AACvB,QAAM,MAAM,CAAC;AACb,MAAI,UAAU,WAAW,GAAG;AAC1B,SAAK,KAAK,MAAM,QAAQ,MAAM,SAAS,EAAE,OAAO,SAAU,IAAI;AAC5D,aAAO,CAAC,CAAC,GAAG;AAAA,IACd,CAAC,EAAE,QAAQ,SAAU,IAAI;AACvB,YAAM,IAAI,GAAG,MAAM,SAAS;AAC5B,UAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;AAAA,IACjB,CAAC;AACD,WAAO;AAAA,EACT;AACA,MAAI,UAAU,SAAS,GAAG;AACxB,QAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,iBAAW,SAAS,OAAO;AACzB,cAAM,QAAQ;AACd,YAAI,KAAK,IAAI,KAAK,KAAK,MAAM,iBAAiB,KAAK;AAAA,MACrD;AACA,aAAO;AAAA,IACT;AACA,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO,KAAK,KAAK,MAAM,iBAAiB,KAAK;AAAA,IAC/C;AACA,QAAI,OAAO,UAAU,UAAU;AAC7B,iBAAW,SAAS,OAAO;AACzB,aAAK,KAAK,MAAM,YAAY,OAAO,MAAM,KAAK,KAAK,QAAQ,QAAQ,KAAK,MAAM,KAAK,CAAC,IAAI,KAAK,MAAM,KAAK,CAAC;AAAA,MAC3G;AAAA,IACF;AAAA,EACF;AACA,MAAI,UAAU,WAAW,GAAG;AAC1B,SAAK,KAAK,MAAM,YAAY,OAAO,OAAO,QAAQ,QAAQ,KAAK,GAAG,IAAI,KAAK,GAAG;AAAA,EAChF;AACA,SAAO;AACT;AACA,SAAS,OAAO;AACd,SAAO,KAAK,IAAI,WAAW,EAAE;AAC/B;AACA,SAAS,OAAO;AACd,SAAO,KAAK,IAAI,WAAW,MAAM;AACnC;AACA,SAAS,UAAU;AACjB,SAAO,KAAK,IAAI,SAAS,MAAM;AACjC;AACA,gBAAgB,OAAO;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,SAAS,KAAK,GAAG,GAAG,GAAG;AACrB,MAAI,KAAK,MAAM;AACb,WAAO,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,YAAY,QAAM,GAAG,SAAS,QAAQ,OAAO,MAAM,CAAC,GAAG,QAAM,GAAG,SAAS,MAAM,CAAC,CAAC,CAAC;AAAA,EAC1H,WAAW,aAAa,OAAO;AAC7B,UAAM,QAAQ,CAAC;AACf,eAAW,OAAO,GAAG;AACnB,YAAM,GAAG,IAAI,KAAK,KAAK,GAAG;AAAA,IAC5B;AACA,WAAO;AAAA,EACT,WAAW,OAAO,MAAM,UAAU;AAChC,SAAK,KAAK,GAAG;AACX,WAAK,KAAK,GAAG,EAAE,CAAC,CAAC;AAAA,IACnB;AAAA,EACF,WAAW,UAAU,SAAS,GAAG;AAC/B,QAAI;AACF,aAAO,KAAK,MAAM,KAAK,KAAK,UAAU,CAAC,CAAC;AAAA,IAC1C,SAAS,GAAG;AACV,aAAO,KAAK,KAAK,UAAU,CAAC;AAAA,IAC9B;AAAA,EACF,OAAO;AACL,SAAK,KAAK,UAAU,GAAG,MAAM,OAAO,OAAO,MAAM,QAAQ,OAAO,MAAM,YAAY,OAAO,MAAM,WAAW,IAAI,KAAK,UAAU,CAAC,CAAC;AAAA,EACjI;AACA,SAAO;AACT;AACA,gBAAgB,OAAO;AAAA,EACrB;AACF,CAAC;AACD,SAAS,SAAS,GAAG,GAAG;AACtB,MAAI,OAAO,UAAU,CAAC,MAAM,UAAU;AACpC,eAAW,OAAO,GAAG;AACnB,WAAK,SAAS,KAAK,EAAE,GAAG,CAAC;AAAA,IAC3B;AAAA,EACF,WAAW,UAAU,WAAW,GAAG;AACjC,WAAO,KAAK,OAAO,EAAE,CAAC;AAAA,EACxB,OAAO;AACL,SAAK,OAAO,EAAE,CAAC,IAAI;AAAA,EACrB;AACA,SAAO;AACT;AACA,SAAS,SAAS;AAChB,MAAI,UAAU,WAAW,GAAG;AAC1B,SAAK,UAAU,CAAC;AAAA,EAClB,OAAO;AACL,aAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9C,aAAO,KAAK,OAAO,EAAE,UAAU,CAAC,CAAC;AAAA,IACnC;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,SAAS;AAChB,SAAO,KAAK,UAAU,KAAK,WAAW,CAAC;AACzC;AACA,gBAAgB,OAAO;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,SAAS,YAAY,MAAM;AACzB,SAAO,KAAK,WAAW,IAAI,CAAC,KAAK,KAAK,UAAU,GAAG,CAAC,GAAG,KAAK,UAAU,GAAG,CAAC,GAAG,KAAK,UAAU,GAAG,CAAC,GAAG,KAAK,UAAU,GAAG,CAAC,GAAG,KAAK,UAAU,GAAG,CAAC,GAAG,KAAK,UAAU,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI;AAClL;AACA,SAAS,aAAa,WAAW;AAC/B,QAAM,UAAU,KAAK,MAAM,SAAS;AACpC,QAAM,UAAU,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,OAAO,CAAC;AAClD,QAAM,OAAO,QAAQ,SAAS,EAAE;AAChC,SAAO,KAAK,WAAW,IAAI,MAAM,OAAO;AAC1C;AACA,SAAS,GAAG,QAAQ,OAAO;AACzB,WAAS,IAAI,MAAM,QAAQ,OAAM;AAC/B,QAAI,OAAO,MAAM,CAAC,CAAC,KAAK,MAAM;AAC5B,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,cAAc,GAAG,GAAG;AAC3B,QAAM,SAAS,GAAG,GAAG,KAAK,IAAI;AAAA,IAC5B,IAAI,EAAE;AAAA,IACN,IAAI,EAAE;AAAA,IACN,IAAI,EAAE;AAAA,IACN,IAAI;AAAA,IACJ,OAAO;AAAA,EACT,IAAI,GAAG,GAAG,KAAK,IAAI;AAAA,IACjB,IAAI,EAAE;AAAA,IACN,IAAI,EAAE;AAAA,IACN,IAAI,EAAE;AAAA,IACN,IAAI;AAAA,IACJ,OAAO;AAAA,EACT,IAAI,GAAG,GAAG,KAAK,IAAI;AAAA,IACjB,IAAI,EAAE;AAAA,IACN,IAAI,EAAE;AAAA,IACN,IAAI,EAAE;AAAA,IACN,IAAI;AAAA,IACJ,OAAO;AAAA,EACT,IAAI,GAAG,GAAG,KAAK,IAAI;AAAA,IACjB,IAAI,EAAE;AAAA,IACN,IAAI,EAAE;AAAA,IACN,IAAI,EAAE;AAAA,IACN,IAAI;AAAA,IACJ,OAAO;AAAA,EACT,IAAI,GAAG,GAAG,KAAK,IAAI;AAAA,IACjB,IAAI,EAAE;AAAA,IACN,IAAI,EAAE;AAAA,IACN,IAAI,EAAE;AAAA,IACN,IAAI;AAAA,IACJ,OAAO;AAAA,EACT,IAAI,GAAG,GAAG,MAAM,IAAI;AAAA,IAClB,IAAI,EAAE;AAAA,IACN,IAAI,EAAE;AAAA,IACN,IAAI,EAAE;AAAA,IACN,IAAI,EAAE;AAAA,IACN,OAAO;AAAA,EACT,IAAI;AAAA,IACF,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,OAAO;AAAA,EACT;AACA,SAAO,QAAQ,KAAK,OAAO;AAC3B,SAAO;AACT;AACA,SAAS,SAAS,OAAO;AACvB,MAAI,UAAU,SAAS,UAAU,SAAS,UAAU,OAAO;AACzD,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,SAAS,SAAS,GAAG,GAAG,GAAG;AACzB,MAAI,IAAI,EAAG,MAAK;AAChB,MAAI,IAAI,EAAG,MAAK;AAChB,MAAI,IAAI,IAAI,EAAG,QAAO,KAAK,IAAI,KAAK,IAAI;AACxC,MAAI,IAAI,IAAI,EAAG,QAAO;AACtB,MAAI,IAAI,IAAI,EAAG,QAAO,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK;AAClD,SAAO;AACT;AACA,IAAM,QAAN,MAAM,OAAM;AAAA,EACV,eAAe,QAAQ;AACrB,SAAK,KAAK,GAAG,MAAM;AAAA,EACrB;AAAA;AAAA,EAEA,OAAO,QAAQ,OAAO;AACpB,WAAO,UAAU,iBAAiB,UAAS,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AAAA,EACjF;AAAA;AAAA,EAEA,OAAO,MAAM,OAAO;AAClB,WAAO,SAAS,OAAO,MAAM,MAAM,YAAY,OAAO,MAAM,MAAM,YAAY,OAAO,MAAM,MAAM;AAAA,EACnG;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,OAAO,WAAW,GAAG;AACjC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA,IAAI;AAAA,IACN,IAAI;AACJ,QAAI,SAAS,WAAW;AACtB,YAAM,KAAK,KAAK,MAAM,OAAO,IAAI;AACjC,YAAM,KAAK,KAAK,MAAM,OAAO,IAAI;AACjC,YAAM,IAAI,MAAM,OAAO;AACvB,YAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,GAAG,KAAK;AACtC,aAAO;AAAA,IACT,WAAW,SAAS,QAAQ;AAC1B,UAAI,KAAK,OAAO,OAAO,IAAI;AAC3B,YAAM,IAAI,MAAM,KAAK,IAAI,IAAI,KAAK,IAAI,MAAM,IAAI,IAAI,GAAG;AACvD,YAAM,IAAI,MAAM,KAAK,IAAI,IAAI,KAAK,IAAI,MAAM,GAAG,IAAI,GAAG;AACtD,YAAM,IAAI,MAAM,MAAM,IAAI,IAAI,KAAK,IAAI,MAAM,GAAG,IAAI,GAAG;AACvD,YAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,CAAC;AAC/B,aAAO;AAAA,IACT,WAAW,SAAS,UAAU;AAC5B,YAAM,KAAK,KAAK,MAAM,OAAO,IAAI;AACjC,YAAM,KAAK,KAAK,KAAK,OAAO,IAAI;AAChC,YAAM,IAAI,MAAM,OAAO;AACvB,YAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,GAAG,KAAK;AACtC,aAAO;AAAA,IACT,WAAW,SAAS,QAAQ;AAC1B,YAAM,IAAI,KAAK,KAAK,OAAO;AAC3B,YAAM,KAAK,MAAM,MAAM,OAAO,IAAI;AAClC,YAAM,IAAI,MAAM,OAAO;AACvB,YAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,GAAG,KAAK;AACtC,aAAO;AAAA,IACT,WAAW,SAAS,OAAO;AACzB,YAAM,IAAI,MAAM,OAAO;AACvB,YAAM,IAAI,MAAM,OAAO;AACvB,YAAM,IAAI,MAAM,OAAO;AACvB,YAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,CAAC;AAC/B,aAAO;AAAA,IACT,WAAW,SAAS,OAAO;AACzB,YAAM,IAAI,MAAM,OAAO;AACvB,YAAM,IAAI,MAAM,OAAO,IAAI;AAC3B,YAAM,IAAI,MAAM,OAAO,IAAI;AAC3B,YAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,GAAG,KAAK;AACtC,aAAO;AAAA,IACT,WAAW,SAAS,QAAQ;AAC1B,YAAM,OAAO,MAAM,OAAO;AAC1B,YAAM,QAAQ,IAAI,OAAM,MAAM,MAAM,IAAI;AACxC,aAAO;AAAA,IACT,OAAO;AACL,YAAM,IAAI,MAAM,+BAA+B;AAAA,IACjD;AAAA,EACF;AAAA;AAAA,EAEA,OAAO,KAAK,OAAO;AACjB,WAAO,OAAO,UAAU,aAAa,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK;AAAA,EAC5E;AAAA,EACA,OAAO;AACL,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK,IAAI;AACb,UAAM,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,OAAK,IAAI,GAAG;AAC/C,UAAM,IAAI,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AACtC,QAAI,MAAM,GAAG;AACX,aAAO,IAAI,OAAM,GAAG,GAAG,GAAG,GAAG,MAAM;AAAA,IACrC;AACA,UAAM,KAAK,IAAI,IAAI,MAAM,IAAI;AAC7B,UAAM,KAAK,IAAI,IAAI,MAAM,IAAI;AAC7B,UAAM,MAAM,IAAI,IAAI,MAAM,IAAI;AAC9B,UAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,IAAI,GAAG,MAAM;AAC3C,WAAO;AAAA,EACT;AAAA,EACA,MAAM;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK,IAAI;AACb,UAAM,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,OAAK,IAAI,GAAG;AAC/C,UAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,UAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,UAAM,KAAK,MAAM,OAAO;AACxB,UAAM,SAAS,QAAQ;AACvB,UAAM,QAAQ,MAAM;AACpB,UAAM,IAAI,SAAS,IAAI,IAAI,MAAM,SAAS,IAAI,MAAM,OAAO,SAAS,MAAM;AAC1E,UAAM,IAAI,SAAS,IAAI,QAAQ,MAAM,IAAI,KAAK,SAAS,IAAI,IAAI,IAAI,MAAM,IAAI,QAAQ,MAAM,IAAI,KAAK,QAAQ,KAAK,IAAI,QAAQ,MAAM,IAAI,KAAK,QAAQ,KAAK,IAAI;AAC7J,UAAM,QAAQ,IAAI,OAAM,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,KAAK;AACxD,WAAO;AAAA,EACT;AAAA,EACA,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,QAAQ,OAAO;AAC9C,QAAI,CAAC,IAAI,IAAI;AACb,QAAI,KAAK,OAAO;AACd,iBAAW,aAAa,KAAK,OAAO;AAClC,eAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AAAA,MACnC;AAAA,IACF;AACA,QAAI,OAAO,MAAM,UAAU;AACzB,cAAQ,OAAO,MAAM,WAAW,IAAI;AACpC,UAAI,OAAO,MAAM,WAAW,IAAI;AAChC,aAAO,OAAO,MAAM;AAAA,QAClB,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ;AAAA,MACF,CAAC;AAAA,IACH,WAAW,aAAa,OAAO;AAC7B,WAAK,QAAQ,MAAM,OAAO,EAAE,CAAC,MAAM,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM;AAC9D,aAAO,OAAO,MAAM;AAAA,QAClB,IAAI,EAAE,CAAC;AAAA,QACP,IAAI,EAAE,CAAC;AAAA,QACP,IAAI,EAAE,CAAC;AAAA,QACP,IAAI,EAAE,CAAC,KAAK;AAAA,MACd,CAAC;AAAA,IACH,WAAW,aAAa,QAAQ;AAC9B,YAAM,SAAS,cAAc,GAAG,CAAC;AACjC,aAAO,OAAO,MAAM,MAAM;AAAA,IAC5B,WAAW,OAAO,MAAM,UAAU;AAChC,UAAI,MAAM,KAAK,CAAC,GAAG;AACjB,cAAM,eAAe,EAAE,QAAQ,YAAY,EAAE;AAC7C,cAAM,CAAC,KAAK,KAAK,GAAG,IAAI,IAAI,KAAK,YAAY,EAAE,MAAM,GAAG,CAAC,EAAE,IAAI,OAAK,SAAS,CAAC,CAAC;AAC/E,eAAO,OAAO,MAAM;AAAA,UAClB,IAAI;AAAA,UACJ,IAAI;AAAA,UACJ,IAAI;AAAA,UACJ,IAAI;AAAA,UACJ,OAAO;AAAA,QACT,CAAC;AAAA,MACH,WAAW,MAAM,KAAK,CAAC,GAAG;AACxB,cAAM,WAAW,OAAK,SAAS,GAAG,EAAE;AACpC,cAAM,CAAC,EAAE,KAAK,KAAK,GAAG,IAAI,IAAI,KAAK,YAAY,CAAC,CAAC,EAAE,IAAI,QAAQ;AAC/D,eAAO,OAAO,MAAM;AAAA,UAClB,IAAI;AAAA,UACJ,IAAI;AAAA,UACJ,IAAI;AAAA,UACJ,IAAI;AAAA,UACJ,OAAO;AAAA,QACT,CAAC;AAAA,MACH,MAAO,OAAM,MAAM,kDAAkD;AAAA,IACvE;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,aAAa,KAAK,UAAU,QAAQ;AAAA,MACxC,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI,KAAK,UAAU,QAAQ;AAAA,MACzB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI,KAAK,UAAU,QAAQ;AAAA,MACzB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI,KAAK,UAAU,QAAQ;AAAA,MACzB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI,KAAK,UAAU,QAAQ;AAAA,MACzB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI,KAAK,UAAU,SAAS;AAAA,MAC1B,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI,CAAC;AACL,WAAO,OAAO,MAAM,UAAU;AAAA,EAChC;AAAA,EACA,MAAM;AACJ,UAAM;AAAA,MACJ,GAAG;AAAA,MACH,GAAG;AAAA,MACH;AAAA,IACF,IAAI,KAAK,IAAI;AACb,UAAM,IAAI,MAAM,KAAK;AACrB,UAAM,IAAI,OAAO,KAAK;AACtB,UAAM,IAAI,OAAO,KAAK;AACtB,UAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,GAAG,KAAK;AACtC,WAAO;AAAA,EACT;AAAA,EACA,MAAM;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK,IAAI;AACb,UAAM,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,CAAC;AACnC,QAAI,IAAI,MAAM,KAAK,MAAM,GAAG,CAAC,IAAI,KAAK;AACtC,QAAI,IAAI,GAAG;AACT,WAAK;AACL,UAAI,MAAM;AAAA,IACZ;AACA,UAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,GAAG,KAAK;AACtC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM;AACJ,QAAI,KAAK,UAAU,OAAO;AACxB,aAAO;AAAA,IACT,WAAW,SAAS,KAAK,KAAK,GAAG;AAC/B,UAAI;AAAA,QACF,GAAG;AAAA,QACH,GAAG;AAAA,QACH;AAAA,MACF,IAAI;AACJ,UAAI,KAAK,UAAU,SAAS,KAAK,UAAU,OAAO;AAChD,YAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA,GAAG;AAAA,QACL,IAAI;AACJ,YAAI,KAAK,UAAU,OAAO;AACxB,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM,OAAO,KAAK,KAAK;AACvB,cAAI,IAAI,KAAK,IAAI,OAAO,CAAC;AACzB,eAAK,IAAI,KAAK,IAAI,OAAO,CAAC;AAAA,QAC5B;AACA,cAAM,MAAM,IAAI,MAAM;AACtB,cAAM,KAAK,IAAI,MAAM;AACrB,cAAM,KAAK,KAAK,KAAK;AACrB,cAAM,KAAK,KAAK;AAChB,cAAM,KAAK;AACX,cAAM,KAAK;AACX,aAAK,WAAW,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK,MAAM;AACrD,aAAK,KAAK,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK,MAAM;AAC/C,YAAI,WAAW,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK,MAAM;AAAA,MACtD;AACA,YAAM,KAAK,KAAK,SAAS,KAAK,UAAU,IAAI;AAC5C,YAAM,KAAK,KAAK,UAAU,KAAK,SAAS,IAAI;AAC5C,YAAM,KAAK,KAAK,SAAS,KAAK,SAAS,IAAI;AAC3C,YAAM,MAAM,KAAK;AACjB,YAAM,KAAK;AACX,YAAM,IAAI,KAAK,KAAK,QAAQ,IAAI,IAAI,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAC/D,YAAM,IAAI,KAAK,KAAK,QAAQ,IAAI,IAAI,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAC/D,YAAM,IAAI,KAAK,KAAK,QAAQ,IAAI,IAAI,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAC/D,YAAM,QAAQ,IAAI,OAAM,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AACjD,aAAO;AAAA,IACT,WAAW,KAAK,UAAU,OAAO;AAC/B,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,WAAK;AACL,WAAK;AACL,WAAK;AACL,UAAI,MAAM,GAAG;AACX,aAAK;AACL,cAAM,SAAS,IAAI,OAAM,GAAG,GAAG,CAAC;AAChC,eAAO;AAAA,MACT;AACA,YAAM,IAAI,IAAI,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI;AAC9C,YAAM,IAAI,IAAI,IAAI;AAClB,YAAM,IAAI,MAAM,SAAS,GAAG,GAAG,IAAI,IAAI,CAAC;AACxC,YAAM,IAAI,MAAM,SAAS,GAAG,GAAG,CAAC;AAChC,YAAM,IAAI,MAAM,SAAS,GAAG,GAAG,IAAI,IAAI,CAAC;AACxC,YAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,CAAC;AAC/B,aAAO;AAAA,IACT,WAAW,KAAK,UAAU,QAAQ;AAChC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA,GAAG;AAAA,QACH;AAAA,MACF,IAAI;AACJ,YAAM,IAAI,OAAO,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,CAAC;AAChD,YAAM,IAAI,OAAO,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,CAAC;AAChD,YAAM,IAAI,OAAO,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,KAAK,CAAC;AACjD,YAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,CAAC;AAC/B,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,UAAU;AACR,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK;AAAA,EAC/B;AAAA,EACA,QAAQ;AACN,UAAM,CAAC,GAAG,GAAG,CAAC,IAAI,KAAK,SAAS,EAAE,IAAI,YAAY;AAClD,WAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;AAAA,EACtB;AAAA,EACA,QAAQ;AACN,UAAM,CAAC,IAAI,IAAI,EAAE,IAAI,KAAK,SAAS;AACnC,UAAM,SAAS,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE;AACpC,WAAO;AAAA,EACT;AAAA,EACA,WAAW;AACT,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,MAAM;AACJ,UAAM;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,IAAI,KAAK,IAAI;AACb,UAAM,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,MAAM,IAAI,EAAE,IAAI,OAAK,IAAI,GAAG;AACrD,UAAM,KAAK,IAAI,UAAU,KAAK,KAAK,IAAI,SAAS,OAAO,GAAG,IAAI,IAAI;AAClE,UAAM,KAAK,IAAI,UAAU,KAAK,KAAK,IAAI,SAAS,OAAO,GAAG,IAAI,IAAI;AAClE,UAAM,KAAK,IAAI,UAAU,KAAK,KAAK,IAAI,SAAS,OAAO,GAAG,IAAI,IAAI;AAClE,UAAM,MAAM,KAAK,SAAS,KAAK,SAAS,KAAK,UAAU;AACvD,UAAM,MAAM,KAAK,SAAS,KAAK,SAAS,KAAK,UAAU;AACvD,UAAM,MAAM,KAAK,SAAS,KAAK,SAAS,KAAK,UAAU;AACvD,UAAM,KAAK,KAAK,UAAU,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,QAAQ,KAAK,KAAK;AAClE,UAAM,KAAK,KAAK,UAAU,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,QAAQ,KAAK,KAAK;AAClE,UAAM,IAAI,KAAK,UAAU,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,QAAQ,KAAK,KAAK;AACjE,UAAM,QAAQ,IAAI,OAAM,IAAI,IAAI,GAAG,KAAK;AACxC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACT,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK,IAAI;AACb,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,OAAK,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,GAAG,CAAC;AAC7C,WAAO,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,MAAM;AAAA,EAChC;AAAA;AAAA;AAAA;AAIF;AACA,IAAM,QAAN,MAAM,OAAM;AAAA;AAAA,EAEV,eAAe,MAAM;AACnB,SAAK,KAAK,GAAG,IAAI;AAAA,EACnB;AAAA;AAAA,EAEA,QAAQ;AACN,WAAO,IAAI,OAAM,IAAI;AAAA,EACvB;AAAA,EACA,KAAK,IAAI,IAAI;AACX,UAAM,OAAO;AAAA,MACX,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,UAAM,SAAS,MAAM,QAAQ,EAAE,IAAI;AAAA,MACjC,GAAG,GAAG,CAAC;AAAA,MACP,GAAG,GAAG,CAAC;AAAA,IACT,IAAI,OAAO,OAAO,WAAW;AAAA,MAC3B,GAAG,GAAG;AAAA,MACN,GAAG,GAAG;AAAA,IACR,IAAI;AAAA,MACF,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,SAAK,IAAI,OAAO,KAAK,OAAO,KAAK,IAAI,OAAO;AAC5C,SAAK,IAAI,OAAO,KAAK,OAAO,KAAK,IAAI,OAAO;AAC5C,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,WAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AAAA,EACxB;AAAA,EACA,UAAU,GAAG;AACX,WAAO,KAAK,MAAM,EAAE,WAAW,CAAC;AAAA,EAClC;AAAA;AAAA,EAEA,WAAW,GAAG;AACZ,QAAI,CAAC,OAAO,aAAa,CAAC,GAAG;AAC3B,UAAI,IAAI,OAAO,CAAC;AAAA,IAClB;AACA,UAAM;AAAA,MACJ,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI;AACJ,SAAK,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,KAAK,EAAE;AACjC,SAAK,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,KAAK,EAAE;AACjC,WAAO;AAAA,EACT;AACF;AACA,SAAS,MAAM,IAAI,IAAI;AACrB,SAAO,IAAI,MAAM,IAAI,EAAE,EAAE,WAAW,KAAK,UAAU,EAAE,SAAS,CAAC;AACjE;AACA,SAAS,YAAY,GAAG,GAAG,WAAW;AACpC,SAAO,KAAK,IAAI,IAAI,CAAC,IAAI;AAC3B;AACA,IAAM,SAAN,MAAM,QAAO;AAAA,EACX,eAAe,MAAM;AACnB,SAAK,KAAK,GAAG,IAAI;AAAA,EACnB;AAAA,EACA,OAAO,iBAAiB,GAAG;AACzB,UAAM,WAAW,EAAE,SAAS,UAAU,EAAE,SAAS;AACjD,UAAM,QAAQ,EAAE,SAAS,YAAY,EAAE,SAAS,OAAO,KAAK;AAC5D,UAAM,QAAQ,EAAE,SAAS,YAAY,EAAE,SAAS,OAAO,KAAK;AAC5D,UAAM,QAAQ,EAAE,QAAQ,EAAE,KAAK,SAAS,EAAE,KAAK,CAAC,IAAI,SAAS,EAAE,IAAI,IAAI,EAAE,OAAO,SAAS,EAAE,KAAK,IAAI,EAAE,QAAQ;AAC9G,UAAM,QAAQ,EAAE,QAAQ,EAAE,KAAK,SAAS,EAAE,KAAK,CAAC,IAAI,SAAS,EAAE,IAAI,IAAI,EAAE,OAAO,SAAS,EAAE,KAAK,IAAI,EAAE,QAAQ;AAC9G,UAAM,SAAS,EAAE,SAAS,EAAE,MAAM,SAAS,EAAE,MAAM,CAAC,IAAI,QAAQ,SAAS,EAAE,KAAK,IAAI,EAAE,QAAQ,QAAQ,SAAS,EAAE,MAAM,IAAI,EAAE,SAAS,QAAQ;AAC9I,UAAM,SAAS,EAAE,SAAS,EAAE,MAAM,SAAS,EAAE,MAAM,CAAC,IAAI,QAAQ,SAAS,EAAE,KAAK,IAAI,EAAE,QAAQ,QAAQ,SAAS,EAAE,MAAM,IAAI,EAAE,SAAS,QAAQ;AAC9I,UAAM,QAAQ,EAAE,SAAS;AACzB,UAAM,QAAQ,EAAE,UAAU,EAAE,SAAS;AACrC,UAAM,SAAS,IAAI,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO;AACrF,UAAM,KAAK,OAAO;AAClB,UAAM,KAAK,OAAO;AAClB,UAAM,YAAY,IAAI,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,aAAa,KAAK,EAAE,MAAM,EAAE,aAAa,GAAG;AAChG,UAAM,KAAK,UAAU;AACrB,UAAM,KAAK,UAAU;AACrB,UAAM,YAAY,IAAI,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;AACrF,UAAM,KAAK,UAAU;AACrB,UAAM,KAAK,UAAU;AACrB,UAAM,WAAW,IAAI,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS;AACjF,UAAM,MAAM,SAAS;AACrB,UAAM,MAAM,SAAS;AACrB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,UAAU,GAAG;AAClB,WAAO;AAAA,MACL,GAAG,EAAE,CAAC;AAAA,MACN,GAAG,EAAE,CAAC;AAAA,MACN,GAAG,EAAE,CAAC;AAAA,MACN,GAAG,EAAE,CAAC;AAAA,MACN,GAAG,EAAE,CAAC;AAAA,MACN,GAAG,EAAE,CAAC;AAAA,IACR;AAAA,EACF;AAAA,EACA,OAAO,aAAa,GAAG;AACrB,WAAO,EAAE,KAAK,QAAQ,EAAE,KAAK,QAAQ,EAAE,KAAK,QAAQ,EAAE,KAAK,QAAQ,EAAE,KAAK,QAAQ,EAAE,KAAK;AAAA,EAC3F;AAAA;AAAA,EAEA,OAAO,eAAe,GAAG,GAAG,GAAG;AAC7B,UAAM,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAC9B,UAAM,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAC9B,UAAM,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAC9B,UAAM,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAC9B,UAAM,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AACpC,UAAM,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AACpC,MAAE,IAAI;AACN,MAAE,IAAI;AACN,MAAE,IAAI;AACN,MAAE,IAAI;AACN,MAAE,IAAI;AACN,MAAE,IAAI;AACN,WAAO;AAAA,EACT;AAAA,EACA,OAAO,KAAK,KAAK,QAAQ;AACvB,WAAO,KAAK,MAAM,EAAE,QAAQ,KAAK,KAAK,MAAM;AAAA,EAC9C;AAAA;AAAA,EAEA,QAAQ,KAAK,KAAK,QAAQ;AACxB,UAAM,MAAM,OAAO;AACnB,UAAM,MAAM,OAAO;AACnB,WAAO,KAAK,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE,WAAW,MAAM,EAAE,WAAW,KAAK,GAAG;AAAA,EAC3E;AAAA;AAAA,EAEA,QAAQ;AACN,WAAO,IAAI,QAAO,IAAI;AAAA,EACxB;AAAA;AAAA,EAEA,UAAU,MAAM,GAAG,MAAM,GAAG;AAC1B,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,cAAc,IAAI,IAAI,IAAI;AAChC,UAAM,MAAM,cAAc,IAAI,IAAI;AAClC,UAAM,KAAK,MAAM,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AACxC,UAAM,WAAW,KAAK,MAAM,MAAM,GAAG,MAAM,CAAC;AAC5C,UAAM,QAAQ,MAAM,KAAK,KAAK;AAC9B,UAAM,KAAK,KAAK,IAAI,QAAQ;AAC5B,UAAM,KAAK,KAAK,IAAI,QAAQ;AAC5B,UAAM,OAAO,IAAI,IAAI,IAAI,KAAK;AAC9B,UAAM,KAAK,IAAI,MAAM,MAAM,IAAI,MAAM,IAAI,MAAM,MAAM,IAAI;AACzD,UAAM,KAAK,IAAI,MAAM,MAAM,KAAK,KAAK,OAAO,MAAM,KAAK,KAAK,KAAK;AACjE,UAAM,KAAK,IAAI,MAAM,MAAM,KAAK,KAAK,OAAO,MAAM,KAAK,KAAK,KAAK;AACjE,WAAO;AAAA;AAAA,MAEL,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,SAAS;AAAA;AAAA,MAET,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AAAA;AAAA,EAEA,OAAO,OAAO;AACZ,QAAI,UAAU,KAAM,QAAO;AAC3B,UAAM,OAAO,IAAI,QAAO,KAAK;AAC7B,WAAO,YAAY,KAAK,GAAG,KAAK,CAAC,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC;AAAA,EAC9L;AAAA;AAAA,EAEA,KAAK,MAAM,QAAQ;AACjB,WAAO,KAAK,MAAM,EAAE,MAAM,MAAM,MAAM;AAAA,EACxC;AAAA,EACA,MAAM,MAAM,QAAQ;AAClB,WAAO,SAAS,MAAM,KAAK,OAAO,IAAI,GAAG,QAAQ,CAAC,IAAI,SAAS,MAAM,KAAK,OAAO,GAAG,IAAI,GAAG,MAAM,IAAI,KAAK,OAAO,IAAI,IAAI,MAAM,UAAU,IAAI;AAAA,EAC/I;AAAA;AAAA,EAEA,KAAK,QAAQ;AACX,UAAM,OAAO,QAAO,UAAU,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AAChD,aAAS,kBAAkB,UAAU,OAAO,UAAU,IAAI,OAAO,WAAW,WAAW,QAAO,UAAU,OAAO,MAAM,SAAS,EAAE,IAAI,UAAU,CAAC,IAAI,MAAM,QAAQ,MAAM,IAAI,QAAO,UAAU,MAAM,IAAI,OAAO,WAAW,YAAY,QAAO,aAAa,MAAM,IAAI,SAAS,OAAO,WAAW,WAAW,IAAI,QAAO,EAAE,UAAU,MAAM,IAAI,UAAU,WAAW,IAAI,QAAO,UAAU,CAAC,EAAE,MAAM,KAAK,SAAS,CAAC,IAAI;AAC/Y,SAAK,IAAI,OAAO,KAAK,OAAO,OAAO,IAAI,KAAK;AAC5C,SAAK,IAAI,OAAO,KAAK,OAAO,OAAO,IAAI,KAAK;AAC5C,SAAK,IAAI,OAAO,KAAK,OAAO,OAAO,IAAI,KAAK;AAC5C,SAAK,IAAI,OAAO,KAAK,OAAO,OAAO,IAAI,KAAK;AAC5C,SAAK,IAAI,OAAO,KAAK,OAAO,OAAO,IAAI,KAAK;AAC5C,SAAK,IAAI,OAAO,KAAK,OAAO,OAAO,IAAI,KAAK;AAC5C,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,WAAO,KAAK,MAAM,EAAE,SAAS;AAAA,EAC/B;AAAA;AAAA,EAEA,WAAW;AACT,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,MAAM,IAAI,IAAI,IAAI;AACxB,QAAI,CAAC,IAAK,OAAM,IAAI,MAAM,mBAAmB,IAAI;AACjD,UAAM,KAAK,IAAI;AACf,UAAM,KAAK,CAAC,IAAI;AAChB,UAAM,KAAK,CAAC,IAAI;AAChB,UAAM,KAAK,IAAI;AACf,UAAM,KAAK,EAAE,KAAK,IAAI,KAAK;AAC3B,UAAM,KAAK,EAAE,KAAK,IAAI,KAAK;AAC3B,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAI;AACT,WAAO;AAAA,EACT;AAAA,EACA,UAAU,QAAQ;AAChB,WAAO,KAAK,MAAM,EAAE,WAAW,MAAM;AAAA,EACvC;AAAA,EACA,WAAW,QAAQ;AACjB,UAAM,IAAI;AACV,UAAM,IAAI,kBAAkB,UAAS,SAAS,IAAI,QAAO,MAAM;AAC/D,WAAO,QAAO,eAAe,GAAG,GAAG,IAAI;AAAA,EACzC;AAAA;AAAA,EAEA,SAAS,QAAQ;AACf,WAAO,KAAK,MAAM,EAAE,UAAU,MAAM;AAAA,EACtC;AAAA,EACA,UAAU,QAAQ;AAChB,UAAM,IAAI;AACV,UAAM,IAAI,kBAAkB,UAAS,SAAS,IAAI,QAAO,MAAM;AAC/D,WAAO,QAAO,eAAe,GAAG,GAAG,IAAI;AAAA,EACzC;AAAA;AAAA,EAEA,OAAO,GAAG,KAAK,KAAK;AAClB,WAAO,KAAK,MAAM,EAAE,QAAQ,GAAG,KAAK,GAAG;AAAA,EACzC;AAAA,EACA,QAAQ,GAAG,MAAM,GAAG,MAAM,GAAG;AAC3B,QAAI,QAAQ,CAAC;AACb,UAAM,MAAM,KAAK,IAAI,CAAC;AACtB,UAAM,MAAM,KAAK,IAAI,CAAC;AACtB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,SAAK,IAAI,IAAI,MAAM,IAAI;AACvB,SAAK,IAAI,IAAI,MAAM,IAAI;AACvB,SAAK,IAAI,IAAI,MAAM,IAAI;AACvB,SAAK,IAAI,IAAI,MAAM,IAAI;AACvB,SAAK,IAAI,IAAI,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,SAAK,IAAI,IAAI,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,QAAQ;AACN,WAAO,KAAK,MAAM,EAAE,OAAO,GAAG,SAAS;AAAA,EACzC;AAAA,EACA,OAAO,IAAI,KAAK,IAAI,MAAM,GAAG,MAAM,GAAG;AACpC,QAAI,UAAU,WAAW,GAAG;AAC1B,YAAM;AACN,YAAM;AACN,WAAK;AAAA,IACP;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI,KAAK,MAAM,KAAK;AAC7B,SAAK,IAAI,IAAI,KAAK,MAAM,KAAK;AAC7B,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,MAAM,GAAG,KAAK,KAAK;AACjB,WAAO,KAAK,MAAM,EAAE,OAAO,GAAG,KAAK,GAAG;AAAA,EACxC;AAAA;AAAA,EAEA,OAAO,IAAI,MAAM,GAAG,MAAM,GAAG;AAC3B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,SAAK,IAAI,IAAI,IAAI;AACjB,SAAK,IAAI,IAAI,IAAI;AACjB,SAAK,IAAI,IAAI,IAAI,KAAK,MAAM;AAC5B,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,OAAO;AACL,WAAO,KAAK,MAAM,EAAE,MAAM,GAAG,SAAS;AAAA,EACxC;AAAA,EACA,MAAM,IAAI,KAAK,IAAI,MAAM,GAAG,MAAM,GAAG;AACnC,QAAI,UAAU,WAAW,GAAG;AAC1B,YAAM;AACN,YAAM;AACN,WAAK;AAAA,IACP;AACA,SAAK,QAAQ,EAAE;AACf,SAAK,QAAQ,EAAE;AACf,UAAM,KAAK,KAAK,IAAI,EAAE;AACtB,UAAM,KAAK,KAAK,IAAI,EAAE;AACtB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,SAAK,IAAI,IAAI,IAAI;AACjB,SAAK,IAAI,IAAI,IAAI;AACjB,SAAK,IAAI,IAAI,IAAI;AACjB,SAAK,IAAI,IAAI,IAAI;AACjB,SAAK,IAAI,IAAI,IAAI,KAAK,MAAM;AAC5B,SAAK,IAAI,IAAI,IAAI,KAAK,MAAM;AAC5B,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,MAAM,IAAI,KAAK,KAAK;AAClB,WAAO,KAAK,KAAK,IAAI,GAAG,KAAK,GAAG;AAAA,EAClC;AAAA;AAAA,EAEA,MAAM,IAAI,KAAK,KAAK;AAClB,WAAO,KAAK,KAAK,GAAG,IAAI,KAAK,GAAG;AAAA,EAClC;AAAA,EACA,UAAU;AACR,WAAO,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAAA,EACxD;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,YAAY,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI;AAAA,EACzG;AAAA;AAAA,EAEA,UAAU,GAAG;AACX,QAAI,QAAO,aAAa,CAAC,GAAG;AAC1B,YAAM,SAAS,IAAI,QAAO,CAAC;AAC3B,aAAO,OAAO,UAAU,IAAI;AAAA,IAC9B;AACA,UAAM,IAAI,QAAO,iBAAiB,CAAC;AACnC,UAAM,UAAU;AAChB,UAAM;AAAA,MACJ,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI,IAAI,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,OAAO;AAC3C,UAAM,cAAc,IAAI,QAAO,EAAE,WAAW,EAAE,IAAI,EAAE,EAAE,EAAE,WAAW,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,IAAI,EAAE;AACtM,QAAI,SAAS,EAAE,EAAE,KAAK,SAAS,EAAE,EAAE,GAAG;AACpC,YAAM,SAAS,IAAI,MAAM,IAAI,EAAE,EAAE,UAAU,WAAW;AACtD,YAAM,MAAM,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,OAAO,IAAI;AAC/C,YAAM,MAAM,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,OAAO,IAAI;AAC/C,kBAAY,WAAW,KAAK,GAAG;AAAA,IACjC;AACA,gBAAY,WAAW,EAAE,IAAI,EAAE,EAAE;AACjC,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,UAAU,IAAI,IAAI;AAChB,WAAO,KAAK,MAAM,EAAE,WAAW,IAAI,EAAE;AAAA,EACvC;AAAA,EACA,WAAW,IAAI,IAAI;AACjB,SAAK,KAAK,MAAM;AAChB,SAAK,KAAK,MAAM;AAChB,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,WAAO;AAAA,MACL,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AACF;AACA,SAAS,MAAM;AACb,SAAO,IAAI,OAAO,KAAK,KAAK,OAAO,CAAC;AACtC;AACA,SAAS,YAAY;AACnB,MAAI;AACF,QAAI,OAAO,KAAK,WAAW,cAAc,CAAC,KAAK,OAAO,GAAG;AACvD,YAAM,OAAO,KAAK,KAAK,GAAG,CAAC;AAC3B,YAAM,IAAI,KAAK,KAAK,aAAa;AACjC,WAAK,OAAO;AACZ,aAAO,IAAI,OAAO,CAAC;AAAA,IACrB;AACA,WAAO,IAAI,OAAO,KAAK,KAAK,aAAa,CAAC;AAAA,EAC5C,SAAS,GAAG;AACV,YAAQ,KAAK,gCAAgC,KAAK,KAAK,QAAQ,4BAA4B;AAC3F,WAAO,IAAI,OAAO;AAAA,EACpB;AACF;AACA,SAAS,QAAQ,QAAQ;AACzB,SAAS,SAAS;AAChB,MAAI,CAAC,OAAO,OAAO;AACjB,UAAM,OAAO,aAAa,EAAE,KAAK,GAAG,CAAC;AACrC,SAAK,KAAK,MAAM,UAAU,CAAC,cAAc,sBAAsB,eAAe,cAAc,kBAAkB,EAAE,KAAK,GAAG;AACxH,SAAK,KAAK,aAAa,OAAO;AAC9B,SAAK,KAAK,eAAe,MAAM;AAC/B,UAAM,OAAO,KAAK,KAAK,EAAE;AACzB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,OAAO,MAAM,IAAI,KAAK,YAAY;AACrC,UAAM,IAAI,QAAQ,SAAS,QAAQ,QAAQ,SAAS;AACpD,WAAO,MAAM,IAAI,MAAM,CAAC;AAAA,EAC1B;AACA,SAAO,OAAO;AAChB;AACA,SAAS,YAAY,KAAK;AACxB,SAAO,CAAC,IAAI,SAAS,CAAC,IAAI,UAAU,CAAC,IAAI,KAAK,CAAC,IAAI;AACrD;AACA,SAAS,YAAY,MAAM;AACzB,SAAO,SAAS,QAAQ,aAAa,QAAQ,SAAS,gBAAgB,YAAY,SAAU,OAAO;AACjG,WAAO,MAAM,YAAY;AACvB,cAAQ,MAAM;AAAA,IAChB;AACA,WAAO,UAAU,QAAQ;AAAA,EAC3B,GAAG,KAAK,QAAQ,SAAS,iBAAiB,IAAI;AAChD;AACA,IAAM,MAAN,MAAM,KAAI;AAAA,EACR,eAAe,MAAM;AACnB,SAAK,KAAK,GAAG,IAAI;AAAA,EACnB;AAAA,EACA,YAAY;AACV,SAAK,KAAK,QAAQ,OAAO;AACzB,SAAK,KAAK,QAAQ,OAAO;AACzB,WAAO,IAAI,KAAI,IAAI;AAAA,EACrB;AAAA,EACA,KAAK,QAAQ;AACX,UAAM,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AACxB,aAAS,OAAO,WAAW,WAAW,OAAO,MAAM,SAAS,EAAE,IAAI,UAAU,IAAI,MAAM,QAAQ,MAAM,IAAI,SAAS,OAAO,WAAW,WAAW,CAAC,OAAO,QAAQ,OAAO,OAAO,OAAO,OAAO,GAAG,OAAO,OAAO,OAAO,OAAO,MAAM,OAAO,GAAG,OAAO,OAAO,OAAO,MAAM,IAAI,UAAU,WAAW,IAAI,CAAC,EAAE,MAAM,KAAK,SAAS,IAAI;AAC5T,SAAK,IAAI,OAAO,CAAC,KAAK;AACtB,SAAK,IAAI,OAAO,CAAC,KAAK;AACtB,SAAK,QAAQ,KAAK,IAAI,OAAO,CAAC,KAAK;AACnC,SAAK,SAAS,KAAK,IAAI,OAAO,CAAC,KAAK;AACpC,SAAK,KAAK,KAAK,IAAI,KAAK;AACxB,SAAK,KAAK,KAAK,IAAI,KAAK;AACxB,SAAK,KAAK,KAAK,IAAI,KAAK,IAAI;AAC5B,SAAK,KAAK,KAAK,IAAI,KAAK,IAAI;AAC5B,WAAO;AAAA,EACT;AAAA,EACA,WAAW;AACT,WAAO,YAAY,IAAI;AAAA,EACzB;AAAA;AAAA,EAEA,MAAM,KAAK;AACT,UAAM,KAAK,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC;AACjC,UAAM,KAAK,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC;AACjC,UAAM,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,IAAI,IAAI,IAAI,KAAK,IAAI;AAClE,UAAM,UAAU,KAAK,IAAI,KAAK,IAAI,KAAK,QAAQ,IAAI,IAAI,IAAI,MAAM,IAAI;AACrE,WAAO,IAAI,KAAI,IAAI,IAAI,QAAQ,OAAO;AAAA,EACxC;AAAA,EACA,UAAU;AACR,WAAO,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO,KAAK,MAAM;AAAA,EACjD;AAAA,EACA,WAAW;AACT,WAAO,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,QAAQ,MAAM,KAAK;AAAA,EAC/D;AAAA,EACA,UAAU,GAAG;AACX,QAAI,EAAE,aAAa,SAAS;AAC1B,UAAI,IAAI,OAAO,CAAC;AAAA,IAClB;AACA,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,OAAO;AACX,UAAM,MAAM,CAAC,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,IAAI,MAAM,KAAK,IAAI,KAAK,CAAC,GAAG,IAAI,MAAM,KAAK,GAAG,KAAK,EAAE,GAAG,IAAI,MAAM,KAAK,IAAI,KAAK,EAAE,CAAC;AAC3H,QAAI,QAAQ,SAAU,GAAG;AACvB,UAAI,EAAE,UAAU,CAAC;AACjB,aAAO,KAAK,IAAI,MAAM,EAAE,CAAC;AACzB,aAAO,KAAK,IAAI,MAAM,EAAE,CAAC;AACzB,aAAO,KAAK,IAAI,MAAM,EAAE,CAAC;AACzB,aAAO,KAAK,IAAI,MAAM,EAAE,CAAC;AAAA,IAC3B,CAAC;AACD,WAAO,IAAI,KAAI,MAAM,MAAM,OAAO,MAAM,OAAO,IAAI;AAAA,EACrD;AACF;AACA,SAAS,OAAO,IAAI,WAAW,OAAO;AACpC,MAAI;AACJ,MAAI;AACF,UAAM,UAAU,GAAG,IAAI;AACvB,QAAI,YAAY,GAAG,KAAK,CAAC,YAAY,GAAG,IAAI,GAAG;AAC7C,YAAM,IAAI,MAAM,wBAAwB;AAAA,IAC1C;AAAA,EACF,SAAS,GAAG;AACV,UAAM,MAAM,EAAE;AAAA,EAChB;AACA,SAAO;AACT;AACA,SAAS,OAAO;AACd,QAAM,UAAU,UAAQ,KAAK,QAAQ;AACrC,QAAM,QAAQ,QAAM;AAClB,QAAI;AACF,YAAM,QAAQ,GAAG,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,EAAE,KAAK;AAClD,YAAM,OAAO,MAAM,KAAK,QAAQ;AAChC,YAAM,OAAO;AACb,aAAO;AAAA,IACT,SAAS,GAAG;AACV,YAAM,IAAI,MAAM,4BAA4B,GAAG,KAAK,QAAQ,sBAAsB,EAAE,SAAS,CAAC,EAAE;AAAA,IAClG;AAAA,EACF;AACA,QAAM,MAAM,OAAO,MAAM,SAAS,KAAK;AACvC,QAAM,QAAQ,IAAI,IAAI,GAAG;AACzB,SAAO;AACT;AACA,SAAS,KAAK,IAAI;AAChB,QAAM,UAAU,UAAQ,KAAK,sBAAsB;AACnD,QAAM,QAAQ,SAAO;AACnB,UAAM,IAAI,MAAM,4BAA4B,IAAI,KAAK,QAAQ,mBAAmB;AAAA,EAClF;AACA,QAAM,MAAM,OAAO,MAAM,SAAS,KAAK;AACvC,QAAM,QAAQ,IAAI,IAAI,GAAG;AACzB,MAAI,IAAI;AACN,WAAO,MAAM,UAAU,GAAG,UAAU,EAAE,SAAS,CAAC;AAAA,EAClD;AACA,SAAO,MAAM,UAAU;AACzB;AACA,SAAS,OAAO,IAAI,IAAI;AACtB,QAAM,MAAM,KAAK,KAAK;AACtB,SAAO,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,SAAS,KAAK,IAAI,IAAI,IAAI;AAChF;AACA,gBAAgB;AAAA,EACd,SAAS;AAAA,IACP,QAAQ,IAAI,IAAI,QAAQ,SAAS;AAC/B,UAAI,MAAM,KAAM,QAAO,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AACnD,aAAO,KAAK,KAAK,WAAW,IAAI,IAAI,IAAI,IAAI,QAAQ,OAAO,CAAC;AAAA,IAC9D;AAAA,IACA,KAAK,OAAO,QAAQ;AAClB,UAAI;AAAA,QACF,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,IAAI,KAAK,KAAK,CAAC,SAAS,QAAQ,CAAC;AACjC,UAAI,CAAC,UAAU,CAAC,WAAW,OAAO,WAAW,YAAY,OAAO,YAAY,UAAU;AACpF,iBAAS,KAAK,KAAK;AACnB,kBAAU,KAAK,KAAK;AAAA,MACtB;AACA,UAAI,CAAC,UAAU,CAAC,SAAS;AACvB,cAAM,IAAI,MAAM,2HAA2H;AAAA,MAC7I;AACA,YAAM,IAAI,KAAK,QAAQ;AACvB,YAAM,QAAQ,SAAS,EAAE;AACzB,YAAM,QAAQ,UAAU,EAAE;AAC1B,YAAM,OAAO,KAAK,IAAI,OAAO,KAAK;AAClC,UAAI,SAAS,MAAM;AACjB,eAAO;AAAA,MACT;AACA,UAAI,aAAa,OAAO;AACxB,UAAI,eAAe,SAAU,cAAa,OAAO,mBAAmB;AACpE,eAAS,UAAU,IAAI,MAAM,SAAS,IAAI,QAAQ,EAAE,GAAG,UAAU,IAAI,QAAQ,EAAE,CAAC;AAChF,YAAM,MAAM,IAAI,IAAI,CAAC,EAAE,UAAU,IAAI,OAAO;AAAA,QAC1C,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,CAAC,CAAC;AACF,aAAO,KAAK,QAAQ,GAAG;AAAA,IACzB;AAAA,EACF;AACF,CAAC;AACD,SAAS,KAAK,KAAK;AACnB,IAAM,OAAN,cAAmB,MAAM;AAAA,EACvB,YAAY,MAAM,CAAC,MAAM,MAAM;AAC7B,UAAM,KAAK,GAAG,IAAI;AAClB,QAAI,OAAO,QAAQ,SAAU,QAAO;AACpC,SAAK,SAAS;AACd,SAAK,KAAK,GAAG,GAAG;AAAA,EAClB;AACF;AACA,OAAO,CAAC,IAAI,GAAG;AAAA,EACb,KAAK,mBAAmB,MAAM;AAC5B,QAAI,OAAO,mBAAmB,YAAY;AACxC,aAAO,KAAK,IAAI,CAAC,IAAI,GAAG,QAAQ;AAC9B,eAAO,eAAe,KAAK,IAAI,IAAI,GAAG,GAAG;AAAA,MAC3C,CAAC;AAAA,IACH,OAAO;AACL,aAAO,KAAK,IAAI,QAAM;AACpB,eAAO,GAAG,cAAc,EAAE,GAAG,IAAI;AAAA,MACnC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,UAAU;AACR,WAAO,MAAM,UAAU,OAAO,MAAM,CAAC,GAAG,IAAI;AAAA,EAC9C;AACF,CAAC;AACD,IAAM,WAAW,CAAC,WAAW,eAAe,MAAM;AAClD,KAAK,SAAS,SAAU,UAAU;AAChC,aAAW,SAAS,OAAO,CAAC,KAAK,UAAU;AACzC,QAAI,SAAS,SAAS,KAAK,EAAG,QAAO;AACrC,QAAI,MAAM,CAAC,MAAM,IAAK,QAAO;AAC7B,QAAI,SAAS,MAAM,WAAW;AAC5B,UAAI,MAAM,KAAK,IAAI,MAAM,UAAU,KAAK;AAAA,IAC1C;AACA,QAAI,KAAK,IAAI,YAAa,QAAQ;AAChC,aAAO,KAAK,KAAK,OAAO,GAAG,MAAM;AAAA,IACnC;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,SAAO,CAAC,IAAI,GAAG,QAAQ;AACzB;AACA,SAAS,SAAS,OAAO,QAAQ;AAC/B,SAAO,IAAI,KAAK,KAAK,UAAU,QAAQ,UAAU,iBAAiB,KAAK,GAAG,SAAU,MAAM;AACxF,WAAO,MAAM,IAAI;AAAA,EACnB,CAAC,CAAC;AACJ;AACA,SAAS,KAAK,OAAO;AACnB,SAAO,SAAS,OAAO,KAAK,IAAI;AAClC;AACA,SAAS,QAAQ,OAAO;AACtB,SAAO,MAAM,KAAK,KAAK,cAAc,KAAK,CAAC;AAC7C;AACA,IAAI,aAAa;AACjB,IAAM,eAAe,CAAC;AACtB,SAAS,UAAU,UAAU;AAC3B,MAAI,IAAI,SAAS,eAAe;AAChC,MAAI,MAAM,QAAQ,OAAQ,KAAI;AAC9B,MAAI,CAAC,EAAE,OAAQ,GAAE,SAAS,CAAC;AAC3B,SAAO,EAAE;AACX;AACA,SAAS,eAAe,UAAU;AAChC,SAAO,SAAS,eAAe;AACjC;AACA,SAAS,YAAY,UAAU;AAC7B,MAAI,IAAI,SAAS,eAAe;AAChC,MAAI,MAAM,QAAQ,OAAQ,KAAI;AAC9B,MAAI,EAAE,OAAQ,GAAE,SAAS,CAAC;AAC5B;AACA,SAAS,GAAG,MAAM,QAAQ,UAAU,SAAS,SAAS;AACpD,QAAM,IAAI,SAAS,KAAK,WAAW,IAAI;AACvC,QAAM,WAAW,aAAa,IAAI;AAClC,QAAM,MAAM,UAAU,QAAQ;AAC9B,QAAM,IAAI,eAAe,QAAQ;AACjC,WAAS,MAAM,QAAQ,MAAM,IAAI,SAAS,OAAO,MAAM,SAAS;AAChE,MAAI,CAAC,SAAS,kBAAkB;AAC9B,aAAS,mBAAmB,EAAE;AAAA,EAChC;AACA,SAAO,QAAQ,SAAU,OAAO;AAC9B,UAAM,KAAK,MAAM,MAAM,GAAG,EAAE,CAAC;AAC7B,UAAM,KAAK,MAAM,MAAM,GAAG,EAAE,CAAC,KAAK;AAClC,QAAI,EAAE,IAAI,IAAI,EAAE,KAAK,CAAC;AACtB,QAAI,EAAE,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE,EAAE,KAAK,CAAC;AAC9B,QAAI,EAAE,EAAE,EAAE,EAAE,SAAS,gBAAgB,IAAI;AACzC,MAAE,iBAAiB,IAAI,GAAG,WAAW,KAAK;AAAA,EAC5C,CAAC;AACH;AACA,SAAS,IAAI,MAAM,QAAQ,UAAU,SAAS;AAC5C,QAAM,WAAW,aAAa,IAAI;AAClC,QAAM,MAAM,UAAU,QAAQ;AAC9B,QAAM,IAAI,eAAe,QAAQ;AACjC,MAAI,OAAO,aAAa,YAAY;AAClC,eAAW,SAAS;AACpB,QAAI,CAAC,SAAU;AAAA,EACjB;AACA,WAAS,MAAM,QAAQ,MAAM,IAAI,UAAU,UAAU,IAAI,MAAM,SAAS;AACxE,SAAO,QAAQ,SAAU,OAAO;AAC9B,UAAM,KAAK,SAAS,MAAM,MAAM,GAAG,EAAE,CAAC;AACtC,UAAM,KAAK,SAAS,MAAM,MAAM,GAAG,EAAE,CAAC;AACtC,QAAI,WAAW;AACf,QAAI,UAAU;AACZ,UAAI,IAAI,EAAE,KAAK,IAAI,EAAE,EAAE,MAAM,GAAG,GAAG;AACjC,UAAE,oBAAoB,IAAI,IAAI,EAAE,EAAE,MAAM,GAAG,EAAE,QAAQ,GAAG,WAAW,KAAK;AACxE,eAAO,IAAI,EAAE,EAAE,MAAM,GAAG,EAAE,QAAQ;AAAA,MACpC;AAAA,IACF,WAAW,MAAM,IAAI;AACnB,UAAI,IAAI,EAAE,KAAK,IAAI,EAAE,EAAE,EAAE,GAAG;AAC1B,aAAK,KAAK,IAAI,EAAE,EAAE,EAAE,GAAG;AACrB,cAAI,GAAG,CAAC,IAAI,EAAE,EAAE,KAAK,GAAG,GAAG,CAAC;AAAA,QAC9B;AACA,eAAO,IAAI,EAAE,EAAE,EAAE;AAAA,MACnB;AAAA,IACF,WAAW,IAAI;AACb,WAAK,SAAS,KAAK;AACjB,aAAK,aAAa,IAAI,KAAK,GAAG;AAC5B,cAAI,OAAO,WAAW;AACpB,gBAAI,GAAG,CAAC,OAAO,EAAE,EAAE,KAAK,GAAG,CAAC;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AAAA,IACF,WAAW,IAAI;AACb,UAAI,IAAI,EAAE,GAAG;AACX,aAAK,aAAa,IAAI,EAAE,GAAG;AACzB,cAAI,GAAG,CAAC,IAAI,SAAS,EAAE,KAAK,GAAG,CAAC;AAAA,QAClC;AACA,eAAO,IAAI,EAAE;AAAA,MACf;AAAA,IACF,OAAO;AACL,WAAK,SAAS,KAAK;AACjB,YAAI,GAAG,KAAK;AAAA,MACd;AACA,kBAAY,QAAQ;AAAA,IACtB;AAAA,EACF,CAAC;AACH;AACA,SAAS,SAAS,MAAM,OAAO,OAAO,SAAS;AAC7C,QAAM,IAAI,eAAe,IAAI;AAC7B,MAAI,iBAAiB,QAAQ,OAAO,OAAO;AACzC,MAAE,cAAc,KAAK;AAAA,EACvB,OAAO;AACL,YAAQ,IAAI,QAAQ,OAAO,YAAY,OAAO;AAAA,MAC5C,QAAQ;AAAA,MACR,YAAY;AAAA,OACT,QACJ;AACD,MAAE,cAAc,KAAK;AAAA,EACvB;AACA,SAAO;AACT;AACA,IAAM,cAAN,cAA0B,KAAK;AAAA,EAC7B,mBAAmB;AAAA,EAAC;AAAA,EACpB,SAAS,OAAO,OAAO,SAAS;AAC9B,WAAO,SAAS,MAAM,OAAO,OAAO,OAAO;AAAA,EAC7C;AAAA,EACA,cAAc,OAAO;AACnB,UAAM,MAAM,KAAK,eAAe,EAAE;AAClC,QAAI,CAAC,IAAK,QAAO;AACjB,UAAM,SAAS,IAAI,MAAM,IAAI;AAC7B,eAAW,KAAK,QAAQ;AACtB,iBAAW,KAAK,OAAO,CAAC,GAAG;AACzB,eAAO,CAAC,EAAE,CAAC,EAAE,KAAK;AAAA,MACpB;AAAA,IACF;AACA,WAAO,CAAC,MAAM;AAAA,EAChB;AAAA;AAAA,EAEA,KAAK,OAAO,OAAO,SAAS;AAC1B,SAAK,SAAS,OAAO,OAAO,OAAO;AACnC,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AACf,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AACf,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,IAAI,OAAO,UAAU,SAAS;AAC5B,QAAI,MAAM,OAAO,UAAU,OAAO;AAClC,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,GAAG,OAAO,UAAU,SAAS,SAAS;AACpC,OAAG,MAAM,OAAO,UAAU,SAAS,OAAO;AAC1C,WAAO;AAAA,EACT;AAAA,EACA,sBAAsB;AAAA,EAAC;AACzB;AACA,SAAS,aAAa,aAAa;AACnC,SAAS,OAAO;AAAC;AACjB,IAAM,WAAW;AAAA,EACf,UAAU;AAAA,EACV,MAAM;AAAA,EACN,OAAO;AACT;AACA,IAAM,QAAQ;AAAA;AAAA,EAEZ,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,SAAS;AAAA;AAAA,EAET,GAAG;AAAA,EACH,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA;AAAA,EAEJ,OAAO;AAAA,EACP,QAAQ;AAAA;AAAA,EAER,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA;AAAA,EAEJ,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,cAAc;AAAA;AAAA,EAEd,eAAe;AACjB;AACA,IAAM,WAAN,cAAuB,MAAM;AAAA,EAC3B,eAAe,MAAM;AACnB,UAAM,GAAG,IAAI;AACb,SAAK,KAAK,GAAG,IAAI;AAAA,EACnB;AAAA,EACA,QAAQ;AACN,WAAO,IAAI,KAAK,YAAY,IAAI;AAAA,EAClC;AAAA,EACA,KAAK,KAAK;AACR,QAAI,OAAO,QAAQ,SAAU,QAAO;AACpC,SAAK,SAAS;AACd,SAAK,KAAK,GAAG,KAAK,MAAM,GAAG,CAAC;AAC5B,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,MAAM,SAAS,CAAC,GAAG;AACjB,QAAI,kBAAkB,MAAO,QAAO;AACpC,WAAO,OAAO,KAAK,EAAE,MAAM,SAAS,EAAE,IAAI,UAAU;AAAA,EACtD;AAAA,EACA,UAAU;AACR,WAAO,MAAM,UAAU,OAAO,MAAM,CAAC,GAAG,IAAI;AAAA,EAC9C;AAAA,EACA,QAAQ;AACN,WAAO,IAAI,IAAI,IAAI;AAAA,EACrB;AAAA,EACA,WAAW;AACT,WAAO,KAAK,KAAK,GAAG;AAAA,EACtB;AAAA;AAAA,EAEA,UAAU;AACR,UAAM,MAAM,CAAC;AACb,QAAI,KAAK,GAAG,IAAI;AAChB,WAAO;AAAA,EACT;AACF;AACA,IAAM,YAAN,MAAM,WAAU;AAAA;AAAA,EAEd,eAAe,MAAM;AACnB,SAAK,KAAK,GAAG,IAAI;AAAA,EACnB;AAAA,EACA,QAAQ,MAAM;AACZ,WAAO,IAAI,WAAU,KAAK,OAAO,IAAI;AAAA,EACvC;AAAA;AAAA,EAEA,OAAO,QAAQ;AACb,aAAS,IAAI,WAAU,MAAM;AAC7B,WAAO,IAAI,WAAU,OAAO,QAAQ,KAAK,QAAQ,OAAO,IAAI;AAAA,EAC9D;AAAA,EACA,KAAK,OAAO,MAAM;AAChB,WAAO,MAAM,QAAQ,KAAK,IAAI,MAAM,CAAC,IAAI;AACzC,YAAQ,MAAM,QAAQ,KAAK,IAAI,MAAM,CAAC,IAAI;AAC1C,SAAK,QAAQ;AACb,SAAK,OAAO,QAAQ;AACpB,QAAI,OAAO,UAAU,UAAU;AAC7B,WAAK,QAAQ,MAAM,KAAK,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,QAAQ,IAAI,SAAS,QAAQ;AAAA,IAClF,WAAW,OAAO,UAAU,UAAU;AACpC,aAAO,MAAM,MAAM,aAAa;AAChC,UAAI,MAAM;AACR,aAAK,QAAQ,WAAW,KAAK,CAAC,CAAC;AAC/B,YAAI,KAAK,CAAC,MAAM,KAAK;AACnB,eAAK,SAAS;AAAA,QAChB,WAAW,KAAK,CAAC,MAAM,KAAK;AAC1B,eAAK,SAAS;AAAA,QAChB;AACA,aAAK,OAAO,KAAK,CAAC;AAAA,MACpB;AAAA,IACF,OAAO;AACL,UAAI,iBAAiB,YAAW;AAC9B,aAAK,QAAQ,MAAM,QAAQ;AAC3B,aAAK,OAAO,MAAM;AAAA,MACpB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,MAAM,QAAQ;AACZ,aAAS,IAAI,WAAU,MAAM;AAC7B,WAAO,IAAI,WAAU,OAAO,QAAQ,KAAK,QAAQ,OAAO,IAAI;AAAA,EAC9D;AAAA;AAAA,EAEA,KAAK,QAAQ;AACX,aAAS,IAAI,WAAU,MAAM;AAC7B,WAAO,IAAI,WAAU,OAAO,QAAQ,KAAK,QAAQ,OAAO,IAAI;AAAA,EAC9D;AAAA;AAAA,EAEA,MAAM,QAAQ;AACZ,aAAS,IAAI,WAAU,MAAM;AAC7B,WAAO,IAAI,WAAU,OAAO,QAAQ,KAAK,QAAQ,OAAO,IAAI;AAAA,EAC9D;AAAA,EACA,UAAU;AACR,WAAO,CAAC,KAAK,OAAO,KAAK,IAAI;AAAA,EAC/B;AAAA,EACA,SAAS;AACP,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,WAAW;AACT,YAAQ,KAAK,SAAS,MAAM,CAAC,EAAE,KAAK,QAAQ,OAAO,MAAM,KAAK,SAAS,MAAM,KAAK,QAAQ,MAAM,KAAK,SAAS,KAAK;AAAA,EACrH;AAAA,EACA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AACF;AACA,IAAM,kBAAiC,oBAAI,IAAI,CAAC,QAAQ,UAAU,SAAS,WAAW,cAAc,eAAe,gBAAgB,CAAC;AACpI,IAAM,QAAQ,CAAC;AACf,SAAS,iBAAiB,IAAI;AAC5B,QAAM,KAAK,EAAE;AACf;AACA,SAAS,KAAK,OAAO,KAAK,IAAI;AAC5B,MAAI,SAAS,MAAM;AACjB,YAAQ,CAAC;AACT,UAAM,KAAK,KAAK;AAChB,eAAW,QAAQ,KAAK;AACtB,YAAM,KAAK,QAAQ,IAAI,SAAS,KAAK,KAAK,SAAS,IAAI,WAAW,KAAK,SAAS,IAAI,KAAK;AAAA,IAC3F;AACA,WAAO;AAAA,EACT,WAAW,iBAAiB,OAAO;AACjC,WAAO,MAAM,OAAO,CAAC,MAAM,SAAS;AAClC,WAAK,IAAI,IAAI,KAAK,KAAK,IAAI;AAC3B,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP,WAAW,OAAO,UAAU,YAAY,MAAM,gBAAgB,QAAQ;AACpE,SAAK,OAAO,MAAO,MAAK,KAAK,KAAK,MAAM,GAAG,CAAC;AAAA,EAC9C,WAAW,QAAQ,MAAM;AACvB,SAAK,KAAK,gBAAgB,KAAK;AAAA,EACjC,WAAW,OAAO,MAAM;AACtB,UAAM,KAAK,KAAK,aAAa,KAAK;AAClC,WAAO,OAAO,OAAO,MAAM,KAAK,IAAI,SAAS,KAAK,GAAG,IAAI,WAAW,GAAG,IAAI;AAAA,EAC7E,OAAO;AACL,UAAM,MAAM,OAAO,CAAC,MAAM,SAAS;AACjC,aAAO,KAAK,OAAO,MAAM,IAAI;AAAA,IAC/B,GAAG,GAAG;AACN,QAAI,OAAO,QAAQ,UAAU;AAC3B,YAAM,IAAI,UAAU,GAAG;AAAA,IACzB,WAAW,gBAAgB,IAAI,KAAK,KAAK,MAAM,QAAQ,GAAG,GAAG;AAC3D,YAAM,IAAI,MAAM,GAAG;AAAA,IACrB,WAAW,IAAI,gBAAgB,OAAO;AACpC,YAAM,IAAI,SAAS,GAAG;AAAA,IACxB;AACA,QAAI,UAAU,WAAW;AACvB,UAAI,KAAK,SAAS;AAChB,aAAK,QAAQ,GAAG;AAAA,MAClB;AAAA,IACF,OAAO;AACL,aAAO,OAAO,WAAW,KAAK,KAAK,eAAe,IAAI,OAAO,IAAI,SAAS,CAAC,IAAI,KAAK,KAAK,aAAa,OAAO,IAAI,SAAS,CAAC;AAAA,IAC7H;AACA,QAAI,KAAK,YAAY,UAAU,eAAe,UAAU,MAAM;AAC5D,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,MAAN,MAAM,aAAY,YAAY;AAAA,EAC5B,YAAY,MAAM,QAAQ;AACxB,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,OAAO,KAAK;AACjB,QAAI,UAAU,SAAS,QAAQ;AAC7B,WAAK,KAAK,MAAM;AAAA,IAClB;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,SAAS,GAAG;AACd,cAAU,aAAa,OAAO;AAC9B,QAAI,QAAQ,mBAAmB,KAAK,gBAAgB,QAAQ,OAAO,YAAY;AAC7E,cAAQ,gBAAgB;AAAA,IAC1B;AACA,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,YAAY,QAAQ,IAAI;AAAA,IACpC,WAAW,QAAQ,SAAS,KAAK,KAAK,WAAW,CAAC,GAAG;AACnD,WAAK,KAAK,aAAa,QAAQ,MAAM,KAAK,KAAK,WAAW,CAAC,CAAC;AAAA,IAC9D;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,MAAM,QAAQ,GAAG;AACf,WAAO,aAAa,MAAM,EAAE,IAAI,MAAM,CAAC;AAAA,EACzC;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,IAAI,KAAK,IAAI,KAAK,KAAK,UAAU,SAAU,MAAM;AACtD,aAAO,MAAM,IAAI;AAAA,IACnB,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA,EAEA,QAAQ;AACN,WAAO,KAAK,KAAK,cAAc,GAAG;AAChC,WAAK,KAAK,YAAY,KAAK,KAAK,SAAS;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,MAAM,OAAO,MAAM,eAAe,MAAM;AACtC,SAAK,eAAe;AACpB,QAAI,YAAY,KAAK,KAAK,UAAU,IAAI;AACxC,QAAI,cAAc;AAChB,kBAAY,YAAY,SAAS;AAAA,IACnC;AACA,WAAO,IAAI,KAAK,YAAY,SAAS;AAAA,EACvC;AAAA;AAAA,EAEA,KAAK,OAAO,MAAM;AAChB,UAAM,WAAW,KAAK,SAAS;AAC/B,QAAI,GAAG;AACP,SAAK,IAAI,GAAG,KAAK,SAAS,QAAQ,IAAI,IAAI,KAAK;AAC7C,YAAM,MAAM,SAAS,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;AACtC,UAAI,MAAM;AACR,iBAAS,CAAC,EAAE,KAAK,OAAO,IAAI;AAAA,MAC9B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,UAAU,QAAQ;AACxB,WAAO,KAAK,IAAI,IAAI,KAAI,OAAO,QAAQ,GAAG,MAAM,CAAC;AAAA,EACnD;AAAA;AAAA,EAEA,QAAQ;AACN,WAAO,MAAM,KAAK,KAAK,UAAU;AAAA,EACnC;AAAA;AAAA,EAEA,IAAI,GAAG;AACL,WAAO,MAAM,KAAK,KAAK,WAAW,CAAC,CAAC;AAAA,EACtC;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK,MAAM,OAAO,KAAK;AAAA,EAChC;AAAA,EACA,KAAK,UAAU,WAAW;AACxB,WAAO,KAAK,IAAI,UAAU,WAAW,IAAI;AAAA,EAC3C;AAAA;AAAA,EAEA,GAAG,IAAI;AACL,QAAI,OAAO,OAAO,eAAe,CAAC,KAAK,KAAK,IAAI;AAC9C,WAAK,KAAK,KAAK,IAAI,KAAK,IAAI;AAAA,IAC9B;AACA,WAAO,KAAK,KAAK,MAAM,EAAE;AAAA,EAC3B;AAAA;AAAA,EAEA,MAAM,SAAS;AACb,WAAO,CAAC,EAAE,MAAM,KAAK,KAAK,KAAK,UAAU,EAAE,QAAQ,QAAQ,IAAI;AAAA,EACjE;AAAA;AAAA,EAEA,OAAO;AACL,WAAO,MAAM,KAAK,KAAK,SAAS;AAAA,EAClC;AAAA;AAAA,EAEA,QAAQ,UAAU;AAChB,UAAM,KAAK,KAAK;AAChB,UAAM,UAAU,GAAG,WAAW,GAAG,mBAAmB,GAAG,qBAAqB,GAAG,sBAAsB,GAAG,yBAAyB,GAAG,oBAAoB;AACxJ,WAAO,WAAW,QAAQ,KAAK,IAAI,QAAQ;AAAA,EAC7C;AAAA;AAAA,EAEA,OAAO,MAAM;AACX,QAAI,SAAS;AACb,QAAI,CAAC,OAAO,KAAK,WAAY,QAAO;AACpC,aAAS,MAAM,OAAO,KAAK,UAAU;AACrC,QAAI,CAAC,KAAM,QAAO;AAClB,OAAG;AACD,UAAI,OAAO,SAAS,WAAW,OAAO,QAAQ,IAAI,IAAI,kBAAkB,KAAM,QAAO;AAAA,IACvF,SAAS,SAAS,MAAM,OAAO,KAAK,UAAU;AAC9C,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,IAAI,SAAS,GAAG;AACd,cAAU,aAAa,OAAO;AAC9B,SAAK,IAAI,SAAS,CAAC;AACnB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,MAAM,QAAQ,GAAG;AACf,WAAO,aAAa,MAAM,EAAE,IAAI,MAAM,CAAC;AAAA,EACzC;AAAA;AAAA,EAEA,SAAS;AACP,QAAI,KAAK,OAAO,GAAG;AACjB,WAAK,OAAO,EAAE,cAAc,IAAI;AAAA,IAClC;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,cAAc,SAAS;AACrB,SAAK,KAAK,YAAY,QAAQ,IAAI;AAClC,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,QAAQ,SAAS;AACf,cAAU,aAAa,OAAO;AAC9B,QAAI,KAAK,KAAK,YAAY;AACxB,WAAK,KAAK,WAAW,aAAa,QAAQ,MAAM,KAAK,IAAI;AAAA,IAC3D;AACA,WAAO;AAAA,EACT;AAAA,EACA,MAAM,YAAY,GAAG,OAAO,MAAM;AAChC,UAAM,SAAS,MAAM;AACrB,UAAM,SAAS,KAAK,KAAK,IAAI;AAC7B,eAAW,KAAK,QAAQ;AACtB,UAAI,OAAO,OAAO,CAAC,MAAM,UAAU;AACjC,eAAO,CAAC,IAAI,KAAK,MAAM,OAAO,CAAC,IAAI,MAAM,IAAI;AAAA,MAC/C;AAAA,IACF;AACA,SAAK,KAAK,MAAM;AAChB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,IAAI,SAAS,UAAU;AACrB,WAAO,KAAK,IAAI,SAAS,UAAU,GAAG;AAAA,EACxC;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,KAAK,GAAG;AAAA,EACjB;AAAA,EACA,MAAM,MAAM;AACV,SAAK,KAAK,cAAc;AACxB,WAAO;AAAA,EACT;AAAA,EACA,KAAK,MAAM;AACT,UAAM,SAAS,KAAK,OAAO;AAC3B,QAAI,CAAC,QAAQ;AACX,aAAO,KAAK,MAAM,IAAI;AAAA,IACxB;AACA,UAAM,YAAY,OAAO,MAAM,IAAI;AACnC,WAAO,OAAO,IAAI,MAAM,SAAS,EAAE,IAAI,IAAI;AAAA,EAC7C;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,KAAK,WAAY;AACpB,WAAK,eAAe;AAAA,IACtB,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,IAAI,SAAS,UAAU,IAAI;AACzB,QAAI,OAAO,YAAY,WAAW;AAChC,WAAK;AACL,iBAAW;AACX,gBAAU;AAAA,IACZ;AACA,QAAI,WAAW,QAAQ,OAAO,YAAY,YAAY;AACpD,iBAAW,YAAY,OAAO,OAAO;AACrC,WAAK,eAAe;AACpB,UAAI,UAAU;AACd,UAAI,WAAW,MAAM;AACnB,kBAAU,MAAM,QAAQ,KAAK,UAAU,IAAI,CAAC;AAC5C,YAAI,UAAU;AACZ,gBAAM,SAAS,QAAQ,OAAO;AAC9B,oBAAU,UAAU;AACpB,cAAI,WAAW,MAAO,QAAO;AAAA,QAC/B;AACA,gBAAQ,KAAK,WAAY;AACvB,gBAAM,SAAS,QAAQ,IAAI;AAC3B,gBAAM,QAAQ,UAAU;AACxB,cAAI,WAAW,OAAO;AACpB,iBAAK,OAAO;AAAA,UACd,WAAW,UAAU,SAAS,OAAO;AACnC,iBAAK,QAAQ,KAAK;AAAA,UACpB;AAAA,QACF,GAAG,IAAI;AAAA,MACT;AACA,aAAO,WAAW,QAAQ,KAAK,YAAY,QAAQ,KAAK;AAAA,IAC1D;AACA,eAAW,YAAY,OAAO,QAAQ;AACtC,UAAM,OAAO,OAAO,WAAW,EAAE;AACjC,UAAM,WAAW,QAAQ,SAAS,uBAAuB;AACzD,SAAK,YAAY;AACjB,aAAS,MAAM,KAAK,SAAS,QAAQ,SAAQ;AAC3C,eAAS,YAAY,KAAK,iBAAiB;AAAA,IAC7C;AACA,UAAM,SAAS,KAAK,OAAO;AAC3B,WAAO,WAAW,KAAK,QAAQ,QAAQ,KAAK,SAAS,KAAK,IAAI,QAAQ;AAAA,EACxE;AACF;AACA,OAAO,KAAK;AAAA,EACV;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,SAAS,KAAK,KAAK;AACnB,IAAM,UAAN,cAAsB,IAAI;AAAA,EACxB,YAAY,MAAM,QAAQ;AACxB,UAAM,MAAM,MAAM;AAClB,SAAK,MAAM,CAAC;AACZ,SAAK,KAAK,WAAW;AACrB,QAAI,KAAK,aAAa,YAAY,KAAK,KAAK,aAAa,YAAY,GAAG;AACtE,WAAK,QAAQ,KAAK,MAAM,KAAK,aAAa,YAAY,CAAC,KAAK,KAAK,MAAM,KAAK,aAAa,YAAY,CAAC,KAAK,CAAC,CAAC;AAAA,IAC/G;AAAA,EACF;AAAA;AAAA,EAEA,OAAO,IAAI,IAAI;AACb,WAAO,KAAK,GAAG,EAAE,EAAE,GAAG,EAAE;AAAA,EAC1B;AAAA;AAAA,EAEA,GAAG,IAAI;AACL,WAAO,MAAM,OAAO,KAAK,EAAE,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,KAAK,KAAK,MAAM,IAAI,CAAC;AAAA,EAChF;AAAA;AAAA,EAEA,GAAG,IAAI;AACL,WAAO,MAAM,OAAO,KAAK,EAAE,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,EAAE,KAAK,KAAK,OAAO,IAAI,CAAC;AAAA,EAClF;AAAA;AAAA,EAEA,OAAO;AACL,UAAM,QAAQ,KAAK,KAAK;AACxB,WAAO,SAAS,MAAM,KAAK;AAAA,EAC7B;AAAA;AAAA,EAEA,MAAM,IAAI,IAAI;AACZ,WAAO,KAAK,GAAG,EAAE,EAAE,GAAG,EAAE;AAAA,EAC1B;AAAA;AAAA,EAEA,GAAG,KAAK,GAAG;AACT,WAAO,KAAK,EAAE,IAAI,UAAU,EAAE,EAAE,KAAK,KAAK,EAAE,CAAC,CAAC;AAAA,EAChD;AAAA;AAAA,EAEA,GAAG,KAAK,GAAG;AACT,WAAO,KAAK,EAAE,IAAI,UAAU,EAAE,EAAE,KAAK,KAAK,EAAE,CAAC,CAAC;AAAA,EAChD;AAAA,EACA,iBAAiB;AACf,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,OAAO,SAAS;AACd,WAAO,KAAK,KAAK,UAAU,OAAO;AAAA,EACpC;AAAA;AAAA,EAEA,KAAK,IAAI,IAAI;AACX,WAAO,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE;AAAA,EACxB;AAAA;AAAA,EAEA,QAAQ,QAAQ,KAAK,KAAK,GAAG;AAC3B,UAAM,aAAa,OAAO,UAAU;AACpC,QAAI,CAAC,YAAY;AACf,cAAQ,aAAa,KAAK;AAAA,IAC5B;AACA,UAAM,UAAU,IAAI,KAAK;AACzB,QAAI,SAAS;AACb,YAAQ,SAAS,OAAO,OAAO,MAAM,OAAO,SAAS,QAAQ,YAAY,OAAO,aAAa,sBAAsB;AACjH,cAAQ,KAAK,MAAM;AACnB,UAAI,CAAC,cAAc,OAAO,SAAS,MAAM,MAAM;AAC7C;AAAA,MACF;AACA,UAAI,cAAc,OAAO,QAAQ,KAAK,GAAG;AACvC;AAAA,MACF;AACA,UAAI,OAAO,SAAS,KAAK,KAAK,EAAE,MAAM;AACpC,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,UAAU,OAAO;AACf,YAAQ,KAAK,KAAK,KAAK;AACvB,QAAI,CAAC,MAAO,QAAO;AACnB,UAAM,KAAK,QAAQ,IAAI,MAAM,SAAS;AACtC,WAAO,IAAI,aAAa,EAAE,CAAC,CAAC,IAAI;AAAA,EAClC;AAAA;AAAA,EAEA,OAAO;AACL,UAAM,IAAI,KAAK,OAAO,SAAS,IAAI,CAAC;AACpC,WAAO,KAAK,EAAE,KAAK;AAAA,EACrB;AAAA;AAAA,EAEA,QAAQ,GAAG;AACT,SAAK,MAAM;AACX,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,KAAK,QAAQ,SAAS;AACpB,UAAM,IAAI,iBAAiB,MAAM,QAAQ,OAAO;AAChD,WAAO,KAAK,MAAM,IAAI,UAAU,EAAE,KAAK,CAAC,EAAE,OAAO,IAAI,UAAU,EAAE,MAAM,CAAC;AAAA,EAC1E;AAAA;AAAA,EAEA,MAAM,QAAQ;AACZ,WAAO,KAAK,KAAK,SAAS,MAAM;AAAA,EAClC;AAAA;AAAA,EAEA,iBAAiB;AACf,mBAAe,MAAM,KAAK,GAAG;AAC7B,WAAO,MAAM,eAAe;AAAA,EAC9B;AAAA;AAAA,EAEA,EAAE,IAAI;AACJ,WAAO,KAAK,KAAK,KAAK,EAAE;AAAA,EAC1B;AAAA;AAAA,EAEA,EAAE,IAAI;AACJ,WAAO,KAAK,KAAK,KAAK,EAAE;AAAA,EAC1B;AACF;AACA,OAAO,SAAS;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,SAAS,SAAS,SAAS;AAC3B,IAAM,QAAQ;AAAA,EACZ,QAAQ,CAAC,SAAS,SAAS,WAAW,WAAW,YAAY,cAAc,aAAa,YAAY;AAAA,EACpG,MAAM,CAAC,SAAS,WAAW,MAAM;AAAA,EACjC,QAAQ,SAAU,GAAG,GAAG;AACtB,WAAO,MAAM,UAAU,IAAI,IAAI,MAAM;AAAA,EACvC;AACF;AACA,CAAC,QAAQ,QAAQ,EAAE,QAAQ,SAAU,GAAG;AACtC,QAAM,YAAY,CAAC;AACnB,MAAI;AACJ,YAAU,CAAC,IAAI,SAAU,GAAG;AAC1B,QAAI,OAAO,MAAM,aAAa;AAC5B,aAAO,KAAK,KAAK,CAAC;AAAA,IACpB;AACA,QAAI,OAAO,MAAM,YAAY,aAAa,SAAS,MAAM,MAAM,CAAC,KAAK,aAAa,SAAS;AACzF,WAAK,KAAK,GAAG,CAAC;AAAA,IAChB,OAAO;AACL,WAAK,IAAI,MAAM,CAAC,EAAE,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,YAAI,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM;AAC1B,eAAK,KAAK,MAAM,OAAO,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA,QACxD;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,kBAAgB,CAAC,WAAW,QAAQ,GAAG,SAAS;AAClD,CAAC;AACD,gBAAgB,CAAC,WAAW,QAAQ,GAAG;AAAA;AAAA,EAErC,QAAQ,SAAU,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;AACpC,QAAI,OAAO,MAAM;AACf,aAAO,IAAI,OAAO,IAAI;AAAA,IACxB;AACA,WAAO,KAAK,KAAK,aAAa,IAAI,OAAO,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,EAC9D;AAAA;AAAA,EAEA,QAAQ,SAAU,OAAO,KAAK,KAAK;AACjC,WAAO,KAAK,UAAU;AAAA,MACpB,QAAQ;AAAA,MACR,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,GAAG,IAAI;AAAA,EACT;AAAA;AAAA,EAEA,MAAM,SAAU,IAAI,IAAI,KAAK,KAAK;AAChC,WAAO,UAAU,WAAW,KAAK,UAAU,WAAW,IAAI,KAAK,UAAU;AAAA,MACvE,MAAM;AAAA,MACN,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,GAAG,IAAI,IAAI,KAAK,UAAU;AAAA,MACxB,MAAM,CAAC,IAAI,EAAE;AAAA,MACb,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,GAAG,IAAI;AAAA,EACT;AAAA,EACA,OAAO,SAAU,KAAK,KAAK,KAAK;AAC9B,WAAO,KAAK,UAAU;AAAA,MACpB,OAAO;AAAA,MACP,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,GAAG,IAAI;AAAA,EACT;AAAA;AAAA,EAEA,OAAO,SAAU,IAAI,IAAI,KAAK,KAAK;AACjC,WAAO,UAAU,WAAW,KAAK,UAAU,WAAW,IAAI,KAAK,UAAU;AAAA,MACvE,OAAO;AAAA,MACP,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,GAAG,IAAI,IAAI,KAAK,UAAU;AAAA,MACxB,OAAO,CAAC,IAAI,EAAE;AAAA,MACd,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,GAAG,IAAI;AAAA,EACT;AAAA;AAAA,EAEA,WAAW,SAAU,IAAI,IAAI;AAC3B,WAAO,KAAK,UAAU;AAAA,MACpB,WAAW,CAAC,IAAI,EAAE;AAAA,IACpB,GAAG,IAAI;AAAA,EACT;AAAA;AAAA,EAEA,UAAU,SAAU,IAAI,IAAI;AAC1B,WAAO,KAAK,UAAU;AAAA,MACpB,UAAU,CAAC,IAAI,EAAE;AAAA,IACnB,GAAG,IAAI;AAAA,EACT;AAAA;AAAA,EAEA,MAAM,SAAU,YAAY,QAAQ,SAAS,UAAU;AACrD,QAAI,aAAa,QAAQ,SAAS,MAAM,IAAI;AAC1C,eAAS;AACT,kBAAY;AAAA,IACd;AACA,WAAO,KAAK,UAAU;AAAA,MACpB,MAAM;AAAA,MACN;AAAA,IACF,GAAG,IAAI;AAAA,EACT;AAAA;AAAA,EAEA,SAAS,SAAU,OAAO;AACxB,WAAO,KAAK,KAAK,WAAW,KAAK;AAAA,EACnC;AACF,CAAC;AACD,gBAAgB,UAAU;AAAA;AAAA,EAExB,QAAQ,SAAU,IAAI,KAAK,IAAI;AAC7B,UAAM,QAAQ,KAAK,YAAY,MAAM;AACrC,WAAO,SAAS,mBAAmB,KAAK,KAAK,KAAK,IAAI,UAAU,EAAE,CAAC,IAAI,KAAK,GAAG,EAAE,EAAE,GAAG,EAAE;AAAA,EAC1F;AACF,CAAC;AACD,gBAAgB,QAAQ;AAAA;AAAA,EAEtB,QAAQ,WAAY;AAClB,WAAO,KAAK,KAAK,eAAe;AAAA,EAClC;AAAA;AAAA,EAEA,SAAS,SAAU,SAAS;AAC1B,WAAO,IAAI,MAAM,KAAK,KAAK,iBAAiB,OAAO,CAAC;AAAA,EACtD;AACF,CAAC;AACD,gBAAgB,CAAC,WAAW,QAAQ,GAAG;AAAA;AAAA,EAErC,MAAM,SAAU,GAAG,GAAG;AACpB,QAAI,OAAO,MAAM,UAAU;AACzB,WAAK,KAAK,EAAG,MAAK,KAAK,GAAG,EAAE,CAAC,CAAC;AAC9B,aAAO;AAAA,IACT;AACA,WAAO,MAAM,YAAY,KAAK,QAAQ,CAAC,IAAI,MAAM,WAAW,KAAK,KAAK,eAAe,CAAC,IAAI,MAAM,UAAU,MAAM,YAAY,MAAM,YAAY,MAAM,aAAa,MAAM,aAAa,MAAM,UAAU,KAAK,KAAK,UAAU,GAAG,CAAC,IAAI,KAAK,KAAK,GAAG,CAAC;AAAA,EAChP;AACF,CAAC;AACD,IAAM,UAAU,CAAC,SAAS,YAAY,aAAa,WAAW,aAAa,YAAY,aAAa,cAAc,cAAc,cAAc,aAAa,cAAc,YAAY,eAAe,eAAe,SAAS,eAAe,eAAe,aAAa,gBAAgB,eAAe,EAAE,OAAO,SAAU,MAAM,OAAO;AACpU,QAAM,KAAK,SAAU,GAAG;AACtB,QAAI,MAAM,MAAM;AACd,WAAK,IAAI,KAAK;AAAA,IAChB,OAAO;AACL,WAAK,GAAG,OAAO,CAAC;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AACA,OAAK,KAAK,IAAI;AACd,SAAO;AACT,GAAG,CAAC,CAAC;AACL,gBAAgB,WAAW,OAAO;AAClC,SAAS,cAAc;AACrB,SAAO,KAAK,KAAK,aAAa,IAAI;AACpC;AACA,SAAS,YAAY;AACnB,QAAM,UAAU,KAAK,KAAK,WAAW,KAAK,IAAI,MAAM,UAAU,EAAE,MAAM,GAAG,EAAE,EAAE,IAAI,SAAU,KAAK;AAC9F,UAAM,KAAK,IAAI,KAAK,EAAE,MAAM,GAAG;AAC/B,WAAO,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE,MAAM,SAAS,EAAE,IAAI,SAAU,MAAM;AACxD,aAAO,WAAW,IAAI;AAAA,IACxB,CAAC,CAAC;AAAA,EACJ,CAAC,EAAE,QAAQ,EAAE,OAAO,SAAU,SAAS,YAAY;AACjD,QAAI,WAAW,CAAC,MAAM,UAAU;AAC9B,aAAO,QAAQ,UAAU,OAAO,UAAU,WAAW,CAAC,CAAC,CAAC;AAAA,IAC1D;AACA,WAAO,QAAQ,WAAW,CAAC,CAAC,EAAE,MAAM,SAAS,WAAW,CAAC,CAAC;AAAA,EAC5D,GAAG,IAAI,OAAO,CAAC;AACf,SAAO;AACT;AACA,SAAS,SAAS,QAAQ,GAAG;AAC3B,MAAI,SAAS,OAAQ,QAAO;AAC5B,MAAI,cAAc,KAAK,IAAI,EAAG,QAAO,KAAK,MAAM,QAAQ,CAAC;AACzD,QAAM,OAAO,KAAK,UAAU;AAC5B,QAAM,OAAO,OAAO,UAAU,EAAE,QAAQ;AACxC,OAAK,MAAM,QAAQ,CAAC,EAAE,YAAY,EAAE,UAAU,KAAK,SAAS,IAAI,CAAC;AACjE,SAAO;AACT;AACA,SAAS,OAAO,GAAG;AACjB,SAAO,KAAK,SAAS,KAAK,KAAK,GAAG,CAAC;AACrC;AACA,SAAS,UAAU,GAAG,UAAU;AAC9B,MAAI,KAAK,QAAQ,OAAO,MAAM,UAAU;AACtC,UAAM,aAAa,IAAI,OAAO,IAAI,EAAE,UAAU;AAC9C,WAAO,KAAK,OAAO,aAAa,WAAW,CAAC;AAAA,EAC9C;AACA,MAAI,CAAC,OAAO,aAAa,CAAC,GAAG;AAC3B,QAAI,iCACC,IADD;AAAA,MAEF,QAAQ,UAAU,GAAG,IAAI;AAAA,IAC3B;AAAA,EACF;AACA,QAAM,gBAAgB,aAAa,OAAO,OAAO,YAAY;AAC7D,QAAM,SAAS,IAAI,OAAO,aAAa,EAAE,UAAU,CAAC;AACpD,SAAO,KAAK,KAAK,aAAa,MAAM;AACtC;AACA,gBAAgB,WAAW;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAM,YAAN,MAAM,mBAAkB,QAAQ;AAAA,EAC9B,UAAU;AACR,SAAK,KAAK,WAAY;AACpB,UAAI,gBAAgB,YAAW;AAC7B,eAAO,KAAK,QAAQ,EAAE,QAAQ;AAAA,MAChC;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,SAAS,KAAK,OAAO,GAAG,QAAQ,OAAO,MAAM,IAAI,GAAG;AAC1D,YAAQ,UAAU,KAAK,OAAO,SAAS,EAAE,SAAS;AAClD,SAAK,KAAK,SAAU,GAAG,UAAU;AAC/B,aAAO,SAAS,SAAS,SAAS,IAAI,CAAC,EAAE,SAAS,QAAQ,KAAK;AAAA,IACjE,CAAC;AACD,WAAO,KAAK,OAAO;AAAA,EACrB;AACF;AACA,SAAS,WAAW,WAAW;AAC/B,IAAM,OAAN,cAAmB,UAAU;AAAA,EAC3B,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,QAAQ,IAAI,GAAG,MAAM;AAAA,EACvC;AAAA,EACA,UAAU;AACR,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,WAAO;AAAA,EACT;AACF;AACA,SAAS,MAAM,MAAM;AACrB,IAAM,QAAN,cAAoB,QAAQ;AAAC;AAC7B,SAAS,OAAO,OAAO;AACvB,SAAS,GAAG,KAAK;AACf,SAAO,KAAK,KAAK,MAAM,GAAG;AAC5B;AACA,SAAS,GAAG,KAAK;AACf,SAAO,KAAK,KAAK,MAAM,GAAG;AAC5B;AACA,SAAS,IAAI,IAAI;AACf,SAAO,MAAM,OAAO,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,KAAK,KAAK,GAAG,CAAC;AACpE;AACA,SAAS,IAAI,IAAI;AACf,SAAO,MAAM,OAAO,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,KAAK,KAAK,GAAG,CAAC;AACpE;AACA,SAAS,KAAK,IAAI;AAChB,SAAO,KAAK,KAAK,MAAM,EAAE;AAC3B;AACA,SAAS,KAAK,IAAI;AAChB,SAAO,KAAK,KAAK,MAAM,EAAE;AAC3B;AACA,SAAS,QAAQ,QAAQ;AACvB,SAAO,UAAU,OAAO,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,UAAU,MAAM,EAAE,OAAO,CAAC,CAAC;AACjF;AACA,SAAS,SAAS,SAAS;AACzB,SAAO,WAAW,OAAO,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,UAAU,OAAO,EAAE,OAAO,CAAC,CAAC;AACnF;AACA,IAAM,UAAyB,OAAO,OAAsB,OAAO,eAAe;AAAA,EAChF,WAAW;AAAA,EACX,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA,OAAO;AAAA,EACP,GAAG;AAAA,EACH,GAAG;AACL,GAAG,OAAO,aAAa;AAAA,EACrB,OAAO;AACT,CAAC,CAAC;AACF,IAAM,UAAN,cAAsB,MAAM;AAAA,EAC1B,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,WAAW,IAAI,GAAG,MAAM;AAAA,EAC1C;AAAA,EACA,KAAK,QAAQ,SAAS;AACpB,UAAM,IAAI,iBAAiB,MAAM,QAAQ,OAAO;AAChD,WAAO,KAAK,GAAG,IAAI,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,GAAG,IAAI,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAAA,EACvF;AACF;AACA,OAAO,SAAS,OAAO;AACvB,gBAAgB,aAAa;AAAA;AAAA,EAE3B,SAAS,kBAAkB,SAAU,SAAS,GAAG,UAAU,QAAQ;AACjE,WAAO,KAAK,IAAI,IAAI,QAAQ,CAAC,EAAE,KAAK,QAAQ,OAAO,EAAE,KAAK,GAAG,CAAC;AAAA,EAChE,CAAC;AACH,CAAC;AACD,SAAS,SAAS,SAAS;AAC3B,IAAM,WAAN,cAAuB,IAAI;AAAA,EACzB,YAAY,OAAO,QAAQ,SAAS,uBAAuB,GAAG;AAC5D,UAAM,IAAI;AAAA,EACZ;AAAA;AAAA,EAEA,IAAI,SAAS,UAAU,IAAI;AACzB,QAAI,OAAO,YAAY,WAAW;AAChC,WAAK;AACL,iBAAW;AACX,gBAAU;AAAA,IACZ;AACA,QAAI,WAAW,QAAQ,OAAO,YAAY,YAAY;AACpD,YAAM,UAAU,IAAI,IAAI,OAAO,WAAW,EAAE,CAAC;AAC7C,cAAQ,IAAI,KAAK,KAAK,UAAU,IAAI,CAAC;AACrC,aAAO,QAAQ,IAAI,OAAO,EAAE;AAAA,IAC9B;AACA,WAAO,MAAM,IAAI,SAAS,OAAO,EAAE;AAAA,EACrC;AACF;AACA,SAAS,UAAU,UAAU;AAC7B,SAAS,KAAK,IAAI,IAAI;AACpB,UAAQ,KAAK,YAAY,MAAM,SAAS,mBAAmB,KAAK,KAAK;AAAA,IACnE,IAAI,IAAI,UAAU,EAAE;AAAA,IACpB,IAAI,IAAI,UAAU,EAAE;AAAA,EACtB,CAAC,IAAI,KAAK,KAAK;AAAA,IACb,IAAI,IAAI,UAAU,EAAE;AAAA,IACpB,IAAI,IAAI,UAAU,EAAE;AAAA,EACtB,CAAC;AACH;AACA,SAAS,GAAG,IAAI,IAAI;AAClB,UAAQ,KAAK,YAAY,MAAM,SAAS,mBAAmB,KAAK,KAAK;AAAA,IACnE,IAAI,IAAI,UAAU,EAAE;AAAA,IACpB,IAAI,IAAI,UAAU,EAAE;AAAA,EACtB,CAAC,IAAI,KAAK,KAAK;AAAA,IACb,IAAI,IAAI,UAAU,EAAE;AAAA,IACpB,IAAI,IAAI,UAAU,EAAE;AAAA,EACtB,CAAC;AACH;AACA,IAAM,aAA4B,OAAO,OAAsB,OAAO,eAAe;AAAA,EACnF,WAAW;AAAA,EACX;AAAA,EACA;AACF,GAAG,OAAO,aAAa;AAAA,EACrB,OAAO;AACT,CAAC,CAAC;AACF,IAAM,WAAN,cAAuB,UAAU;AAAA,EAC/B,YAAY,MAAM,QAAQ;AACxB,UAAM,UAAU,OAAO,YAAY,OAAO,SAAS,WAAW,OAAO,IAAI,GAAG,MAAM;AAAA,EACpF;AAAA;AAAA,EAEA,KAAK,GAAG,GAAG,GAAG;AACZ,QAAI,MAAM,YAAa,KAAI;AAC3B,WAAO,MAAM,KAAK,GAAG,GAAG,CAAC;AAAA,EAC3B;AAAA,EACA,OAAO;AACL,WAAO,IAAI,IAAI;AAAA,EACjB;AAAA,EACA,UAAU;AACR,WAAO,SAAS,gBAAgB,KAAK,GAAG,IAAI,GAAG;AAAA,EACjD;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA;AAAA,EAEA,OAAO,OAAO;AACZ,SAAK,MAAM;AACX,QAAI,OAAO,UAAU,YAAY;AAC/B,YAAM,KAAK,MAAM,IAAI;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,MAAM;AACJ,WAAO,UAAU,KAAK,GAAG,IAAI;AAAA,EAC/B;AACF;AACA,OAAO,UAAU,UAAU;AAC3B,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,YAAY,MAAM;AAChB,aAAO,KAAK,KAAK,EAAE,SAAS,GAAG,IAAI;AAAA,IACrC;AAAA,EACF;AAAA;AAAA,EAEA,MAAM;AAAA,IACJ,UAAU,kBAAkB,SAAU,MAAM,OAAO;AACjD,aAAO,KAAK,IAAI,IAAI,SAAS,IAAI,CAAC,EAAE,OAAO,KAAK;AAAA,IAClD,CAAC;AAAA,EACH;AACF,CAAC;AACD,SAAS,UAAU,UAAU;AAC7B,IAAM,UAAN,cAAsB,UAAU;AAAA;AAAA,EAE9B,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,WAAW,IAAI,GAAG,MAAM;AAAA,EAC1C;AAAA;AAAA,EAEA,KAAK,GAAG,GAAG,GAAG;AACZ,QAAI,MAAM,YAAa,KAAI;AAC3B,WAAO,MAAM,KAAK,GAAG,GAAG,CAAC;AAAA,EAC3B;AAAA,EACA,OAAO;AACL,WAAO,IAAI,IAAI;AAAA,EACjB;AAAA,EACA,UAAU;AACR,WAAO,SAAS,gBAAgB,KAAK,GAAG,IAAI,GAAG;AAAA,EACjD;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA;AAAA,EAEA,OAAO,OAAO;AACZ,SAAK,MAAM;AACX,QAAI,OAAO,UAAU,YAAY;AAC/B,YAAM,KAAK,MAAM,IAAI;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,MAAM;AACJ,WAAO,UAAU,KAAK,GAAG,IAAI;AAAA,EAC/B;AACF;AACA,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,WAAW,MAAM;AACf,aAAO,KAAK,KAAK,EAAE,QAAQ,GAAG,IAAI;AAAA,IACpC;AAAA,EACF;AAAA,EACA,MAAM;AAAA,IACJ,SAAS,kBAAkB,SAAU,QAAQ,SAAS,OAAO;AAC3D,aAAO,KAAK,IAAI,IAAI,QAAQ,CAAC,EAAE,OAAO,KAAK,EAAE,KAAK;AAAA,QAChD,GAAG;AAAA,QACH,GAAG;AAAA,QACH,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,cAAc;AAAA,MAChB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF,CAAC;AACD,SAAS,SAAS,SAAS;AAC3B,IAAI,UAAU,MAAM,eAAe,MAAM;AAAA,EACvC,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,SAAS,IAAI,GAAG,MAAM;AAAA,EACxC;AAAA;AAAA,EAEA,KAAK,KAAK,UAAU;AAClB,QAAI,CAAC,IAAK,QAAO;AACjB,UAAM,MAAM,IAAI,QAAQ,OAAO,MAAM;AACrC,OAAG,KAAK,QAAQ,SAAU,GAAG;AAC3B,YAAM,IAAI,KAAK,OAAO,OAAO;AAC7B,UAAI,KAAK,MAAM,MAAM,KAAK,KAAK,OAAO,MAAM,GAAG;AAC7C,aAAK,KAAK,IAAI,OAAO,IAAI,MAAM;AAAA,MACjC;AACA,UAAI,aAAa,SAAS;AACxB,YAAI,EAAE,MAAM,MAAM,KAAK,EAAE,OAAO,MAAM,GAAG;AACvC,YAAE,KAAK,KAAK,MAAM,GAAG,KAAK,OAAO,CAAC;AAAA,QACpC;AAAA,MACF;AACA,UAAI,OAAO,aAAa,YAAY;AAClC,iBAAS,KAAK,MAAM,CAAC;AAAA,MACvB;AAAA,IACF,GAAG,IAAI;AACP,OAAG,KAAK,cAAc,WAAY;AAChC,UAAI,GAAG;AAAA,IACT,CAAC;AACD,WAAO,KAAK,KAAK,QAAQ,IAAI,MAAM,KAAK,KAAK;AAAA,EAC/C;AACF;AACA,iBAAiB,SAAU,OAAO,KAAK,OAAO;AAC5C,MAAI,UAAU,UAAU,UAAU,UAAU;AAC1C,QAAI,QAAQ,KAAK,GAAG,GAAG;AACrB,YAAM,MAAM,KAAK,EAAE,KAAK,EAAE,MAAM,GAAG;AAAA,IACrC;AAAA,EACF;AACA,MAAI,eAAe,SAAS;AAC1B,UAAM,MAAM,KAAK,EAAE,KAAK,EAAE,QAAQ,GAAG,GAAG,aAAW;AACjD,cAAQ,IAAI,GAAG;AAAA,IACjB,CAAC;AAAA,EACH;AACA,SAAO;AACT,CAAC;AACD,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,OAAO,kBAAkB,SAAU,QAAQ,UAAU;AACnD,aAAO,KAAK,IAAI,IAAI,QAAQ,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,QAAQ,QAAQ;AAAA,IACjE,CAAC;AAAA,EACH;AACF,CAAC;AACD,SAAS,SAAS,OAAO;AACzB,IAAM,aAAN,cAAyB,SAAS;AAAA;AAAA,EAEhC,OAAO;AACL,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,OAAO;AACX,SAAK,QAAQ,SAAU,IAAI;AACzB,aAAO,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI;AAC3B,aAAO,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI;AAC3B,aAAO,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI;AAC3B,aAAO,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI;AAAA,IAC7B,CAAC;AACD,WAAO,IAAI,IAAI,MAAM,MAAM,OAAO,MAAM,OAAO,IAAI;AAAA,EACrD;AAAA;AAAA,EAEA,KAAK,IAAI,IAAI;AACX,UAAM,MAAM,KAAK,KAAK;AACtB,UAAM,IAAI;AACV,UAAM,IAAI;AACV,QAAI,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,GAAG;AAC5B,eAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,aAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE;AAAA,MAC7C;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,MAAM,SAAS,CAAC,GAAG,CAAC,GAAG;AACrB,UAAM,SAAS,CAAC;AAChB,QAAI,kBAAkB,OAAO;AAC3B,eAAS,MAAM,UAAU,OAAO,MAAM,CAAC,GAAG,MAAM;AAAA,IAClD,OAAO;AACL,eAAS,OAAO,KAAK,EAAE,MAAM,SAAS,EAAE,IAAI,UAAU;AAAA,IACxD;AACA,QAAI,OAAO,SAAS,MAAM,EAAG,QAAO,IAAI;AACxC,aAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,IAAI,IAAI,GAAG;AACvD,aAAO,KAAK,CAAC,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC,CAAC;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,KAAK,QAAQ,SAAS;AACpB,QAAI;AACJ,UAAM,MAAM,KAAK,KAAK;AACtB,SAAK,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACrC,UAAI,IAAI,MAAO,MAAK,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAK,SAAS,IAAI,QAAQ,IAAI;AAC5E,UAAI,IAAI,OAAQ,MAAK,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAK,UAAU,IAAI,SAAS,IAAI;AAAA,IACjF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,IAAI,KAAK,CAAC,EAAE,CAAC;AAAA,MACb,IAAI,KAAK,CAAC,EAAE,CAAC;AAAA,MACb,IAAI,KAAK,CAAC,EAAE,CAAC;AAAA,MACb,IAAI,KAAK,CAAC,EAAE,CAAC;AAAA,IACf;AAAA,EACF;AAAA;AAAA,EAEA,WAAW;AACT,UAAM,SAAS,CAAC;AAChB,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,KAAK;AAC7C,aAAO,KAAK,KAAK,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,IAC/B;AACA,WAAO,OAAO,KAAK,GAAG;AAAA,EACxB;AAAA,EACA,UAAU,GAAG;AACX,WAAO,KAAK,MAAM,EAAE,WAAW,CAAC;AAAA,EAClC;AAAA;AAAA,EAEA,WAAW,GAAG;AACZ,QAAI,CAAC,OAAO,aAAa,CAAC,GAAG;AAC3B,UAAI,IAAI,OAAO,CAAC;AAAA,IAClB;AACA,aAAS,IAAI,KAAK,QAAQ,OAAM;AAC9B,YAAM,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC;AACvB,WAAK,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,KAAK,EAAE;AACrC,WAAK,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,KAAK,EAAE;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,aAAa;AACnB,SAAS,IAAI,IAAI;AACf,SAAO,MAAM,OAAO,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,EAAE,CAAC;AACjE;AACA,SAAS,IAAI,IAAI;AACf,SAAO,MAAM,OAAO,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK,KAAK,EAAE,GAAG,EAAE;AACjE;AACA,SAAS,QAAQ,QAAQ;AACvB,QAAM,IAAI,KAAK,KAAK;AACpB,SAAO,UAAU,OAAO,EAAE,QAAQ,KAAK,KAAK,QAAQ,EAAE,MAAM;AAC9D;AACA,SAAS,SAAS,SAAS;AACzB,QAAM,IAAI,KAAK,KAAK;AACpB,SAAO,WAAW,OAAO,EAAE,SAAS,KAAK,KAAK,EAAE,OAAO,OAAO;AAChE;AACA,IAAM,UAAyB,OAAO,OAAsB,OAAO,eAAe;AAAA,EAChF,WAAW;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,GAAG;AAAA,EACH,GAAG;AACL,GAAG,OAAO,aAAa;AAAA,EACrB,OAAO;AACT,CAAC,CAAC;AACF,IAAM,OAAN,cAAmB,MAAM;AAAA;AAAA,EAEvB,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,QAAQ,IAAI,GAAG,MAAM;AAAA,EACvC;AAAA;AAAA,EAEA,QAAQ;AACN,WAAO,IAAI,WAAW,CAAC,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC;AAAA,EAChG;AAAA;AAAA,EAEA,KAAK,IAAI,IAAI;AACX,WAAO,KAAK,KAAK,KAAK,MAAM,EAAE,KAAK,IAAI,EAAE,EAAE,OAAO,CAAC;AAAA,EACrD;AAAA;AAAA,EAEA,KAAK,IAAI,IAAI,IAAI,IAAI;AACnB,QAAI,MAAM,MAAM;AACd,aAAO,KAAK,MAAM;AAAA,IACpB,WAAW,OAAO,OAAO,aAAa;AACpC,WAAK;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,OAAO;AACL,WAAK,IAAI,WAAW,EAAE,EAAE,OAAO;AAAA,IACjC;AACA,WAAO,KAAK,KAAK,EAAE;AAAA,EACrB;AAAA;AAAA,EAEA,KAAK,QAAQ,SAAS;AACpB,UAAM,IAAI,iBAAiB,MAAM,QAAQ,OAAO;AAChD,WAAO,KAAK,KAAK,KAAK,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;AAAA,EAChE;AACF;AACA,OAAO,MAAM,OAAO;AACpB,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,MAAM,kBAAkB,YAAa,MAAM;AACzC,aAAO,KAAK,UAAU,KAAK,MAAM,KAAK,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,OAAO,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,IAC9F,CAAC;AAAA,EACH;AACF,CAAC;AACD,SAAS,MAAM,MAAM;AACrB,IAAM,SAAN,cAAqB,UAAU;AAAA;AAAA,EAE7B,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,UAAU,IAAI,GAAG,MAAM;AAAA,EACzC;AAAA;AAAA,EAEA,OAAO,SAAS;AACd,WAAO,KAAK,KAAK,gBAAgB,OAAO;AAAA,EAC1C;AAAA,EACA,OAAO,QAAQ;AACb,WAAO,KAAK,KAAK,UAAU,MAAM;AAAA,EACnC;AAAA;AAAA,EAEA,IAAI,IAAI,IAAI;AACV,WAAO,KAAK,KAAK,QAAQ,EAAE,EAAE,KAAK,QAAQ,EAAE;AAAA,EAC9C;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,UAAU,KAAK,GAAG,IAAI;AAAA,EAC/B;AAAA;AAAA,EAEA,OAAO,OAAO;AACZ,SAAK,MAAM;AACX,QAAI,OAAO,UAAU,YAAY;AAC/B,YAAM,KAAK,MAAM,IAAI;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,MAAM,QAAQ;AACZ,WAAO,KAAK,KAAK,eAAe,MAAM;AAAA,EACxC;AACF;AACA,gBAAgB;AAAA,EACd,WAAW;AAAA,IACT,UAAU,MAAM;AACd,aAAO,KAAK,KAAK,EAAE,OAAO,GAAG,IAAI;AAAA,IACnC;AAAA,EACF;AAAA,EACA,MAAM;AAAA;AAAA,IAEJ,QAAQ,kBAAkB,SAAU,QAAQ,SAAS,OAAO;AAC1D,aAAO,KAAK,IAAI,IAAI,OAAO,CAAC,EAAE,KAAK,QAAQ,OAAO,EAAE,IAAI,SAAS,GAAG,UAAU,CAAC,EAAE,QAAQ,GAAG,GAAG,QAAQ,OAAO,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,KAAK;AAAA,IACrJ,CAAC;AAAA,EACH;AAAA,EACA,QAAQ;AAAA;AAAA,IAEN,OAAO,QAAQ,QAAQ,SAAS,OAAO;AACrC,UAAI,QAAQ,CAAC,QAAQ;AACrB,UAAI,WAAW,MAAO,OAAM,KAAK,MAAM;AACvC,cAAQ,MAAM,KAAK,GAAG;AACtB,eAAS,UAAU,CAAC,aAAa,SAAS,UAAU,CAAC,IAAI,KAAK,KAAK,EAAE,OAAO,QAAQ,SAAS,KAAK;AAClG,aAAO,KAAK,KAAK,OAAO,MAAM;AAAA,IAChC;AAAA,EACF;AACF,CAAC;AACD,SAAS,QAAQ,QAAQ;AACzB,SAAS,iBAAiB,GAAG,GAAG;AAC9B,SAAO,SAAU,GAAG;AAClB,QAAI,KAAK,KAAM,QAAO,KAAK,CAAC;AAC5B,SAAK,CAAC,IAAI;AACV,QAAI,EAAG,GAAE,KAAK,IAAI;AAClB,WAAO;AAAA,EACT;AACF;AACA,IAAM,SAAS;AAAA,EACb,KAAK,SAAU,KAAK;AAClB,WAAO;AAAA,EACT;AAAA,EACA,MAAM,SAAU,KAAK;AACnB,WAAO,CAAC,KAAK,IAAI,MAAM,KAAK,EAAE,IAAI,IAAI;AAAA,EACxC;AAAA,EACA,KAAK,SAAU,KAAK;AAClB,WAAO,KAAK,IAAI,MAAM,KAAK,KAAK,CAAC;AAAA,EACnC;AAAA,EACA,KAAK,SAAU,KAAK;AAClB,WAAO,CAAC,KAAK,IAAI,MAAM,KAAK,KAAK,CAAC,IAAI;AAAA,EACxC;AAAA,EACA,QAAQ,SAAU,IAAI,IAAI,IAAI,IAAI;AAChC,WAAO,SAAU,GAAG;AAClB,UAAI,IAAI,GAAG;AACT,YAAI,KAAK,GAAG;AACV,iBAAO,KAAK,KAAK;AAAA,QACnB,WAAW,KAAK,GAAG;AACjB,iBAAO,KAAK,KAAK;AAAA,QACnB,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,WAAW,IAAI,GAAG;AAChB,YAAI,KAAK,GAAG;AACV,kBAAQ,IAAI,OAAO,IAAI,MAAM,KAAK,KAAK,OAAO,IAAI;AAAA,QACpD,WAAW,KAAK,GAAG;AACjB,kBAAQ,IAAI,OAAO,IAAI,MAAM,KAAK,KAAK,OAAO,IAAI;AAAA,QACpD,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,OAAO;AACL,eAAO,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK;AAAA,MACtE;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,OAAO,SAAU,OAAO,eAAe,OAAO;AAC5C,mBAAe,aAAa,MAAM,GAAG,EAAE,QAAQ,EAAE,CAAC;AAClD,QAAI,QAAQ;AACZ,QAAI,iBAAiB,QAAQ;AAC3B,QAAE;AAAA,IACJ,WAAW,iBAAiB,QAAQ;AAClC,QAAE;AAAA,IACJ;AACA,WAAO,CAAC,GAAG,aAAa,UAAU;AAChC,UAAI,OAAO,KAAK,MAAM,IAAI,KAAK;AAC/B,YAAM,UAAU,IAAI,OAAO,MAAM;AACjC,UAAI,iBAAiB,WAAW,iBAAiB,QAAQ;AACvD,UAAE;AAAA,MACJ;AACA,UAAI,cAAc,SAAS;AACzB,UAAE;AAAA,MACJ;AACA,UAAI,KAAK,KAAK,OAAO,GAAG;AACtB,eAAO;AAAA,MACT;AACA,UAAI,KAAK,KAAK,OAAO,OAAO;AAC1B,eAAO;AAAA,MACT;AACA,aAAO,OAAO;AAAA,IAChB;AAAA,EACF;AACF;AACA,IAAM,UAAN,MAAc;AAAA,EACZ,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,IAAM,OAAN,cAAmB,QAAQ;AAAA,EACzB,YAAY,KAAK,SAAS,MAAM;AAC9B,UAAM;AACN,SAAK,OAAO,OAAO,EAAE,KAAK;AAAA,EAC5B;AAAA,EACA,KAAK,OAAO,KAAK,KAAK;AACpB,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO,MAAM,IAAI,QAAQ;AAAA,IAC3B;AACA,WAAO,SAAS,MAAM,SAAS,KAAK,KAAK,GAAG;AAAA,EAC9C;AACF;AACA,IAAM,aAAN,cAAyB,QAAQ;AAAA,EAC/B,YAAY,IAAI;AACd,UAAM;AACN,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,KAAK,GAAG;AACN,WAAO,EAAE;AAAA,EACX;AAAA,EACA,KAAK,SAAS,QAAQ,IAAI,GAAG;AAC3B,WAAO,KAAK,QAAQ,SAAS,QAAQ,IAAI,CAAC;AAAA,EAC5C;AACF;AACA,SAAS,cAAc;AACrB,QAAM,YAAY,KAAK,aAAa,OAAO;AAC3C,QAAM,YAAY,KAAK,cAAc;AACrC,QAAM,MAAM;AACZ,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK,IAAI,YAAY,MAAM,GAAG;AACzC,QAAM,OAAO,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAC9C,QAAM,KAAK,OAAO,OAAO;AACzB,OAAK,IAAI,IAAI,OAAO;AACpB,OAAK,IAAI,KAAK;AAChB;AACA,IAAM,SAAN,cAAqB,WAAW;AAAA,EAC9B,YAAY,WAAW,KAAK,YAAY,GAAG;AACzC,UAAM;AACN,SAAK,SAAS,QAAQ,EAAE,UAAU,SAAS;AAAA,EAC7C;AAAA,EACA,KAAK,SAAS,QAAQ,IAAI,GAAG;AAC3B,QAAI,OAAO,YAAY,SAAU,QAAO;AACxC,MAAE,OAAO,OAAO;AAChB,QAAI,OAAO,SAAU,QAAO;AAC5B,QAAI,OAAO,EAAG,QAAO;AACrB,QAAI,KAAK,IAAK,MAAK;AACnB,UAAM;AACN,UAAM,WAAW,EAAE,YAAY;AAC/B,UAAM,eAAe,CAAC,KAAK,IAAI,WAAW,KAAK,KAAK,UAAU;AAC9D,UAAM,cAAc,UAAU,WAAW,KAAK,eAAe,KAAK,KAAK;AACvE,MAAE,WAAW,WAAW,eAAe;AACvC,MAAE,OAAO,KAAK,IAAI,SAAS,WAAW,IAAI,KAAK,IAAI,QAAQ,IAAI;AAC/D,WAAO,EAAE,OAAO,SAAS;AAAA,EAC3B;AACF;AACA,OAAO,QAAQ;AAAA,EACb,UAAU,iBAAiB,aAAa,WAAW;AAAA,EACnD,WAAW,iBAAiB,cAAc,WAAW;AACvD,CAAC;AACD,IAAM,MAAN,cAAkB,WAAW;AAAA,EAC3B,YAAY,IAAI,KAAK,IAAI,MAAM,IAAI,GAAG,SAAS,KAAK;AAClD,UAAM;AACN,SAAK,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,MAAM;AAAA,EACnC;AAAA,EACA,KAAK,SAAS,QAAQ,IAAI,GAAG;AAC3B,QAAI,OAAO,YAAY,SAAU,QAAO;AACxC,MAAE,OAAO,OAAO;AAChB,QAAI,OAAO,SAAU,QAAO;AAC5B,QAAI,OAAO,EAAG,QAAO;AACrB,UAAM,IAAI,SAAS;AACnB,QAAI,KAAK,EAAE,YAAY,KAAK,IAAI;AAChC,UAAM,KAAK,KAAK,EAAE,SAAS,MAAM;AACjC,UAAM,SAAS,KAAK;AACpB,QAAI,WAAW,OAAO;AACpB,UAAI,KAAK,IAAI,CAAC,QAAQ,KAAK,IAAI,GAAG,MAAM,CAAC;AAAA,IAC3C;AACA,MAAE,QAAQ;AACV,MAAE,WAAW;AACb,MAAE,OAAO,KAAK,IAAI,CAAC,IAAI;AACvB,WAAO,EAAE,OAAO,SAAS,WAAW,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI;AAAA,EACzE;AACF;AACA,OAAO,KAAK;AAAA,EACV,QAAQ,iBAAiB,SAAS;AAAA,EAClC,GAAG,iBAAiB,GAAG;AAAA,EACvB,GAAG,iBAAiB,GAAG;AAAA,EACvB,GAAG,iBAAiB,GAAG;AACzB,CAAC;AACD,IAAM,oBAAoB;AAAA,EACxB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACL;AACA,IAAM,eAAe;AAAA,EACnB,GAAG,SAAU,GAAG,GAAG,IAAI;AACrB,MAAE,IAAI,GAAG,IAAI,EAAE,CAAC;AAChB,MAAE,IAAI,GAAG,IAAI,EAAE,CAAC;AAChB,WAAO,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;AAAA,EACvB;AAAA,EACA,GAAG,SAAU,GAAG,GAAG;AACjB,MAAE,IAAI,EAAE,CAAC;AACT,MAAE,IAAI,EAAE,CAAC;AACT,WAAO,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EACzB;AAAA,EACA,GAAG,SAAU,GAAG,GAAG;AACjB,MAAE,IAAI,EAAE,CAAC;AACT,WAAO,CAAC,KAAK,EAAE,CAAC,CAAC;AAAA,EACnB;AAAA,EACA,GAAG,SAAU,GAAG,GAAG;AACjB,MAAE,IAAI,EAAE,CAAC;AACT,WAAO,CAAC,KAAK,EAAE,CAAC,CAAC;AAAA,EACnB;AAAA,EACA,GAAG,SAAU,GAAG,GAAG;AACjB,MAAE,IAAI,EAAE,CAAC;AACT,MAAE,IAAI,EAAE,CAAC;AACT,WAAO,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EACjD;AAAA,EACA,GAAG,SAAU,GAAG,GAAG;AACjB,MAAE,IAAI,EAAE,CAAC;AACT,MAAE,IAAI,EAAE,CAAC;AACT,WAAO,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EACrC;AAAA,EACA,GAAG,SAAU,GAAG,GAAG;AACjB,MAAE,IAAI,EAAE,CAAC;AACT,MAAE,IAAI,EAAE,CAAC;AACT,WAAO,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EACrC;AAAA,EACA,GAAG,SAAU,GAAG,GAAG;AACjB,MAAE,IAAI,EAAE,CAAC;AACT,MAAE,IAAI,EAAE,CAAC;AACT,WAAO,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EACzB;AAAA,EACA,GAAG,SAAU,GAAG,GAAG,IAAI;AACrB,MAAE,IAAI,GAAG;AACT,MAAE,IAAI,GAAG;AACT,WAAO,CAAC,GAAG;AAAA,EACb;AAAA,EACA,GAAG,SAAU,GAAG,GAAG;AACjB,MAAE,IAAI,EAAE,CAAC;AACT,MAAE,IAAI,EAAE,CAAC;AACT,WAAO,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EACvD;AACF;AACA,IAAM,aAAa,aAAa,MAAM,EAAE;AACxC,SAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,eAAa,WAAW,CAAC,CAAC,IAAmB,yBAAU,IAAI;AACzD,WAAO,SAAU,GAAG,GAAG,IAAI;AACzB,UAAI,OAAO,IAAK,GAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE;AAAA,eAAW,OAAO,IAAK,GAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE;AAAA,eAAW,OAAO,KAAK;AAC5F,UAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE;AAChB,UAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE;AAAA,MAClB,OAAO;AACL,iBAAS,IAAI,GAAG,KAAK,EAAE,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC1C,YAAE,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,IAAI,EAAE,IAAI,EAAE;AAAA,QACjC;AAAA,MACF;AACA,aAAO,aAAa,EAAE,EAAE,GAAG,GAAG,EAAE;AAAA,IAClC;AAAA,EACF,EAAE,WAAW,CAAC,EAAE,YAAY,CAAC;AAC/B;AACA,SAAS,YAAY,SAAS;AAC5B,QAAM,UAAU,QAAQ,QAAQ,CAAC;AACjC,SAAO,aAAa,OAAO,EAAE,QAAQ,QAAQ,MAAM,CAAC,GAAG,QAAQ,GAAG,QAAQ,EAAE;AAC9E;AACA,SAAS,gBAAgB,SAAS;AAChC,SAAO,QAAQ,QAAQ,UAAU,QAAQ,QAAQ,SAAS,MAAM,kBAAkB,QAAQ,QAAQ,CAAC,EAAE,YAAY,CAAC;AACpH;AACA,SAAS,gBAAgB,SAAS,OAAO;AACvC,UAAQ,YAAY,eAAe,SAAS,KAAK;AACjD,QAAM,aAAa,aAAa,KAAK,KAAK;AAC1C,MAAI,YAAY;AACd,YAAQ,UAAU,CAAC,KAAK;AAAA,EAC1B,OAAO;AACL,UAAM,cAAc,QAAQ;AAC5B,UAAM,QAAQ,YAAY,YAAY;AACtC,UAAM,UAAU,gBAAgB;AAChC,YAAQ,UAAU,CAAC,UAAU,MAAM,UAAU,MAAM,MAAM,WAAW;AAAA,EACtE;AACA,UAAQ,YAAY;AACpB,UAAQ,cAAc,QAAQ,QAAQ,CAAC;AACvC,SAAO;AACT;AACA,SAAS,eAAe,SAAS,UAAU;AACzC,MAAI,CAAC,QAAQ,SAAU,OAAM,IAAI,MAAM,cAAc;AACrD,UAAQ,UAAU,QAAQ,QAAQ,KAAK,WAAW,QAAQ,MAAM,CAAC;AACjE,UAAQ,WAAW;AACnB,UAAQ,SAAS;AACjB,UAAQ,YAAY;AACpB,UAAQ,cAAc;AACtB,MAAI,gBAAgB,OAAO,GAAG;AAC5B,oBAAgB,OAAO;AAAA,EACzB;AACF;AACA,SAAS,gBAAgB,SAAS;AAChC,UAAQ,YAAY;AACpB,MAAI,QAAQ,UAAU;AACpB,YAAQ,UAAU,YAAY,OAAO;AAAA,EACvC;AACA,UAAQ,SAAS,KAAK,QAAQ,OAAO;AACvC;AACA,SAAS,UAAU,SAAS;AAC1B,MAAI,CAAC,QAAQ,QAAQ,OAAQ,QAAO;AACpC,QAAM,QAAQ,QAAQ,QAAQ,CAAC,EAAE,YAAY,MAAM;AACnD,QAAM,UAAU,QAAQ,QAAQ;AAChC,SAAO,UAAU,YAAY,KAAK,YAAY;AAChD;AACA,SAAS,cAAc,SAAS;AAC9B,SAAO,QAAQ,UAAU,YAAY,MAAM;AAC7C;AACA,IAAM,iBAAgC,oBAAI,IAAI,CAAC,KAAK,KAAK,KAAK,MAAM,MAAM,IAAI,CAAC;AAC/E,SAAS,WAAW,GAAG,aAAa,MAAM;AACxC,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,QAAM,UAAU;AAAA,IACd,SAAS,CAAC;AAAA,IACV,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU,CAAC;AAAA,IACX,WAAW;AAAA,IACX,aAAa;AAAA,IACb,UAAU;AAAA,IACV,IAAI,IAAI,MAAM;AAAA,IACd,GAAG,IAAI,MAAM;AAAA,EACf;AACA,SAAO,QAAQ,YAAY,OAAO,QAAQ,EAAE,OAAO,OAAO,GAAG;AAC3D,QAAI,CAAC,QAAQ,WAAW;AACtB,UAAI,gBAAgB,SAAS,KAAK,GAAG;AACnC;AAAA,MACF;AAAA,IACF;AACA,QAAI,UAAU,KAAK;AACjB,UAAI,QAAQ,aAAa,QAAQ,aAAa;AAC5C,uBAAe,SAAS,KAAK;AAC7B,UAAE;AACF;AAAA,MACF;AACA,cAAQ,WAAW;AACnB,cAAQ,YAAY;AACpB,cAAQ,UAAU;AAClB;AAAA,IACF;AACA,QAAI,CAAC,MAAM,SAAS,KAAK,CAAC,GAAG;AAC3B,UAAI,QAAQ,WAAW,OAAO,UAAU,OAAO,GAAG;AAChD,gBAAQ,WAAW;AACnB,gBAAQ,SAAS;AACjB,uBAAe,SAAS,IAAI;AAC5B;AAAA,MACF;AACA,cAAQ,WAAW;AACnB,cAAQ,UAAU;AAClB;AAAA,IACF;AACA,QAAI,eAAe,IAAI,KAAK,GAAG;AAC7B,UAAI,QAAQ,UAAU;AACpB,uBAAe,SAAS,KAAK;AAAA,MAC/B;AACA;AAAA,IACF;AACA,QAAI,UAAU,OAAO,UAAU,KAAK;AAClC,UAAI,QAAQ,YAAY,CAAC,cAAc,OAAO,GAAG;AAC/C,uBAAe,SAAS,KAAK;AAC7B,UAAE;AACF;AAAA,MACF;AACA,cAAQ,UAAU;AAClB,cAAQ,WAAW;AACnB;AAAA,IACF;AACA,QAAI,MAAM,YAAY,MAAM,KAAK;AAC/B,cAAQ,UAAU;AAClB,cAAQ,cAAc;AACtB;AAAA,IACF;AACA,QAAI,aAAa,KAAK,KAAK,GAAG;AAC5B,UAAI,QAAQ,UAAU;AACpB,uBAAe,SAAS,KAAK;AAAA,MAC/B,WAAW,CAAC,gBAAgB,OAAO,GAAG;AACpC,cAAM,IAAI,MAAM,cAAc;AAAA,MAChC,OAAO;AACL,wBAAgB,OAAO;AAAA,MACzB;AACA,QAAE;AAAA,IACJ;AAAA,EACF;AACA,MAAI,QAAQ,UAAU;AACpB,mBAAe,SAAS,KAAK;AAAA,EAC/B;AACA,MAAI,QAAQ,aAAa,gBAAgB,OAAO,GAAG;AACjD,oBAAgB,OAAO;AAAA,EACzB;AACA,SAAO,QAAQ;AACjB;AACA,SAAS,cAAc,GAAG;AACxB,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,KAAK,EAAE,QAAQ,IAAI,IAAI,KAAK;AAC1C,SAAK,EAAE,CAAC,EAAE,CAAC;AACX,QAAI,EAAE,CAAC,EAAE,CAAC,KAAK,MAAM;AACnB,WAAK,EAAE,CAAC,EAAE,CAAC;AACX,UAAI,EAAE,CAAC,EAAE,CAAC,KAAK,MAAM;AACnB,aAAK;AACL,aAAK,EAAE,CAAC,EAAE,CAAC;AACX,YAAI,EAAE,CAAC,EAAE,CAAC,KAAK,MAAM;AACnB,eAAK;AACL,eAAK,EAAE,CAAC,EAAE,CAAC;AACX,eAAK;AACL,eAAK,EAAE,CAAC,EAAE,CAAC;AACX,cAAI,EAAE,CAAC,EAAE,CAAC,KAAK,MAAM;AACnB,iBAAK;AACL,iBAAK,EAAE,CAAC,EAAE,CAAC;AACX,iBAAK;AACL,iBAAK,EAAE,CAAC,EAAE,CAAC;AACX,gBAAI,EAAE,CAAC,EAAE,CAAC,KAAK,MAAM;AACnB,mBAAK;AACL,mBAAK,EAAE,CAAC,EAAE,CAAC;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI;AACb;AACA,IAAM,YAAN,cAAwB,SAAS;AAAA;AAAA,EAE/B,OAAO;AACL,WAAO,EAAE,KAAK,aAAa,KAAK,KAAK,SAAS,CAAC;AAC/C,WAAO,IAAI,IAAI,OAAO,MAAM,KAAK,QAAQ,CAAC;AAAA,EAC5C;AAAA;AAAA,EAEA,KAAK,IAAI,IAAI;AACX,UAAM,MAAM,KAAK,KAAK;AACtB,UAAM,IAAI;AACV,UAAM,IAAI;AACV,QAAI,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,GAAG;AAC5B,eAAS,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AAC5C,YAAI,KAAK,CAAC,EAAE,CAAC;AACb,YAAI,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;AACvC,eAAK,CAAC,EAAE,CAAC,KAAK;AACd,eAAK,CAAC,EAAE,CAAC,KAAK;AAAA,QAChB,WAAW,MAAM,KAAK;AACpB,eAAK,CAAC,EAAE,CAAC,KAAK;AAAA,QAChB,WAAW,MAAM,KAAK;AACpB,eAAK,CAAC,EAAE,CAAC,KAAK;AAAA,QAChB,WAAW,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;AAC9C,eAAK,CAAC,EAAE,CAAC,KAAK;AACd,eAAK,CAAC,EAAE,CAAC,KAAK;AACd,eAAK,CAAC,EAAE,CAAC,KAAK;AACd,eAAK,CAAC,EAAE,CAAC,KAAK;AACd,cAAI,MAAM,KAAK;AACb,iBAAK,CAAC,EAAE,CAAC,KAAK;AACd,iBAAK,CAAC,EAAE,CAAC,KAAK;AAAA,UAChB;AAAA,QACF,WAAW,MAAM,KAAK;AACpB,eAAK,CAAC,EAAE,CAAC,KAAK;AACd,eAAK,CAAC,EAAE,CAAC,KAAK;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,MAAM,IAAI,QAAQ;AAChB,QAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,UAAI,MAAM,UAAU,OAAO,MAAM,CAAC,GAAG,CAAC,EAAE,SAAS;AAAA,IACnD;AACA,WAAO,WAAW,CAAC;AAAA,EACrB;AAAA;AAAA,EAEA,KAAK,QAAQ,SAAS;AACpB,UAAM,MAAM,KAAK,KAAK;AACtB,QAAI,GAAG;AACP,QAAI,QAAQ,IAAI,UAAU,IAAI,IAAI,IAAI;AACtC,QAAI,SAAS,IAAI,WAAW,IAAI,IAAI,IAAI;AACxC,SAAK,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACrC,UAAI,KAAK,CAAC,EAAE,CAAC;AACb,UAAI,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;AACvC,aAAK,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAK,SAAS,IAAI,QAAQ,IAAI;AAC7D,aAAK,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAK,UAAU,IAAI,SAAS,IAAI;AAAA,MACjE,WAAW,MAAM,KAAK;AACpB,aAAK,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAK,SAAS,IAAI,QAAQ,IAAI;AAAA,MAC/D,WAAW,MAAM,KAAK;AACpB,aAAK,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAK,UAAU,IAAI,SAAS,IAAI;AAAA,MACjE,WAAW,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;AAC9C,aAAK,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAK,SAAS,IAAI,QAAQ,IAAI;AAC7D,aAAK,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAK,UAAU,IAAI,SAAS,IAAI;AAC/D,aAAK,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAK,SAAS,IAAI,QAAQ,IAAI;AAC7D,aAAK,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAK,UAAU,IAAI,SAAS,IAAI;AAC/D,YAAI,MAAM,KAAK;AACb,eAAK,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAK,SAAS,IAAI,QAAQ,IAAI;AAC7D,eAAK,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAK,UAAU,IAAI,SAAS,IAAI;AAAA,QACjE;AAAA,MACF,WAAW,MAAM,KAAK;AACpB,aAAK,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,SAAS,IAAI;AACvC,aAAK,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,UAAU,IAAI;AACxC,aAAK,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAK,SAAS,IAAI,QAAQ,IAAI;AAC7D,aAAK,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAK,UAAU,IAAI,SAAS,IAAI;AAAA,MACjE;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,cAAc,IAAI;AAAA,EAC3B;AACF;AACA,IAAM,kBAAkB,WAAS;AAC/B,QAAM,OAAO,OAAO;AACpB,MAAI,SAAS,UAAU;AACrB,WAAO;AAAA,EACT,WAAW,SAAS,UAAU;AAC5B,QAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,aAAO;AAAA,IACT,WAAW,UAAU,KAAK,KAAK,GAAG;AAChC,aAAO,aAAa,KAAK,KAAK,IAAI,YAAY;AAAA,IAChD,WAAW,cAAc,KAAK,KAAK,GAAG;AACpC,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,WAAW,eAAe,QAAQ,MAAM,WAAW,IAAI,IAAI;AACzD,WAAO,MAAM;AAAA,EACf,WAAW,MAAM,QAAQ,KAAK,GAAG;AAC/B,WAAO;AAAA,EACT,WAAW,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,IAAM,YAAN,MAAgB;AAAA,EACd,YAAY,SAAS;AACnB,SAAK,WAAW,WAAW,IAAI,KAAK,GAAG;AACvC,SAAK,QAAQ;AACb,SAAK,MAAM;AACX,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,GAAG,KAAK;AACN,WAAO,KAAK,UAAU,MAAM,KAAK,OAAO,KAAK,KAAK,KAAK,KAAK,UAAU,KAAK,QAAQ;AAAA,EACrF;AAAA,EACA,OAAO;AACL,UAAM,WAAW,KAAK,SAAS,IAAI,KAAK,SAAS,IAAI,EAAE,OAAO,SAAU,MAAM,MAAM;AAClF,aAAO,QAAQ;AAAA,IACjB,GAAG,IAAI;AACP,WAAO;AAAA,EACT;AAAA,EACA,KAAK,KAAK;AACR,QAAI,OAAO,MAAM;AACf,aAAO,KAAK;AAAA,IACd;AACA,SAAK,QAAQ,KAAK,KAAK,GAAG;AAC1B,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,SAAS;AACf,QAAI,WAAW,KAAM,QAAO,KAAK;AACjC,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AAAA,EACA,GAAG,KAAK;AACN,QAAI,OAAO,MAAM;AACf,aAAO,KAAK;AAAA,IACd;AACA,SAAK,MAAM,KAAK,KAAK,GAAG;AACxB,WAAO;AAAA,EACT;AAAA,EACA,KAAK,MAAM;AACT,QAAI,QAAQ,MAAM;AAChB,aAAO,KAAK;AAAA,IACd;AACA,SAAK,QAAQ;AACb,WAAO;AAAA,EACT;AAAA,EACA,KAAK,OAAO;AACV,QAAI,CAAC,KAAK,OAAO;AACf,WAAK,KAAK,gBAAgB,KAAK,CAAC;AAAA,IAClC;AACA,QAAI,SAAS,IAAI,KAAK,MAAM,KAAK;AACjC,QAAI,KAAK,UAAU,OAAO;AACxB,eAAS,KAAK,MAAM,OAAO,KAAK,IAAI,CAAC,CAAC,EAAE,IAAI,KAAK,QAAQ,OAAO,KAAK,MAAM,CAAC,CAAC,EAAE,IAAI;AAAA,IACrF;AACA,QAAI,KAAK,UAAU,WAAW;AAC5B,eAAS,KAAK,MAAM,OAAO,MAAM,KAAK,GAAG,IAAI,KAAK,QAAQ,OAAO,MAAM,KAAK,KAAK,IAAI;AAAA,IACvF;AACA,aAAS,OAAO,aAAa;AAC7B,SAAK,YAAY,KAAK,aAAa,IAAI,KAAK,MAAM;AAClD,SAAK,WAAW,KAAK,YAAY,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,CAAC,EAAE,IAAI,MAAM,EAAE,IAAI,SAAU,GAAG;AACpG,QAAE,OAAO;AACT,aAAO;AAAA,IACT,CAAC;AACD,WAAO;AAAA,EACT;AACF;AACA,IAAM,eAAN,MAAmB;AAAA,EACjB,eAAe,MAAM;AACnB,SAAK,KAAK,GAAG,IAAI;AAAA,EACnB;AAAA,EACA,KAAK,KAAK;AACR,UAAM,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI;AACpC,SAAK,QAAQ;AACb,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,WAAO,CAAC,KAAK,KAAK;AAAA,EACpB;AAAA,EACA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AACF;AACA,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,eAAe,MAAM;AACnB,SAAK,KAAK,GAAG,IAAI;AAAA,EACnB;AAAA,EACA,KAAK,KAAK;AACR,QAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,YAAM;AAAA,QACJ,QAAQ,IAAI,CAAC;AAAA,QACb,QAAQ,IAAI,CAAC;AAAA,QACb,OAAO,IAAI,CAAC;AAAA,QACZ,QAAQ,IAAI,CAAC;AAAA,QACb,YAAY,IAAI,CAAC;AAAA,QACjB,YAAY,IAAI,CAAC;AAAA,QACjB,SAAS,IAAI,CAAC;AAAA,QACd,SAAS,IAAI,CAAC;AAAA,MAChB;AAAA,IACF;AACA,WAAO,OAAO,MAAM,cAAa,UAAU,GAAG;AAC9C,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,UAAM,IAAI;AACV,WAAO,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO;AAAA,EACjG;AACF;AACA,aAAa,WAAW;AAAA,EACtB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,SAAS;AACX;AACA,IAAM,YAAY,CAAC,GAAG,MAAM;AAC1B,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI;AAC9C;AACA,IAAM,YAAN,MAAgB;AAAA,EACd,eAAe,MAAM;AACnB,SAAK,KAAK,GAAG,IAAI;AAAA,EACnB;AAAA,EACA,MAAM,OAAO;AACX,UAAM,SAAS,KAAK;AACpB,aAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC/C,UAAI,OAAO,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,GAAG;AAClC,YAAI,OAAO,IAAI,CAAC,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,GAAG;AAC7D,gBAAM,QAAQ,MAAM,IAAI,CAAC;AACzB,gBAAM,QAAQ,IAAI,MAAM,KAAK,OAAO,OAAO,IAAI,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,QAAQ;AACvE,eAAK,OAAO,OAAO,IAAI,GAAG,GAAG,GAAG,KAAK;AAAA,QACvC;AACA,aAAK,OAAO,IAAI,CAAC,IAAI;AACrB;AAAA,MACF;AACA,UAAI,CAAC,MAAM,IAAI,CAAC,GAAG;AACjB,eAAO;AAAA,MACT;AACA,YAAM,gBAAgB,IAAI,MAAM,IAAI,CAAC,EAAE,EAAE,QAAQ;AACjD,YAAM,WAAW,OAAO,IAAI,CAAC,IAAI;AACjC,aAAO,OAAO,GAAG,UAAU,MAAM,CAAC,GAAG,MAAM,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,GAAG,GAAG,aAAa;AACjF,WAAK,OAAO,IAAI,CAAC,IAAI;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AAAA,EACA,KAAK,UAAU;AACb,SAAK,SAAS,CAAC;AACf,QAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,WAAK,SAAS,SAAS,MAAM;AAC7B;AAAA,IACF;AACA,eAAW,YAAY,CAAC;AACxB,UAAM,UAAU,CAAC;AACjB,eAAW,KAAK,UAAU;AACxB,YAAM,OAAO,gBAAgB,SAAS,CAAC,CAAC;AACxC,YAAM,MAAM,IAAI,KAAK,SAAS,CAAC,CAAC,EAAE,QAAQ;AAC1C,cAAQ,KAAK,CAAC,GAAG,MAAM,IAAI,QAAQ,GAAG,GAAG,CAAC;AAAA,IAC5C;AACA,YAAQ,KAAK,SAAS;AACtB,SAAK,SAAS,QAAQ,OAAO,CAAC,MAAM,SAAS,KAAK,OAAO,IAAI,GAAG,CAAC,CAAC;AAClE,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EACA,UAAU;AACR,UAAM,MAAM,CAAC;AACb,UAAM,MAAM,KAAK;AACjB,WAAO,IAAI,QAAQ;AACjB,YAAM,MAAM,IAAI,MAAM;AACtB,YAAM,OAAO,IAAI,MAAM;AACvB,YAAM,MAAM,IAAI,MAAM;AACtB,YAAM,SAAS,IAAI,OAAO,GAAG,GAAG;AAChC,UAAI,GAAG,IAAI,IAAI,KAAK,MAAM;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,iBAAiB,CAAC,cAAc,cAAc,SAAS;AAC7D,SAAS,sBAAsB,OAAO,CAAC,GAAG;AACxC,iBAAe,KAAK,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC;AACxC;AACA,SAAS,gBAAgB;AACvB,SAAO,gBAAgB;AAAA,IACrB,GAAG,KAAK;AACN,aAAO,IAAI,UAAU,EAAE,KAAK,KAAK,WAAW,EAAE,KAAK,KAAK,QAAQ,CAAC,EAAE,GAAG,GAAG;AAAA,IAC3E;AAAA,IACA,UAAU,KAAK;AACb,WAAK,KAAK,GAAG;AACb,aAAO;AAAA,IACT;AAAA,IACA,eAAe;AACb,aAAO,KAAK,QAAQ;AAAA,IACtB;AAAA,IACA,MAAM,OAAO,KAAK,KAAK,SAAS,SAAS;AACvC,YAAM,SAAS,SAAU,GAAG,OAAO;AACjC,eAAO,QAAQ,KAAK,GAAG,IAAI,KAAK,GAAG,KAAK,QAAQ,KAAK,GAAG,OAAO;AAAA,MACjE;AACA,aAAO,KAAK,UAAU,MAAM,IAAI,MAAM,CAAC;AAAA,IACzC;AAAA,EACF,CAAC;AACH;AACA,IAAM,OAAN,cAAmB,MAAM;AAAA;AAAA,EAEvB,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,QAAQ,IAAI,GAAG,MAAM;AAAA,EACvC;AAAA;AAAA,EAEA,QAAQ;AACN,WAAO,KAAK,WAAW,KAAK,SAAS,IAAI,UAAU,KAAK,KAAK,GAAG,CAAC;AAAA,EACnE;AAAA;AAAA,EAEA,QAAQ;AACN,WAAO,KAAK;AACZ,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,OAAO,SAAS;AACd,WAAO,WAAW,OAAO,KAAK,KAAK,EAAE,SAAS,KAAK,KAAK,KAAK,KAAK,EAAE,OAAO,OAAO;AAAA,EACpF;AAAA;AAAA,EAEA,KAAK,IAAI,IAAI;AACX,WAAO,KAAK,KAAK,KAAK,KAAK,MAAM,EAAE,KAAK,IAAI,EAAE,CAAC;AAAA,EACjD;AAAA;AAAA,EAEA,KAAK,GAAG;AACN,WAAO,KAAK,OAAO,KAAK,MAAM,IAAI,KAAK,MAAM,EAAE,KAAK,KAAK,OAAO,MAAM,WAAW,IAAI,KAAK,SAAS,IAAI,UAAU,CAAC,CAAC;AAAA,EACrH;AAAA;AAAA,EAEA,KAAK,QAAQ,SAAS;AACpB,UAAM,IAAI,iBAAiB,MAAM,QAAQ,OAAO;AAChD,WAAO,KAAK,KAAK,KAAK,KAAK,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;AAAA,EAC5D;AAAA;AAAA,EAEA,MAAM,QAAQ;AACZ,WAAO,UAAU,OAAO,KAAK,KAAK,EAAE,QAAQ,KAAK,KAAK,QAAQ,KAAK,KAAK,EAAE,MAAM;AAAA,EAClF;AAAA;AAAA,EAEA,EAAE,IAAI;AACJ,WAAO,MAAM,OAAO,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,EAAE,CAAC;AAAA,EACjE;AAAA;AAAA,EAEA,EAAE,IAAI;AACJ,WAAO,MAAM,OAAO,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK,KAAK,EAAE,GAAG,EAAE;AAAA,EACjE;AACF;AACA,KAAK,UAAU,aAAa;AAC5B,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,MAAM,kBAAkB,SAAU,GAAG;AACnC,aAAO,KAAK,IAAI,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK,IAAI,UAAU,CAAC;AAAA,IACvD,CAAC;AAAA,EACH;AACF,CAAC;AACD,SAAS,MAAM,MAAM;AACrB,SAAS,QAAQ;AACf,SAAO,KAAK,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,KAAK,QAAQ,CAAC;AACzE;AACA,SAAS,QAAQ;AACf,SAAO,KAAK;AACZ,SAAO;AACT;AACA,SAAS,OAAO,IAAI,IAAI;AACtB,SAAO,KAAK,KAAK,UAAU,KAAK,MAAM,EAAE,KAAK,IAAI,EAAE,CAAC;AACtD;AACA,SAAS,KAAK,GAAG;AACf,SAAO,KAAK,OAAO,KAAK,MAAM,IAAI,KAAK,MAAM,EAAE,KAAK,UAAU,OAAO,MAAM,WAAW,IAAI,KAAK,SAAS,IAAI,WAAW,CAAC,CAAC;AAC3H;AACA,SAAS,OAAO,QAAQ,SAAS;AAC/B,QAAM,IAAI,iBAAiB,MAAM,QAAQ,OAAO;AAChD,SAAO,KAAK,KAAK,UAAU,KAAK,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;AACjE;AACA,IAAM,OAAsB,OAAO,OAAsB,OAAO,eAAe;AAAA,EAC7E,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN;AAAA,EACA,MAAM;AACR,GAAG,OAAO,aAAa;AAAA,EACrB,OAAO;AACT,CAAC,CAAC;AACF,IAAM,UAAN,cAAsB,MAAM;AAAA;AAAA,EAE1B,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,WAAW,IAAI,GAAG,MAAM;AAAA,EAC1C;AACF;AACA,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,SAAS,kBAAkB,SAAU,GAAG;AACtC,aAAO,KAAK,IAAI,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,WAAW,CAAC;AAAA,IAC3D,CAAC;AAAA,EACH;AACF,CAAC;AACD,OAAO,SAAS,OAAO;AACvB,OAAO,SAAS,IAAI;AACpB,SAAS,SAAS,SAAS;AAC3B,IAAM,WAAN,cAAuB,MAAM;AAAA;AAAA,EAE3B,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,YAAY,IAAI,GAAG,MAAM;AAAA,EAC3C;AACF;AACA,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,UAAU,kBAAkB,SAAU,GAAG;AACvC,aAAO,KAAK,IAAI,IAAI,SAAS,CAAC,EAAE,KAAK,KAAK,IAAI,WAAW,CAAC;AAAA,IAC5D,CAAC;AAAA,EACH;AACF,CAAC;AACD,OAAO,UAAU,OAAO;AACxB,OAAO,UAAU,IAAI;AACrB,SAAS,UAAU,UAAU;AAC7B,IAAM,OAAN,cAAmB,MAAM;AAAA;AAAA,EAEvB,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,QAAQ,IAAI,GAAG,MAAM;AAAA,EACvC;AACF;AACA,OAAO,MAAM;AAAA,EACX;AAAA,EACA;AACF,CAAC;AACD,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,MAAM,kBAAkB,SAAU,QAAQ,SAAS;AACjD,aAAO,KAAK,IAAI,IAAI,KAAK,CAAC,EAAE,KAAK,QAAQ,OAAO;AAAA,IAClD,CAAC;AAAA,EACH;AACF,CAAC;AACD,SAAS,MAAM,MAAM;AACrB,IAAM,QAAN,MAAY;AAAA,EACV,cAAc;AACZ,SAAK,SAAS;AACd,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA,EAEA,QAAQ;AACN,WAAO,KAAK,UAAU,KAAK,OAAO;AAAA,EACpC;AAAA;AAAA,EAEA,OAAO;AACL,WAAO,KAAK,SAAS,KAAK,MAAM;AAAA,EAClC;AAAA,EACA,KAAK,OAAO;AACV,UAAM,OAAO,OAAO,MAAM,SAAS,cAAc,QAAQ;AAAA,MACvD;AAAA,MACA,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AACA,QAAI,KAAK,OAAO;AACd,WAAK,OAAO,KAAK;AACjB,WAAK,MAAM,OAAO;AAClB,WAAK,QAAQ;AAAA,IACf,OAAO;AACL,WAAK,QAAQ;AACb,WAAK,SAAS;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,OAAO,MAAM;AACX,QAAI,KAAK,KAAM,MAAK,KAAK,OAAO,KAAK;AACrC,QAAI,KAAK,KAAM,MAAK,KAAK,OAAO,KAAK;AACrC,QAAI,SAAS,KAAK,MAAO,MAAK,QAAQ,KAAK;AAC3C,QAAI,SAAS,KAAK,OAAQ,MAAK,SAAS,KAAK;AAC7C,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,EACd;AAAA,EACA,QAAQ;AACN,UAAM,SAAS,KAAK;AACpB,QAAI,CAAC,OAAQ,QAAO;AACpB,SAAK,SAAS,OAAO;AACrB,QAAI,KAAK,OAAQ,MAAK,OAAO,OAAO;AACpC,SAAK,QAAQ,KAAK,SAAS,KAAK,QAAQ;AACxC,WAAO,OAAO;AAAA,EAChB;AACF;AACA,IAAM,WAAW;AAAA,EACf,UAAU;AAAA,EACV,QAAQ,IAAI,MAAM;AAAA,EAClB,UAAU,IAAI,MAAM;AAAA,EACpB,YAAY,IAAI,MAAM;AAAA,EACtB,OAAO,MAAM,QAAQ,OAAO,eAAe,QAAQ,OAAO;AAAA,EAC1D,YAAY,CAAC;AAAA,EACb,MAAM,IAAI;AACR,UAAM,OAAO,SAAS,OAAO,KAAK;AAAA,MAChC,KAAK;AAAA,IACP,CAAC;AACD,QAAI,SAAS,aAAa,MAAM;AAC9B,eAAS,WAAW,QAAQ,OAAO,sBAAsB,SAAS,KAAK;AAAA,IACzE;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,IAAI,OAAO;AACjB,YAAQ,SAAS;AACjB,UAAM,OAAO,SAAS,MAAM,EAAE,IAAI,IAAI;AACtC,UAAM,OAAO,SAAS,SAAS,KAAK;AAAA,MAClC,KAAK;AAAA,MACL;AAAA,IACF,CAAC;AACD,QAAI,SAAS,aAAa,MAAM;AAC9B,eAAS,WAAW,QAAQ,OAAO,sBAAsB,SAAS,KAAK;AAAA,IACzE;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU,IAAI;AACZ,UAAM,OAAO,SAAS,WAAW,KAAK,EAAE;AACxC,QAAI,SAAS,aAAa,MAAM;AAC9B,eAAS,WAAW,QAAQ,OAAO,sBAAsB,SAAS,KAAK;AAAA,IACzE;AACA,WAAO;AAAA,EACT;AAAA,EACA,YAAY,MAAM;AAChB,YAAQ,QAAQ,SAAS,OAAO,OAAO,IAAI;AAAA,EAC7C;AAAA,EACA,aAAa,MAAM;AACjB,YAAQ,QAAQ,SAAS,SAAS,OAAO,IAAI;AAAA,EAC/C;AAAA,EACA,gBAAgB,MAAM;AACpB,YAAQ,QAAQ,SAAS,WAAW,OAAO,IAAI;AAAA,EACjD;AAAA,EACA,MAAM,KAAK;AACT,QAAI,cAAc;AAClB,UAAM,cAAc,SAAS,SAAS,KAAK;AAC3C,WAAO,cAAc,SAAS,SAAS,MAAM,GAAG;AAC9C,UAAI,OAAO,YAAY,MAAM;AAC3B,oBAAY,IAAI;AAAA,MAClB,OAAO;AACL,iBAAS,SAAS,KAAK,WAAW;AAAA,MACpC;AACA,UAAI,gBAAgB,YAAa;AAAA,IACnC;AACA,QAAI,YAAY;AAChB,UAAM,YAAY,SAAS,OAAO,KAAK;AACvC,WAAO,cAAc,cAAc,YAAY,SAAS,OAAO,MAAM,IAAI;AACvE,gBAAU,IAAI,GAAG;AAAA,IACnB;AACA,QAAI,gBAAgB;AACpB,WAAO,gBAAgB,SAAS,WAAW,MAAM,GAAG;AAClD,oBAAc;AAAA,IAChB;AACA,aAAS,WAAW,SAAS,SAAS,MAAM,KAAK,SAAS,OAAO,MAAM,IAAI,QAAQ,OAAO,sBAAsB,SAAS,KAAK,IAAI;AAAA,EACpI;AACF;AACA,IAAM,eAAe,SAAU,YAAY;AACzC,QAAM,QAAQ,WAAW;AACzB,QAAM,WAAW,WAAW,OAAO,SAAS;AAC5C,QAAM,MAAM,QAAQ;AACpB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ,WAAW;AAAA,EACrB;AACF;AACA,IAAM,gBAAgB,WAAY;AAChC,QAAM,IAAI,QAAQ;AAClB,UAAQ,EAAE,eAAe,EAAE,MAAM,IAAI;AACvC;AACA,IAAM,WAAN,cAAuB,YAAY;AAAA;AAAA,EAEjC,YAAY,aAAa,eAAe;AACtC,UAAM;AACN,SAAK,cAAc;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,SAAS;AACP,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA,EACA,SAAS;AACP,SAAK,KAAK,KAAK,qBAAqB,IAAI,CAAC;AACzC,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA,EAEA,aAAa;AACX,UAAM,iBAAiB,KAAK,kBAAkB;AAC9C,UAAM,eAAe,iBAAiB,eAAe,OAAO,SAAS,IAAI;AACzE,UAAM,gBAAgB,iBAAiB,eAAe,QAAQ,KAAK;AACnE,WAAO,gBAAgB;AAAA,EACzB;AAAA,EACA,uBAAuB;AACrB,UAAM,WAAW,KAAK,SAAS,IAAI,OAAK,EAAE,QAAQ,EAAE,OAAO,SAAS,CAAC;AACrE,WAAO,KAAK,IAAI,GAAG,GAAG,QAAQ;AAAA,EAChC;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK,kBAAkB,KAAK,aAAa;AAAA,EAClD;AAAA,EACA,kBAAkB,IAAI;AACpB,WAAO,KAAK,SAAS,KAAK,WAAW,QAAQ,EAAE,CAAC,KAAK;AAAA,EACvD;AAAA,EACA,QAAQ;AACN,SAAK,UAAU;AACf,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,QAAQ,aAAa;AACnB,QAAI,eAAe,KAAM,QAAO,KAAK;AACrC,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,UAAU;AACf,WAAO,KAAK,WAAW,EAAE,UAAU;AAAA,EACrC;AAAA,EACA,QAAQ,KAAK;AACX,UAAM,eAAe,KAAK,MAAM;AAChC,QAAI,OAAO,KAAM,QAAO,KAAK,MAAM,CAAC,YAAY;AAChD,UAAM,WAAW,KAAK,IAAI,YAAY;AACtC,WAAO,KAAK,MAAM,MAAM,CAAC,WAAW,QAAQ;AAAA,EAC9C;AAAA;AAAA,EAEA,SAAS,QAAQ,OAAO,MAAM;AAC5B,QAAI,UAAU,MAAM;AAClB,aAAO,KAAK,SAAS,IAAI,YAAY;AAAA,IACvC;AACA,QAAI,oBAAoB;AACxB,UAAM,UAAU,KAAK,WAAW;AAChC,YAAQ,SAAS;AACjB,QAAI,QAAQ,QAAQ,SAAS,UAAU,SAAS,SAAS;AACvD,0BAAoB;AAAA,IACtB,WAAW,SAAS,cAAc,SAAS,SAAS;AAClD,0BAAoB;AACpB,cAAQ;AAAA,IACV,WAAW,SAAS,OAAO;AACzB,0BAAoB,KAAK;AAAA,IAC3B,WAAW,SAAS,YAAY;AAC9B,YAAM,cAAc,KAAK,kBAAkB,OAAO,EAAE;AACpD,UAAI,aAAa;AACf,4BAAoB,YAAY,QAAQ;AACxC,gBAAQ;AAAA,MACV;AAAA,IACF,WAAW,SAAS,aAAa;AAC/B,YAAM,iBAAiB,KAAK,kBAAkB;AAC9C,YAAM,gBAAgB,iBAAiB,eAAe,QAAQ,KAAK;AACnE,0BAAoB;AAAA,IACtB,OAAO;AACL,YAAM,IAAI,MAAM,wCAAwC;AAAA,IAC1D;AACA,WAAO,WAAW;AAClB,WAAO,SAAS,IAAI;AACpB,UAAM,UAAU,OAAO,QAAQ;AAC/B,UAAM,aAAa;AAAA,MACjB,SAAS,YAAY,OAAO,KAAK,WAAW;AAAA,MAC5C,OAAO,oBAAoB;AAAA,MAC3B;AAAA,IACF;AACA,SAAK,gBAAgB,OAAO;AAC5B,SAAK,SAAS,KAAK,UAAU;AAC7B,SAAK,SAAS,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK;AAC9C,SAAK,aAAa,KAAK,SAAS,IAAI,UAAQ,KAAK,OAAO,EAAE;AAC1D,SAAK,WAAW,EAAE,UAAU;AAC5B,WAAO;AAAA,EACT;AAAA,EACA,KAAK,IAAI;AACP,WAAO,KAAK,KAAK,KAAK,QAAQ,EAAE;AAAA,EAClC;AAAA,EACA,OAAO,IAAI;AACT,QAAI,MAAM,KAAM,QAAO,KAAK;AAC5B,SAAK,cAAc;AACnB,WAAO;AAAA,EACT;AAAA,EACA,MAAM,OAAO;AACX,QAAI,SAAS,KAAM,QAAO,KAAK;AAC/B,SAAK,SAAS;AACd,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,KAAK,CAAC;AACX,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,KAAK,MAAM;AACT,QAAI,QAAQ,KAAM,QAAO,KAAK;AAC9B,SAAK,QAAQ;AACb,WAAO,KAAK,UAAU,IAAI;AAAA,EAC5B;AAAA;AAAA,EAEA,WAAW,QAAQ;AACjB,UAAM,QAAQ,KAAK,WAAW,QAAQ,OAAO,EAAE;AAC/C,QAAI,QAAQ,EAAG,QAAO;AACtB,SAAK,SAAS,OAAO,OAAO,CAAC;AAC7B,SAAK,WAAW,OAAO,OAAO,CAAC;AAC/B,WAAO,SAAS,IAAI;AACpB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,aAAa;AACX,QAAI,CAAC,KAAK,OAAO,GAAG;AAClB,WAAK,kBAAkB,KAAK,YAAY;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,UAAU,gBAAgB,OAAO;AAC/B,aAAS,YAAY,KAAK,UAAU;AACpC,SAAK,aAAa;AAClB,QAAI,cAAe,QAAO,KAAK,eAAe;AAC9C,QAAI,KAAK,QAAS,QAAO;AACzB,SAAK,aAAa,SAAS,MAAM,KAAK,KAAK;AAC3C,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,gBAAgB,OAAO;AAC7B,UAAM,OAAO,KAAK,YAAY;AAC9B,QAAI,WAAW,OAAO,KAAK;AAC3B,QAAI,cAAe,YAAW;AAC9B,UAAM,SAAS,KAAK,SAAS,YAAY,KAAK,QAAQ,KAAK;AAC3D,SAAK,kBAAkB;AACvB,QAAI,CAAC,eAAe;AAClB,WAAK,SAAS;AACd,WAAK,QAAQ,KAAK,QAAQ,IAAI,IAAI,KAAK;AAAA,IACzC;AACA,SAAK,gBAAgB,KAAK;AAC1B,SAAK,KAAK,QAAQ,KAAK,KAAK;AAC5B,aAAS,IAAI,KAAK,SAAS,QAAQ,OAAM;AACvC,YAAM,aAAa,KAAK,SAAS,CAAC;AAClC,YAAM,SAAS,WAAW;AAC1B,YAAM,YAAY,KAAK,QAAQ,WAAW;AAC1C,UAAI,aAAa,GAAG;AAClB,eAAO,MAAM;AAAA,MACf;AAAA,IACF;AACA,QAAI,cAAc;AAClB,aAAS,IAAI,GAAG,MAAM,KAAK,SAAS,QAAQ,IAAI,KAAK,KAAK;AACxD,YAAM,aAAa,KAAK,SAAS,CAAC;AAClC,YAAM,SAAS,WAAW;AAC1B,UAAI,KAAK;AACT,YAAM,YAAY,KAAK,QAAQ,WAAW;AAC1C,UAAI,aAAa,GAAG;AAClB,sBAAc;AACd;AAAA,MACF,WAAW,YAAY,IAAI;AACzB,aAAK;AAAA,MACP;AACA,UAAI,CAAC,OAAO,OAAO,EAAG;AACtB,YAAM,WAAW,OAAO,KAAK,EAAE,EAAE;AACjC,UAAI,CAAC,UAAU;AACb,sBAAc;AAAA,MAChB,WAAW,WAAW,YAAY,MAAM;AACtC,cAAM,UAAU,OAAO,SAAS,IAAI,OAAO,KAAK,IAAI,KAAK;AACzD,YAAI,UAAU,WAAW,UAAU,KAAK,OAAO;AAC7C,iBAAO,WAAW;AAClB,YAAE;AACF,YAAE;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AACA,QAAI,eAAe,EAAE,KAAK,SAAS,KAAK,KAAK,UAAU,MAAM,KAAK,WAAW,UAAU,KAAK,SAAS,KAAK,KAAK,QAAQ,GAAG;AACxH,WAAK,UAAU;AAAA,IACjB,OAAO;AACL,WAAK,MAAM;AACX,WAAK,KAAK,UAAU;AAAA,IACtB;AACA,WAAO;AAAA,EACT;AAAA,EACA,YAAY;AACV,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,WAAW,CAAC;AACjB,SAAK,aAAa,CAAC;AACnB,SAAK,gBAAgB;AACrB,SAAK,QAAQ;AACb,SAAK,kBAAkB;AACvB,SAAK,gBAAgB;AACrB,SAAK,QAAQ,KAAK,QAAQ,KAAK,MAAM,KAAK;AAC1C,SAAK,iBAAiB,KAAK,QAAQ,KAAK,MAAM,IAAI;AAAA,EACpD;AACF;AACA,gBAAgB;AAAA,EACd,SAAS;AAAA,IACP,UAAU,SAAU,WAAW;AAC7B,UAAI,aAAa,MAAM;AACrB,aAAK,YAAY,KAAK,aAAa,IAAI,SAAS;AAChD,eAAO,KAAK;AAAA,MACd,OAAO;AACL,aAAK,YAAY;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF,CAAC;AACD,IAAM,SAAN,MAAM,gBAAe,YAAY;AAAA,EAC/B,YAAY,SAAS;AACnB,UAAM;AACN,SAAK,KAAK,QAAO;AACjB,cAAU,WAAW,OAAO,SAAS,WAAW;AAChD,cAAU,OAAO,YAAY,aAAa,IAAI,WAAW,OAAO,IAAI;AACpE,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,OAAO;AACZ,SAAK,SAAS,CAAC;AACf,SAAK,YAAY,OAAO,YAAY,YAAY;AAChD,SAAK,iBAAiB,mBAAmB;AACzC,SAAK,WAAW,KAAK,iBAAiB,UAAU,IAAI,KAAK;AACzD,SAAK,WAAW,CAAC;AACjB,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,aAAa,IAAI,OAAO;AAC7B,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,WAAW,KAAK,iBAAiB,OAAO;AAAA,EAC/C;AAAA,EACA,OAAO,SAAS,UAAU,OAAO,MAAM;AACrC,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,QAAI,OAAO;AACX,eAAW,YAAY,SAAS;AAChC,YAAQ,SAAS,SAAS;AAC1B,WAAO,QAAQ;AACf,QAAI,OAAO,aAAa,YAAY,EAAE,oBAAoB,UAAU;AAClE,cAAQ,SAAS,SAAS;AAC1B,aAAO,SAAS,QAAQ;AACxB,cAAQ,SAAS,SAAS;AAC1B,cAAQ,SAAS,SAAS;AAC1B,aAAO,SAAS,QAAQ;AACxB,iBAAW,SAAS,YAAY,SAAS;AAAA,IAC3C;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,SAAS;AACd,QAAI,WAAW,KAAM,QAAO,KAAK;AACjC,SAAK,UAAU;AACf,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,YAAY;AACvB,SAAK,WAAW,WAAW,UAAU;AACrC,WAAO;AAAA,EACT;AAAA,EACA,MAAM,IAAI;AACR,WAAO,KAAK,GAAG,YAAY,EAAE;AAAA,EAC/B;AAAA,EACA,QAAQ,UAAU,OAAO,MAAM;AAC7B,UAAM,IAAI,QAAO,SAAS,UAAU,OAAO,IAAI;AAC/C,UAAM,SAAS,IAAI,QAAO,EAAE,QAAQ;AACpC,QAAI,KAAK,UAAW,QAAO,SAAS,KAAK,SAAS;AAClD,QAAI,KAAK,SAAU,QAAO,QAAQ,KAAK,QAAQ;AAC/C,WAAO,OAAO,KAAK,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI;AAAA,EAChD;AAAA,EACA,iBAAiB;AACf,SAAK,aAAa,IAAI,OAAO;AAC7B,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,2BAA2B;AACzB,QAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,aAAa,CAAC,KAAK,UAAU,WAAW,SAAS,KAAK,EAAE,GAAG;AACjF,WAAK,SAAS,KAAK,OAAO,OAAO,UAAQ;AACvC,eAAO,CAAC,KAAK;AAAA,MACf,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,MAAM,OAAO;AACX,WAAO,KAAK,QAAQ,GAAG,KAAK;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,WAAO,KAAK,UAAU,KAAK,QAAQ,KAAK,aAAa,KAAK;AAAA,EAC5D;AAAA,EACA,OAAO,IAAI;AACT,WAAO,KAAK,MAAM,MAAM,EAAE;AAAA,EAC5B;AAAA,EACA,KAAK,IAAI;AACP,SAAK,WAAW,IAAI,KAAK,EAAE;AAC3B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,SAAS;AACf,QAAI,WAAW,KAAM,QAAO,KAAK;AACjC,SAAK,WAAW;AAChB,YAAQ,eAAe;AACvB,WAAO;AAAA,EACT;AAAA,EACA,SAAS;AACP,WAAO,KAAK,KAAK,QAAQ;AAAA,EAC3B;AAAA,EACA,KAAK,OAAO,OAAO,MAAM;AACvB,QAAI,OAAO,UAAU,UAAU;AAC7B,cAAQ,MAAM;AACd,aAAO,MAAM;AACb,cAAQ,MAAM;AAAA,IAChB;AACA,SAAK,SAAS,SAAS;AACvB,SAAK,SAAS,SAAS;AACvB,SAAK,QAAQ,QAAQ;AACrB,QAAI,KAAK,WAAW,MAAM;AACxB,WAAK,SAAS;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AAAA,EACA,MAAM,GAAG;AACP,UAAM,eAAe,KAAK,YAAY,KAAK;AAC3C,QAAI,KAAK,MAAM;AACb,YAAM,YAAY,KAAK,MAAM,KAAK,QAAQ,YAAY;AACtD,YAAM,eAAe,KAAK,QAAQ,YAAY;AAC9C,YAAM,YAAY,eAAe,KAAK;AACtC,aAAO,KAAK,IAAI,YAAY,WAAW,KAAK,MAAM;AAAA,IACpD;AACA,UAAM,QAAQ,KAAK,MAAM,CAAC;AAC1B,UAAM,UAAU,IAAI;AACpB,UAAM,OAAO,eAAe,QAAQ,KAAK,YAAY;AACrD,WAAO,KAAK,KAAK,IAAI;AAAA,EACvB;AAAA,EACA,QAAQ,aAAa;AACnB,QAAI,eAAe,KAAM,QAAO,KAAK;AACrC,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AAAA,EACA,SAAS,GAAG;AACV,UAAM,KAAK,KAAK;AAChB,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,QAAI;AACJ,QAAI,KAAK,MAAM;AACb,YAAM,IAAI,SAAU,IAAI;AACtB,cAAM,WAAW,IAAI,KAAK,MAAM,MAAM,KAAK,IAAI,OAAO,IAAI,EAAE;AAC5D,cAAM,YAAY,YAAY,CAAC,KAAK,CAAC,YAAY;AACjD,cAAM,WAAW,KAAK,IAAI,IAAI,SAAS,KAAK,MAAM,IAAI,MAAM,IAAI;AAChE,cAAM,UAAU,KAAK,IAAI,KAAK,IAAI,UAAU,CAAC,GAAG,CAAC;AACjD,eAAO;AAAA,MACT;AACA,YAAM,UAAU,KAAK,IAAI,KAAK;AAC9B,kBAAY,MAAM,IAAI,KAAK,MAAM,EAAE,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE,EAAE,IAAI,KAAK,MAAM,EAAE,UAAU,IAAI,CAAC;AAC/F,aAAO;AAAA,IACT;AACA,UAAM,YAAY,KAAK,MAAM,KAAK,MAAM,CAAC;AACzC,UAAM,eAAe,KAAK,YAAY,MAAM;AAC5C,UAAM,WAAW,gBAAgB,CAAC,KAAK,KAAK;AAC5C,gBAAY,aAAa,WAAW,IAAI,IAAI;AAC5C,WAAO,KAAK,MAAM,SAAS;AAAA,EAC7B;AAAA,EACA,SAAS,GAAG;AACV,QAAI,KAAK,MAAM;AACb,aAAO,KAAK,IAAI,GAAG,KAAK,QAAQ,KAAK,SAAS,CAAC;AAAA,IACjD;AACA,WAAO,KAAK,KAAK,IAAI,KAAK,SAAS,CAAC;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,QAAQ,OAAO,YAAY,aAAa;AAC5C,SAAK,OAAO,KAAK;AAAA,MACf,aAAa,UAAU;AAAA,MACvB,QAAQ,SAAS;AAAA,MACjB,UAAU;AAAA,MACV;AAAA,MACA,aAAa;AAAA,MACb,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,YAAY,KAAK,SAAS;AAChC,iBAAa,KAAK,SAAS,EAAE,UAAU;AACvC,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,QAAI,KAAK,SAAU,QAAO;AAC1B,SAAK,KAAK,CAAC;AACX,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,SAAS;AACf,SAAK,WAAW,WAAW,OAAO,CAAC,KAAK,WAAW;AACnD,WAAO;AAAA,EACT;AAAA,EACA,SAAS,WAAW,OAAO,MAAM;AAC/B,QAAI,EAAE,qBAAqB,WAAW;AACpC,aAAO;AACP,cAAQ;AACR,kBAAY,KAAK,SAAS;AAAA,IAC5B;AACA,QAAI,CAAC,WAAW;AACd,YAAM,MAAM,6CAA6C;AAAA,IAC3D;AACA,cAAU,SAAS,MAAM,OAAO,IAAI;AACpC,WAAO;AAAA,EACT;AAAA,EACA,KAAK,IAAI;AACP,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,SAAK,MAAM,OAAO,KAAK;AACvB,SAAK,SAAS;AACd,UAAM,YAAY,KAAK,SAAS;AAChC,UAAM,UAAU,KAAK,kBAAkB,aAAa,KAAK,SAAS;AAClE,SAAK,gBAAgB;AACrB,UAAM,WAAW,KAAK,SAAS;AAC/B,UAAM,cAAc,KAAK,aAAa,KAAK,KAAK,QAAQ;AACxD,UAAM,eAAe,KAAK,YAAY,YAAY,KAAK,SAAS;AAChE,SAAK,YAAY,KAAK;AACtB,QAAI,aAAa;AACf,WAAK,KAAK,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,cAAc,KAAK;AACzB,SAAK,OAAO,CAAC,eAAe,CAAC,gBAAgB,KAAK,SAAS;AAC3D,SAAK,WAAW;AAChB,QAAI,YAAY;AAChB,QAAI,WAAW,aAAa;AAC1B,WAAK,YAAY,OAAO;AACxB,WAAK,aAAa,IAAI,OAAO;AAC7B,kBAAY,KAAK,KAAK,cAAc,KAAK,SAAS;AAClD,WAAK,KAAK,QAAQ,IAAI;AAAA,IACxB;AACA,SAAK,OAAO,KAAK,QAAQ,aAAa;AACtC,QAAI,cAAc;AAChB,WAAK,KAAK,YAAY,IAAI;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,MAAM;AACT,QAAI,QAAQ,MAAM;AAChB,aAAO,KAAK;AAAA,IACd;AACA,UAAM,KAAK,OAAO,KAAK;AACvB,SAAK,KAAK,EAAE;AACZ,WAAO;AAAA,EACT;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,OAAO,cAAc,YAAa,QAAO,KAAK;AAClD,SAAK,YAAY;AACjB,WAAO;AAAA,EACT;AAAA,EACA,aAAa;AACX,UAAM,YAAY,KAAK,SAAS;AAChC,iBAAa,UAAU,WAAW,IAAI;AACtC,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,YAAY,SAAS;AACnB,QAAI,CAAC,WAAW,CAAC,KAAK,eAAgB;AACtC,aAAS,IAAI,GAAG,MAAM,KAAK,OAAO,QAAQ,IAAI,KAAK,EAAE,GAAG;AACtD,YAAM,UAAU,KAAK,OAAO,CAAC;AAC7B,YAAM,UAAU,KAAK,kBAAkB,CAAC,QAAQ,eAAe;AAC/D,gBAAU,CAAC,QAAQ;AACnB,UAAI,WAAW,SAAS;AACtB,gBAAQ,YAAY,KAAK,IAAI;AAC7B,gBAAQ,cAAc;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,QAAQ,SAAS;AAChC,SAAK,SAAS,MAAM,IAAI;AAAA,MACtB;AAAA,MACA,QAAQ,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAAA,IAC5C;AACA,QAAI,KAAK,gBAAgB;AACvB,YAAM,YAAY,KAAK,SAAS;AAChC,mBAAa,UAAU,KAAK;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA;AAAA,EAGA,KAAK,cAAc;AACjB,QAAI,cAAc;AAClB,aAAS,IAAI,GAAG,MAAM,KAAK,OAAO,QAAQ,IAAI,KAAK,EAAE,GAAG;AACtD,YAAM,UAAU,KAAK,OAAO,CAAC;AAC7B,YAAM,YAAY,QAAQ,OAAO,KAAK,MAAM,YAAY;AACxD,cAAQ,WAAW,QAAQ,YAAY,cAAc;AACrD,oBAAc,eAAe,QAAQ;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,aAAa,QAAQ,QAAQ,OAAO;AAClC,QAAI,KAAK,SAAS,MAAM,GAAG;AACzB,UAAI,CAAC,KAAK,SAAS,MAAM,EAAE,OAAO,aAAa;AAC7C,cAAM,QAAQ,KAAK,OAAO,QAAQ,KAAK,SAAS,MAAM,EAAE,MAAM;AAC9D,aAAK,OAAO,OAAO,OAAO,CAAC;AAC3B,eAAO;AAAA,MACT;AACA,UAAI,KAAK,SAAS,MAAM,EAAE,OAAO,UAAU;AACzC,aAAK,SAAS,MAAM,EAAE,OAAO,SAAS,KAAK,MAAM,QAAQ,KAAK;AAAA,MAChE,OAAO;AACL,aAAK,SAAS,MAAM,EAAE,QAAQ,GAAG,MAAM;AAAA,MACzC;AACA,WAAK,SAAS,MAAM,EAAE,OAAO,WAAW;AACxC,YAAM,YAAY,KAAK,SAAS;AAChC,mBAAa,UAAU,KAAK;AAC5B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACF;AACA,OAAO,KAAK;AACZ,IAAM,aAAN,MAAiB;AAAA,EACf,YAAY,cAAc,IAAI,OAAO,GAAG,KAAK,IAAI,OAAO,MAAM;AAC5D,SAAK,aAAa;AAClB,SAAK,KAAK;AACV,SAAK,OAAO;AAAA,EACd;AAAA,EACA,2BAA2B;AAAA,EAAC;AAC9B;AACA,OAAO,CAAC,QAAQ,UAAU,GAAG;AAAA,EAC3B,UAAU,QAAQ;AAChB,WAAO,IAAI,WAAW,OAAO,WAAW,UAAU,KAAK,UAAU,GAAG,OAAO,EAAE;AAAA,EAC/E;AACF,CAAC;AACD,IAAM,YAAY,CAAC,MAAM,SAAS,KAAK,WAAW,IAAI;AACtD,IAAM,qBAAqB,YAAU,OAAO;AAC5C,SAAS,kBAAkB;AACzB,QAAM,UAAU,KAAK,uBAAuB;AAC5C,QAAM,eAAe,QAAQ,IAAI,kBAAkB,EAAE,OAAO,WAAW,IAAI,OAAO,CAAC;AACnF,OAAK,UAAU,YAAY;AAC3B,OAAK,uBAAuB,MAAM;AAClC,MAAI,KAAK,uBAAuB,OAAO,MAAM,GAAG;AAC9C,SAAK,WAAW;AAAA,EAClB;AACF;AACA,IAAM,cAAN,MAAkB;AAAA,EAChB,cAAc;AACZ,SAAK,UAAU,CAAC;AAChB,SAAK,MAAM,CAAC;AAAA,EACd;AAAA,EACA,IAAI,QAAQ;AACV,QAAI,KAAK,QAAQ,SAAS,MAAM,EAAG;AACnC,UAAM,KAAK,OAAO,KAAK;AACvB,SAAK,QAAQ,KAAK,MAAM;AACxB,SAAK,IAAI,KAAK,EAAE;AAChB,WAAO;AAAA,EACT;AAAA,EACA,YAAY,IAAI;AACd,UAAM,YAAY,KAAK,IAAI,QAAQ,KAAK,CAAC,KAAK;AAC9C,SAAK,IAAI,OAAO,GAAG,WAAW,CAAC;AAC/B,SAAK,QAAQ,OAAO,GAAG,WAAW,IAAI,WAAW,CAAC,EAAE,QAAQ,OAAK,EAAE,yBAAyB,CAAC;AAC7F,WAAO;AAAA,EACT;AAAA,EACA,KAAK,IAAI,WAAW;AAClB,UAAM,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC;AACrC,SAAK,IAAI,OAAO,OAAO,GAAG,KAAK,CAAC;AAChC,SAAK,QAAQ,OAAO,OAAO,GAAG,SAAS;AACvC,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,IAAI;AACV,WAAO,KAAK,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,CAAC;AAAA,EAC9C;AAAA,EACA,SAAS;AACP,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA,EACA,QAAQ;AACN,QAAI,aAAa;AACjB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,EAAE,GAAG;AAC5C,YAAM,SAAS,KAAK,QAAQ,CAAC;AAC7B,YAAM,YAAY,cAAc,OAAO,QAAQ,WAAW;AAAA,OAE1D,CAAC,OAAO,aAAa,CAAC,OAAO,UAAU,WAAW,SAAS,OAAO,EAAE,OAAO,CAAC,WAAW,aAAa,CAAC,WAAW,UAAU,WAAW,SAAS,WAAW,EAAE;AAC3J,UAAI,WAAW;AACb,aAAK,OAAO,OAAO,EAAE;AACrB,cAAM,YAAY,OAAO,UAAU,UAAU;AAC7C,aAAK,KAAK,WAAW,IAAI,SAAS;AAClC,qBAAa;AACb,UAAE;AAAA,MACJ,OAAO;AACL,qBAAa;AAAA,MACf;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,IAAI;AACT,UAAM,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC;AACrC,SAAK,IAAI,OAAO,OAAO,CAAC;AACxB,SAAK,QAAQ,OAAO,OAAO,CAAC;AAC5B,WAAO;AAAA,EACT;AACF;AACA,gBAAgB;AAAA,EACd,SAAS;AAAA,IACP,QAAQ,UAAU,OAAO,MAAM;AAC7B,YAAM,IAAI,OAAO,SAAS,UAAU,OAAO,IAAI;AAC/C,YAAM,YAAY,KAAK,SAAS;AAChC,aAAO,IAAI,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE,QAAQ,IAAI,EAAE,SAAS,UAAU,KAAK,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI;AAAA,IACzG;AAAA,IACA,MAAM,IAAI,MAAM;AACd,aAAO,KAAK,QAAQ,GAAG,IAAI,IAAI;AAAA,IACjC;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,6BAA6B,eAAe;AAC1C,WAAK,uBAAuB,YAAY,cAAc,EAAE;AAAA,IAC1D;AAAA,IACA,kBAAkB,SAAS;AACzB,aAAO,KAAK,uBAAuB,QAAQ,OAAO,YAAU,OAAO,MAAM,QAAQ,EAAE,EAAE,IAAI,kBAAkB,EAAE,OAAO,WAAW,IAAI,OAAO,CAAC;AAAA,IAC7I;AAAA,IACA,WAAW,QAAQ;AACjB,WAAK,uBAAuB,IAAI,MAAM;AACtC,eAAS,gBAAgB,KAAK,QAAQ;AACtC,WAAK,WAAW,SAAS,UAAU,gBAAgB,KAAK,IAAI,CAAC;AAAA,IAC/D;AAAA,IACA,iBAAiB;AACf,UAAI,KAAK,YAAY,MAAM;AACzB,aAAK,yBAAyB,IAAI,YAAY,EAAE,IAAI,IAAI,WAAW,IAAI,OAAO,IAAI,CAAC,CAAC;AAAA,MACtF;AAAA,IACF;AAAA,EACF;AACF,CAAC;AACD,IAAM,aAAa,CAAC,GAAG,MAAM,EAAE,OAAO,QAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAC3D,OAAO,QAAQ;AAAA,EACb,KAAK,GAAG,GAAG;AACT,WAAO,KAAK,UAAU,QAAQ,GAAG,CAAC;AAAA,EACpC;AAAA;AAAA,EAEA,IAAI,GAAG,GAAG;AACR,WAAO,KAAK,UAAU,OAAO,GAAG,CAAC;AAAA,EACnC;AAAA,EACA,UAAU,MAAM,aAAa,KAAK;AAChC,QAAI,OAAO,gBAAgB,UAAU;AACnC,aAAO,KAAK,UAAU,MAAM;AAAA,QAC1B,CAAC,WAAW,GAAG;AAAA,MACjB,CAAC;AAAA,IACH;AACA,QAAI,SAAS;AACb,QAAI,KAAK,aAAa,MAAM,MAAM,EAAG,QAAO;AAC5C,QAAI,UAAU,IAAI,UAAU,KAAK,QAAQ,EAAE,GAAG,MAAM;AACpD,QAAI,OAAO,OAAO,KAAK,MAAM;AAC7B,SAAK,MAAM,WAAY;AACrB,gBAAU,QAAQ,KAAK,KAAK,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC;AAAA,IACnD,GAAG,SAAU,KAAK;AAChB,WAAK,QAAQ,EAAE,IAAI,EAAE,QAAQ,GAAG,GAAG,EAAE,QAAQ,CAAC;AAC9C,aAAO,QAAQ,KAAK;AAAA,IACtB,GAAG,SAAU,YAAY;AACvB,YAAM,UAAU,OAAO,KAAK,UAAU;AACtC,YAAM,cAAc,WAAW,SAAS,IAAI;AAC5C,UAAI,YAAY,QAAQ;AACtB,cAAM,iBAAiB,KAAK,QAAQ,EAAE,IAAI,EAAE,WAAW;AACvD,cAAM,eAAe,IAAI,UAAU,QAAQ,KAAK,CAAC,EAAE,QAAQ;AAC3D,eAAO,OAAO,cAAc,cAAc;AAC1C,gBAAQ,KAAK,YAAY;AAAA,MAC3B;AACA,YAAM,aAAa,IAAI,UAAU,QAAQ,GAAG,CAAC,EAAE,QAAQ;AACvD,aAAO,OAAO,YAAY,UAAU;AACpC,cAAQ,GAAG,UAAU;AACrB,aAAO;AACP,eAAS;AAAA,IACX,CAAC;AACD,SAAK,iBAAiB,MAAM,OAAO;AACnC,WAAO;AAAA,EACT;AAAA,EACA,KAAK,OAAO,QAAQ;AAClB,QAAI,KAAK,aAAa,QAAQ,OAAO,MAAM,EAAG,QAAO;AACrD,QAAI,UAAU,IAAI,UAAU,KAAK,QAAQ,EAAE,GAAG,IAAI,UAAU,KAAK,CAAC;AAClE,SAAK,MAAM,WAAY;AACrB,gBAAU,QAAQ,KAAK,KAAK,QAAQ,EAAE,KAAK,CAAC;AAAA,IAC9C,GAAG,SAAU,KAAK;AAChB,WAAK,QAAQ,EAAE,KAAK,QAAQ,GAAG,GAAG,GAAG,MAAM;AAC3C,aAAO,QAAQ,KAAK;AAAA,IACtB,GAAG,SAAU,UAAU,UAAU;AAC/B,eAAS;AACT,cAAQ,GAAG,QAAQ;AAAA,IACrB,CAAC;AACD,SAAK,iBAAiB,QAAQ,OAAO;AACrC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,UAAU,aAAa,UAAU,QAAQ;AACvC,eAAW,YAAY,YAAY;AACnC,QAAI,KAAK,kBAAkB,CAAC,YAAY,KAAK,aAAa,aAAa,WAAW,GAAG;AACnF,aAAO;AAAA,IACT;AACA,UAAM,WAAW,OAAO,aAAa,WAAW;AAChD,aAAS,YAAY,UAAU,OAAO,YAAY,SAAS,UAAU,OAAO,SAAS,CAAC;AACtF,UAAM,UAAU,IAAI,UAAU,KAAK,QAAQ,EAAE,KAAK,SAAS,eAAe,MAAM;AAChF,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,aAAS,QAAQ;AACf,gBAAU,WAAW,KAAK,QAAQ;AAClC,eAAS,UAAU,UAAU,aAAa,OAAO;AACjD,uBAAiB,IAAI,OAAO,WAAW,SAAS,OAAO;AACvD,cAAQ,WAAW,IAAI;AACvB,UAAI,CAAC,UAAU;AACb,gBAAQ,6BAA6B,IAAI;AAAA,MAC3C;AAAA,IACF;AACA,aAAS,IAAI,KAAK;AAChB,UAAI,CAAC,SAAU,MAAK,eAAe;AACnC,YAAM;AAAA,QACJ,GAAG;AAAA,QACH,GAAG;AAAA,MACL,IAAI,IAAI,MAAM,MAAM,EAAE,UAAU,QAAQ,kBAAkB,IAAI,CAAC;AAC/D,UAAI,SAAS,IAAI,OAAO,iCACnB,cADmB;AAAA,QAEtB,QAAQ,CAAC,IAAI,EAAE;AAAA,MACjB,EAAC;AACD,UAAI,QAAQ,KAAK,kBAAkB,UAAU,UAAU;AACvD,UAAI,QAAQ;AACV,iBAAS,OAAO,UAAU,IAAI,EAAE;AAChC,gBAAQ,MAAM,UAAU,IAAI,EAAE;AAC9B,cAAM,UAAU,OAAO;AACvB,cAAM,WAAW,MAAM;AACvB,cAAM,gBAAgB,CAAC,UAAU,KAAK,SAAS,UAAU,GAAG;AAC5D,cAAM,YAAY,cAAc,IAAI,OAAK,KAAK,IAAI,IAAI,QAAQ,CAAC;AAC/D,cAAM,WAAW,KAAK,IAAI,GAAG,SAAS;AACtC,cAAM,QAAQ,UAAU,QAAQ,QAAQ;AACxC,eAAO,SAAS,cAAc,KAAK;AAAA,MACrC;AACA,UAAI,UAAU;AACZ,YAAI,CAAC,UAAU;AACb,iBAAO,SAAS,YAAY,UAAU;AAAA,QACxC;AACA,YAAI,KAAK,kBAAkB,cAAc;AACvC,gBAAM,SAAS;AAAA,QACjB;AAAA,MACF;AACA,cAAQ,KAAK,KAAK;AAClB,cAAQ,GAAG,MAAM;AACjB,YAAM,mBAAmB,QAAQ,GAAG,GAAG;AACvC,qBAAe,iBAAiB;AAChC,gBAAU,IAAI,OAAO,gBAAgB;AACrC,WAAK,aAAa,OAAO;AACzB,cAAQ,WAAW,IAAI;AACvB,aAAO,QAAQ,KAAK;AAAA,IACtB;AACA,aAAS,SAAS,eAAe;AAC/B,WAAK,cAAc,UAAU,UAAU,SAAS,OAAO,YAAY,UAAU,UAAU,SAAS,GAAG;AACjG,iBAAS,UAAU,eAAe,OAAO;AAAA,MAC3C;AACA,oBAAc,iCACT,gBADS;AAAA,QAEZ;AAAA,MACF;AAAA,IACF;AACA,SAAK,MAAM,OAAO,KAAK,UAAU,IAAI;AACrC,SAAK,kBAAkB,KAAK,iBAAiB,aAAa,OAAO;AACjE,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,EAAE,IAAI;AACJ,WAAO,KAAK,aAAa,KAAK,EAAE;AAAA,EAClC;AAAA;AAAA,EAEA,EAAE,IAAI;AACJ,WAAO,KAAK,aAAa,KAAK,EAAE;AAAA,EAClC;AAAA,EACA,GAAG,IAAI;AACL,WAAO,KAAK,aAAa,MAAM,EAAE;AAAA,EACnC;AAAA,EACA,GAAG,IAAI;AACL,WAAO,KAAK,aAAa,MAAM,EAAE;AAAA,EACnC;AAAA,EACA,GAAG,KAAK,GAAG;AACT,WAAO,KAAK,kBAAkB,KAAK,EAAE;AAAA,EACvC;AAAA,EACA,GAAG,KAAK,GAAG;AACT,WAAO,KAAK,kBAAkB,KAAK,EAAE;AAAA,EACvC;AAAA,EACA,MAAM,IAAI,IAAI;AACZ,WAAO,KAAK,GAAG,EAAE,EAAE,GAAG,EAAE;AAAA,EAC1B;AAAA,EACA,kBAAkB,QAAQ,KAAK;AAC7B,UAAM,IAAI,UAAU,GAAG;AACvB,QAAI,KAAK,aAAa,QAAQ,GAAG,EAAG,QAAO;AAC3C,UAAM,UAAU,IAAI,UAAU,KAAK,QAAQ,EAAE,GAAG,GAAG;AACnD,QAAI,QAAQ;AACZ,SAAK,MAAM,WAAY;AACrB,cAAQ,KAAK,QAAQ,EAAE,MAAM,EAAE;AAC/B,cAAQ,KAAK,KAAK;AAClB,cAAQ,GAAG,QAAQ,GAAG;AAAA,IACxB,GAAG,SAAU,KAAK;AAChB,WAAK,QAAQ,EAAE,MAAM,EAAE,QAAQ,GAAG,GAAG,CAAC;AACtC,aAAO,QAAQ,KAAK;AAAA,IACtB,GAAG,SAAU,OAAO;AAClB,cAAQ,GAAG,QAAQ,IAAI,UAAU,KAAK,CAAC;AAAA,IACzC,CAAC;AACD,SAAK,iBAAiB,QAAQ,OAAO;AACrC,WAAO;AAAA,EACT;AAAA,EACA,aAAa,QAAQ,KAAK;AACxB,QAAI,KAAK,aAAa,QAAQ,GAAG,EAAG,QAAO;AAC3C,UAAM,UAAU,IAAI,UAAU,KAAK,QAAQ,EAAE,GAAG,GAAG;AACnD,SAAK,MAAM,WAAY;AACrB,cAAQ,KAAK,KAAK,QAAQ,EAAE,MAAM,EAAE,CAAC;AAAA,IACvC,GAAG,SAAU,KAAK;AAChB,WAAK,QAAQ,EAAE,MAAM,EAAE,QAAQ,GAAG,GAAG,CAAC;AACtC,aAAO,QAAQ,KAAK;AAAA,IACtB,CAAC;AACD,SAAK,iBAAiB,QAAQ,OAAO;AACrC,WAAO;AAAA,EACT;AAAA,EACA,aAAa,QAAQ,OAAO;AAC1B,WAAO,KAAK,aAAa,QAAQ,IAAI,UAAU,KAAK,CAAC;AAAA,EACvD;AAAA;AAAA,EAEA,GAAG,IAAI;AACL,WAAO,KAAK,aAAa,MAAM,EAAE;AAAA,EACnC;AAAA;AAAA,EAEA,GAAG,IAAI;AACL,WAAO,KAAK,aAAa,MAAM,EAAE;AAAA,EACnC;AAAA;AAAA,EAEA,KAAK,IAAI,IAAI;AACX,WAAO,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE;AAAA,EACxB;AAAA,EACA,MAAM,IAAI,IAAI;AACZ,WAAO,KAAK,GAAG,EAAE,EAAE,GAAG,EAAE;AAAA,EAC1B;AAAA;AAAA,EAEA,OAAO,IAAI,IAAI;AACb,WAAO,KAAK,GAAG,EAAE,EAAE,GAAG,EAAE;AAAA,EAC1B;AAAA;AAAA,EAEA,KAAK,QAAQ,SAAS;AACpB,QAAI;AACJ,QAAI,CAAC,UAAU,CAAC,SAAS;AACvB,YAAM,KAAK,SAAS,KAAK;AAAA,IAC3B;AACA,QAAI,CAAC,QAAQ;AACX,eAAS,IAAI,QAAQ,IAAI,SAAS;AAAA,IACpC;AACA,QAAI,CAAC,SAAS;AACZ,gBAAU,IAAI,SAAS,IAAI,QAAQ;AAAA,IACrC;AACA,WAAO,KAAK,MAAM,MAAM,EAAE,OAAO,OAAO;AAAA,EAC1C;AAAA;AAAA,EAEA,MAAM,QAAQ;AACZ,WAAO,KAAK,aAAa,SAAS,MAAM;AAAA,EAC1C;AAAA;AAAA,EAEA,OAAO,SAAS;AACd,WAAO,KAAK,aAAa,UAAU,OAAO;AAAA,EAC5C;AAAA;AAAA,EAEA,KAAK,GAAG,GAAG,GAAG,GAAG;AACf,QAAI,UAAU,WAAW,GAAG;AAC1B,aAAO,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,IAC/B;AACA,QAAI,KAAK,aAAa,QAAQ,CAAC,EAAG,QAAO;AACzC,UAAM,UAAU,IAAI,UAAU,KAAK,QAAQ,EAAE,KAAK,KAAK,SAAS,UAAU,EAAE,GAAG,CAAC;AAChF,SAAK,MAAM,WAAY;AACrB,cAAQ,KAAK,KAAK,SAAS,MAAM,CAAC;AAAA,IACpC,GAAG,SAAU,KAAK;AAChB,WAAK,SAAS,KAAK,QAAQ,GAAG,GAAG,CAAC;AAClC,aAAO,QAAQ,KAAK;AAAA,IACtB,CAAC;AACD,SAAK,iBAAiB,QAAQ,OAAO;AACrC,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,QAAQ,OAAO;AACb,WAAO,KAAK,aAAa,WAAW,KAAK;AAAA,EAC3C;AAAA;AAAA,EAEA,QAAQ,IAAI,IAAI,QAAQ,SAAS;AAC/B,WAAO,KAAK,aAAa,WAAW,IAAI,IAAI,IAAI,IAAI,QAAQ,OAAO,CAAC;AAAA,EACtE;AAAA,EACA,OAAO,GAAG;AACR,QAAI,OAAO,MAAM,UAAU;AACzB,aAAO,KAAK,OAAO;AAAA,QACjB,QAAQ,UAAU,CAAC;AAAA,QACnB,OAAO,UAAU,CAAC;AAAA,QAClB,SAAS,UAAU,CAAC;AAAA,MACtB,CAAC;AAAA,IACH;AACA,QAAI,EAAE,WAAW,KAAM,MAAK,KAAK,gBAAgB,EAAE,OAAO;AAC1D,QAAI,EAAE,SAAS,KAAM,MAAK,KAAK,cAAc,EAAE,KAAK;AACpD,QAAI,EAAE,UAAU,KAAM,MAAK,KAAK,UAAU,EAAE,MAAM;AAClD,WAAO;AAAA,EACT;AACF,CAAC;AACD,OAAO,QAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,SAAS,QAAQ,QAAQ;AACzB,IAAM,MAAN,cAAkB,UAAU;AAAA,EAC1B,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,OAAO,IAAI,GAAG,MAAM;AACpC,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA,EAEA,OAAO;AACL,QAAI,CAAC,KAAK,OAAO,EAAG,QAAO,KAAK,KAAK,EAAE,KAAK;AAC5C,WAAO,MAAM,KAAK,KAAK,cAAc,MAAM,CAAC,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC;AAAA,EACtE;AAAA,EACA,SAAS;AACP,WAAO,CAAC,KAAK,KAAK,cAAc,EAAE,KAAK,KAAK,sBAAsB,QAAQ,OAAO,eAAe,KAAK,KAAK,WAAW,aAAa;AAAA,EACpI;AAAA;AAAA,EAEA,YAAY;AACV,QAAI,CAAC,KAAK,OAAO,EAAG,QAAO,KAAK,KAAK,EAAE,UAAU;AACjD,WAAO,KAAK,KAAK;AAAA,MACf,OAAO;AAAA,MACP,SAAS;AAAA,IACX,CAAC,EAAE,KAAK,eAAe,OAAO,KAAK;AAAA,EACrC;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,KAAK;AAAA,MACf,OAAO;AAAA,MACP,SAAS;AAAA,IACX,CAAC,EAAE,KAAK,eAAe,MAAM,KAAK,EAAE,KAAK,eAAe,MAAM,KAAK;AAAA,EACrE;AAAA;AAAA;AAAA,EAGA,OAAO;AACL,QAAI,KAAK,OAAO,EAAG,QAAO;AAC1B,WAAO,MAAM,KAAK;AAAA,EACpB;AACF;AACA,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,QAAQ,kBAAkB,WAAY;AACpC,aAAO,KAAK,IAAI,IAAI,IAAI,CAAC;AAAA,IAC3B,CAAC;AAAA,EACH;AACF,CAAC;AACD,SAAS,KAAK,OAAO,IAAI;AACzB,IAAI,WAAW,MAAM,gBAAgB,UAAU;AAAA;AAAA,EAE7C,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,UAAU,IAAI,GAAG,MAAM;AAAA,EACzC;AACF;AACA,gBAAgB;AAAA,EACd,WAAW;AAAA,IACT,QAAQ,kBAAkB,WAAY;AACpC,aAAO,KAAK,IAAI,IAAI,SAAS,CAAC;AAAA,IAChC,CAAC;AAAA,EACH;AACF,CAAC;AACD,SAAS,UAAU,QAAQ;AAC3B,SAAS,MAAM,MAAM;AACnB,MAAI,KAAK,WAAW,OAAO;AACzB,SAAK,MAAM;AAAA,EACb;AACA,OAAK,KAAK,YAAY,QAAQ,SAAS,eAAe,IAAI,CAAC;AAC3D,SAAO;AACT;AACA,SAAS,SAAS;AAChB,SAAO,KAAK,KAAK,sBAAsB;AACzC;AACA,SAAS,IAAI,IAAI,MAAM,KAAK,KAAK,GAAG;AAClC,MAAI,MAAM,MAAM;AACd,WAAO,IAAI;AAAA,EACb;AACA,SAAO,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,IAAI,KAAK,IAAI,CAAC;AACnD;AACA,SAAS,IAAI,IAAI,MAAM,KAAK,KAAK,GAAG;AAClC,MAAI,MAAM,MAAM;AACd,WAAO,IAAI;AAAA,EACb;AACA,SAAO,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,IAAI,KAAK,IAAI,CAAC;AACnD;AACA,SAAS,OAAO,IAAI,IAAI,MAAM,KAAK,KAAK,GAAG;AACzC,SAAO,KAAK,EAAE,IAAI,GAAG,EAAE,EAAE,IAAI,GAAG;AAClC;AACA,SAAS,GAAG,IAAI,MAAM,KAAK,KAAK,GAAG;AACjC,MAAI,MAAM,MAAM;AACd,WAAO,IAAI;AAAA,EACb;AACA,SAAO,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,IAAI,KAAK,IAAI,EAAE;AACpD;AACA,SAAS,GAAG,IAAI,MAAM,KAAK,KAAK,GAAG;AACjC,MAAI,MAAM,MAAM;AACd,WAAO,IAAI;AAAA,EACb;AACA,SAAO,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,IAAI,KAAK,IAAI,EAAE;AACpD;AACA,SAAS,OAAO,IAAI,IAAI,MAAM,KAAK,KAAK,GAAG;AACzC,SAAO,KAAK,GAAG,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG;AACpC;AACA,SAAS,GAAG,IAAI;AACd,SAAO,KAAK,KAAK,KAAK,EAAE;AAC1B;AACA,SAAS,GAAG,IAAI;AACd,SAAO,KAAK,KAAK,KAAK,EAAE;AAC1B;AACA,SAAS,MAAM,IAAI,IAAI;AACrB,SAAO,KAAK,GAAG,EAAE,EAAE,GAAG,EAAE;AAC1B;AACA,SAAS,MAAM,QAAQ;AACrB,OAAK,SAAS,CAAC,CAAC;AAChB,SAAO;AACT;AACA,IAAM,WAA0B,OAAO,OAAsB,OAAO,eAAe;AAAA,EACjF,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN;AAAA,EACA,GAAG;AAAA,EACH,GAAG;AACL,GAAG,OAAO,aAAa;AAAA,EACrB,OAAO;AACT,CAAC,CAAC;AACF,IAAM,OAAN,cAAmB,MAAM;AAAA;AAAA,EAEvB,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,QAAQ,IAAI,GAAG,MAAM;AACrC,SAAK,IAAI,UAAU,KAAK,IAAI,WAAW,IAAI,UAAU,GAAG;AACxD,SAAK,WAAW;AAChB,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA,EAEA,QAAQ,OAAO;AACb,QAAI,SAAS,MAAM;AACjB,aAAO,KAAK,IAAI;AAAA,IAClB;AACA,SAAK,IAAI,UAAU,IAAI,UAAU,KAAK;AACtC,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA;AAAA,EAEA,QAAQ,SAAS;AACf,QAAI,OAAO,YAAY,WAAW;AAChC,WAAK,WAAW;AAAA,IAClB;AACA,QAAI,KAAK,UAAU;AACjB,YAAM,OAAO;AACb,UAAI,kBAAkB;AACtB,YAAM,UAAU,KAAK,IAAI;AACzB,WAAK,KAAK,SAAU,GAAG;AACrB,YAAI,cAAc,KAAK,IAAI,EAAG;AAC9B,cAAM,WAAW,QAAQ,OAAO,iBAAiB,KAAK,IAAI,EAAE,iBAAiB,WAAW;AACxF,cAAM,MAAM,UAAU,IAAI,UAAU,QAAQ;AAC5C,YAAI,KAAK,IAAI,UAAU;AACrB,eAAK,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC;AAC7B,cAAI,KAAK,KAAK,MAAM,MAAM;AACxB,+BAAmB;AAAA,UACrB,OAAO;AACL,iBAAK,KAAK,MAAM,IAAI,MAAM,kBAAkB,CAAC;AAC7C,8BAAkB;AAAA,UACpB;AAAA,QACF;AAAA,MACF,CAAC;AACD,WAAK,KAAK,SAAS;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,QAAQ,GAAG;AACT,SAAK,MAAM;AACX,SAAK,IAAI,UAAU,IAAI,UAAU,EAAE,WAAW,GAAG;AACjD,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AACf,mBAAe,MAAM,KAAK,KAAK;AAAA,MAC7B,SAAS;AAAA,IACX,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,KAAK,MAAM;AACT,QAAI,SAAS,QAAQ;AACnB,YAAM,WAAW,KAAK,KAAK;AAC3B,UAAI,YAAY;AAChB,aAAO;AACP,eAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,IAAI,KAAK,EAAE,GAAG;AACnD,YAAI,SAAS,CAAC,EAAE,aAAa,cAAc,cAAc,SAAS,CAAC,CAAC,GAAG;AACrE,cAAI,MAAM,EAAG,aAAY,IAAI;AAC7B;AAAA,QACF;AACA,YAAI,MAAM,aAAa,SAAS,CAAC,EAAE,aAAa,KAAK,MAAM,SAAS,CAAC,CAAC,EAAE,IAAI,aAAa,MAAM;AAC7F,kBAAQ;AAAA,QACV;AACA,gBAAQ,SAAS,CAAC,EAAE;AAAA,MACtB;AACA,aAAO;AAAA,IACT;AACA,SAAK,MAAM,EAAE,MAAM,IAAI;AACvB,QAAI,OAAO,SAAS,YAAY;AAC9B,WAAK,KAAK,MAAM,IAAI;AAAA,IACtB,OAAO;AACL,cAAQ,OAAO,IAAI,MAAM,IAAI;AAC7B,eAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,KAAK;AAC7C,aAAK,QAAQ,KAAK,CAAC,CAAC;AAAA,MACtB;AAAA,IACF;AACA,WAAO,KAAK,MAAM,KAAK,EAAE,QAAQ;AAAA,EACnC;AACF;AACA,OAAO,MAAM,QAAQ;AACrB,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,MAAM,kBAAkB,SAAU,OAAO,IAAI;AAC3C,aAAO,KAAK,IAAI,IAAI,KAAK,CAAC,EAAE,KAAK,IAAI;AAAA,IACvC,CAAC;AAAA;AAAA,IAED,OAAO,kBAAkB,SAAU,OAAO,IAAI;AAC5C,aAAO,KAAK,IAAI,IAAI,KAAK,CAAC,EAAE,MAAM,IAAI;AAAA,IACxC,CAAC;AAAA,EACH;AACF,CAAC;AACD,SAAS,MAAM,MAAM;AACrB,IAAM,QAAN,cAAoB,MAAM;AAAA;AAAA,EAExB,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,SAAS,IAAI,GAAG,MAAM;AACtC,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA,EAEA,GAAG,KAAK;AACN,WAAO,KAAK,KAAK,MAAM,GAAG;AAAA,EAC5B;AAAA;AAAA,EAEA,GAAG,KAAK;AACN,WAAO,KAAK,KAAK,MAAM,GAAG;AAAA,EAC5B;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,IAAI,WAAW;AACpB,UAAM,OAAO,KAAK,OAAO;AACzB,QAAI,EAAE,gBAAgB,OAAO;AAC3B,aAAO;AAAA,IACT;AACA,UAAM,IAAI,KAAK,MAAM,IAAI;AACzB,UAAM,WAAW,QAAQ,OAAO,iBAAiB,KAAK,IAAI,EAAE,iBAAiB,WAAW;AACxF,UAAM,MAAM,KAAK,IAAI,UAAU,IAAI,UAAU,QAAQ;AACrD,WAAO,KAAK,GAAG,IAAI,MAAM,CAAC,EAAE,KAAK,KAAK,KAAK,EAAE,CAAC;AAAA,EAChD;AAAA;AAAA,EAEA,KAAK,MAAM;AACT,QAAI,QAAQ,KAAM,QAAO,KAAK,KAAK,eAAe,KAAK,IAAI,WAAW,OAAO;AAC7E,QAAI,OAAO,SAAS,YAAY;AAC9B,WAAK,MAAM,EAAE,MAAM,IAAI;AACvB,WAAK,KAAK,MAAM,IAAI;AACpB,WAAK,MAAM,KAAK;AAAA,IAClB,OAAO;AACL,WAAK,MAAM,IAAI;AAAA,IACjB;AACA,WAAO;AAAA,EACT;AACF;AACA,OAAO,OAAO,QAAQ;AACtB,gBAAgB;AAAA,EACd,OAAO;AAAA,IACL,OAAO,kBAAkB,SAAU,OAAO,IAAI;AAC5C,YAAM,QAAQ,IAAI,MAAM;AACxB,UAAI,CAAC,KAAK,QAAQ;AAChB,aAAK,MAAM;AAAA,MACb;AACA,aAAO,KAAK,IAAI,KAAK,EAAE,KAAK,IAAI;AAAA,IAClC,CAAC;AAAA,EACH;AAAA,EACA,MAAM;AAAA,IACJ,SAAS,SAAU,OAAO,IAAI;AAC5B,aAAO,KAAK,MAAM,IAAI,EAAE,QAAQ;AAAA,IAClC;AAAA,EACF;AACF,CAAC;AACD,SAAS,OAAO,OAAO;AACvB,IAAM,SAAN,cAAqB,MAAM;AAAA,EACzB,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,UAAU,IAAI,GAAG,MAAM;AAAA,EACzC;AAAA,EACA,OAAO,GAAG;AACR,WAAO,KAAK,KAAK,KAAK,CAAC;AAAA,EACzB;AAAA;AAAA,EAEA,GAAG,KAAK;AACN,WAAO,KAAK,KAAK,KAAK,GAAG;AAAA,EAC3B;AAAA;AAAA,EAEA,GAAG,KAAK;AACN,WAAO,KAAK,GAAG,GAAG;AAAA,EACpB;AAAA,EACA,KAAK,OAAO;AACV,WAAO,KAAK,OAAO,IAAI,UAAU,KAAK,EAAE,OAAO,CAAC,CAAC;AAAA,EACnD;AACF;AACA,OAAO,QAAQ;AAAA,EACb,GAAG;AAAA,EACH,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,QAAQ;AACV,CAAC;AACD,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,QAAQ,kBAAkB,SAAU,QAAQ,GAAG;AAC7C,aAAO,KAAK,IAAI,IAAI,OAAO,CAAC,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG,CAAC;AAAA,IACrD,CAAC;AAAA,EACH;AACF,CAAC;AACD,SAAS,QAAQ,QAAQ;AACzB,IAAM,WAAN,cAAuB,UAAU;AAAA,EAC/B,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,YAAY,IAAI,GAAG,MAAM;AAAA,EAC3C;AAAA;AAAA,EAEA,SAAS;AACP,SAAK,QAAQ,EAAE,QAAQ,SAAU,IAAI;AACnC,SAAG,OAAO;AAAA,IACZ,CAAC;AACD,WAAO,MAAM,OAAO;AAAA,EACtB;AAAA,EACA,UAAU;AACR,WAAO,SAAS,qBAAqB,KAAK,GAAG,IAAI,GAAG;AAAA,EACtD;AACF;AACA,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,MAAM,kBAAkB,WAAY;AAClC,aAAO,KAAK,KAAK,EAAE,IAAI,IAAI,SAAS,CAAC;AAAA,IACvC,CAAC;AAAA,EACH;AAAA,EACA,SAAS;AAAA;AAAA,IAEP,UAAU;AACR,aAAO,KAAK,UAAU,WAAW;AAAA,IACnC;AAAA,IACA,SAAS,SAAS;AAChB,YAAM,UAAU,mBAAmB,WAAW,UAAU,KAAK,OAAO,EAAE,KAAK,EAAE,IAAI,OAAO;AACxF,aAAO,KAAK,KAAK,aAAa,UAAU,QAAQ,GAAG,IAAI,GAAG;AAAA,IAC5D;AAAA;AAAA,IAEA,SAAS;AACP,aAAO,KAAK,KAAK,aAAa,IAAI;AAAA,IACpC;AAAA,EACF;AACF,CAAC;AACD,SAAS,UAAU,UAAU;AAC7B,IAAM,gBAAN,cAA4B,QAAQ;AAAA,EAClC,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,iBAAiB,IAAI,GAAG,MAAM;AAAA,EAChD;AACF;AACA,gBAAgB;AAAA,EACd,WAAW;AAAA,IACT,eAAe,kBAAkB,SAAU,QAAQ,SAAS;AAC1D,aAAO,KAAK,IAAI,IAAI,cAAc,CAAC,EAAE,KAAK,QAAQ,OAAO;AAAA,IAC3D,CAAC;AAAA,EACH;AACF,CAAC;AACD,SAAS,eAAe,eAAe;AACvC,SAAS,MAAM,KAAK,KAAK;AACvB,OAAK,SAAS,EAAE,QAAQ,WAAS;AAC/B,QAAI;AACJ,QAAI;AACF,cAAQ,MAAM,gBAAgB,UAAU,EAAE,gBAAgB,IAAI,IAAI,MAAM,KAAK,CAAC,KAAK,KAAK,SAAS,QAAQ,CAAC,CAAC,IAAI,MAAM,KAAK;AAAA,IAC5H,SAAS,GAAG;AACV;AAAA,IACF;AACA,UAAM,IAAI,IAAI,OAAO,KAAK;AAC1B,UAAM,SAAS,EAAE,UAAU,KAAK,GAAG,EAAE,UAAU,EAAE,QAAQ,CAAC;AAC1D,UAAM,IAAI,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,EAAE,UAAU,MAAM;AACtD,UAAM,KAAK,EAAE,GAAG,EAAE,CAAC;AAAA,EACrB,CAAC;AACD,SAAO;AACT;AACA,SAAS,GAAG,KAAK;AACf,SAAO,KAAK,MAAM,KAAK,CAAC;AAC1B;AACA,SAAS,GAAG,KAAK;AACf,SAAO,KAAK,MAAM,GAAG,GAAG;AAC1B;AACA,SAAS,OAAO,SAAS,MAAM,KAAK,KAAK,GAAG;AAC1C,MAAI,WAAW,KAAM,QAAO,IAAI;AAChC,SAAO,KAAK,KAAK,IAAI,OAAO,SAAS,GAAG;AAC1C;AACA,SAAS,KAAK,KAAK,GAAG,KAAK,GAAG,MAAM,KAAK,KAAK,GAAG;AAC/C,QAAM,MAAM,KAAK,IAAI;AACrB,QAAM,MAAM,KAAK,IAAI;AACrB,SAAO,KAAK,MAAM,KAAK,GAAG;AAC5B;AACA,SAAS,KAAK,QAAQ,SAAS,MAAM,KAAK,KAAK,GAAG;AAChD,QAAM,IAAI,iBAAiB,MAAM,QAAQ,SAAS,GAAG;AACrD,QAAM,SAAS,EAAE,QAAQ,IAAI;AAC7B,QAAM,SAAS,EAAE,SAAS,IAAI;AAC9B,OAAK,SAAS,EAAE,QAAQ,WAAS;AAC/B,UAAM,IAAI,IAAI,MAAM,GAAG,EAAE,UAAU,IAAI,OAAO,KAAK,EAAE,QAAQ,CAAC;AAC9D,UAAM,MAAM,QAAQ,QAAQ,EAAE,GAAG,EAAE,CAAC;AAAA,EACtC,CAAC;AACD,SAAO;AACT;AACA,SAAS,MAAM,QAAQ,MAAM,KAAK,KAAK,GAAG;AACxC,MAAI,UAAU,KAAM,QAAO,IAAI;AAC/B,SAAO,KAAK,KAAK,QAAQ,IAAI,QAAQ,GAAG;AAC1C;AACA,SAAS,EAAE,IAAI,MAAM,KAAK,KAAK,GAAG;AAChC,MAAI,MAAM,KAAM,QAAO,IAAI;AAC3B,SAAO,KAAK,KAAK,IAAI,IAAI,GAAG,GAAG;AACjC;AACA,SAAS,EAAE,IAAI,MAAM,KAAK,KAAK,GAAG;AAChC,MAAI,MAAM,KAAM,QAAO,IAAI;AAC3B,SAAO,KAAK,KAAK,IAAI,GAAG,IAAI,GAAG;AACjC;AACA,IAAM,oBAAmC,OAAO,OAAsB,OAAO,eAAe;AAAA,EAC1F,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG,OAAO,aAAa;AAAA,EACrB,OAAO;AACT,CAAC,CAAC;AACF,IAAM,IAAN,cAAgB,UAAU;AAAA,EACxB,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,KAAK,IAAI,GAAG,MAAM;AAAA,EACpC;AACF;AACA,OAAO,GAAG,iBAAiB;AAC3B,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,OAAO,kBAAkB,WAAY;AACnC,aAAO,KAAK,IAAI,IAAI,EAAE,CAAC;AAAA,IACzB,CAAC;AAAA,EACH;AACF,CAAC;AACD,SAAS,GAAG,GAAG;AACf,IAAM,IAAN,cAAgB,UAAU;AAAA,EACxB,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,KAAK,IAAI,GAAG,MAAM;AAAA,EACpC;AAAA;AAAA,EAEA,OAAO,QAAQ;AACb,WAAO,KAAK,KAAK,UAAU,MAAM;AAAA,EACnC;AAAA;AAAA,EAEA,GAAG,KAAK;AACN,WAAO,KAAK,KAAK,QAAQ,KAAK,KAAK;AAAA,EACrC;AACF;AACA,OAAO,GAAG,iBAAiB;AAC3B,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,MAAM,kBAAkB,SAAU,KAAK;AACrC,aAAO,KAAK,IAAI,IAAI,EAAE,CAAC,EAAE,GAAG,GAAG;AAAA,IACjC,CAAC;AAAA,EACH;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AACP,YAAM,OAAO,KAAK,OAAO;AACzB,UAAI,CAAC,KAAM,QAAO;AAClB,YAAM,SAAS,KAAK,OAAO;AAC3B,UAAI,CAAC,QAAQ;AACX,eAAO,KAAK,OAAO;AAAA,MACrB;AACA,YAAM,QAAQ,OAAO,MAAM,IAAI;AAC/B,aAAO,IAAI,MAAM,KAAK;AACtB,WAAK,OAAO;AACZ,aAAO;AAAA,IACT;AAAA,IACA,OAAO,KAAK;AACV,UAAI,OAAO,KAAK,OAAO;AACvB,UAAI,CAAC,MAAM;AACT,eAAO,IAAI,EAAE;AACb,aAAK,KAAK,IAAI;AAAA,MAChB;AACA,UAAI,OAAO,QAAQ,YAAY;AAC7B,YAAI,KAAK,MAAM,IAAI;AAAA,MACrB,OAAO;AACL,aAAK,GAAG,GAAG;AAAA,MACb;AACA,aAAO;AAAA,IACT;AAAA,IACA,SAAS;AACP,YAAM,OAAO,KAAK,OAAO;AACzB,UAAI,QAAQ,KAAK,KAAK,SAAS,YAAY,MAAM,KAAK;AACpD,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF,CAAC;AACD,SAAS,GAAG,GAAG;AACf,IAAM,OAAN,cAAmB,UAAU;AAAA;AAAA,EAE3B,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,QAAQ,IAAI,GAAG,MAAM;AAAA,EACvC;AAAA;AAAA,EAEA,SAAS;AACP,SAAK,QAAQ,EAAE,QAAQ,SAAU,IAAI;AACnC,SAAG,OAAO;AAAA,IACZ,CAAC;AACD,WAAO,MAAM,OAAO;AAAA,EACtB;AAAA,EACA,UAAU;AACR,WAAO,SAAS,gBAAgB,KAAK,GAAG,IAAI,GAAG;AAAA,EACjD;AACF;AACA,gBAAgB;AAAA,EACd,WAAW;AAAA,IACT,MAAM,kBAAkB,WAAY;AAClC,aAAO,KAAK,KAAK,EAAE,IAAI,IAAI,KAAK,CAAC;AAAA,IACnC,CAAC;AAAA,EACH;AAAA,EACA,SAAS;AAAA;AAAA,IAEP,SAAS;AACP,aAAO,KAAK,UAAU,MAAM;AAAA,IAC9B;AAAA,IACA,SAAS,SAAS;AAChB,YAAM,SAAS,mBAAmB,OAAO,UAAU,KAAK,OAAO,EAAE,KAAK,EAAE,IAAI,OAAO;AACnF,aAAO,KAAK,KAAK,QAAQ,UAAU,OAAO,GAAG,IAAI,GAAG;AAAA,IACtD;AAAA;AAAA,IAEA,SAAS;AACP,aAAO,KAAK,KAAK,QAAQ,IAAI;AAAA,IAC/B;AAAA,EACF;AACF,CAAC;AACD,SAAS,MAAM,MAAM;AACrB,IAAM,OAAN,cAAmB,QAAQ;AAAA,EACzB,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,QAAQ,IAAI,GAAG,MAAM;AAAA,EACvC;AAAA;AAAA,EAEA,OAAO,GAAG;AACR,QAAI,OAAO,MAAM,YAAY,aAAa,WAAW;AACnD,UAAI;AAAA,QACF,QAAQ,UAAU,CAAC;AAAA,QACnB,OAAO,UAAU,CAAC;AAAA,QAClB,SAAS,UAAU,CAAC;AAAA,MACtB;AAAA,IACF;AACA,QAAI,EAAE,WAAW,KAAM,MAAK,KAAK,gBAAgB,EAAE,OAAO;AAC1D,QAAI,EAAE,SAAS,KAAM,MAAK,KAAK,cAAc,EAAE,KAAK;AACpD,QAAI,EAAE,UAAU,KAAM,MAAK,KAAK,UAAU,IAAI,UAAU,EAAE,MAAM,CAAC;AACjE,WAAO;AAAA,EACT;AACF;AACA,gBAAgB;AAAA,EACd,UAAU;AAAA;AAAA,IAER,MAAM,SAAU,QAAQ,OAAO,SAAS;AACtC,aAAO,KAAK,IAAI,IAAI,KAAK,CAAC,EAAE,OAAO,QAAQ,OAAO,OAAO;AAAA,IAC3D;AAAA,EACF;AACF,CAAC;AACD,SAAS,MAAM,MAAM;AACrB,SAAS,QAAQ,UAAU,MAAM;AAC/B,MAAI,CAAC,SAAU,QAAO;AACtB,MAAI,CAAC,KAAM,QAAO;AAClB,MAAI,MAAM,WAAW;AACrB,aAAW,KAAK,MAAM;AACpB,WAAO,YAAY,CAAC,IAAI,MAAM,KAAK,CAAC,IAAI;AAAA,EAC1C;AACA,SAAO;AACP,SAAO;AACT;AACA,IAAM,QAAN,cAAoB,QAAQ;AAAA,EAC1B,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,SAAS,IAAI,GAAG,MAAM;AAAA,EACxC;AAAA,EACA,QAAQ,IAAI,IAAI;AACd,SAAK,KAAK,eAAe;AACzB,WAAO;AAAA,EACT;AAAA,EACA,KAAK,OAAO,KAAK,SAAS,CAAC,GAAG;AAC5B,WAAO,KAAK,KAAK,cAAc;AAAA,MAC7B,YAAY;AAAA,MACZ;AAAA,OACG,OACJ;AAAA,EACH;AAAA,EACA,KAAK,UAAU,KAAK;AAClB,WAAO,KAAK,QAAQ,QAAQ,UAAU,GAAG,CAAC;AAAA,EAC5C;AACF;AACA,gBAAgB,OAAO;AAAA,EACrB,MAAM,UAAU,KAAK;AACnB,WAAO,KAAK,IAAI,IAAI,MAAM,CAAC,EAAE,KAAK,UAAU,GAAG;AAAA,EACjD;AAAA,EACA,SAAS,OAAO,KAAK,QAAQ;AAC3B,WAAO,KAAK,IAAI,IAAI,MAAM,CAAC,EAAE,KAAK,OAAO,KAAK,MAAM;AAAA,EACtD;AACF,CAAC;AACD,SAAS,OAAO,OAAO;AACvB,IAAM,WAAN,cAAuB,KAAK;AAAA;AAAA,EAE1B,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,YAAY,IAAI,GAAG,MAAM;AAAA,EAC3C;AAAA;AAAA,EAEA,QAAQ;AACN,UAAM,QAAQ,KAAK,MAAM;AACzB,WAAO,QAAQ,MAAM,MAAM,IAAI;AAAA,EACjC;AAAA;AAAA,EAEA,KAAK,GAAG;AACN,UAAM,QAAQ,KAAK,MAAM;AACzB,QAAI,YAAY;AAChB,QAAI,OAAO;AACT,kBAAY,MAAM,KAAK,CAAC;AAAA,IAC1B;AACA,WAAO,KAAK,OAAO,YAAY;AAAA,EACjC;AAAA;AAAA,EAEA,QAAQ;AACN,WAAO,KAAK,UAAU,MAAM;AAAA,EAC9B;AACF;AACA,gBAAgB;AAAA,EACd,WAAW;AAAA,IACT,UAAU,kBAAkB,SAAU,MAAM,MAAM;AAChD,UAAI,EAAE,gBAAgB,OAAO;AAC3B,eAAO,KAAK,KAAK,IAAI;AAAA,MACvB;AACA,aAAO,KAAK,KAAK,IAAI;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EACA,MAAM;AAAA;AAAA,IAEJ,MAAM,kBAAkB,SAAU,OAAO,cAAc,MAAM;AAC3D,YAAM,WAAW,IAAI,SAAS;AAC9B,UAAI,EAAE,iBAAiB,OAAO;AAC5B,gBAAQ,KAAK,KAAK,EAAE,KAAK,KAAK;AAAA,MAChC;AACA,eAAS,KAAK,QAAQ,MAAM,OAAO,KAAK;AACxC,UAAI;AACJ,UAAI,aAAa;AACf,eAAO,OAAO,KAAK,KAAK,YAAY;AAClC,mBAAS,KAAK,YAAY,IAAI;AAAA,QAChC;AAAA,MACF;AACA,aAAO,KAAK,IAAI,QAAQ;AAAA,IAC1B,CAAC;AAAA;AAAA,IAED,WAAW;AACT,aAAO,KAAK,QAAQ,UAAU;AAAA,IAChC;AAAA,EACF;AAAA,EACA,MAAM;AAAA;AAAA,IAEJ,MAAM,kBAAkB,SAAU,MAAM;AACtC,UAAI,EAAE,gBAAgB,OAAO;AAC3B,eAAO,IAAI,KAAK,EAAE,MAAM,KAAK,OAAO,CAAC,EAAE,KAAK,IAAI;AAAA,MAClD;AACA,aAAO,KAAK,KAAK,IAAI;AAAA,IACvB,CAAC;AAAA,IACD,UAAU;AACR,aAAO,SAAS,cAAc,EAAE,OAAO,UAAQ;AAC7C,gBAAQ,KAAK,KAAK,MAAM,KAAK,IAAI,SAAS,KAAK,GAAG,CAAC;AAAA,MACrD,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;AACD,SAAS,UAAU,aAAa;AAChC,SAAS,UAAU,UAAU;AAC7B,IAAM,MAAN,cAAkB,MAAM;AAAA,EACtB,YAAY,MAAM,SAAS,MAAM;AAC/B,UAAM,UAAU,OAAO,IAAI,GAAG,MAAM;AAAA,EACtC;AAAA;AAAA,EAEA,IAAI,SAAS,MAAM;AACjB,WAAO,KAAK,KAAK,SAAS,QAAQ,MAAM,MAAM,SAAS,KAAK;AAAA,EAC9D;AACF;AACA,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,KAAK,kBAAkB,SAAU,SAAS,MAAM;AAC9C,aAAO,KAAK,IAAI,IAAI,IAAI,CAAC,EAAE,IAAI,SAAS,IAAI;AAAA,IAC9C,CAAC;AAAA,EACH;AACF,CAAC;AACD,SAAS,KAAK,KAAK;AACnB,IAAM,MAAM;AACZ,OAAO,CAAC,KAAK,UAAU,SAAS,SAAS,MAAM,GAAG,cAAc,SAAS,CAAC;AAC1E,OAAO,CAAC,MAAM,UAAU,SAAS,IAAI,GAAG,cAAc,QAAQ,CAAC;AAC/D,OAAO,MAAM,cAAc,MAAM,CAAC;AAClC,OAAO,MAAM,cAAc,MAAM,CAAC;AAClC,OAAO,MAAM,cAAc,MAAM,CAAC;AAClC,OAAO,CAAC,MAAM,KAAK,GAAG,cAAc,OAAO,CAAC;AAC5C,OAAO,CAAC,MAAM,SAAS,UAAU,MAAM,GAAG,cAAc,QAAQ,CAAC;AACjE,OAAO,aAAa,cAAc,aAAa,CAAC;AAChD,OAAO,KAAK,cAAc,KAAK,CAAC;AAChC,OAAO,SAAS,cAAc,SAAS,CAAC;AACxC,OAAO,OAAO,cAAc,OAAO,CAAC;AACpC,OAAO,CAAC,WAAW,QAAQ,GAAG,cAAc,WAAW,CAAC;AACxD,OAAO,UAAU,cAAc,UAAU,CAAC;AAC1C,OAAO,QAAQ,cAAc,QAAQ,CAAC;AACtC,KAAK,OAAO,eAAe,CAAC;AAC5B,sBAAsB,CAAC,WAAW,OAAO,KAAK,QAAQ,UAAU,YAAY,WAAW,KAAK,CAAC;AAC7F,cAAc;AACd,IAAM,sBAAsB,aAAW;AACrC,SAAO,6GAA6G,OAAO;AAC7H;AACA,IAAM,iBAAiB;AAAA,EACrB,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,WAAW;AAAA,EACX,eAAe;AAAA,EACf,aAAa;AAAA,EACb,aAAa;AAAA,EACb,cAAc;AAAA,EACd,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,eAAe;AAAA,EACf,WAAW;AAAA,EACX,WAAW;AAAA,EACX,gBAAgB;AAClB;AACA,IAAI,iBAAiB,SAAS,gBAAgB,IAAI;AAChD,SAAO,GAAG,WAAW,CAAC;AAAA,IACpB,SAAS,GAAG;AAAA,IACZ,SAAS,GAAG;AAAA,EACd,CAAC;AACH;AACA,OAAO,KAAK;AAAA,EACV,SAAS,SAAS,QAAQ,SAAS;AACjC,QAAI,UACF,qBACA,kBACA,kBACA,oBACA,oBACA,kBACA,oBACA,uBACA,kBACA,uBACA,wBACA,QAAQ;AACV,SAAK,IAAI,UAAU;AACnB,QAAI,YAAY,MAAO,QAAO;AAC9B,eAAW,WAAW,YAAY,OAAO,WAAW,CAAC;AACrD,QAAI,cAAc,sBAAsB,QAAQ,eAAe,OAAO,sBAAsB;AAC5F,QAAI,WAAW,mBAAmB,QAAQ,YAAY,OAAO,mBAAmB,OAAO;AACvF,QAAI,WAAW,mBAAmB,QAAQ,YAAY,OAAO,mBAAmB,OAAO;AACvF,QAAI,eAAe,qBAAqB,QAAQ,cAAc,OAAO,qBAAqB;AAC1F,QAAI,eAAe,qBAAqB,QAAQ,cAAc,OAAO,qBAAqB;AAC1F,QAAI,aAAa,mBAAmB,QAAQ,YAAY,OAAO,mBAAmB;AAClF,QAAI,aAAa,qBAAqB,QAAQ,cAAc,OAAO,qBAAqB;AACxF,QAAI,gBAAgB,wBAAwB,QAAQ,iBAAiB,OAAO,wBAAwB;AACpG,QAAI,WAAW,mBAAmB,QAAQ,YAAY,OAAO,mBAAmB;AAChF,QAAI,gCAAgC,wBAAwB,QAAQ,iCAAiC,OAAO,wBAAwB;AACpI,QAAI,kCAAkC,yBAAyB,QAAQ,mCAAmC,OAAO,yBAAyB;AAC1I,QAAI;AACJ,QAAI;AACJ,QAAI,iBAAiB;AACrB,QAAI,UAAU,KAAK,QAAQ;AAC3B,QAAI,oBAAoB,SAAS,mBAAmB,KAAK;AACvD,UAAI,CAAC,QAAS,QAAO;AACrB,UAAI,MAAM,QAAQ,KAChB,OAAO,QAAQ,MACf,SAAS,QAAQ,QACjB,QAAQ,QAAQ;AAClB,UAAI,aAAa,MAAM,KAAK,CAAC,SAAS,QAAQ,CAAC,GAC7C,SAAS,WAAW,OACpB,UAAU,WAAW;AACvB,UAAI,sBAAsB,MAAM,KAAK,oBAAoB;AACzD,UAAI,qBAAqB;AACzB,UAAI,sBAAsB;AAC1B,UAAI,oBAAoB;AACxB,UAAI,uBAAuB;AAC3B,UAAI,oBAAoB,UAAU,oBAAoB,8BAA8B;AAClF,YAAI,iBAAiB,SAAS;AAC9B,YAAI,qBAAqB,QAAQ,QAAQ,QAAQ;AACjD,YAAI,uBAAuB,gBAAgB;AACzC,cAAI,SAAS,oBAAoB,gBAAgB,oBAAoB;AACrE,cAAI,cAAc,iBAAiB,qBAAqB,UAAU;AAClE,cAAI,UAAU,gBAAgB;AAC9B,cAAI,mBAAmB,UAAU,WAAW,CAAC,UAAU,CAAC;AACxD,cAAI,QAAQ,mBAAmB,iBAAiB,qBAAqB,qBAAqB;AAC1F,cAAI,SAAS,IAAI,WAAW,IAAI,IAAI,WAAW,IAAI;AACnD,cAAI,kBAAkB;AACpB,gBAAI,oBAAoB,UAAU,oBAAoB,oCAAoC,oBAAoB,UAAU,oBAAoB,oCAAoC,oBAAoB,UAAU,oBAAoB,kCAAkC;AAClQ,mCAAqB,SAAS;AAC9B,oCAAsB,CAAC,SAAS;AAAA,YAClC,WAAW,oBAAoB,UAAU,oBAAoB,oCAAoC,oBAAoB,UAAU,oBAAoB,oCAAoC,oBAAoB,UAAU,oBAAoB,kCAAkC;AACzQ,oCAAsB,CAAC;AAAA,YACzB,WAAW,oBAAoB,UAAU,oBAAoB,oCAAoC,oBAAoB,UAAU,oBAAoB,oCAAoC,oBAAoB,UAAU,oBAAoB,kCAAkC;AACzQ,mCAAqB;AAAA,YACvB;AAAA,UACF,OAAO;AACL,gBAAI,oBAAoB,UAAU,oBAAoB,oCAAoC,oBAAoB,UAAU,oBAAoB,oCAAoC,oBAAoB,UAAU,oBAAoB,kCAAkC;AAClQ,kCAAoB,SAAS;AAC7B,qCAAuB,CAAC,SAAS;AAAA,YACnC,WAAW,oBAAoB,UAAU,oBAAoB,oCAAoC,oBAAoB,UAAU,oBAAoB,oCAAoC,oBAAoB,UAAU,oBAAoB,kCAAkC;AACzQ,qCAAuB,CAAC;AAAA,YAC1B,WAAW,oBAAoB,UAAU,oBAAoB,oCAAoC,oBAAoB,UAAU,oBAAoB,oCAAoC,oBAAoB,UAAU,oBAAoB,kCAAkC;AACzQ,kCAAoB;AAAA,YACtB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,YAAY,QAAQ,QAAQ,QAAQ,IAAI,OAAO;AACnD,UAAI,aAAa,QAAQ,IAAI,QAAQ,IAAI,QAAQ;AACjD,UAAI,WAAW,QAAQ,SAAS,QAAQ,IAAI,MAAM;AAClD,UAAI,cAAc,QAAQ,IAAI,SAAS,IAAI,SAAS;AACpD,UAAI,IAAI,KAAK,IAAI,WAAW,KAAK,IAAI,YAAY,IAAI,CAAC,CAAC;AACvD,UAAI,IAAI,KAAK,IAAI,UAAU,KAAK,IAAI,aAAa,IAAI,CAAC,CAAC;AACvD,aAAO;AAAA,IACT;AACA,QAAI,YAAY,SAAS,WAAW,IAAI;AACtC,SAAG,eAAe;AAClB,UAAI;AACJ,cAAQ,GAAG,WAAW;AAAA,QACpB,KAAK;AACH,kCAAwB,GAAG,SAAS;AACpC;AAAA,QACF,KAAK;AACH,kCAAwB,GAAG,SAAS;AACpC;AAAA,QACF;AACE,kCAAwB,GAAG;AAC3B;AAAA,MACJ;AACA,UAAI,MAAM,KAAK,IAAI,IAAI,YAAY,KAAK,wBAAwB,GAAG,IAAI,KAAK,KAAK;AACjF,UAAI,IAAI,KAAK,MAAM,GAAG,SAAS,GAAG,OAAO;AACzC,UAAI,MAAM,SAAS;AACjB,cAAM;AAAA,MACR;AACA,UAAI,MAAM,SAAS;AACjB,cAAM;AAAA,MACR;AACA,UAAI,KAAK,SAAS,QAAQ;AAAA,QACxB,OAAO;AAAA,QACP,OAAO;AAAA,MACT,CAAC,EAAE,kBAAkB;AACnB,eAAO;AAAA,MACT;AACA,WAAK,KAAK,KAAK,CAAC;AAChB,UAAI,SAAS;AACX,YAAI,MAAM,kBAAkB,KAAK,QAAQ,CAAC;AAC1C,aAAK,QAAQ,GAAG;AAAA,MAClB;AAAA,IACF;AACA,QAAI,iBAAiB,SAAS,gBAAgB,IAAI;AAChD,oBAAc,eAAe,EAAE;AAC/B,UAAI,YAAY,SAAS,GAAG;AAC1B,YAAI,aAAa,cAAc;AAC7B,mBAAS,KAAK,MAAM,EAAE;AAAA,QACxB;AACA;AAAA,MACF;AACA,UAAI,aAAa,cAAc;AAC7B,gBAAQ,KAAK,MAAM,EAAE;AAAA,MACvB;AACA,SAAG,eAAe;AAClB,UAAI,KAAK,SAAS,kBAAkB;AAAA,QAClC,OAAO;AAAA,MACT,CAAC,EAAE,kBAAkB;AACnB;AAAA,MACF;AACA,WAAK,IAAI,sBAAsB,eAAe;AAC9C,uBAAiB;AACjB,SAAG,UAAU,qBAAqB,WAAW,MAAM;AAAA,QACjD,SAAS;AAAA,MACX,CAAC;AACD,SAAG,UAAU,oBAAoB,eAAe,MAAM;AAAA,QACpD,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AACA,QAAI,gBAAgB,SAAS,eAAe,IAAI;AAC9C,SAAG,eAAe;AAClB,UAAI,iBAAiB,eAAe,EAAE;AACtC,UAAI,eAAe,SAAS,GAAG;AAC7B;AAAA,MACF;AACA,uBAAiB;AACjB,WAAK,SAAS,gBAAgB;AAAA,QAC5B,OAAO;AAAA,MACT,CAAC;AACD,UAAI,UAAU,qBAAqB,SAAS;AAC5C,UAAI,UAAU,oBAAoB,cAAc;AAChD,WAAK,GAAG,sBAAsB,cAAc;AAC5C,UAAI,eAAe,UAAU,aAAa,cAAc;AACtD,iBAAS,KAAK,MAAM,EAAE;AAAA,MACxB;AAAA,IACF;AACA,QAAI,YAAY,SAAS,WAAW,IAAI;AACtC,SAAG,eAAe;AAClB,UAAI,iBAAiB,eAAe,EAAE;AACtC,UAAI,OAAO,KAAK,KAAK;AACrB,UAAI,YAAY,KAAK,KAAK,KAAK,IAAI,YAAY,CAAC,EAAE,UAAU,YAAY,CAAC,EAAE,SAAS,CAAC,IAAI,KAAK,IAAI,YAAY,CAAC,EAAE,UAAU,YAAY,CAAC,EAAE,SAAS,CAAC,CAAC;AACrJ,UAAI,eAAe,KAAK,KAAK,KAAK,IAAI,eAAe,CAAC,EAAE,UAAU,eAAe,CAAC,EAAE,SAAS,CAAC,IAAI,KAAK,IAAI,eAAe,CAAC,EAAE,UAAU,eAAe,CAAC,EAAE,SAAS,CAAC,CAAC;AACpK,UAAI,aAAa,YAAY;AAC7B,UAAI,OAAO,WAAW,aAAa,KAAK,OAAO,WAAW,aAAa,GAAG;AACxE,qBAAa;AAAA,MACf;AACA,UAAI,eAAe;AAAA,QACjB,GAAG,eAAe,CAAC,EAAE,UAAU,OAAO,eAAe,CAAC,EAAE,UAAU,eAAe,CAAC,EAAE;AAAA,QACpF,GAAG,eAAe,CAAC,EAAE,UAAU,OAAO,eAAe,CAAC,EAAE,UAAU,eAAe,CAAC,EAAE;AAAA,MACtF;AACA,UAAI,YAAY;AAAA,QACd,GAAG,YAAY,CAAC,EAAE,UAAU,OAAO,YAAY,CAAC,EAAE,UAAU,YAAY,CAAC,EAAE;AAAA,QAC3E,GAAG,YAAY,CAAC,EAAE,UAAU,OAAO,YAAY,CAAC,EAAE,UAAU,YAAY,CAAC,EAAE;AAAA,MAC7E;AACA,UAAI,IAAI,KAAK,MAAM,aAAa,GAAG,aAAa,CAAC;AACjD,UAAI,SAAS,KAAK,MAAM,IAAI,aAAa,IAAI,UAAU,GAAG,IAAI,aAAa,IAAI,UAAU,CAAC;AAC1F,UAAI,MAAM,IAAI,IAAI,KAAK,QAAQ,CAAC,EAAE,UAAU,IAAI,OAAO,EAAE,UAAU,CAAC,OAAO,GAAG,CAAC,OAAO,CAAC,EAAE,MAAM,YAAY,GAAG,CAAC,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC;AACpI,wBAAkB,GAAG;AACrB,WAAK,QAAQ,GAAG;AAChB,oBAAc;AACd,WAAK,SAAS,QAAQ;AAAA,QACpB;AAAA,QACA,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,QAAI,WAAW,SAAS,UAAU,IAAI;AACpC,UAAI,UAAU,GAAG,KAAK,QAAQ,OAAO,IAAI;AACzC,UAAI,WAAW,GAAG,WAAW,aAAa,GAAG,UAAU,YAAY,GAAG;AACpE;AAAA,MACF;AACA,SAAG,eAAe;AAClB,WAAK,IAAI,qBAAqB,SAAS;AACvC,oBAAc,eAAe,EAAE;AAC/B,UAAI,eAAgB;AACpB,WAAK,SAAS,YAAY;AAAA,QACxB,OAAO;AAAA,MACT,CAAC;AACD,cAAQ;AAAA,QACN,GAAG,YAAY,CAAC,EAAE;AAAA,QAClB,GAAG,YAAY,CAAC,EAAE;AAAA,MACpB;AACA,SAAG,UAAU,uCAAuC,SAAS,MAAM;AAAA,QACjE,SAAS;AAAA,MACX,CAAC;AACD,SAAG,UAAU,oCAAoC,SAAS,MAAM;AAAA,QAC9D,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AACA,QAAI,UAAU,SAAS,SAAS,IAAI;AAClC,SAAG,eAAe;AAClB,UAAI,UAAU,uCAAuC,OAAO;AAC5D,UAAI,UAAU,oCAAoC,QAAQ;AAC1D,WAAK,GAAG,qBAAqB,QAAQ;AACrC,WAAK,SAAS,UAAU;AAAA,QACtB,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,QAAI,UAAU,SAAS,SAAS,IAAI;AAClC,SAAG,eAAe;AAClB,UAAI,iBAAiB,eAAe,EAAE;AACtC,UAAI,WAAW;AAAA,QACb,GAAG,eAAe,CAAC,EAAE;AAAA,QACrB,GAAG,eAAe,CAAC,EAAE;AAAA,MACvB;AACA,UAAI,KAAK,KAAK,MAAM,SAAS,GAAG,SAAS,CAAC;AAC1C,UAAI,KAAK,KAAK,MAAM,MAAM,GAAG,MAAM,CAAC;AACpC,UAAI,SAAS,CAAC,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC;AACtC,UAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG;AAC5B;AAAA,MACF;AACA,UAAI,MAAM,IAAI,IAAI,KAAK,QAAQ,CAAC,EAAE,UAAU,IAAI,OAAO,EAAE,UAAU,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;AACxF,cAAQ;AACR,wBAAkB,GAAG;AACrB,UAAI,KAAK,SAAS,WAAW;AAAA,QAC3B;AAAA,QACA,OAAO;AAAA,MACT,CAAC,EAAE,kBAAkB;AACnB;AAAA,MACF;AACA,WAAK,QAAQ,GAAG;AAAA,IAClB;AACA,QAAI,aAAa;AACf,WAAK,GAAG,iBAAiB,WAAW,MAAM;AAAA,QACxC,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AACA,QAAI,aAAa;AACf,WAAK,GAAG,sBAAsB,gBAAgB,MAAM;AAAA,QAClD,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AACA,QAAI,WAAW;AACb,WAAK,GAAG,qBAAqB,UAAU,MAAM;AAAA,QAC3C,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACF,CAAC;AACD,IAAM,QAAN,MAAY;AAAA,EACV,YAAY,SAAS,QAAQ,SAAS,aAAa;AACjD,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,SAAS,IAAI,EAAE,MAAM,OAAO,EAAE,KAAK,QAAQ,OAAO,EAAE,QAAQ,OAAO,MAAM,IAAI,OAAO,EAAE,EAAE,QAAQ;AAAA,MACnG,YAAY;AAAA,MACZ,SAAS;AAAA,IACX,CAAC,EAAE,KAAK;AAAA,MACN,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,IAAI,SAAS;AACX,SAAK,OAAO,IAAI,OAAO;AAAA,EACzB;AAAA,EACA,eAAe;AACb,SAAK,OAAO,QAAQ,OAAO,KAAK,KAAK,IAAI,KAAK,MAAM,EAAE;AAAA,EACxD;AAAA,EACA,cAAc,IAAI,IAAI,QAAQ,SAAS;AACrC,SAAK,OAAO,QAAQ,GAAG,EAAE,IAAI,EAAE,IAAI,MAAM,IAAI,OAAO,EAAE;AAAA,EACxD;AAAA,EACA,KAAK,YAAY;AACf,UAAM,aAAa,KAAK,OAAO,KAAK,IAAI;AACxC,QAAI,cAAc,KAAK;AACrB,WAAK,OAAO,KAAK,UAAU;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,QAAQ;AACN,SAAK,OAAO,MAAM,EAAE,QAAQ,OAAO,KAAK,KAAK,IAAI,KAAK,MAAM,EAAE;AAAA,EAChE;AAAA,EACA,OAAO,SAAS;AAAA,IACd,KAAK;AAAA,IACL,KAAK;AAAA,IACL,OAAO,SAAS;AAAA,IAChB,QAAQ,UAAU;AAAA,IAClB,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ,IAAI,CAAC,GAAG;AACN,UAAM,OAAO,IAAI,KAAK;AACtB,SAAK,KAAK;AAAA,MACR,GAAG,MAAM;AAAA,MACT,GAAG,MAAM;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ;AAAA,IACF,CAAC;AACD,SAAK,KAAK,KAAK;AACf,WAAO;AAAA,EACT;AAAA,EACA,OAAO,WAAW,aAAa,CAAC,GAAG;AACjC,UAAM,SAAS,IAAI,OAAO;AAC1B,WAAO,KAAK,UAAU;AACtB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,SAAS,OAAO,IAAI;AAAA,IACzB,GAAG;AAAA,IACH,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,EACN,GAAG;AACD,UAAM,UAAU,IAAI,KAAK;AACzB,YAAQ,KAAK;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AACD,YAAQ,MAAM,IAAI;AAClB,QAAI,OAAO,UAAU,OAAO,QAAQ;AAClC,cAAQ,KAAK,IAAI,EAAE;AAAA,IACrB;AACA,QAAI,QAAQ,UAAU,QAAQ,QAAQ;AACpC,cAAQ,KAAK;AAAA,QACX,IAAI;AAAA,QACJ,IAAI;AAAA,MACN,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,aAAa,UAAU;AAAA,IAC5B;AAAA,IACA;AAAA,EACF,IAAI,CAAC,GAAG;AACN,UAAM,SAAS,IAAI,cAAc;AAAA,MAC/B,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AACD,UAAM,UAAU,SAAS,cAAc,KAAK;AAC5C,YAAQ,YAAY;AACpB,YAAQ,aAAa,SAAS,8BAA8B;AAC5D,WAAO,IAAI,OAAO;AAClB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,UAAU,KAAK,GAAG,KAAK,GAAG,IAAI,QAAQ;AAC3C,UAAM,QAAQ,IAAI,EAAE;AACpB,UAAM,KAAK;AAAA,MACT,WAAW,aAAa,EAAE,KAAK,EAAE;AAAA,MACjC,aAAa;AAAA,MACb,eAAe;AAAA,IACjB,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,OAAO,SAAS,YAAY;AAAA,IAC1B,KAAK;AAAA,IACL,cAAc,eAAe;AAAA,EAC/B,IAAI,CAAC,GAAG;AACN,UAAM,OAAO,IAAI,KAAK;AAAA,MACpB,GAAG;AAAA,IACL,CAAC;AACD,SAAK,GAAG,EAAE;AACV,SAAK,KAAK,MAAM,EAAE,OAAO;AAAA,MACvB,OAAO;AAAA,MACP,OAAO;AAAA,IACT,CAAC;AACD,WAAO;AAAA,EACT;AACF;AACA,IAAM,SAAS;AACf,IAAM,WAAW;AACjB,IAAM,QAAN,cAAoB,MAAM;AAAA,EACxB,YAAY,SAAS,SAAS;AAC5B,UAAM,SAAS,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,WAAW;AACjE,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,UAAU,OAAO;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,UAAM,aAAa,SAAS;AAAA,MAC1B,UAAU,MAAM;AACd,eAAO,gBAAgB,KAAK,QAAQ,SAAS,EAAE,aAAa;AAAA,UAC1D;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AACD,UAAM,OAAO,WAAW,UAAU,KAAK;AACvC,SAAK,WAAW,WAAW,IAAI;AAAA,EACjC;AAAA,EACA,WAAW,MAAM,WAAW;AAC1B,QAAI,IAAI;AACR,UAAM,UAAU,KAAK;AACrB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,kCACC,UACA,KAAK,KAAK;AAEf,UAAM;AAAA,MACJ,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI,gBAAgB,QAAQ,SAAS,EAAE,KAAK,IAAI;AAChD,UAAM,gBAAgB;AACtB,UAAM,QAAQ,MAAM,UAAU,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,WAAW,OAAO,SAAS,GAAG,KAAK,EAAE;AACpG,UAAM,cAAc,aAAa,KAAK,KAAK,QAAQ,UAAU,CAAC;AAC9D,UAAM,SAAS,MAAM,aAAa,aAAa;AAAA,MAC7C;AAAA,MACA;AAAA,IACF,CAAC;AACD,UAAM,aAAa,eAAe;AAAA,MAChC;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO;AAAA,IACT,CAAC;AACD,UAAM,kBAAkB,eAAe;AAAA,MACrC;AAAA,MACA;AAAA,MACA,aAAa,GAAG,WAAW;AAAA,MAC3B;AAAA,MACA,iBAAiB;AAAA,MACjB,QAAQ;AAAA,MACR,WAAW;AAAA,IACb,CAAC;AACD,WAAO,MAAM,EAAE,KAAK,SAAS,gBAAgB,OAAO,SAAS,CAAC;AAC9D,WAAO,KAAK,SAAS,aAAa;AAClC,UAAM,KAAK,SAAS,UAAU;AAC9B,UAAM,IAAI,MAAM;AAChB,UAAM,UAAU,KAAK,KAAK,aAAa,OAAO,SAAS,GAAG,UAAU,CAAC;AACrE,QAAI,kBAAkB;AACpB,YAAM,GAAG,aAAa,WAAY;AAChC,cAAM,OAAO,KAAK,KAAK,QAAQ;AAC/B,cAAM,WAAW,MAAM,KAAK,OAAK,EAAE,KAAK,OAAO,IAAI;AACnD,oBAAY,gBAAgB,OAAO,UAAU,MAAM,OAAO;AAAA,MAC5D,CAAC;AACD,YAAM,GAAG,YAAY,WAAY;AAC/B,cAAM,OAAO,KAAK,KAAK,QAAQ;AAC/B,cAAM,WAAW,MAAM,KAAK,OAAK,EAAE,KAAK,OAAO,IAAI;AACnD,oBAAY,gBAAgB,OAAO,UAAU,OAAO,OAAO;AAAA,MAC7D,CAAC;AAAA,IACH;AACA,QAAI,eAAe;AACjB,YAAM,iBAAiB,kBAAkB,gBAAgB,KAAK,KAAK,KAAK,QAAQ,UAAU,CAAC,IAAI;AAC/F,YAAM,GAAG,aAAa,SAAU,GAAG;AACjC,cAAM,SAAS,iBAAiB,EAAE,OAAO,EAAE,OAAO,iBAAiB,oBAAoB,gBAAgB,CAAC,eAAe;AACvH,sBAAc,WAAW,OAAO,KAAK,GAAG,GAAG,cAAc;AAAA,MAC3D,CAAC;AACD,YAAM,GAAG,YAAY,SAAU,GAAG;AAChC,YAAI,EAAE,cAAc,YAAY,OAAO;AACrC,wBAAc,SAAS;AAAA,QACzB;AAAA,MACF,CAAC;AAAA,IACH;AACA,cAAU,IAAI,KAAK;AACnB,QAAI,CAAC,KAAK,YAAY,CAAC,KAAK,gBAAgB;AAC1C;AAAA,IACF;AACA,QAAI,sBAAsB;AACxB,YAAM,qBAAqB,2BAA2B;AACtD,YAAM,cAAc,MAAM,UAAU,KAAK,YAAY,IAAI,oBAAoB,KAAK,aAAa,oBAAoB,KAAK,KAAK,EAAE;AAC/H,YAAM,kBAAkB,MAAM,WAAW;AAAA,QACvC,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,GAAG;AAAA,QACH,OAAO;AAAA,MACT,CAAC;AACD,kBAAY,KAAK,YAAY,KAAK;AAClC,kBAAY,IAAI,eAAe;AAC/B,UAAI,KAAK,gBAAgB;AACvB,oBAAY,IAAI,MAAM;AAAA,MACxB,OAAO;AACL,oBAAY,IAAI,QAAQ;AAAA,MAC1B;AACA,kBAAY,GAAG,SAAS,WAAY;AAClC,YAAI,KAAK,gBAAgB;AACvB,wBAAc,OAAO,KAAK,KAAK,QAAQ,IAAI;AAAA,QAC7C,OAAO;AACL,wBAAc,SAAS,KAAK,KAAK,QAAQ,IAAI;AAAA,QAC/C;AAAA,MACF,CAAC;AACD,gBAAU,IAAI,WAAW;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,WAAW,MAAM,OAAO;AACtB,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,UAAM,OAAO,QAAQ,MAAM,WAAW,YAAY,KAAK,QAAQ,SAAS;AACxE,QAAI,CAAC,KAAM;AACX,UAAM,OAAO,MAAM,SAAS,MAAM;AAAA,MAChC,IAAI,GAAG,KAAK,KAAK,EAAE,KAAK,KAAK,KAAK,WAAW,OAAO,SAAS,GAAG,KAAK,EAAE;AAAA,IACzE,CAAC;AACD,SAAK,OAAO;AACZ,UAAM,IAAI,IAAI;AAAA,EAChB;AAAA,EACA,SAAS,QAAQ;AACf,QAAI;AACJ,UAAM,UAAU,KAAK,KAAK,aAAa,OAAO,SAAS,GAAG,YAAY,MAAM,CAAC;AAC7E,UAAM,OAAO,MAAM,KAAK,OAAK,EAAE,KAAK,OAAO,MAAM;AACjD,QAAI,QAAQ,OAAO,SAAS,KAAK,UAAU;AACzC,WAAK,iBAAiB,KAAK;AAC3B,WAAK,eAAe,QAAQ,WAAS,KAAK,SAAS,KAAK,CAAC;AACzD,WAAK,WAAW;AAChB,WAAK,OAAO;AAAA,QACV,iBAAiB;AAAA,MACnB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,QAAQ;AACb,QAAI;AACJ,UAAM,UAAU,KAAK,KAAK,aAAa,OAAO,SAAS,GAAG,YAAY,MAAM,CAAC;AAC7E,UAAM,OAAO,MAAM,KAAK,OAAK,EAAE,KAAK,OAAO,MAAM;AACjD,QAAI,QAAQ,OAAO,SAAS,KAAK,gBAAgB;AAC/C,WAAK,WAAW,KAAK;AACrB,WAAK,SAAS,QAAQ,WAAS,KAAK,OAAO,KAAK,CAAC;AACjD,WAAK,iBAAiB;AACtB,WAAK,OAAO;AAAA,QACV,iBAAiB;AAAA,MACnB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,aAAa,YAAY,OAAO;AAC9B,SAAK,UAAU,iCACV,KAAK,UADK;AAAA,MAEb;AAAA,IACF;AACA,SAAK,OAAO;AAAA,MACV,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AAAA,EACA,YAAY;AACV,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,gBAAgB,KAAK,QAAQ,SAAS;AAC1C,UAAM;AAAA,MACJ,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,IAAI,kBAAkB;AAAA,MACpB,UAAU,KAAK;AAAA,MACf;AAAA,MACA;AAAA,IACF,CAAC;AACD,SAAK,cAAc,IAAI,IAAI,QAAQ,OAAO;AAAA,EAC5C;AAAA,EACA,OAAO;AAAA,IACL,kBAAkB;AAAA,EACpB,IAAI,CAAC,GAAG;AACN,QAAI;AACJ,UAAM,aAAa,KAAK,OAAO,QAAQ;AACvC,SAAK,MAAM;AACX,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,UAAM,cAAc,eAAe;AAAA,MACjC;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO;AAAA,IACT,CAAC;AACD,UAAM,YAAY,MAAM,UAAU,GAAG,GAAG,kBAAkB;AAC1D,cAAU,KAAK,SAAS,WAAW;AACnC,cAAU,GAAG,kBAAkB;AAC/B,UAAM,UAAU,KAAK,KAAK,aAAa,OAAO,SAAS,GAAG,UAAU,CAAC;AACrE,UAAM,QAAQ,UAAQ;AACpB,WAAK,WAAW,MAAM,SAAS;AAAA,IACjC,CAAC;AACD,UAAM,QAAQ,UAAQ;AACpB,WAAK,WAAW,MAAM,SAAS;AAAA,IACjC,CAAC;AACD,SAAK,IAAI,SAAS;AAClB,SAAK,UAAU;AACf,QAAI,iBAAiB;AACnB,WAAK,cAAc,WAAW,GAAG,WAAW,GAAG,WAAW,OAAO,WAAW,MAAM;AAAA,IACpF;AACA,QAAI,eAAe;AACjB,YAAM,iBAAiB,WAAW,SAAS;AAC3C,YAAM,OAAO,SAAS,QAAQ,SAAS,qBAAqB,MAAM,EAAE,CAAC;AACrE,WAAK,OAAO,cAAc;AAAA,IAC5B;AAAA,EACF;AACF;AACA,IAAM,aAAa;AACnB,IAAM,cAAc;AACpB,IAAM,gBAAgB;AACtB,IAAM,aAAa;AACnB,IAAM,SAAN,MAAa;AAAA,EACX,YAAY,OAAO;AACjB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,eAAe;AACb,UAAM,YAAY,KAAK,MAAM,OAAO,IAAI;AACxC,WAAO,UAAU,QAAQ,uBAAuB,OAAO,EAAE,QAAQ,aAAa,OAAO,EAAE,QAAQ,aAAa,OAAO;AAAA,EACrH;AAAA,EACA,SAAS;AACP,UAAM,UAAU,KAAK,aAAa;AAClC,UAAM,UAAU,IAAI,KAAK,CAAC,OAAO,GAAG;AAAA,MAClC,MAAM;AAAA,IACR,CAAC;AACD,WAAO,IAAI,gBAAgB,OAAO;AAAA,EACpC;AAAA,EACA,gBAAgB,MAAM,UAAU;AAC9B,UAAM,eAAe,SAAS,cAAc,GAAG;AAC/C,iBAAa,OAAO;AACpB,iBAAa,WAAW;AACxB,aAAS,KAAK,YAAY,YAAY;AACtC,iBAAa,MAAM;AACnB,aAAS,KAAK,YAAY,YAAY;AAAA,EACxC;AAAA,EACA,cAAc;AACZ,SAAK,gBAAgB,KAAK,OAAO,GAAG,cAA6B,oBAAI,KAAK,GAAG,QAAQ,CAAC,MAAM;AAAA,EAC9F;AACF;AACA,IAAM,eAAe;AAAA,EACnB;AAAA,IAAC;AAAA;AAAA,EACW,GAAG;AAAA,EACf;AAAA,IAAC;AAAA;AAAA,EACY,GAAG;AAAA,EAChB;AAAA,IAAC;AAAA;AAAA,EACc,GAAG;AAAA,EAClB;AAAA,IAAC;AAAA;AAAA,EACW,GAAG;AACjB;AACA,IAAM,mBAAmB;AACzB,IAAM,UAAN,MAAc;AAAA,EACZ,YAAY,SAAS,OAAO;AAC1B,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,SAAS,IAAI,OAAO,KAAK;AAAA,EAChC;AAAA,EACA,SAAS;AACP,QAAI;AACJ,UAAM,YAAY,SAAS,cAAc,KAAK;AAC9C,cAAU,KAAK;AACf,UAAM,kBAAkB,eAAe;AAAA,MACrC,SAAS;AAAA,MACT,KAAK;AAAA,MACL,UAAU;AAAA,MACV,OAAO;AAAA,MACP,KAAK;AAAA,IACP,CAAC;AACD,cAAU,aAAa,SAAS,eAAe;AAC/C,UAAM,YAAY,KAAK,kBAAkB,WAAW;AAAA,MAAa;AAAA;AAAA,IACrD,CAAC;AACb,UAAM,aAAa,KAAK,kBAAkB,YAAY;AAAA,MAAa;AAAA;AAAA,IACtD,CAAC;AACd,UAAM,eAAe,KAAK,kBAAkB,cAAc;AAAA,MAAa;AAAA;AAAA,IACxD,CAAC;AAChB,UAAM,YAAY,KAAK,kBAAkB,UAAU;AAAA,MAAa;AAAA;AAAA,IACpD,CAAC;AACb,cAAU,iBAAiB,SAAS,MAAM;AACxC,WAAK,MAAM,KAAK,gBAAgB;AAAA,IAClC,CAAC;AACD,eAAW,iBAAiB,SAAS,MAAM;AACzC,WAAK,MAAM,KAAK,CAAC,gBAAgB;AAAA,IACnC,CAAC;AACD,iBAAa,iBAAiB,SAAS,MAAM;AAC3C,WAAK,MAAM,UAAU;AAAA,IACvB,CAAC;AACD,cAAU,iBAAiB,SAAS,MAAM;AACxC,WAAK,OAAO,YAAY;AAAA,IAC1B,CAAC;AACD,cAAU,OAAO,WAAW,YAAY,cAAc,SAAS;AAC/D,KAAC,KAAK,KAAK,YAAY,OAAO,SAAS,GAAG,OAAO,SAAS;AAAA,EAC5D;AAAA,EACA,kBAAkB,UAAU,MAAM;AAChC,UAAM,gBAAgB,SAAS,cAAc,KAAK;AAClD,UAAM,QAAQ,IAAI,MAAM;AACxB,UAAM,MAAM;AACZ,kBAAc,KAAK;AACnB,kBAAc,OAAO,KAAK;AAC1B,UAAM,kBAAkB,eAAe;AAAA,MACrC,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,QAAQ;AAAA,MACR,iBAAiB;AAAA,MACjB,QAAQ;AAAA,IACV,CAAC;AACD,kBAAc,aAAa,SAAS,eAAe;AACnD,WAAO;AAAA,EACT;AACF;AACA,IAAM,WAAN,MAAe;AAAA,EACb,YAAY,SAAS,SAAS;AAC5B,SAAK,UAAU;AACf,SAAK,UAAU,kCACV,iBACA;AAEL,UAAM,cAAc,SAAS,cAAc,KAAK;AAChD,gBAAY,KAAK;AACjB,gBAAY,MAAM,WAAW;AAC7B,SAAK,QAAQ,IAAI,MAAM,aAAa,KAAK,OAAO;AAChD,SAAK,QAAQ,OAAO,WAAW;AAAA,EACjC;AAAA,EACA,OAAO,OAAO;AACZ,QAAI,CAAC,KAAK,SAAS;AACjB,YAAM,IAAI,MAAM,mBAAmB;AAAA,IACrC;AACA,SAAK,MAAM,UAAU,KAAK;AAC1B,SAAK,MAAM,OAAO;AAClB,QAAI,KAAK,QAAQ,eAAe;AAC9B,YAAM,UAAU,IAAI,QAAQ,SAAS,eAAe,iBAAiB,GAAG,KAAK,KAAK;AAClF,cAAQ,OAAO;AAAA,IACjB;AACA,WAAO,KAAK;AAAA,EACd;AACF;", "names": []}