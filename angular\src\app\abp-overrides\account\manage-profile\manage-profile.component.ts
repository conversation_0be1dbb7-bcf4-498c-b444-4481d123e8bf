import { Component, DestroyRef, inject, signal } from '@angular/core';
import { <PERSON><PERSON><PERSON>, MatCardContent } from '@angular/material/card';
import { <PERSON><PERSON><PERSON>I<PERSON>, MatNavList } from '@angular/material/list';
import { AuthService, ConfigStateService, LocalizationModule, LocalizationService } from '@abp/ng.core';
import { ProfileService } from '@abp/ng.account.core/proxy';
import {
  AlertService,
  fields,
  LOADING,
  model,
  requiredValidator,
  TtwrFormComponent
} from '@ttwr-framework/ngx-main-visuals';
import { MatDivider } from '@angular/material/divider';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Validators } from '@angular/forms';
import { ManageProfileStateService } from '@abp/ng.account';

@Component({
  selector: 'app-manage-profile',
  standalone: true,
  imports: [
    <PERSON><PERSON><PERSON>,
    MatCardContent,
    Mat<PERSON>av<PERSON>ist,
    MatListItem,
    LocalizationModule,
    TtwrForm<PERSON>omponent,
    MatDivider
  ],
  templateUrl: './manage-profile.component.html',
  styleUrl: './manage-profile.component.scss',
})
export class ManageProfileComponent {
  private localization = inject(LocalizationService);
  private alert = inject(AlertService);
  private profile = inject(ProfileService);
  private destroyRef = inject(DestroyRef);
  private state = inject(ConfigStateService);
  private manageState = inject(ManageProfileStateService);
  private auth = inject(AuthService);
  private loading = inject(LOADING);

  protected activeSection = signal<'password' | 'settings'>('password');

  protected passwordForm = model({
    currentPassword: fields.text(),
    newPassword: fields.text(),
    confirmPassword: fields.text()
  }).form({
    initialRequired: true,
    submitAction: {
      onSubmit: (value, formGroupDirective) => {
        if (value.newPassword !== value.confirmPassword) {
          this.alert.error(
            this.localization.instant('AbpIdentity::Volo.Abp.Identity:PasswordConfirmationFailed')
          );
          return;
        }

        this.loading.set(true);

        this.profile.changePassword(value)
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe({
            next: () => {
              const message = this.localization.instant('AbpAccount::PasswordChangedMessage');
              this.alert.success(message, {
                duration: 5000
              });

              formGroupDirective.resetForm();
            },
            error: err => {
              const message = this.localization.instant(err.error?.error?.message || 'AbpAccount::DefaultErrorMessage');
              this.alert.error(message);
            }
          });
      },
      submitLabel: 'Save',
      submitIcon: 'check'
    },
    fields: {
      currentPassword: {
        textInputType: 'password',
        label: this.localization.instant('AbpIdentity::DisplayName:CurrentPassword')
      },
      newPassword: {
        textInputType: 'password',
        label: this.localization.instant('AbpIdentity::DisplayName:NewPassword')
      },
      confirmPassword: {
        textInputType: 'password',
        label: this.localization.instant('AbpIdentity::DisplayName:NewPasswordConfirm'),
      }
    }
  });

  protected settingsForm = model({
    userName: fields.text(),
    name: fields.text(),
    surname: fields.text(),
    email: fields.text(),
    phoneNumber: fields.text(),
  }).form({
    submitAction: {
      onSubmit: (value) => {
        this.loading.set(true);
        const isRefreshTokenExists = this.auth.getRefreshToken();

        this.profile.update(value)
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe({
          next: profile => {
            this.alert.success(
              this.localization.instant('AbpAccount::PersonalSettingsSaved'),
              {
                duration: 5000,
              }
            );

            this.loading.set(false);
            this.manageState.setProfile(profile);
            this.state.refreshAppState();
          },
          error: err => {
            this.loading.set(false);
            const message = this.localization.instant(err.error?.error?.message || 'AbpAccount::DefaultErrorMessage');
            this.alert.error(message);
          }
        })

        if (isRefreshTokenExists) {
          this.auth.refreshToken();
        }
      },
      submitLabel: 'Save',
      submitIcon: 'check'
    },
    fields: {
      userName: {
        validators: [requiredValidator],
        inputSize: 'span 2',
        label: this.localization.instant('AbpIdentity::DisplayName:UserName'),
      },
      name: {
        label: this.localization.instant('AbpIdentity::DisplayName:Name'),
      },
      surname: {
        label: this.localization.instant('AbpIdentity::DisplayName:Surname'),
      },
      email: {
        validators: [
          requiredValidator,
          {
            name: 'email',
            validator: Validators.email,
            message: this.localization.instant('AbpValidation::ThisFieldIsNotAValidEmailAddress.'),
          }
        ],
        inputSize: 'span 2',
        label: this.localization.instant('AbpIdentity::DisplayName:Email'),
      },
      phoneNumber: {
        inputSize: 'span 2',
        label: this.localization.instant('AbpIdentity::DisplayName:PhoneNumber'),
        validators: [
          {
            name: 'maxlength',
            validator: Validators.maxLength(16),
            message: this.localization.instant(
              'AbpValidation::ThisFieldMustBeAStringOrArrayTypeWithAMaximumLengthOf{0}',
              '16',
            ),
          }
        ],
      },
    },
    viewFunc: () => this.profile.get() as any,
  })
}
