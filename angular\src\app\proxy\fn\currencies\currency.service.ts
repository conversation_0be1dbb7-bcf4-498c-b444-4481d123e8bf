import type { CurrencyDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class CurrencyService {
  apiName = 'Default';
  

  changeActivation = (code: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/currency/change-activation',
      params: { code },
    },
    { apiName: this.apiName,...config });
  

  getList = (dto: PagedResultRequestDto, name: string, isActive: boolean, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<CurrencyDto>>({
      method: 'GET',
      url: '/api/app/currency',
      params: { skipCount: dto.skipCount, maxResultCount: dto.maxResultCount, name, isActive },
    },
    { apiName: this.apiName,...config });
  

  getView = (code: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CurrencyDto>({
      method: 'GET',
      url: '/api/app/currency/view',
      params: { code },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
