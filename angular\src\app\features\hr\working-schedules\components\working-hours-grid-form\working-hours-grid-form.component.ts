import { Component, computed, DestroyRef, inject, model } from '@angular/core';
import { LanguagePipe, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { workingHour } from '../../working-schedules.model';
import { WorkingHoursDto } from '@proxy/hr/working-schedules/dto';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { map } from 'rxjs';
import { requireAllOperator } from '@shared';
import { MatDialog } from '@angular/material/dialog';
import { formatDate } from '@angular/common';
import { WorkingHourFormDialogComponent } from '../working-hour-form-dialog/working-hour-form-dialog.component';
import { DayOfWeek } from '../../day-of-week.enum';
import { MatMiniFabButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { MatTooltip } from '@angular/material/tooltip';

@Component({
  selector: 'app-working-hours-grid-form',
  standalone: true,
  imports: [TtwrGridComponent, LanguagePipe, MatIcon, MatTooltip, MatMiniFabButton],
  templateUrl: './working-hours-grid-form.component.html',
  styleUrl: './working-hours-grid-form.component.scss',
})
export class WorkingHoursGridFormComponent {
  private dialog = inject(MatDialog);
  private destroyRef = inject(DestroyRef);

  public lastId = model.required<number>();
  public workingHours = model.required<(WorkingHoursDto & { id: number })[]>();

  private data$ = toObservable(this.workingHours).pipe(
    map(hours => ({
      items: hours,
      totalCount: hours.length,
    })),
    requireAllOperator(),
  );

  protected averageHourNumber = computed(() => {
    const workingHours = this.workingHours();

    const totalHours = workingHours
      .map(hour => hour.expectedAttendance!.split('T')[1].split(':')[0])
      .map(Number)
      .reduce((a, b) => a + b, 0);

    return (totalHours / workingHours.length).toFixed(1);
  });

  protected config = workingHour.grid({
    hiddenPagination: true,
    elevationClass: 'mat-elevation-z0',
    dataFunc: () => this.data$,
    fields: {
      day: {
        columnName: '::GoldenOwl:Day',
      },
      from: {
        columnName: '::GoldenOwl:From',
        displayCell: cell => formatDate(cell, 'hh:mm a', 'en-Us'),
      },
      to: {
        columnName: '::GoldenOwl:To',
        displayCell: cell => formatDate(cell, 'hh:mm a', 'en-Us'),
      },
      expectedAttendance: {
        columnName: '::GoldenOwl:ExpectedAttendance',
        displayCell: cell => formatDate(cell, 'HH:mm', 'en-Us'),
      },
    },
    fieldActions: [
      {
        matIcon: 'edit',
        tooltip: 'Edit',
        delegateFunc: (obj) => {
          const ref = this.dialog.open(WorkingHourFormDialogComponent, {
            width: '700px',
            data: obj,
          });

          ref.afterClosed().pipe(
            takeUntilDestroyed(this.destroyRef),
          ).subscribe(result => {
            if (result) {
              this.workingHours.update(pre => pre.map(
                  item => item.id === obj.id
                    ? { id: obj.id, ...result }
                    : item
                )
              );
            }
          });
        }
      },
      {
        matIcon: 'delete',
        tooltip: 'Remove',
        color: 'warn',
        delegateFunc: (obj) => {
          this.workingHours.update(pre => pre.filter(item => item.id !== obj.id));
        }
      }
    ],
  });

  addAction() {
    const ref = this.dialog.open(WorkingHourFormDialogComponent, {
      width: '700px',
    });

    ref.afterClosed().pipe(
      takeUntilDestroyed(this.destroyRef),
    ).subscribe(result => {
      if (result) {
        let i = 1;
        result.day.forEach((d: DayOfWeek) => {
          this.workingHours.update(pre => [...pre, {
            id: this.lastId() + i,
            ...result,
            day: d,
          }]);

          i++;
        });

        this.lastId.update(pre => pre + i);
      }
    });
  }
}
