import type { ContractType } from '../contract-type.enum';
import type { ContractState } from '../contract-state.enum';
import type { WorkEntrySource } from '../work-entry-source.enum';
import type { EntityDto } from '@abp/ng.core';

export interface CancelContractDto {
  reason?: string;
}

export interface ContractDto {
  id?: string;
  name?: string;
  type: ContractType;
  start?: string;
  end?: string;
  employeeId?: string;
  state: ContractState;
  cancelReason?: string;
  workEntrySource: WorkEntrySource;
  jobTitleName?: string;
  salaryStructureId?: string;
  jobTitleId?: string;
  contractWorkingSchedules: ContractWorkingScheduleDto[];
}

export interface ContractWorkingScheduleDto extends EntityDto<string> {
  workingScheduleId?: string;
  workingScheduleName?: string;
  from?: string;
  to?: string;
}

export interface CreateUpdateContractDto {
  name?: string;
  type: ContractType;
  start?: string;
  end?: string;
  employeeId?: string;
  workEntrySource: WorkEntrySource;
  jobTitleId?: string;
  salaryStructureId?: string;
  contractWorkingSchedules: CreateUpdateContractWorkingScheduleDto[];
}

export interface CreateUpdateContractWorkingScheduleDto {
  workingScheduleId?: string;
  from?: string;
  to?: string;
}
