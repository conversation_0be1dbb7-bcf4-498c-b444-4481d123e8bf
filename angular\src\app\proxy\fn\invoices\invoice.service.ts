import type { CreateInvoiceDto, InvoiceDto, UpdateInvoiceDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class InvoiceService {
  apiName = 'Default';
  

  create = (input: CreateInvoiceDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/invoice',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, InvoiceDto>({
      method: 'GET',
      url: `/api/app/invoice/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, invoiceNumber: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<InvoiceDto>>({
      method: 'GET',
      url: '/api/app/invoice',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount, invoiceNumber },
    },
    { apiName: this.apiName,...config });
  

  postGetTemplateForInvoiceByInput = (input: CreateInvoiceDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CreateInvoiceDto>({
      method: 'POST',
      url: '/api/app/invoice/get-template-for-invoice',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  postInvoice = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/invoice/${id}/invoice`,
    },
    { apiName: this.apiName,...config });
  

  unPostInvoice = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/invoice/${id}/un-post-invoice`,
    },
    { apiName: this.apiName,...config });
  

  update = (id: string, input: UpdateInvoiceDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: `/api/app/invoice/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
