import { Component, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  MatDialogContent,
  MatDialogRef,
  MatDialogTitle,
} from '@angular/material/dialog';
import { PenaltyTypeService } from '@proxy/hr/penalty-types';
import {
  AlertService,
  LanguagePipe,
  LOADING,
  requiredValidator,
  TtwrFormComponent,
} from '@ttwr-framework/ngx-main-visuals';
import { finalize } from 'rxjs';
import { rewardsTypes } from '../rewards-type.model';
import { RewardTypeService } from '@proxy/hr/reward-types';

@Component({
  selector: 'app-rewards-types-create-dialog',
  standalone: true,
  imports: [TtwrFormComponent, MatDialogTitle, MatDialogContent, LanguagePipe],
  template: `
    <h1 mat-dialog-title>{{ '::AddRewardType' | i18n }}</h1>
    <mat-dialog-content>
      <ttwr-form [config]="config" />
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-grid-template-columns: 1fr 1fr;
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class RewardsTypesCreateDialogComponent {
  private rewardTypeService = inject(RewardTypeService);
  private alert = inject(AlertService);
  private dialogRef = inject(MatDialogRef);
  private destroyRef = inject(DestroyRef);
  private loading = inject(LOADING);

  protected config = rewardsTypes.form({
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        this.rewardTypeService
          .create(body)
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef)
          )
          .subscribe(() => {
            this.alert.success('::CreatedSuccessfully');
            this.dialogRef.close(true);
          });
      },
    },
    fields: {
      name: {
        validators: [requiredValidator],
      },
      code: {
        label: '::RewardType:Code',
        validators: [requiredValidator],
      },
      salaryEffectValue: {
        label: '::RewardType:SalaryEffectValue',
      },
      salaryEffectValueType: {
        label: '::RewardType:SalaryEffectValueType',
      },
    },
  });
}
