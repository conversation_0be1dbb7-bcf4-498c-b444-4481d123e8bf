import { Component, DestroyRef, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  MAT_DIALOG_DATA,
  MatDialogContent,
  MatDialogRef,
  MatDialogTitle,
} from '@angular/material/dialog';
import {
  AlertService,
  LanguagePipe,
  LOADING,
  TtwrFormComponent,
} from '@ttwr-framework/ngx-main-visuals';
import { finalize, of } from 'rxjs';
import { clearDate } from '@shared';
import { FinancialPeriodService } from '@proxy/fn/financial-periods';
import { addAccountingAuditing } from '../../fiscal-period.model';

@Component({
  selector: 'app-fiscal-period-add-accounting-auditing-dialog',
  standalone: true,
  imports: [TtwrFormComponent, MatDialogTitle, MatDialogContent, LanguagePipe],
  template: `
    <h1 mat-dialog-title>{{ '::FiscalPeriod:AddAccountingAuditing' | i18n }}</h1>
    <mat-dialog-content>
      <ttwr-form [config]="config" />
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-gap: 1rem;
      --ttwr-form-grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
  `,
})
export class FiscalPeriodAddAccountingAuditingDialogComponent {
  private financialPeriodService = inject(FinancialPeriodService);
  private alert = inject(AlertService);
  private dialogRef = inject(MatDialogRef);
  private destroyRef = inject(DestroyRef);
  private data = inject<{ id: string }>(MAT_DIALOG_DATA);
  private loading = inject(LOADING);

  protected config = addAccountingAuditing
    .form({
      initialRequired: true,
      submitAction: {
        onSubmit: (body) => {
          this.loading.set(true);
          this.financialPeriodService
            .putAddAuditing( {
              accountingAuditingDate: clearDate(body.accountingAuditingDate),
              id: this.data.id,
            })
            .pipe(
              finalize(() => this.loading.set(false)),
              takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(() => {
              this.alert.success('::AddedSuccessfully');
              this.dialogRef.close(true);
            });
        },
      },
    });
}
