import {
  <PERSON>FieldType,
  FormFieldWithControlAndName,
  ICustomInputComponent,
  LanguagePipe,
  TtwrFilePickerComponent
} from '@ttwr-framework/ngx-main-visuals';
import { Component, inject, input } from '@angular/core';
import { <PERSON><PERSON><PERSON>r, MatFormField, MatLabel, MatSuffix } from '@angular/material/form-field';
import { ReactiveFormsModule, Validators } from '@angular/forms';
import { MatIcon } from '@angular/material/icon';
import { MatIconButton } from '@angular/material/button';
import {
  AttachmentCreateDialogComponent
} from './attachment-create-dialog.component';

@Component({
  selector: 'app-runtime-accept-file-custom-input',
  standalone: true,
  imports: [
    MatFormField,
    MatLabel,
    MatError,
    MatSuffix,
    LanguagePipe,
    TtwrFilePickerComponent,
    ReactiveFormsModule,
    MatIcon,
    MatIconButton,
  ],
  template: `
    <mat-form-field>
      <mat-label>
        {{ (field().label ?? field().name) | i18n }}
        @if (field().control.hasValidator(ngRequiredValidator)) {*}
      </mat-label>
      <ttwr-file-picker
        #filePicker
        [accept]="attachmentCreateDialog.accept()"
        [formControl]="field().control"
      />
      @if (filePicker.value.length !== 0) {
        <button [disabled]="field().control.disabled" (click)="filePicker.clear($event)" type="button" mat-icon-button matSuffix>
          <mat-icon>close</mat-icon>
        </button>
      } @else {
        <button [disabled]="field().control.disabled" type="button" mat-icon-button matSuffix>
          <mat-icon>folder</mat-icon>
        </button>
      }
      <mat-error>
        @for (validator of field().validators ?? []; track validator.name) {
          <span [hidden]="!field().control.hasError(validator.name)">
            {{ validator.message | i18n }}
          </span>
        }
      </mat-error>
    </mat-form-field>

  `,
  styles: `
    mat-form-field {
      width: 100%;
    }
  `,
})
export class RuntimeAcceptFileCustomInputComponent implements ICustomInputComponent<FileFieldType> {
  protected attachmentCreateDialog = inject(AttachmentCreateDialogComponent);
  public field = input.required<FormFieldWithControlAndName<FileFieldType>>();

  protected ngRequiredValidator = Validators.required;
}
