import { Component, DestroyRef, inject } from '@angular/core';
import { TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { requireAllOperator } from '@shared';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatDialog } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { TerminalService } from '@proxy/hr/terminals';
import { terminals } from './terminals.model';
import { TerminalsUpdateDialogComponent } from './terminals-update-dialog/terminals-update-dialog.component';
import { TerminalsCreateDialogComponent } from './terminals-create-dialog/terminals-create-dialog.component';

@Component({
  selector: 'app-terminals',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `<ttwr-grid class="actions-end" [config]="config"/>`,
})
export class TerminalsComponent {
  private terminal = inject(TerminalService);
  private dialog = inject(MatDialog);
  private destroyRef = inject(DestroyRef);

  private refreshSubject = new Subject<void>();

  protected config = terminals.exclude({ port: true }).grid({
    title: '::FingerPrintTerminalDevices',
    refreshSubject: this.refreshSubject,
    dataFunc: pagination => this.terminal.getList({
      skipCount: pagination.pageSize * pagination.pageIndex,
      maxResultCount: pagination.pageSize,
    }).pipe(requireAllOperator()),
    actions: [
      {
        matIcon: 'add',
        tooltip: 'Add',
        delegateFunc: () => {
          const ref = this.dialog.open(TerminalsCreateDialogComponent, {
            width: '500px'
          });

          ref.afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        }
      }
    ],
    fieldActions: [
      {
        matIcon: 'edit',
        tooltip: 'Edit',
        delegateFunc: (obj) => {
          const ref = this.dialog.open(TerminalsUpdateDialogComponent, {
            width: '700px',
            data: obj,
          });

          ref.afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        }
      },
    ],
    fields: {
      ipAddress: {
        columnName: '::Terminal:IpAddress',
      },
      comKey: {
        columnName: '::Terminal:ComKey',
      },
    },
  });
}
