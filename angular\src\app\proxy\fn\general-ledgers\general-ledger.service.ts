import type { GeneralLedgerDto, GetGeneralLedgerRequestDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class GeneralLedgerService {
  apiName = 'Default';
  

  postGetGeneralLedgerReportByInput = (input: GetGeneralLedgerRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, GeneralLedgerDto>({
      method: 'POST',
      url: '/api/app/general-ledger/get-general-ledger-report',
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
