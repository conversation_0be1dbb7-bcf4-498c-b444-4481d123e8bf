import type { LeaveTypeCreateUpdateDto, LeaveTypeDetailDto, LeaveTypeDto, LeaveTypeIdAndNameDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class LeaveTypeService {
  apiName = 'Default';
  

  create = (leaveTypeCreateUpdateDto: LeaveTypeCreateUpdateDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/leave-type',
      body: leaveTypeCreateUpdateDto,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/leave-type/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, LeaveTypeDetailDto>({
      method: 'GET',
      url: `/api/app/leave-type/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getAllocatableLeaveTypes = (input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<LeaveTypeDto>>({
      method: 'GET',
      url: '/api/app/leave-type/ocatable-leave-types',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<LeaveTypeDto>>({
      method: 'GET',
      url: '/api/app/leave-type',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getListIdAndName = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, LeaveTypeIdAndNameDto[]>({
      method: 'GET',
      url: '/api/app/leave-type/id-and-name',
    },
    { apiName: this.apiName,...config });
  

  update = (id: string, leaveTypeEditDto: LeaveTypeCreateUpdateDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: `/api/app/leave-type/${id}`,
      body: leaveTypeEditDto,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
