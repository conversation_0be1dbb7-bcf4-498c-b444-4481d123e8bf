using Elsa.Extensions;
using Elsa.Workflows;
using Elsa.Workflows.Attributes;
using ElsaWorkflows.GoldenOwl.Common.Requests;

namespace ElsaWorkflows.Domain.Activities.Requests;


[Activity("Requests",
    "Rejects the request based on the previous requestId, workflow input name \"RequestType\" - input: requestId")]
public class RejectRequest : CodeActivity
{
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        var requestTypesElsaHelper  = context.GetRequiredService<IRequestsElsaHelper>();
        
        var input = context.GetLastResult();
        if (input?.ToString() is null)
        {
            throw new InvalidOperationException("The last result is null or invalid");
        }
        
        var requestId = Guid.Parse(input.ToString());
        var requestTypeName = context.GetWorkflowInput<string>("RequestType");
        var requestType = Enum.Parse<RequestTypes>(requestTypeName);
        
        await requestTypesElsaHelper.RejectRequestAsync(requestType, requestId);
    }
}