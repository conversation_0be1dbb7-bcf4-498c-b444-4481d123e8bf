{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/menu.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, InjectionToken, Optional, SkipSelf, Inject, Injectable, inject, Injector, ViewContainerRef, EventEmitter, NgZone, ElementRef, ChangeDetectorRef, booleanAttribute, Input, Output, signal, computed, ContentChildren, NgModule } from '@angular/core';\nimport { Overlay, OverlayConfig, STANDARD_DROPDOWN_BELOW_POSITIONS, STANDARD_DROPDOWN_ADJACENT_POSITIONS, OverlayModule } from '@angular/cdk/overlay';\nimport { ENTER, SPACE, UP_ARROW, hasModifierKey, DOWN_ARROW, LEFT_ARROW, RIGHT_ARROW, TAB, ESCAPE } from '@angular/cdk/keycodes';\nimport { startWith, debounceTime, distinctUntilChanged, filter, takeUntil, mergeMap, mapTo, mergeAll, switchMap, skipWhile, skip } from 'rxjs/operators';\nimport { UniqueSelectionDispatcher } from '@angular/cdk/collections';\nimport { Subject, merge, fromEvent, defer, partition } from 'rxjs';\nimport { TemplatePortal } from '@angular/cdk/portal';\nimport { InputModalityDetector, FocusKeyManager } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { _getEventTarget } from '@angular/cdk/platform';\n\n/**\n * A grouping container for `CdkMenuItemRadio` instances, similar to a `role=\"radiogroup\"` element.\n */\nclass CdkMenuGroup {\n  static {\n    this.ɵfac = function CdkMenuGroup_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkMenuGroup)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkMenuGroup,\n      selectors: [[\"\", \"cdkMenuGroup\", \"\"]],\n      hostAttrs: [\"role\", \"group\", 1, \"cdk-menu-group\"],\n      exportAs: [\"cdkMenuGroup\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: UniqueSelectionDispatcher,\n        useClass: UniqueSelectionDispatcher\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMenuGroup, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkMenuGroup]',\n      exportAs: 'cdkMenuGroup',\n      standalone: true,\n      host: {\n        'role': 'group',\n        'class': 'cdk-menu-group'\n      },\n      providers: [{\n        provide: UniqueSelectionDispatcher,\n        useClass: UniqueSelectionDispatcher\n      }]\n    }]\n  }], null, null);\n})();\n\n/** Injection token used to return classes implementing the Menu interface */\nconst CDK_MENU = new InjectionToken('cdk-menu');\n\n/** The relative item in the inline menu to focus after closing all popup menus. */\nvar FocusNext;\n(function (FocusNext) {\n  FocusNext[FocusNext[\"nextItem\"] = 0] = \"nextItem\";\n  FocusNext[FocusNext[\"previousItem\"] = 1] = \"previousItem\";\n  FocusNext[FocusNext[\"currentItem\"] = 2] = \"currentItem\";\n})(FocusNext || (FocusNext = {}));\n/** Injection token used for an implementation of MenuStack. */\nconst MENU_STACK = new InjectionToken('cdk-menu-stack');\n/** Provider that provides the parent menu stack, or a new menu stack if there is no parent one. */\nconst PARENT_OR_NEW_MENU_STACK_PROVIDER = {\n  provide: MENU_STACK,\n  deps: [[new Optional(), new SkipSelf(), new Inject(MENU_STACK)]],\n  useFactory: parentMenuStack => parentMenuStack || new MenuStack()\n};\n/** Provider that provides the parent menu stack, or a new inline menu stack if there is no parent one. */\nconst PARENT_OR_NEW_INLINE_MENU_STACK_PROVIDER = orientation => ({\n  provide: MENU_STACK,\n  deps: [[new Optional(), new SkipSelf(), new Inject(MENU_STACK)]],\n  useFactory: parentMenuStack => parentMenuStack || MenuStack.inline(orientation)\n});\n/** The next available menu stack ID. */\nlet nextId$2 = 0;\n/**\n * MenuStack allows subscribers to listen for close events (when a MenuStackItem is popped off\n * of the stack) in order to perform closing actions. Upon the MenuStack being empty it emits\n * from the `empty` observable specifying the next focus action which the listener should perform\n * as requested by the closer.\n */\nclass MenuStack {\n  constructor() {\n    /** The ID of this menu stack. */\n    this.id = `${nextId$2++}`;\n    /** All MenuStackItems tracked by this MenuStack. */\n    this._elements = [];\n    /** Emits the element which was popped off of the stack when requested by a closer. */\n    this._close = new Subject();\n    /** Emits once the MenuStack has become empty after popping off elements. */\n    this._empty = new Subject();\n    /** Emits whether any menu in the menu stack has focus. */\n    this._hasFocus = new Subject();\n    /** Observable which emits the MenuStackItem which has been requested to close. */\n    this.closed = this._close;\n    /** Observable which emits whether any menu in the menu stack has focus. */\n    this.hasFocus = this._hasFocus.pipe(startWith(false), debounceTime(0), distinctUntilChanged());\n    /**\n     * Observable which emits when the MenuStack is empty after popping off the last element. It\n     * emits a FocusNext event which specifies the action the closer has requested the listener\n     * perform.\n     */\n    this.emptied = this._empty;\n    /**\n     * Whether the inline menu associated with this menu stack is vertical or horizontal.\n     * `null` indicates there is no inline menu associated with this menu stack.\n     */\n    this._inlineMenuOrientation = null;\n  }\n  /** Creates a menu stack that originates from an inline menu. */\n  static inline(orientation) {\n    const stack = new MenuStack();\n    stack._inlineMenuOrientation = orientation;\n    return stack;\n  }\n  /**\n   * Adds an item to the menu stack.\n   * @param menu the MenuStackItem to put on the stack.\n   */\n  push(menu) {\n    this._elements.push(menu);\n  }\n  /**\n   * Pop items off of the stack up to and including `lastItem` and emit each on the close\n   * observable. If the stack is empty or `lastItem` is not on the stack it does nothing.\n   * @param lastItem the last item to pop off the stack.\n   * @param options Options that configure behavior on close.\n   */\n  close(lastItem, options) {\n    const {\n      focusNextOnEmpty,\n      focusParentTrigger\n    } = {\n      ...options\n    };\n    if (this._elements.indexOf(lastItem) >= 0) {\n      let poppedElement;\n      do {\n        poppedElement = this._elements.pop();\n        this._close.next({\n          item: poppedElement,\n          focusParentTrigger\n        });\n      } while (poppedElement !== lastItem);\n      if (this.isEmpty()) {\n        this._empty.next(focusNextOnEmpty);\n      }\n    }\n  }\n  /**\n   * Pop items off of the stack up to but excluding `lastItem` and emit each on the close\n   * observable. If the stack is empty or `lastItem` is not on the stack it does nothing.\n   * @param lastItem the element which should be left on the stack\n   * @return whether or not an item was removed from the stack\n   */\n  closeSubMenuOf(lastItem) {\n    let removed = false;\n    if (this._elements.indexOf(lastItem) >= 0) {\n      removed = this.peek() !== lastItem;\n      while (this.peek() !== lastItem) {\n        this._close.next({\n          item: this._elements.pop()\n        });\n      }\n    }\n    return removed;\n  }\n  /**\n   * Pop off all MenuStackItems and emit each one on the `close` observable one by one.\n   * @param options Options that configure behavior on close.\n   */\n  closeAll(options) {\n    const {\n      focusNextOnEmpty,\n      focusParentTrigger\n    } = {\n      ...options\n    };\n    if (!this.isEmpty()) {\n      while (!this.isEmpty()) {\n        const menuStackItem = this._elements.pop();\n        if (menuStackItem) {\n          this._close.next({\n            item: menuStackItem,\n            focusParentTrigger\n          });\n        }\n      }\n      this._empty.next(focusNextOnEmpty);\n    }\n  }\n  /** Return true if this stack is empty. */\n  isEmpty() {\n    return !this._elements.length;\n  }\n  /** Return the length of the stack. */\n  length() {\n    return this._elements.length;\n  }\n  /** Get the top most element on the stack. */\n  peek() {\n    return this._elements[this._elements.length - 1];\n  }\n  /** Whether the menu stack is associated with an inline menu. */\n  hasInlineMenu() {\n    return this._inlineMenuOrientation != null;\n  }\n  /** The orientation of the associated inline menu. */\n  inlineMenuOrientation() {\n    return this._inlineMenuOrientation;\n  }\n  /** Sets whether the menu stack contains the focused element. */\n  setHasFocus(hasFocus) {\n    this._hasFocus.next(hasFocus);\n  }\n  static {\n    this.ɵfac = function MenuStack_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MenuStack)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MenuStack,\n      factory: MenuStack.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenuStack, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/** Injection token used for an implementation of MenuStack. */\nconst MENU_TRIGGER = new InjectionToken('cdk-menu-trigger');\n/** Injection token used to configure the behavior of the menu when the page is scrolled. */\nconst MENU_SCROLL_STRATEGY = new InjectionToken('cdk-menu-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition();\n  }\n});\n/**\n * Abstract directive that implements shared logic common to all menu triggers.\n * This class can be extended to create custom menu trigger types.\n */\nclass CdkMenuTriggerBase {\n  constructor() {\n    /** The DI injector for this component. */\n    this.injector = inject(Injector);\n    /** The view container ref for this component */\n    this.viewContainerRef = inject(ViewContainerRef);\n    /** The menu stack in which this menu resides. */\n    this.menuStack = inject(MENU_STACK);\n    /** Function used to configure the scroll strategy for the menu. */\n    this.menuScrollStrategy = inject(MENU_SCROLL_STRATEGY);\n    /** Emits when the attached menu is requested to open */\n    this.opened = new EventEmitter();\n    /** Emits when the attached menu is requested to close */\n    this.closed = new EventEmitter();\n    /** A reference to the overlay which manages the triggered menu */\n    this.overlayRef = null;\n    /** Emits when this trigger is destroyed. */\n    this.destroyed = new Subject();\n    /** Emits when the outside pointer events listener on the overlay should be stopped. */\n    this.stopOutsideClicksListener = merge(this.closed, this.destroyed);\n  }\n  ngOnDestroy() {\n    this._destroyOverlay();\n    this.destroyed.next();\n    this.destroyed.complete();\n  }\n  /** Whether the attached menu is open. */\n  isOpen() {\n    return !!this.overlayRef?.hasAttached();\n  }\n  /** Registers a child menu as having been opened by this trigger. */\n  registerChildMenu(child) {\n    this.childMenu = child;\n  }\n  /**\n   * Get the portal to be attached to the overlay which contains the menu. Allows for the menu\n   * content to change dynamically and be reflected in the application.\n   */\n  getMenuContentPortal() {\n    const hasMenuContentChanged = this.menuTemplateRef !== this._menuPortal?.templateRef;\n    if (this.menuTemplateRef && (!this._menuPortal || hasMenuContentChanged)) {\n      this._menuPortal = new TemplatePortal(this.menuTemplateRef, this.viewContainerRef, this.menuData, this._getChildMenuInjector());\n    }\n    return this._menuPortal;\n  }\n  /**\n   * Whether the given element is inside the scope of this trigger's menu stack.\n   * @param element The element to check.\n   * @return Whether the element is inside the scope of this trigger's menu stack.\n   */\n  isElementInsideMenuStack(element) {\n    for (let el = element; el; el = el?.parentElement ?? null) {\n      if (el.getAttribute('data-cdk-menu-stack-id') === this.menuStack.id) {\n        return true;\n      }\n    }\n    return false;\n  }\n  /** Destroy and unset the overlay reference it if exists */\n  _destroyOverlay() {\n    if (this.overlayRef) {\n      this.overlayRef.dispose();\n      this.overlayRef = null;\n    }\n  }\n  /** Gets the injector to use when creating a child menu. */\n  _getChildMenuInjector() {\n    this._childMenuInjector = this._childMenuInjector || Injector.create({\n      providers: [{\n        provide: MENU_TRIGGER,\n        useValue: this\n      }, {\n        provide: MENU_STACK,\n        useValue: this.menuStack\n      }],\n      parent: this.injector\n    });\n    return this._childMenuInjector;\n  }\n  static {\n    this.ɵfac = function CdkMenuTriggerBase_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkMenuTriggerBase)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkMenuTriggerBase,\n      hostVars: 2,\n      hostBindings: function CdkMenuTriggerBase_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-controls\", ctx.childMenu == null ? null : ctx.childMenu.id)(\"data-cdk-menu-stack-id\", ctx.menuStack.id);\n        }\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMenuTriggerBase, [{\n    type: Directive,\n    args: [{\n      host: {\n        '[attr.aria-controls]': 'childMenu?.id',\n        '[attr.data-cdk-menu-stack-id]': 'menuStack.id'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Throws an exception when an instance of the PointerFocusTracker is not provided.\n * @docs-private\n */\nfunction throwMissingPointerFocusTracker() {\n  throw Error('expected an instance of PointerFocusTracker to be provided');\n}\n/**\n * Throws an exception when a reference to the parent menu is not provided.\n * @docs-private\n */\nfunction throwMissingMenuReference() {\n  throw Error('expected a reference to the parent menu');\n}\n\n/** Injection token used for an implementation of MenuAim. */\nconst MENU_AIM = new InjectionToken('cdk-menu-aim');\n/** Capture every nth mouse move event. */\nconst MOUSE_MOVE_SAMPLE_FREQUENCY = 3;\n/** The number of mouse move events to track. */\nconst NUM_POINTS = 5;\n/**\n * How long to wait before closing a sibling menu if a user stops short of the submenu they were\n * predicted to go into.\n */\nconst CLOSE_DELAY = 300;\n/** Calculate the slope between point a and b. */\nfunction getSlope(a, b) {\n  return (b.y - a.y) / (b.x - a.x);\n}\n/** Calculate the y intercept for the given point and slope. */\nfunction getYIntercept(point, slope) {\n  return point.y - slope * point.x;\n}\n/**\n * Whether the given mouse trajectory line defined by the slope and y intercept falls within the\n * submenu as defined by `submenuPoints`\n * @param submenuPoints the submenu DOMRect points.\n * @param m the slope of the trajectory line.\n * @param b the y intercept of the trajectory line.\n * @return true if any point on the line falls within the submenu.\n */\nfunction isWithinSubmenu(submenuPoints, m, b) {\n  const {\n    left,\n    right,\n    top,\n    bottom\n  } = submenuPoints;\n  // Check for intersection with each edge of the submenu (left, right, top, bottom)\n  // by fixing one coordinate to that edge's coordinate (either x or y) and checking if the\n  // other coordinate is within bounds.\n  return m * left + b >= top && m * left + b <= bottom || m * right + b >= top && m * right + b <= bottom || (top - b) / m >= left && (top - b) / m <= right || (bottom - b) / m >= left && (bottom - b) / m <= right;\n}\n/**\n * TargetMenuAim predicts if a user is moving into a submenu. It calculates the\n * trajectory of the user's mouse movement in the current menu to determine if the\n * mouse is moving towards an open submenu.\n *\n * The determination is made by calculating the slope of the users last NUM_POINTS moves where each\n * pair of points determines if the trajectory line points into the submenu. It uses consensus\n * approach by checking if at least NUM_POINTS / 2 pairs determine that the user is moving towards\n * to submenu.\n */\nclass TargetMenuAim {\n  constructor() {\n    /** The Angular zone. */\n    this._ngZone = inject(NgZone);\n    /** The last NUM_POINTS mouse move events. */\n    this._points = [];\n    /** Emits when this service is destroyed. */\n    this._destroyed = new Subject();\n  }\n  ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /**\n   * Set the Menu and its PointerFocusTracker.\n   * @param menu The menu that this menu aim service controls.\n   * @param pointerTracker The `PointerFocusTracker` for the given menu.\n   */\n  initialize(menu, pointerTracker) {\n    this._menu = menu;\n    this._pointerTracker = pointerTracker;\n    this._subscribeToMouseMoves();\n  }\n  /**\n   * Calls the `doToggle` callback when it is deemed that the user is not moving towards\n   * the submenu.\n   * @param doToggle the function called when the user is not moving towards the submenu.\n   */\n  toggle(doToggle) {\n    // If the menu is horizontal the sub-menus open below and there is no risk of premature\n    // closing of any sub-menus therefore we automatically resolve the callback.\n    if (this._menu.orientation === 'horizontal') {\n      doToggle();\n    }\n    this._checkConfigured();\n    const siblingItemIsWaiting = !!this._timeoutId;\n    const hasPoints = this._points.length > 1;\n    if (hasPoints && !siblingItemIsWaiting) {\n      if (this._isMovingToSubmenu()) {\n        this._startTimeout(doToggle);\n      } else {\n        doToggle();\n      }\n    } else if (!siblingItemIsWaiting) {\n      doToggle();\n    }\n  }\n  /**\n   * Start the delayed toggle handler if one isn't running already.\n   *\n   * The delayed toggle handler executes the `doToggle` callback after some period of time iff the\n   * users mouse is on an item in the current menu.\n   *\n   * @param doToggle the function called when the user is not moving towards the submenu.\n   */\n  _startTimeout(doToggle) {\n    // If the users mouse is moving towards a submenu we don't want to immediately resolve.\n    // Wait for some period of time before determining if the previous menu should close in\n    // cases where the user may have moved towards the submenu but stopped on a sibling menu\n    // item intentionally.\n    const timeoutId = setTimeout(() => {\n      // Resolve if the user is currently moused over some element in the root menu\n      if (this._pointerTracker.activeElement && timeoutId === this._timeoutId) {\n        doToggle();\n      }\n      this._timeoutId = null;\n    }, CLOSE_DELAY);\n    this._timeoutId = timeoutId;\n  }\n  /** Whether the user is heading towards the open submenu. */\n  _isMovingToSubmenu() {\n    const submenuPoints = this._getSubmenuBounds();\n    if (!submenuPoints) {\n      return false;\n    }\n    let numMoving = 0;\n    const currPoint = this._points[this._points.length - 1];\n    // start from the second last point and calculate the slope between each point and the last\n    // point.\n    for (let i = this._points.length - 2; i >= 0; i--) {\n      const previous = this._points[i];\n      const slope = getSlope(currPoint, previous);\n      if (isWithinSubmenu(submenuPoints, slope, getYIntercept(currPoint, slope))) {\n        numMoving++;\n      }\n    }\n    return numMoving >= Math.floor(NUM_POINTS / 2);\n  }\n  /** Get the bounding DOMRect for the open submenu. */\n  _getSubmenuBounds() {\n    return this._pointerTracker?.previousElement?.getMenu()?.nativeElement.getBoundingClientRect();\n  }\n  /**\n   * Check if a reference to the PointerFocusTracker and menu element is provided.\n   * @throws an error if neither reference is provided.\n   */\n  _checkConfigured() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._pointerTracker) {\n        throwMissingPointerFocusTracker();\n      }\n      if (!this._menu) {\n        throwMissingMenuReference();\n      }\n    }\n  }\n  /** Subscribe to the root menus mouse move events and update the tracked mouse points. */\n  _subscribeToMouseMoves() {\n    this._ngZone.runOutsideAngular(() => {\n      fromEvent(this._menu.nativeElement, 'mousemove').pipe(filter((_, index) => index % MOUSE_MOVE_SAMPLE_FREQUENCY === 0), takeUntil(this._destroyed)).subscribe(event => {\n        this._points.push({\n          x: event.clientX,\n          y: event.clientY\n        });\n        if (this._points.length > NUM_POINTS) {\n          this._points.shift();\n        }\n      });\n    });\n  }\n  static {\n    this.ɵfac = function TargetMenuAim_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TargetMenuAim)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TargetMenuAim,\n      factory: TargetMenuAim.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TargetMenuAim, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * CdkTargetMenuAim is a provider for the TargetMenuAim service. It can be added to an\n * element with either the `cdkMenu` or `cdkMenuBar` directive and child menu items.\n */\nclass CdkTargetMenuAim {\n  static {\n    this.ɵfac = function CdkTargetMenuAim_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkTargetMenuAim)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkTargetMenuAim,\n      selectors: [[\"\", \"cdkTargetMenuAim\", \"\"]],\n      exportAs: [\"cdkTargetMenuAim\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MENU_AIM,\n        useClass: TargetMenuAim\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTargetMenuAim, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTargetMenuAim]',\n      exportAs: 'cdkTargetMenuAim',\n      standalone: true,\n      providers: [{\n        provide: MENU_AIM,\n        useClass: TargetMenuAim\n      }]\n    }]\n  }], null, null);\n})();\n\n/** Checks whether a keyboard event will trigger a native `click` event on an element. */\nfunction eventDispatchesNativeClick(elementRef, event) {\n  // Synthetic events won't trigger clicks.\n  if (!event.isTrusted) {\n    return false;\n  }\n  const el = elementRef.nativeElement;\n  const keyCode = event.keyCode;\n  // Buttons trigger clicks both on space and enter events.\n  if (el.nodeName === 'BUTTON' && !el.disabled) {\n    return keyCode === ENTER || keyCode === SPACE;\n  }\n  // Links only trigger clicks on enter.\n  if (el.nodeName === 'A') {\n    return keyCode === ENTER;\n  }\n  // Any other elements won't dispatch clicks from keyboard events.\n  return false;\n}\n\n/**\n * A directive that turns its host element into a trigger for a popup menu.\n * It can be combined with cdkMenuItem to create sub-menus. If the element is in a top level\n * MenuBar it will open the menu on click, or if a sibling is already opened it will open on hover.\n * If it is inside of a Menu it will open the attached Submenu on hover regardless of its sibling\n * state.\n */\nclass CdkMenuTrigger extends CdkMenuTriggerBase {\n  constructor() {\n    super();\n    this._elementRef = inject(ElementRef);\n    this._overlay = inject(Overlay);\n    this._ngZone = inject(NgZone);\n    this._changeDetectorRef = inject(ChangeDetectorRef);\n    this._inputModalityDetector = inject(InputModalityDetector);\n    this._directionality = inject(Directionality, {\n      optional: true\n    });\n    /** The parent menu this trigger belongs to. */\n    this._parentMenu = inject(CDK_MENU, {\n      optional: true\n    });\n    /** The menu aim service used by this menu. */\n    this._menuAim = inject(MENU_AIM, {\n      optional: true\n    });\n    this._setRole();\n    this._registerCloseHandler();\n    this._subscribeToMenuStackClosed();\n    this._subscribeToMouseEnter();\n    this._subscribeToMenuStackHasFocus();\n    this._setType();\n  }\n  /** Toggle the attached menu. */\n  toggle() {\n    this.isOpen() ? this.close() : this.open();\n  }\n  /** Open the attached menu. */\n  open() {\n    if (!this.isOpen() && this.menuTemplateRef != null) {\n      this.opened.next();\n      this.overlayRef = this.overlayRef || this._overlay.create(this._getOverlayConfig());\n      this.overlayRef.attach(this.getMenuContentPortal());\n      this._changeDetectorRef.markForCheck();\n      this._subscribeToOutsideClicks();\n    }\n  }\n  /** Close the opened menu. */\n  close() {\n    if (this.isOpen()) {\n      this.closed.next();\n      this.overlayRef.detach();\n      this._changeDetectorRef.markForCheck();\n    }\n    this._closeSiblingTriggers();\n  }\n  /**\n   * Get a reference to the rendered Menu if the Menu is open and rendered in the DOM.\n   */\n  getMenu() {\n    return this.childMenu;\n  }\n  /**\n   * Handles keyboard events for the menu item.\n   * @param event The keyboard event to handle\n   */\n  _toggleOnKeydown(event) {\n    const isParentVertical = this._parentMenu?.orientation === 'vertical';\n    switch (event.keyCode) {\n      case SPACE:\n      case ENTER:\n        // Skip events that will trigger clicks so the handler doesn't get triggered twice.\n        if (!hasModifierKey(event) && !eventDispatchesNativeClick(this._elementRef, event)) {\n          this.toggle();\n          this.childMenu?.focusFirstItem('keyboard');\n        }\n        break;\n      case RIGHT_ARROW:\n        if (!hasModifierKey(event)) {\n          if (this._parentMenu && isParentVertical && this._directionality?.value !== 'rtl') {\n            event.preventDefault();\n            this.open();\n            this.childMenu?.focusFirstItem('keyboard');\n          }\n        }\n        break;\n      case LEFT_ARROW:\n        if (!hasModifierKey(event)) {\n          if (this._parentMenu && isParentVertical && this._directionality?.value === 'rtl') {\n            event.preventDefault();\n            this.open();\n            this.childMenu?.focusFirstItem('keyboard');\n          }\n        }\n        break;\n      case DOWN_ARROW:\n      case UP_ARROW:\n        if (!hasModifierKey(event)) {\n          if (!isParentVertical) {\n            event.preventDefault();\n            this.open();\n            event.keyCode === DOWN_ARROW ? this.childMenu?.focusFirstItem('keyboard') : this.childMenu?.focusLastItem('keyboard');\n          }\n        }\n        break;\n    }\n  }\n  /** Handles clicks on the menu trigger. */\n  _handleClick() {\n    this.toggle();\n    this.childMenu?.focusFirstItem('mouse');\n  }\n  /**\n   * Sets whether the trigger's menu stack has focus.\n   * @param hasFocus Whether the menu stack has focus.\n   */\n  _setHasFocus(hasFocus) {\n    if (!this._parentMenu) {\n      this.menuStack.setHasFocus(hasFocus);\n    }\n  }\n  /**\n   * Subscribe to the mouseenter events and close any sibling menu items if this element is moused\n   * into.\n   */\n  _subscribeToMouseEnter() {\n    this._ngZone.runOutsideAngular(() => {\n      fromEvent(this._elementRef.nativeElement, 'mouseenter').pipe(filter(() => {\n        return (\n          // Skip fake `mouseenter` events dispatched by touch devices.\n          this._inputModalityDetector.mostRecentModality !== 'touch' && !this.menuStack.isEmpty() && !this.isOpen()\n        );\n      }), takeUntil(this.destroyed)).subscribe(() => {\n        // Closes any sibling menu items and opens the menu associated with this trigger.\n        const toggleMenus = () => this._ngZone.run(() => {\n          this._closeSiblingTriggers();\n          this.open();\n        });\n        if (this._menuAim) {\n          this._menuAim.toggle(toggleMenus);\n        } else {\n          toggleMenus();\n        }\n      });\n    });\n  }\n  /** Close out any sibling menu trigger menus. */\n  _closeSiblingTriggers() {\n    if (this._parentMenu) {\n      // If nothing was removed from the stack and the last element is not the parent item\n      // that means that the parent menu is a menu bar since we don't put the menu bar on the\n      // stack\n      const isParentMenuBar = !this.menuStack.closeSubMenuOf(this._parentMenu) && this.menuStack.peek() !== this._parentMenu;\n      if (isParentMenuBar) {\n        this.menuStack.closeAll();\n      }\n    } else {\n      this.menuStack.closeAll();\n    }\n  }\n  /** Get the configuration object used to create the overlay. */\n  _getOverlayConfig() {\n    return new OverlayConfig({\n      positionStrategy: this._getOverlayPositionStrategy(),\n      scrollStrategy: this.menuScrollStrategy(),\n      direction: this._directionality || undefined\n    });\n  }\n  /** Build the position strategy for the overlay which specifies where to place the menu. */\n  _getOverlayPositionStrategy() {\n    return this._overlay.position().flexibleConnectedTo(this._elementRef).withLockedPosition().withGrowAfterOpen().withPositions(this._getOverlayPositions());\n  }\n  /** Get the preferred positions for the opened menu relative to the menu item. */\n  _getOverlayPositions() {\n    return this.menuPosition ?? (!this._parentMenu || this._parentMenu.orientation === 'horizontal' ? STANDARD_DROPDOWN_BELOW_POSITIONS : STANDARD_DROPDOWN_ADJACENT_POSITIONS);\n  }\n  /**\n   * Subscribe to the MenuStack close events if this is a standalone trigger and close out the menu\n   * this triggers when requested.\n   */\n  _registerCloseHandler() {\n    if (!this._parentMenu) {\n      this.menuStack.closed.pipe(takeUntil(this.destroyed)).subscribe(({\n        item\n      }) => {\n        if (item === this.childMenu) {\n          this.close();\n        }\n      });\n    }\n  }\n  /**\n   * Subscribe to the overlays outside pointer events stream and handle closing out the stack if a\n   * click occurs outside the menus.\n   */\n  _subscribeToOutsideClicks() {\n    if (this.overlayRef) {\n      this.overlayRef.outsidePointerEvents().pipe(takeUntil(this.stopOutsideClicksListener)).subscribe(event => {\n        const target = _getEventTarget(event);\n        const element = this._elementRef.nativeElement;\n        if (target !== element && !element.contains(target)) {\n          if (!this.isElementInsideMenuStack(target)) {\n            this.menuStack.closeAll();\n          } else {\n            this._closeSiblingTriggers();\n          }\n        }\n      });\n    }\n  }\n  /** Subscribe to the MenuStack hasFocus events. */\n  _subscribeToMenuStackHasFocus() {\n    if (!this._parentMenu) {\n      this.menuStack.hasFocus.pipe(takeUntil(this.destroyed)).subscribe(hasFocus => {\n        if (!hasFocus) {\n          this.menuStack.closeAll();\n        }\n      });\n    }\n  }\n  /** Subscribe to the MenuStack closed events. */\n  _subscribeToMenuStackClosed() {\n    if (!this._parentMenu) {\n      this.menuStack.closed.subscribe(({\n        focusParentTrigger\n      }) => {\n        if (focusParentTrigger && !this.menuStack.length()) {\n          this._elementRef.nativeElement.focus();\n        }\n      });\n    }\n  }\n  /** Sets the role attribute for this trigger if needed. */\n  _setRole() {\n    // If this trigger is part of another menu, the cdkMenuItem directive will handle setting the\n    // role, otherwise this is a standalone trigger, and we should ensure it has role=\"button\".\n    if (!this._parentMenu) {\n      this._elementRef.nativeElement.setAttribute('role', 'button');\n    }\n  }\n  /** Sets thte `type` attribute of the trigger. */\n  _setType() {\n    const element = this._elementRef.nativeElement;\n    if (element.nodeName === 'BUTTON' && !element.getAttribute('type')) {\n      // Prevents form submissions.\n      element.setAttribute('type', 'button');\n    }\n  }\n  static {\n    this.ɵfac = function CdkMenuTrigger_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkMenuTrigger)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkMenuTrigger,\n      selectors: [[\"\", \"cdkMenuTriggerFor\", \"\"]],\n      hostAttrs: [1, \"cdk-menu-trigger\"],\n      hostVars: 2,\n      hostBindings: function CdkMenuTrigger_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focusin\", function CdkMenuTrigger_focusin_HostBindingHandler() {\n            return ctx._setHasFocus(true);\n          })(\"focusout\", function CdkMenuTrigger_focusout_HostBindingHandler() {\n            return ctx._setHasFocus(false);\n          })(\"keydown\", function CdkMenuTrigger_keydown_HostBindingHandler($event) {\n            return ctx._toggleOnKeydown($event);\n          })(\"click\", function CdkMenuTrigger_click_HostBindingHandler() {\n            return ctx._handleClick();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-haspopup\", ctx.menuTemplateRef ? \"menu\" : null)(\"aria-expanded\", ctx.menuTemplateRef == null ? null : ctx.isOpen());\n        }\n      },\n      inputs: {\n        menuTemplateRef: [0, \"cdkMenuTriggerFor\", \"menuTemplateRef\"],\n        menuPosition: [0, \"cdkMenuPosition\", \"menuPosition\"],\n        menuData: [0, \"cdkMenuTriggerData\", \"menuData\"]\n      },\n      outputs: {\n        opened: \"cdkMenuOpened\",\n        closed: \"cdkMenuClosed\"\n      },\n      exportAs: [\"cdkMenuTriggerFor\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MENU_TRIGGER,\n        useExisting: CdkMenuTrigger\n      }, PARENT_OR_NEW_MENU_STACK_PROVIDER]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMenuTrigger, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkMenuTriggerFor]',\n      exportAs: 'cdkMenuTriggerFor',\n      standalone: true,\n      host: {\n        'class': 'cdk-menu-trigger',\n        '[attr.aria-haspopup]': 'menuTemplateRef ? \"menu\" : null',\n        '[attr.aria-expanded]': 'menuTemplateRef == null ? null : isOpen()',\n        '(focusin)': '_setHasFocus(true)',\n        '(focusout)': '_setHasFocus(false)',\n        '(keydown)': '_toggleOnKeydown($event)',\n        '(click)': '_handleClick()'\n      },\n      inputs: [{\n        name: 'menuTemplateRef',\n        alias: 'cdkMenuTriggerFor'\n      }, {\n        name: 'menuPosition',\n        alias: 'cdkMenuPosition'\n      }, {\n        name: 'menuData',\n        alias: 'cdkMenuTriggerData'\n      }],\n      outputs: ['opened: cdkMenuOpened', 'closed: cdkMenuClosed'],\n      providers: [{\n        provide: MENU_TRIGGER,\n        useExisting: CdkMenuTrigger\n      }, PARENT_OR_NEW_MENU_STACK_PROVIDER]\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Directive which provides the ability for an element to be focused and navigated to using the\n * keyboard when residing in a CdkMenu, CdkMenuBar, or CdkMenuGroup. It performs user defined\n * behavior when clicked.\n */\nclass CdkMenuItem {\n  /** Whether the menu item opens a menu. */\n  get hasMenu() {\n    return this._menuTrigger?.menuTemplateRef != null;\n  }\n  constructor() {\n    this._dir = inject(Directionality, {\n      optional: true\n    });\n    this._elementRef = inject(ElementRef);\n    this._ngZone = inject(NgZone);\n    this._inputModalityDetector = inject(InputModalityDetector);\n    /** The menu aim service used by this menu. */\n    this._menuAim = inject(MENU_AIM, {\n      optional: true\n    });\n    /** The stack of menus this menu belongs to. */\n    this._menuStack = inject(MENU_STACK);\n    /** The parent menu in which this menuitem resides. */\n    this._parentMenu = inject(CDK_MENU, {\n      optional: true\n    });\n    /** Reference to the CdkMenuItemTrigger directive if one is added to the same element */\n    this._menuTrigger = inject(CdkMenuTrigger, {\n      optional: true,\n      self: true\n    });\n    /**  Whether the CdkMenuItem is disabled - defaults to false */\n    this.disabled = false;\n    /**\n     * If this MenuItem is a regular MenuItem, outputs when it is triggered by a keyboard or mouse\n     * event.\n     */\n    this.triggered = new EventEmitter();\n    /**\n     * The tabindex for this menu item managed internally and used for implementing roving a\n     * tab index.\n     */\n    this._tabindex = -1;\n    /** Whether the item should close the menu if triggered by the spacebar. */\n    this.closeOnSpacebarTrigger = true;\n    /** Emits when the menu item is destroyed. */\n    this.destroyed = new Subject();\n    this._setupMouseEnter();\n    this._setType();\n    if (this._isStandaloneItem()) {\n      this._tabindex = 0;\n    }\n  }\n  ngOnDestroy() {\n    this.destroyed.next();\n    this.destroyed.complete();\n  }\n  /** Place focus on the element. */\n  focus() {\n    this._elementRef.nativeElement.focus();\n  }\n  /**\n   * If the menu item is not disabled and the element does not have a menu trigger attached, emit\n   * on the cdkMenuItemTriggered emitter and close all open menus.\n   * @param options Options the configure how the item is triggered\n   *   - keepOpen: specifies that the menu should be kept open after triggering the item.\n   */\n  trigger(options) {\n    const {\n      keepOpen\n    } = {\n      ...options\n    };\n    if (!this.disabled && !this.hasMenu) {\n      this.triggered.next();\n      if (!keepOpen) {\n        this._menuStack.closeAll({\n          focusParentTrigger: true\n        });\n      }\n    }\n  }\n  /** Return true if this MenuItem has an attached menu and it is open. */\n  isMenuOpen() {\n    return !!this._menuTrigger?.isOpen();\n  }\n  /**\n   * Get a reference to the rendered Menu if the Menu is open and it is visible in the DOM.\n   * @return the menu if it is open, otherwise undefined.\n   */\n  getMenu() {\n    return this._menuTrigger?.getMenu();\n  }\n  /** Get the CdkMenuTrigger associated with this element. */\n  getMenuTrigger() {\n    return this._menuTrigger;\n  }\n  /** Get the label for this element which is required by the FocusableOption interface. */\n  getLabel() {\n    return this.typeaheadLabel || this._elementRef.nativeElement.textContent?.trim() || '';\n  }\n  /** Reset the tabindex to -1. */\n  _resetTabIndex() {\n    if (!this._isStandaloneItem()) {\n      this._tabindex = -1;\n    }\n  }\n  /**\n   * Set the tab index to 0 if not disabled and it's a focus event, or a mouse enter if this element\n   * is not in a menu bar.\n   */\n  _setTabIndex(event) {\n    if (this.disabled) {\n      return;\n    }\n    // don't set the tabindex if there are no open sibling or parent menus\n    if (!event || !this._menuStack.isEmpty()) {\n      this._tabindex = 0;\n    }\n  }\n  /**\n   * Handles keyboard events for the menu item, specifically either triggering the user defined\n   * callback or opening/closing the current menu based on whether the left or right arrow key was\n   * pressed.\n   * @param event the keyboard event to handle\n   */\n  _onKeydown(event) {\n    switch (event.keyCode) {\n      case SPACE:\n      case ENTER:\n        // Skip events that will trigger clicks so the handler doesn't get triggered twice.\n        if (!hasModifierKey(event) && !eventDispatchesNativeClick(this._elementRef, event)) {\n          this.trigger({\n            keepOpen: event.keyCode === SPACE && !this.closeOnSpacebarTrigger\n          });\n        }\n        break;\n      case RIGHT_ARROW:\n        if (!hasModifierKey(event)) {\n          if (this._parentMenu && this._isParentVertical()) {\n            if (this._dir?.value !== 'rtl') {\n              this._forwardArrowPressed(event);\n            } else {\n              this._backArrowPressed(event);\n            }\n          }\n        }\n        break;\n      case LEFT_ARROW:\n        if (!hasModifierKey(event)) {\n          if (this._parentMenu && this._isParentVertical()) {\n            if (this._dir?.value !== 'rtl') {\n              this._backArrowPressed(event);\n            } else {\n              this._forwardArrowPressed(event);\n            }\n          }\n        }\n        break;\n    }\n  }\n  /** Whether this menu item is standalone or within a menu or menu bar. */\n  _isStandaloneItem() {\n    return !this._parentMenu;\n  }\n  /**\n   * Handles the user pressing the back arrow key.\n   * @param event The keyboard event.\n   */\n  _backArrowPressed(event) {\n    const parentMenu = this._parentMenu;\n    if (this._menuStack.hasInlineMenu() || this._menuStack.length() > 1) {\n      event.preventDefault();\n      this._menuStack.close(parentMenu, {\n        focusNextOnEmpty: this._menuStack.inlineMenuOrientation() === 'horizontal' ? FocusNext.previousItem : FocusNext.currentItem,\n        focusParentTrigger: true\n      });\n    }\n  }\n  /**\n   * Handles the user pressing the forward arrow key.\n   * @param event The keyboard event.\n   */\n  _forwardArrowPressed(event) {\n    if (!this.hasMenu && this._menuStack.inlineMenuOrientation() === 'horizontal') {\n      event.preventDefault();\n      this._menuStack.closeAll({\n        focusNextOnEmpty: FocusNext.nextItem,\n        focusParentTrigger: true\n      });\n    }\n  }\n  /**\n   * Subscribe to the mouseenter events and close any sibling menu items if this element is moused\n   * into.\n   */\n  _setupMouseEnter() {\n    if (!this._isStandaloneItem()) {\n      const closeOpenSiblings = () => this._ngZone.run(() => this._menuStack.closeSubMenuOf(this._parentMenu));\n      this._ngZone.runOutsideAngular(() => fromEvent(this._elementRef.nativeElement, 'mouseenter').pipe(filter(() => {\n        return (\n          // Skip fake `mouseenter` events dispatched by touch devices.\n          this._inputModalityDetector.mostRecentModality !== 'touch' && !this._menuStack.isEmpty() && !this.hasMenu\n        );\n      }), takeUntil(this.destroyed)).subscribe(() => {\n        if (this._menuAim) {\n          this._menuAim.toggle(closeOpenSiblings);\n        } else {\n          closeOpenSiblings();\n        }\n      }));\n    }\n  }\n  /**\n   * Return true if the enclosing parent menu is configured in a horizontal orientation, false\n   * otherwise or if no parent.\n   */\n  _isParentVertical() {\n    return this._parentMenu?.orientation === 'vertical';\n  }\n  /** Sets the `type` attribute of the menu item. */\n  _setType() {\n    const element = this._elementRef.nativeElement;\n    if (element.nodeName === 'BUTTON' && !element.getAttribute('type')) {\n      // Prevent form submissions.\n      element.setAttribute('type', 'button');\n    }\n  }\n  static {\n    this.ɵfac = function CdkMenuItem_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkMenuItem)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkMenuItem,\n      selectors: [[\"\", \"cdkMenuItem\", \"\"]],\n      hostAttrs: [\"role\", \"menuitem\", 1, \"cdk-menu-item\"],\n      hostVars: 2,\n      hostBindings: function CdkMenuItem_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"blur\", function CdkMenuItem_blur_HostBindingHandler() {\n            return ctx._resetTabIndex();\n          })(\"focus\", function CdkMenuItem_focus_HostBindingHandler() {\n            return ctx._setTabIndex();\n          })(\"click\", function CdkMenuItem_click_HostBindingHandler() {\n            return ctx.trigger();\n          })(\"keydown\", function CdkMenuItem_keydown_HostBindingHandler($event) {\n            return ctx._onKeydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"tabindex\", ctx._tabindex);\n          i0.ɵɵattribute(\"aria-disabled\", ctx.disabled || null);\n        }\n      },\n      inputs: {\n        disabled: [2, \"cdkMenuItemDisabled\", \"disabled\", booleanAttribute],\n        typeaheadLabel: [0, \"cdkMenuitemTypeaheadLabel\", \"typeaheadLabel\"]\n      },\n      outputs: {\n        triggered: \"cdkMenuItemTriggered\"\n      },\n      exportAs: [\"cdkMenuItem\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMenuItem, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkMenuItem]',\n      exportAs: 'cdkMenuItem',\n      standalone: true,\n      host: {\n        'role': 'menuitem',\n        'class': 'cdk-menu-item',\n        '[tabindex]': '_tabindex',\n        '[attr.aria-disabled]': 'disabled || null',\n        '(blur)': '_resetTabIndex()',\n        '(focus)': '_setTabIndex()',\n        '(click)': 'trigger()',\n        '(keydown)': '_onKeydown($event)'\n      }\n    }]\n  }], () => [], {\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkMenuItemDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    typeaheadLabel: [{\n      type: Input,\n      args: ['cdkMenuitemTypeaheadLabel']\n    }],\n    triggered: [{\n      type: Output,\n      args: ['cdkMenuItemTriggered']\n    }]\n  });\n})();\n\n/**\n * PointerFocusTracker keeps track of the currently active item under mouse focus. It also has\n * observables which emit when the users mouse enters and leaves a tracked element.\n */\nclass PointerFocusTracker {\n  constructor(/** The list of items being tracked. */\n  _items) {\n    this._items = _items;\n    /** Emits when an element is moused into. */\n    this.entered = this._getItemPointerEntries();\n    /** Emits when an element is moused out. */\n    this.exited = this._getItemPointerExits();\n    /** Emits when this is destroyed. */\n    this._destroyed = new Subject();\n    this.entered.subscribe(element => this.activeElement = element);\n    this.exited.subscribe(() => {\n      this.previousElement = this.activeElement;\n      this.activeElement = undefined;\n    });\n  }\n  /** Stop the managers listeners. */\n  destroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /**\n   * Gets a stream of pointer (mouse) entries into the given items.\n   * This should typically run outside the Angular zone.\n   */\n  _getItemPointerEntries() {\n    return defer(() => this._items.changes.pipe(startWith(this._items), mergeMap(list => list.map(element => fromEvent(element._elementRef.nativeElement, 'mouseenter').pipe(mapTo(element), takeUntil(this._items.changes)))), mergeAll()));\n  }\n  /**\n   * Gets a stream of pointer (mouse) exits out of the given items.\n   * This should typically run outside the Angular zone.\n   */\n  _getItemPointerExits() {\n    return defer(() => this._items.changes.pipe(startWith(this._items), mergeMap(list => list.map(element => fromEvent(element._elementRef.nativeElement, 'mouseout').pipe(mapTo(element), takeUntil(this._items.changes)))), mergeAll()));\n  }\n}\n\n/** Counter used to create unique IDs for menus. */\nlet nextId$1 = 0;\n/**\n * Abstract directive that implements shared logic common to all menus.\n * This class can be extended to create custom menu types.\n */\nclass CdkMenuBase extends CdkMenuGroup {\n  constructor() {\n    super(...arguments);\n    /** The menu's native DOM host element. */\n    this.nativeElement = inject(ElementRef).nativeElement;\n    /** The Angular zone. */\n    this.ngZone = inject(NgZone);\n    /** The stack of menus this menu belongs to. */\n    this.menuStack = inject(MENU_STACK);\n    /** The menu aim service used by this menu. */\n    this.menuAim = inject(MENU_AIM, {\n      optional: true,\n      self: true\n    });\n    /** The directionality (text direction) of the current page. */\n    this.dir = inject(Directionality, {\n      optional: true\n    });\n    /** The id of the menu's host element. */\n    this.id = `cdk-menu-${nextId$1++}`;\n    /** The direction items in the menu flow. */\n    this.orientation = 'vertical';\n    /**\n     * Whether the menu is displayed inline (i.e. always present vs a conditional popup that the\n     * user triggers with a trigger element).\n     */\n    this.isInline = false;\n    /** Emits when the MenuBar is destroyed. */\n    this.destroyed = new Subject();\n    /** Whether this menu's menu stack has focus. */\n    this._menuStackHasFocus = signal(false);\n    this._tabIndexSignal = computed(() => {\n      const tabindexIfInline = this._menuStackHasFocus() ? -1 : 0;\n      return this.isInline ? tabindexIfInline : null;\n    });\n  }\n  ngAfterContentInit() {\n    if (!this.isInline) {\n      this.menuStack.push(this);\n    }\n    this._setKeyManager();\n    this._subscribeToMenuStackHasFocus();\n    this._subscribeToMenuOpen();\n    this._subscribeToMenuStackClosed();\n    this._setUpPointerTracker();\n  }\n  ngOnDestroy() {\n    this.keyManager?.destroy();\n    this.destroyed.next();\n    this.destroyed.complete();\n    this.pointerTracker?.destroy();\n  }\n  /**\n   * Place focus on the first MenuItem in the menu and set the focus origin.\n   * @param focusOrigin The origin input mode of the focus event.\n   */\n  focusFirstItem(focusOrigin = 'program') {\n    this.keyManager.setFocusOrigin(focusOrigin);\n    this.keyManager.setFirstItemActive();\n  }\n  /**\n   * Place focus on the last MenuItem in the menu and set the focus origin.\n   * @param focusOrigin The origin input mode of the focus event.\n   */\n  focusLastItem(focusOrigin = 'program') {\n    this.keyManager.setFocusOrigin(focusOrigin);\n    this.keyManager.setLastItemActive();\n  }\n  /** Gets the tabindex for this menu. */\n  _getTabIndex() {\n    return this._tabIndexSignal();\n  }\n  /**\n   * Close the open menu if the current active item opened the requested MenuStackItem.\n   * @param menu The menu requested to be closed.\n   * @param options Options to configure the behavior on close.\n   *   - `focusParentTrigger` Whether to focus the parent trigger after closing the menu.\n   */\n  closeOpenMenu(menu, options) {\n    const {\n      focusParentTrigger\n    } = {\n      ...options\n    };\n    const keyManager = this.keyManager;\n    const trigger = this.triggerItem;\n    if (menu === trigger?.getMenuTrigger()?.getMenu()) {\n      trigger?.getMenuTrigger()?.close();\n      // If the user has moused over a sibling item we want to focus the element under mouse focus\n      // not the trigger which previously opened the now closed menu.\n      if (focusParentTrigger) {\n        if (trigger) {\n          keyManager.setActiveItem(trigger);\n        } else {\n          keyManager.setFirstItemActive();\n        }\n      }\n    }\n  }\n  /** Setup the FocusKeyManager with the correct orientation for the menu. */\n  _setKeyManager() {\n    this.keyManager = new FocusKeyManager(this.items).withWrap().withTypeAhead().withHomeAndEnd();\n    if (this.orientation === 'horizontal') {\n      this.keyManager.withHorizontalOrientation(this.dir?.value || 'ltr');\n    } else {\n      this.keyManager.withVerticalOrientation();\n    }\n  }\n  /**\n   * Subscribe to the menu trigger's open events in order to track the trigger which opened the menu\n   * and stop tracking it when the menu is closed.\n   */\n  _subscribeToMenuOpen() {\n    const exitCondition = merge(this.items.changes, this.destroyed);\n    this.items.changes.pipe(startWith(this.items), mergeMap(list => list.filter(item => item.hasMenu).map(item => item.getMenuTrigger().opened.pipe(mapTo(item), takeUntil(exitCondition)))), mergeAll(), switchMap(item => {\n      this.triggerItem = item;\n      return item.getMenuTrigger().closed;\n    }), takeUntil(this.destroyed)).subscribe(() => this.triggerItem = undefined);\n  }\n  /** Subscribe to the MenuStack close events. */\n  _subscribeToMenuStackClosed() {\n    this.menuStack.closed.pipe(takeUntil(this.destroyed)).subscribe(({\n      item,\n      focusParentTrigger\n    }) => this.closeOpenMenu(item, {\n      focusParentTrigger\n    }));\n  }\n  /** Subscribe to the MenuStack hasFocus events. */\n  _subscribeToMenuStackHasFocus() {\n    if (this.isInline) {\n      this.menuStack.hasFocus.pipe(takeUntil(this.destroyed)).subscribe(hasFocus => {\n        this._menuStackHasFocus.set(hasFocus);\n      });\n    }\n  }\n  /**\n   * Set the PointerFocusTracker and ensure that when mouse focus changes the key manager is updated\n   * with the latest menu item under mouse focus.\n   */\n  _setUpPointerTracker() {\n    if (this.menuAim) {\n      this.ngZone.runOutsideAngular(() => {\n        this.pointerTracker = new PointerFocusTracker(this.items);\n      });\n      this.menuAim.initialize(this, this.pointerTracker);\n    }\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵCdkMenuBase_BaseFactory;\n      return function CdkMenuBase_Factory(__ngFactoryType__) {\n        return (ɵCdkMenuBase_BaseFactory || (ɵCdkMenuBase_BaseFactory = i0.ɵɵgetInheritedFactory(CdkMenuBase)))(__ngFactoryType__ || CdkMenuBase);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkMenuBase,\n      contentQueries: function CdkMenuBase_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, CdkMenuItem, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.items = _t);\n        }\n      },\n      hostAttrs: [\"role\", \"menu\"],\n      hostVars: 4,\n      hostBindings: function CdkMenuBase_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focus\", function CdkMenuBase_focus_HostBindingHandler() {\n            return ctx.focusFirstItem();\n          })(\"focusin\", function CdkMenuBase_focusin_HostBindingHandler() {\n            return ctx.menuStack.setHasFocus(true);\n          })(\"focusout\", function CdkMenuBase_focusout_HostBindingHandler() {\n            return ctx.menuStack.setHasFocus(false);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"tabindex\", ctx._getTabIndex())(\"id\", ctx.id);\n          i0.ɵɵattribute(\"aria-orientation\", ctx.orientation)(\"data-cdk-menu-stack-id\", ctx.menuStack.id);\n        }\n      },\n      inputs: {\n        id: \"id\"\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMenuBase, [{\n    type: Directive,\n    args: [{\n      host: {\n        'role': 'menu',\n        'class': '',\n        // reset the css class added by the super-class\n        '[tabindex]': '_getTabIndex()',\n        '[id]': 'id',\n        '[attr.aria-orientation]': 'orientation',\n        '[attr.data-cdk-menu-stack-id]': 'menuStack.id',\n        '(focus)': 'focusFirstItem()',\n        '(focusin)': 'menuStack.setHasFocus(true)',\n        '(focusout)': 'menuStack.setHasFocus(false)'\n      },\n      standalone: true\n    }]\n  }], null, {\n    id: [{\n      type: Input\n    }],\n    items: [{\n      type: ContentChildren,\n      args: [CdkMenuItem, {\n        descendants: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Directive which configures the element as a Menu which should contain child elements marked as\n * CdkMenuItem or CdkMenuGroup. Sets the appropriate role and aria-attributes for a menu and\n * contains accessible keyboard and mouse handling logic.\n *\n * It also acts as a RadioGroup for elements marked with role `menuitemradio`.\n */\nclass CdkMenu extends CdkMenuBase {\n  constructor() {\n    super();\n    this._parentTrigger = inject(MENU_TRIGGER, {\n      optional: true\n    });\n    /** Event emitted when the menu is closed. */\n    this.closed = new EventEmitter();\n    /** The direction items in the menu flow. */\n    this.orientation = 'vertical';\n    /** Whether the menu is displayed inline (i.e. always present vs a conditional popup that the user triggers with a trigger element). */\n    this.isInline = !this._parentTrigger;\n    this.destroyed.subscribe(this.closed);\n    this._parentTrigger?.registerChildMenu(this);\n  }\n  ngAfterContentInit() {\n    super.ngAfterContentInit();\n    this._subscribeToMenuStackEmptied();\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this.closed.complete();\n  }\n  /**\n   * Handle keyboard events for the Menu.\n   * @param event The keyboard event to be handled.\n   */\n  _handleKeyEvent(event) {\n    const keyManager = this.keyManager;\n    switch (event.keyCode) {\n      case LEFT_ARROW:\n      case RIGHT_ARROW:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          keyManager.setFocusOrigin('keyboard');\n          keyManager.onKeydown(event);\n        }\n        break;\n      case ESCAPE:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          this.menuStack.close(this, {\n            focusNextOnEmpty: FocusNext.currentItem,\n            focusParentTrigger: true\n          });\n        }\n        break;\n      case TAB:\n        if (!hasModifierKey(event, 'altKey', 'metaKey', 'ctrlKey')) {\n          this.menuStack.closeAll({\n            focusParentTrigger: true\n          });\n        }\n        break;\n      default:\n        keyManager.onKeydown(event);\n    }\n  }\n  /**\n   * Set focus the either the current, previous or next item based on the FocusNext event.\n   * @param focusNext The element to focus.\n   */\n  _toggleMenuFocus(focusNext) {\n    const keyManager = this.keyManager;\n    switch (focusNext) {\n      case FocusNext.nextItem:\n        keyManager.setFocusOrigin('keyboard');\n        keyManager.setNextItemActive();\n        break;\n      case FocusNext.previousItem:\n        keyManager.setFocusOrigin('keyboard');\n        keyManager.setPreviousItemActive();\n        break;\n      case FocusNext.currentItem:\n        if (keyManager.activeItem) {\n          keyManager.setFocusOrigin('keyboard');\n          keyManager.setActiveItem(keyManager.activeItem);\n        }\n        break;\n    }\n  }\n  /** Subscribe to the MenuStack emptied events. */\n  _subscribeToMenuStackEmptied() {\n    this.menuStack.emptied.pipe(takeUntil(this.destroyed)).subscribe(event => this._toggleMenuFocus(event));\n  }\n  static {\n    this.ɵfac = function CdkMenu_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkMenu)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkMenu,\n      selectors: [[\"\", \"cdkMenu\", \"\"]],\n      hostAttrs: [\"role\", \"menu\", 1, \"cdk-menu\"],\n      hostVars: 2,\n      hostBindings: function CdkMenu_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function CdkMenu_keydown_HostBindingHandler($event) {\n            return ctx._handleKeyEvent($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"cdk-menu-inline\", ctx.isInline);\n        }\n      },\n      outputs: {\n        closed: \"closed\"\n      },\n      exportAs: [\"cdkMenu\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkMenuGroup,\n        useExisting: CdkMenu\n      }, {\n        provide: CDK_MENU,\n        useExisting: CdkMenu\n      }, PARENT_OR_NEW_INLINE_MENU_STACK_PROVIDER('vertical')]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMenu, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkMenu]',\n      exportAs: 'cdkMenu',\n      standalone: true,\n      host: {\n        'role': 'menu',\n        'class': 'cdk-menu',\n        '[class.cdk-menu-inline]': 'isInline',\n        '(keydown)': '_handleKeyEvent($event)'\n      },\n      providers: [{\n        provide: CdkMenuGroup,\n        useExisting: CdkMenu\n      }, {\n        provide: CDK_MENU,\n        useExisting: CdkMenu\n      }, PARENT_OR_NEW_INLINE_MENU_STACK_PROVIDER('vertical')]\n    }]\n  }], () => [], {\n    closed: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Directive applied to an element which configures it as a MenuBar by setting the appropriate\n * role, aria attributes, and accessible keyboard and mouse handling logic. The component that\n * this directive is applied to should contain components marked with CdkMenuItem.\n *\n */\nclass CdkMenuBar extends CdkMenuBase {\n  constructor() {\n    super(...arguments);\n    /** The direction items in the menu flow. */\n    this.orientation = 'horizontal';\n    /** Whether the menu is displayed inline (i.e. always present vs a conditional popup that the user triggers with a trigger element). */\n    this.isInline = true;\n  }\n  ngAfterContentInit() {\n    super.ngAfterContentInit();\n    this._subscribeToMenuStackEmptied();\n  }\n  /**\n   * Handle keyboard events for the Menu.\n   * @param event The keyboard event to be handled.\n   */\n  _handleKeyEvent(event) {\n    const keyManager = this.keyManager;\n    switch (event.keyCode) {\n      case UP_ARROW:\n      case DOWN_ARROW:\n      case LEFT_ARROW:\n      case RIGHT_ARROW:\n        if (!hasModifierKey(event)) {\n          const horizontalArrows = event.keyCode === LEFT_ARROW || event.keyCode === RIGHT_ARROW;\n          // For a horizontal menu if the left/right keys were clicked, or a vertical menu if the\n          // up/down keys were clicked: if the current menu is open, close it then focus and open the\n          // next  menu.\n          if (horizontalArrows) {\n            event.preventDefault();\n            const prevIsOpen = keyManager.activeItem?.isMenuOpen();\n            keyManager.activeItem?.getMenuTrigger()?.close();\n            keyManager.setFocusOrigin('keyboard');\n            keyManager.onKeydown(event);\n            if (prevIsOpen) {\n              keyManager.activeItem?.getMenuTrigger()?.open();\n            }\n          }\n        }\n        break;\n      case ESCAPE:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          keyManager.activeItem?.getMenuTrigger()?.close();\n        }\n        break;\n      case TAB:\n        if (!hasModifierKey(event, 'altKey', 'metaKey', 'ctrlKey')) {\n          keyManager.activeItem?.getMenuTrigger()?.close();\n        }\n        break;\n      default:\n        keyManager.onKeydown(event);\n    }\n  }\n  /**\n   * Set focus to either the current, previous or next item based on the FocusNext event, then\n   * open the previous or next item.\n   * @param focusNext The element to focus.\n   */\n  _toggleOpenMenu(focusNext) {\n    const keyManager = this.keyManager;\n    switch (focusNext) {\n      case FocusNext.nextItem:\n        keyManager.setFocusOrigin('keyboard');\n        keyManager.setNextItemActive();\n        keyManager.activeItem?.getMenuTrigger()?.open();\n        break;\n      case FocusNext.previousItem:\n        keyManager.setFocusOrigin('keyboard');\n        keyManager.setPreviousItemActive();\n        keyManager.activeItem?.getMenuTrigger()?.open();\n        break;\n      case FocusNext.currentItem:\n        if (keyManager.activeItem) {\n          keyManager.setFocusOrigin('keyboard');\n          keyManager.setActiveItem(keyManager.activeItem);\n        }\n        break;\n    }\n  }\n  /** Subscribe to the MenuStack emptied events. */\n  _subscribeToMenuStackEmptied() {\n    this.menuStack?.emptied.pipe(takeUntil(this.destroyed)).subscribe(event => this._toggleOpenMenu(event));\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵCdkMenuBar_BaseFactory;\n      return function CdkMenuBar_Factory(__ngFactoryType__) {\n        return (ɵCdkMenuBar_BaseFactory || (ɵCdkMenuBar_BaseFactory = i0.ɵɵgetInheritedFactory(CdkMenuBar)))(__ngFactoryType__ || CdkMenuBar);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkMenuBar,\n      selectors: [[\"\", \"cdkMenuBar\", \"\"]],\n      hostAttrs: [\"role\", \"menubar\", 1, \"cdk-menu-bar\"],\n      hostBindings: function CdkMenuBar_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function CdkMenuBar_keydown_HostBindingHandler($event) {\n            return ctx._handleKeyEvent($event);\n          });\n        }\n      },\n      exportAs: [\"cdkMenuBar\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkMenuGroup,\n        useExisting: CdkMenuBar\n      }, {\n        provide: CDK_MENU,\n        useExisting: CdkMenuBar\n      }, {\n        provide: MENU_STACK,\n        useFactory: () => MenuStack.inline('horizontal')\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMenuBar, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkMenuBar]',\n      exportAs: 'cdkMenuBar',\n      standalone: true,\n      host: {\n        'role': 'menubar',\n        'class': 'cdk-menu-bar',\n        '(keydown)': '_handleKeyEvent($event)'\n      },\n      providers: [{\n        provide: CdkMenuGroup,\n        useExisting: CdkMenuBar\n      }, {\n        provide: CDK_MENU,\n        useExisting: CdkMenuBar\n      }, {\n        provide: MENU_STACK,\n        useFactory: () => MenuStack.inline('horizontal')\n      }]\n    }]\n  }], null, null);\n})();\n\n/** Base class providing checked state for selectable MenuItems. */\nclass CdkMenuItemSelectable extends CdkMenuItem {\n  constructor() {\n    super(...arguments);\n    /** Whether the element is checked */\n    this.checked = false;\n    /** Whether the item should close the menu if triggered by the spacebar. */\n    this.closeOnSpacebarTrigger = false;\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵCdkMenuItemSelectable_BaseFactory;\n      return function CdkMenuItemSelectable_Factory(__ngFactoryType__) {\n        return (ɵCdkMenuItemSelectable_BaseFactory || (ɵCdkMenuItemSelectable_BaseFactory = i0.ɵɵgetInheritedFactory(CdkMenuItemSelectable)))(__ngFactoryType__ || CdkMenuItemSelectable);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkMenuItemSelectable,\n      hostVars: 2,\n      hostBindings: function CdkMenuItemSelectable_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-checked\", !!ctx.checked)(\"aria-disabled\", ctx.disabled || null);\n        }\n      },\n      inputs: {\n        checked: [2, \"cdkMenuItemChecked\", \"checked\", booleanAttribute]\n      },\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMenuItemSelectable, [{\n    type: Directive,\n    args: [{\n      host: {\n        '[attr.aria-checked]': '!!checked',\n        '[attr.aria-disabled]': 'disabled || null'\n      },\n      standalone: true\n    }]\n  }], null, {\n    checked: [{\n      type: Input,\n      args: [{\n        alias: 'cdkMenuItemChecked',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/** Counter used to set a unique id and name for a selectable item */\nlet nextId = 0;\n/**\n * A directive providing behavior for the \"menuitemradio\" ARIA role, which behaves similarly to\n * a conventional radio-button. Any sibling `CdkMenuItemRadio` instances within the same `CdkMenu`\n * or `CdkMenuGroup` comprise a radio group with unique selection enforced.\n */\nclass CdkMenuItemRadio extends CdkMenuItemSelectable {\n  constructor() {\n    super();\n    /** The unique selection dispatcher for this radio's `CdkMenuGroup`. */\n    this._selectionDispatcher = inject(UniqueSelectionDispatcher);\n    /** An ID to identify this radio item to the `UniqueSelectionDispatcher`. */\n    this._id = `${nextId++}`;\n    this._registerDispatcherListener();\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this._removeDispatcherListener();\n  }\n  /**\n   * Toggles the checked state of the radio-button.\n   * @param options Options the configure how the item is triggered\n   *   - keepOpen: specifies that the menu should be kept open after triggering the item.\n   */\n  trigger(options) {\n    super.trigger(options);\n    if (!this.disabled) {\n      this._selectionDispatcher.notify(this._id, '');\n    }\n  }\n  /** Configure the unique selection dispatcher listener in order to toggle the checked state  */\n  _registerDispatcherListener() {\n    this._removeDispatcherListener = this._selectionDispatcher.listen(id => {\n      this.checked = this._id === id;\n    });\n  }\n  static {\n    this.ɵfac = function CdkMenuItemRadio_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkMenuItemRadio)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkMenuItemRadio,\n      selectors: [[\"\", \"cdkMenuItemRadio\", \"\"]],\n      hostAttrs: [\"role\", \"menuitemradio\"],\n      hostVars: 2,\n      hostBindings: function CdkMenuItemRadio_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"cdk-menu-item-radio\", true);\n        }\n      },\n      exportAs: [\"cdkMenuItemRadio\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkMenuItemSelectable,\n        useExisting: CdkMenuItemRadio\n      }, {\n        provide: CdkMenuItem,\n        useExisting: CdkMenuItemSelectable\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMenuItemRadio, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkMenuItemRadio]',\n      exportAs: 'cdkMenuItemRadio',\n      standalone: true,\n      host: {\n        'role': 'menuitemradio',\n        '[class.cdk-menu-item-radio]': 'true'\n      },\n      providers: [{\n        provide: CdkMenuItemSelectable,\n        useExisting: CdkMenuItemRadio\n      }, {\n        provide: CdkMenuItem,\n        useExisting: CdkMenuItemSelectable\n      }]\n    }]\n  }], () => [], null);\n})();\n\n/**\n * A directive providing behavior for the \"menuitemcheckbox\" ARIA role, which behaves similarly to a\n * conventional checkbox.\n */\nclass CdkMenuItemCheckbox extends CdkMenuItemSelectable {\n  /**\n   * Toggle the checked state of the checkbox.\n   * @param options Options the configure how the item is triggered\n   *   - keepOpen: specifies that the menu should be kept open after triggering the item.\n   */\n  trigger(options) {\n    super.trigger(options);\n    if (!this.disabled) {\n      this.checked = !this.checked;\n    }\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵCdkMenuItemCheckbox_BaseFactory;\n      return function CdkMenuItemCheckbox_Factory(__ngFactoryType__) {\n        return (ɵCdkMenuItemCheckbox_BaseFactory || (ɵCdkMenuItemCheckbox_BaseFactory = i0.ɵɵgetInheritedFactory(CdkMenuItemCheckbox)))(__ngFactoryType__ || CdkMenuItemCheckbox);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkMenuItemCheckbox,\n      selectors: [[\"\", \"cdkMenuItemCheckbox\", \"\"]],\n      hostAttrs: [\"role\", \"menuitemcheckbox\"],\n      hostVars: 2,\n      hostBindings: function CdkMenuItemCheckbox_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"cdk-menu-item-checkbox\", true);\n        }\n      },\n      exportAs: [\"cdkMenuItemCheckbox\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkMenuItemSelectable,\n        useExisting: CdkMenuItemCheckbox\n      }, {\n        provide: CdkMenuItem,\n        useExisting: CdkMenuItemSelectable\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMenuItemCheckbox, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkMenuItemCheckbox]',\n      exportAs: 'cdkMenuItemCheckbox',\n      standalone: true,\n      host: {\n        'role': 'menuitemcheckbox',\n        '[class.cdk-menu-item-checkbox]': 'true'\n      },\n      providers: [{\n        provide: CdkMenuItemSelectable,\n        useExisting: CdkMenuItemCheckbox\n      }, {\n        provide: CdkMenuItem,\n        useExisting: CdkMenuItemSelectable\n      }]\n    }]\n  }], null, null);\n})();\n\n/** The preferred menu positions for the context menu. */\nconst CONTEXT_MENU_POSITIONS = STANDARD_DROPDOWN_BELOW_POSITIONS.map(position => {\n  // In cases where the first menu item in the context menu is a trigger the submenu opens on a\n  // hover event. We offset the context menu 2px by default to prevent this from occurring.\n  const offsetX = position.overlayX === 'start' ? 2 : -2;\n  const offsetY = position.overlayY === 'top' ? 2 : -2;\n  return {\n    ...position,\n    offsetX,\n    offsetY\n  };\n});\n/** Tracks the last open context menu trigger across the entire application. */\nclass ContextMenuTracker {\n  /**\n   * Close the previous open context menu and set the given one as being open.\n   * @param trigger The trigger for the currently open Context Menu.\n   */\n  update(trigger) {\n    if (ContextMenuTracker._openContextMenuTrigger !== trigger) {\n      ContextMenuTracker._openContextMenuTrigger?.close();\n      ContextMenuTracker._openContextMenuTrigger = trigger;\n    }\n  }\n  static {\n    this.ɵfac = function ContextMenuTracker_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ContextMenuTracker)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ContextMenuTracker,\n      factory: ContextMenuTracker.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContextMenuTracker, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/**\n * A directive that opens a menu when a user right-clicks within its host element.\n * It is aware of nested context menus and will trigger only the lowest level non-disabled context menu.\n */\nclass CdkContextMenuTrigger extends CdkMenuTriggerBase {\n  constructor() {\n    super();\n    /** The CDK overlay service. */\n    this._overlay = inject(Overlay);\n    /** The directionality of the page. */\n    this._directionality = inject(Directionality, {\n      optional: true\n    });\n    /** The app's context menu tracking registry */\n    this._contextMenuTracker = inject(ContextMenuTracker);\n    this._changeDetectorRef = inject(ChangeDetectorRef);\n    /** Whether the context menu is disabled. */\n    this.disabled = false;\n    this._setMenuStackCloseListener();\n  }\n  /**\n   * Open the attached menu at the specified location.\n   * @param coordinates where to open the context menu\n   */\n  open(coordinates) {\n    this._open(null, coordinates);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Close the currently opened context menu. */\n  close() {\n    this.menuStack.closeAll();\n  }\n  /**\n   * Open the context menu and closes any previously open menus.\n   * @param event the mouse event which opens the context menu.\n   */\n  _openOnContextMenu(event) {\n    if (!this.disabled) {\n      // Prevent the native context menu from opening because we're opening a custom one.\n      event.preventDefault();\n      // Stop event propagation to ensure that only the closest enabled context menu opens.\n      // Otherwise, any context menus attached to containing elements would *also* open,\n      // resulting in multiple stacked context menus being displayed.\n      event.stopPropagation();\n      this._contextMenuTracker.update(this);\n      this._open(event, {\n        x: event.clientX,\n        y: event.clientY\n      });\n      // A context menu can be triggered via a mouse right click or a keyboard shortcut.\n      if (event.button === 2) {\n        this.childMenu?.focusFirstItem('mouse');\n      } else if (event.button === 0) {\n        this.childMenu?.focusFirstItem('keyboard');\n      } else {\n        this.childMenu?.focusFirstItem('program');\n      }\n    }\n  }\n  /**\n   * Get the configuration object used to create the overlay.\n   * @param coordinates the location to place the opened menu\n   */\n  _getOverlayConfig(coordinates) {\n    return new OverlayConfig({\n      positionStrategy: this._getOverlayPositionStrategy(coordinates),\n      scrollStrategy: this.menuScrollStrategy(),\n      direction: this._directionality || undefined\n    });\n  }\n  /**\n   * Get the position strategy for the overlay which specifies where to place the menu.\n   * @param coordinates the location to place the opened menu\n   */\n  _getOverlayPositionStrategy(coordinates) {\n    return this._overlay.position().flexibleConnectedTo(coordinates).withLockedPosition().withGrowAfterOpen().withPositions(this.menuPosition ?? CONTEXT_MENU_POSITIONS);\n  }\n  /** Subscribe to the menu stack close events and close this menu when requested. */\n  _setMenuStackCloseListener() {\n    this.menuStack.closed.pipe(takeUntil(this.destroyed)).subscribe(({\n      item\n    }) => {\n      if (item === this.childMenu && this.isOpen()) {\n        this.closed.next();\n        this.overlayRef.detach();\n      }\n    });\n  }\n  /**\n   * Subscribe to the overlays outside pointer events stream and handle closing out the stack if a\n   * click occurs outside the menus.\n   * @param userEvent User-generated event that opened the menu.\n   */\n  _subscribeToOutsideClicks(userEvent) {\n    if (this.overlayRef) {\n      let outsideClicks = this.overlayRef.outsidePointerEvents();\n      if (userEvent) {\n        const [auxClicks, nonAuxClicks] = partition(outsideClicks, ({\n          type\n        }) => type === 'auxclick');\n        outsideClicks = merge(\n        // Using a mouse, the `contextmenu` event can fire either when pressing the right button\n        // or left button + control. Most browsers won't dispatch a `click` event right after\n        // a `contextmenu` event triggered by left button + control, but Safari will (see #27832).\n        // This closes the menu immediately. To work around it, we check that both the triggering\n        // event and the current outside click event both had the control key pressed, and that\n        // that this is the first outside click event.\n        nonAuxClicks.pipe(skipWhile((event, index) => userEvent.ctrlKey && index === 0 && event.ctrlKey)),\n        // If the menu was triggered by the `contextmenu` event, skip the first `auxclick` event\n        // because it fires when the mouse is released on the same click that opened the menu.\n        auxClicks.pipe(skip(1)));\n      }\n      outsideClicks.pipe(takeUntil(this.stopOutsideClicksListener)).subscribe(event => {\n        if (!this.isElementInsideMenuStack(_getEventTarget(event))) {\n          this.menuStack.closeAll();\n        }\n      });\n    }\n  }\n  /**\n   * Open the attached menu at the specified location.\n   * @param userEvent User-generated event that opened the menu\n   * @param coordinates where to open the context menu\n   */\n  _open(userEvent, coordinates) {\n    if (this.disabled) {\n      return;\n    }\n    if (this.isOpen()) {\n      // since we're moving this menu we need to close any submenus first otherwise they end up\n      // disconnected from this one.\n      this.menuStack.closeSubMenuOf(this.childMenu);\n      this.overlayRef.getConfig().positionStrategy.setOrigin(coordinates);\n      this.overlayRef.updatePosition();\n    } else {\n      this.opened.next();\n      if (this.overlayRef) {\n        this.overlayRef.getConfig().positionStrategy.setOrigin(coordinates);\n        this.overlayRef.updatePosition();\n      } else {\n        this.overlayRef = this._overlay.create(this._getOverlayConfig(coordinates));\n      }\n      this.overlayRef.attach(this.getMenuContentPortal());\n      this._subscribeToOutsideClicks(userEvent);\n    }\n  }\n  static {\n    this.ɵfac = function CdkContextMenuTrigger_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkContextMenuTrigger)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkContextMenuTrigger,\n      selectors: [[\"\", \"cdkContextMenuTriggerFor\", \"\"]],\n      hostVars: 1,\n      hostBindings: function CdkContextMenuTrigger_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"contextmenu\", function CdkContextMenuTrigger_contextmenu_HostBindingHandler($event) {\n            return ctx._openOnContextMenu($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-cdk-menu-stack-id\", null);\n        }\n      },\n      inputs: {\n        menuTemplateRef: [0, \"cdkContextMenuTriggerFor\", \"menuTemplateRef\"],\n        menuPosition: [0, \"cdkContextMenuPosition\", \"menuPosition\"],\n        menuData: [0, \"cdkContextMenuTriggerData\", \"menuData\"],\n        disabled: [2, \"cdkContextMenuDisabled\", \"disabled\", booleanAttribute]\n      },\n      outputs: {\n        opened: \"cdkContextMenuOpened\",\n        closed: \"cdkContextMenuClosed\"\n      },\n      exportAs: [\"cdkContextMenuTriggerFor\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MENU_TRIGGER,\n        useExisting: CdkContextMenuTrigger\n      }, {\n        provide: MENU_STACK,\n        useClass: MenuStack\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkContextMenuTrigger, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkContextMenuTriggerFor]',\n      exportAs: 'cdkContextMenuTriggerFor',\n      standalone: true,\n      host: {\n        '[attr.data-cdk-menu-stack-id]': 'null',\n        '(contextmenu)': '_openOnContextMenu($event)'\n      },\n      inputs: [{\n        name: 'menuTemplateRef',\n        alias: 'cdkContextMenuTriggerFor'\n      }, {\n        name: 'menuPosition',\n        alias: 'cdkContextMenuPosition'\n      }, {\n        name: 'menuData',\n        alias: 'cdkContextMenuTriggerData'\n      }],\n      outputs: ['opened: cdkContextMenuOpened', 'closed: cdkContextMenuClosed'],\n      providers: [{\n        provide: MENU_TRIGGER,\n        useExisting: CdkContextMenuTrigger\n      }, {\n        provide: MENU_STACK,\n        useClass: MenuStack\n      }]\n    }]\n  }], () => [], {\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkContextMenuDisabled',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nconst MENU_DIRECTIVES = [CdkMenuBar, CdkMenu, CdkMenuItem, CdkMenuItemRadio, CdkMenuItemCheckbox, CdkMenuTrigger, CdkMenuGroup, CdkContextMenuTrigger, CdkTargetMenuAim];\n/** Module that declares components and directives for the CDK menu. */\nclass CdkMenuModule {\n  static {\n    this.ɵfac = function CdkMenuModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkMenuModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CdkMenuModule,\n      imports: [OverlayModule, CdkMenuBar, CdkMenu, CdkMenuItem, CdkMenuItemRadio, CdkMenuItemCheckbox, CdkMenuTrigger, CdkMenuGroup, CdkContextMenuTrigger, CdkTargetMenuAim],\n      exports: [CdkMenuBar, CdkMenu, CdkMenuItem, CdkMenuItemRadio, CdkMenuItemCheckbox, CdkMenuTrigger, CdkMenuGroup, CdkContextMenuTrigger, CdkTargetMenuAim]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [OverlayModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, ...MENU_DIRECTIVES],\n      exports: MENU_DIRECTIVES\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CDK_MENU, CdkContextMenuTrigger, CdkMenu, CdkMenuBar, CdkMenuBase, CdkMenuGroup, CdkMenuItem, CdkMenuItemCheckbox, CdkMenuItemRadio, CdkMenuItemSelectable, CdkMenuModule, CdkMenuTrigger, CdkMenuTriggerBase, CdkTargetMenuAim, ContextMenuTracker, FocusNext, MENU_AIM, MENU_SCROLL_STRATEGY, MENU_STACK, MENU_TRIGGER, MenuStack, PARENT_OR_NEW_INLINE_MENU_STACK_PROVIDER, PARENT_OR_NEW_MENU_STACK_PROVIDER, PointerFocusTracker, TargetMenuAim };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeA,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,aAAO,KAAK,qBAAqB,eAAc;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,MACpC,WAAW,CAAC,QAAQ,SAAS,GAAG,gBAAgB;AAAA,MAChD,UAAU,CAAC,cAAc;AAAA,MACzB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC,CAAC,CAAC;AAAA,IACL,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,WAAW,IAAI,eAAe,UAAU;AAG9C,IAAI;AAAA,CACH,SAAUA,YAAW;AACpB,EAAAA,WAAUA,WAAU,UAAU,IAAI,CAAC,IAAI;AACvC,EAAAA,WAAUA,WAAU,cAAc,IAAI,CAAC,IAAI;AAC3C,EAAAA,WAAUA,WAAU,aAAa,IAAI,CAAC,IAAI;AAC5C,GAAG,cAAc,YAAY,CAAC,EAAE;AAEhC,IAAM,aAAa,IAAI,eAAe,gBAAgB;AAEtD,IAAM,oCAAoC;AAAA,EACxC,SAAS;AAAA,EACT,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG,IAAI,OAAO,UAAU,CAAC,CAAC;AAAA,EAC/D,YAAY,qBAAmB,mBAAmB,IAAI,UAAU;AAClE;AAEA,IAAM,2CAA2C,kBAAgB;AAAA,EAC/D,SAAS;AAAA,EACT,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG,IAAI,OAAO,UAAU,CAAC,CAAC;AAAA,EAC/D,YAAY,qBAAmB,mBAAmB,UAAU,OAAO,WAAW;AAChF;AAEA,IAAI,WAAW;AAOf,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,cAAc;AAEZ,SAAK,KAAK,GAAG,UAAU;AAEvB,SAAK,YAAY,CAAC;AAElB,SAAK,SAAS,IAAI,QAAQ;AAE1B,SAAK,SAAS,IAAI,QAAQ;AAE1B,SAAK,YAAY,IAAI,QAAQ;AAE7B,SAAK,SAAS,KAAK;AAEnB,SAAK,WAAW,KAAK,UAAU,KAAK,UAAU,KAAK,GAAG,aAAa,CAAC,GAAG,qBAAqB,CAAC;AAM7F,SAAK,UAAU,KAAK;AAKpB,SAAK,yBAAyB;AAAA,EAChC;AAAA;AAAA,EAEA,OAAO,OAAO,aAAa;AACzB,UAAM,QAAQ,IAAI,WAAU;AAC5B,UAAM,yBAAyB;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,MAAM;AACT,SAAK,UAAU,KAAK,IAAI;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,UAAU,SAAS;AACvB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,mBACC;AAEL,QAAI,KAAK,UAAU,QAAQ,QAAQ,KAAK,GAAG;AACzC,UAAI;AACJ,SAAG;AACD,wBAAgB,KAAK,UAAU,IAAI;AACnC,aAAK,OAAO,KAAK;AAAA,UACf,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AAAA,MACH,SAAS,kBAAkB;AAC3B,UAAI,KAAK,QAAQ,GAAG;AAClB,aAAK,OAAO,KAAK,gBAAgB;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,UAAU;AACvB,QAAI,UAAU;AACd,QAAI,KAAK,UAAU,QAAQ,QAAQ,KAAK,GAAG;AACzC,gBAAU,KAAK,KAAK,MAAM;AAC1B,aAAO,KAAK,KAAK,MAAM,UAAU;AAC/B,aAAK,OAAO,KAAK;AAAA,UACf,MAAM,KAAK,UAAU,IAAI;AAAA,QAC3B,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,SAAS;AAChB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,mBACC;AAEL,QAAI,CAAC,KAAK,QAAQ,GAAG;AACnB,aAAO,CAAC,KAAK,QAAQ,GAAG;AACtB,cAAM,gBAAgB,KAAK,UAAU,IAAI;AACzC,YAAI,eAAe;AACjB,eAAK,OAAO,KAAK;AAAA,YACf,MAAM;AAAA,YACN;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AACA,WAAK,OAAO,KAAK,gBAAgB;AAAA,IACnC;AAAA,EACF;AAAA;AAAA,EAEA,UAAU;AACR,WAAO,CAAC,KAAK,UAAU;AAAA,EACzB;AAAA;AAAA,EAEA,SAAS;AACP,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA,EAEA,OAAO;AACL,WAAO,KAAK,UAAU,KAAK,UAAU,SAAS,CAAC;AAAA,EACjD;AAAA;AAAA,EAEA,gBAAgB;AACd,WAAO,KAAK,0BAA0B;AAAA,EACxC;AAAA;AAAA,EAEA,wBAAwB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,YAAY,UAAU;AACpB,SAAK,UAAU,KAAK,QAAQ;AAAA,EAC9B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kBAAkB,mBAAmB;AACxD,aAAO,KAAK,qBAAqB,YAAW;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,WAAU;AAAA,IACrB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,eAAe,IAAI,eAAe,kBAAkB;AAE1D,IAAM,uBAAuB,IAAI,eAAe,4BAA4B;AAAA,EAC1E,YAAY;AAAA,EACZ,SAAS,MAAM;AACb,UAAM,UAAU,OAAO,OAAO;AAC9B,WAAO,MAAM,QAAQ,iBAAiB,WAAW;AAAA,EACnD;AACF,CAAC;AAKD,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,cAAc;AAEZ,SAAK,WAAW,OAAO,QAAQ;AAE/B,SAAK,mBAAmB,OAAO,gBAAgB;AAE/C,SAAK,YAAY,OAAO,UAAU;AAElC,SAAK,qBAAqB,OAAO,oBAAoB;AAErD,SAAK,SAAS,IAAI,aAAa;AAE/B,SAAK,SAAS,IAAI,aAAa;AAE/B,SAAK,aAAa;AAElB,SAAK,YAAY,IAAI,QAAQ;AAE7B,SAAK,4BAA4B,MAAM,KAAK,QAAQ,KAAK,SAAS;AAAA,EACpE;AAAA,EACA,cAAc;AACZ,SAAK,gBAAgB;AACrB,SAAK,UAAU,KAAK;AACpB,SAAK,UAAU,SAAS;AAAA,EAC1B;AAAA;AAAA,EAEA,SAAS;AACP,WAAO,CAAC,CAAC,KAAK,YAAY,YAAY;AAAA,EACxC;AAAA;AAAA,EAEA,kBAAkB,OAAO;AACvB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,UAAM,wBAAwB,KAAK,oBAAoB,KAAK,aAAa;AACzE,QAAI,KAAK,oBAAoB,CAAC,KAAK,eAAe,wBAAwB;AACxE,WAAK,cAAc,IAAI,eAAe,KAAK,iBAAiB,KAAK,kBAAkB,KAAK,UAAU,KAAK,sBAAsB,CAAC;AAAA,IAChI;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,yBAAyB,SAAS;AAChC,aAAS,KAAK,SAAS,IAAI,KAAK,IAAI,iBAAiB,MAAM;AACzD,UAAI,GAAG,aAAa,wBAAwB,MAAM,KAAK,UAAU,IAAI;AACnE,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,kBAAkB;AAChB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,QAAQ;AACxB,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA;AAAA,EAEA,wBAAwB;AACtB,SAAK,qBAAqB,KAAK,sBAAsB,SAAS,OAAO;AAAA,MACnE,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,MACD,QAAQ,KAAK;AAAA,IACf,CAAC;AACD,WAAO,KAAK;AAAA,EACd;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAoB;AAAA,IACvD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,UAAU;AAAA,MACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,iBAAiB,IAAI,aAAa,OAAO,OAAO,IAAI,UAAU,EAAE,EAAE,0BAA0B,IAAI,UAAU,EAAE;AAAA,QAC7H;AAAA,MACF;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,QACJ,wBAAwB;AAAA,QACxB,iCAAiC;AAAA,MACnC;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,SAAS,kCAAkC;AACzC,QAAM,MAAM,4DAA4D;AAC1E;AAKA,SAAS,4BAA4B;AACnC,QAAM,MAAM,yCAAyC;AACvD;AAGA,IAAM,WAAW,IAAI,eAAe,cAAc;AAElD,IAAM,8BAA8B;AAEpC,IAAM,aAAa;AAKnB,IAAM,cAAc;AAEpB,SAAS,SAAS,GAAG,GAAG;AACtB,UAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;AAChC;AAEA,SAAS,cAAc,OAAO,OAAO;AACnC,SAAO,MAAM,IAAI,QAAQ,MAAM;AACjC;AASA,SAAS,gBAAgB,eAAe,GAAG,GAAG;AAC5C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAIJ,SAAO,IAAI,OAAO,KAAK,OAAO,IAAI,OAAO,KAAK,UAAU,IAAI,QAAQ,KAAK,OAAO,IAAI,QAAQ,KAAK,WAAW,MAAM,KAAK,KAAK,SAAS,MAAM,KAAK,KAAK,UAAU,SAAS,KAAK,KAAK,SAAS,SAAS,KAAK,KAAK;AAChN;AAWA,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,cAAc;AAEZ,SAAK,UAAU,OAAO,MAAM;AAE5B,SAAK,UAAU,CAAC;AAEhB,SAAK,aAAa,IAAI,QAAQ;AAAA,EAChC;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,MAAM,gBAAgB;AAC/B,SAAK,QAAQ;AACb,SAAK,kBAAkB;AACvB,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,UAAU;AAGf,QAAI,KAAK,MAAM,gBAAgB,cAAc;AAC3C,eAAS;AAAA,IACX;AACA,SAAK,iBAAiB;AACtB,UAAM,uBAAuB,CAAC,CAAC,KAAK;AACpC,UAAM,YAAY,KAAK,QAAQ,SAAS;AACxC,QAAI,aAAa,CAAC,sBAAsB;AACtC,UAAI,KAAK,mBAAmB,GAAG;AAC7B,aAAK,cAAc,QAAQ;AAAA,MAC7B,OAAO;AACL,iBAAS;AAAA,MACX;AAAA,IACF,WAAW,CAAC,sBAAsB;AAChC,eAAS;AAAA,IACX;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,UAAU;AAKtB,UAAM,YAAY,WAAW,MAAM;AAEjC,UAAI,KAAK,gBAAgB,iBAAiB,cAAc,KAAK,YAAY;AACvE,iBAAS;AAAA,MACX;AACA,WAAK,aAAa;AAAA,IACpB,GAAG,WAAW;AACd,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA,EAEA,qBAAqB;AACnB,UAAM,gBAAgB,KAAK,kBAAkB;AAC7C,QAAI,CAAC,eAAe;AAClB,aAAO;AAAA,IACT;AACA,QAAI,YAAY;AAChB,UAAM,YAAY,KAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC;AAGtD,aAAS,IAAI,KAAK,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AACjD,YAAM,WAAW,KAAK,QAAQ,CAAC;AAC/B,YAAM,QAAQ,SAAS,WAAW,QAAQ;AAC1C,UAAI,gBAAgB,eAAe,OAAO,cAAc,WAAW,KAAK,CAAC,GAAG;AAC1E;AAAA,MACF;AAAA,IACF;AACA,WAAO,aAAa,KAAK,MAAM,aAAa,CAAC;AAAA,EAC/C;AAAA;AAAA,EAEA,oBAAoB;AAClB,WAAO,KAAK,iBAAiB,iBAAiB,QAAQ,GAAG,cAAc,sBAAsB;AAAA,EAC/F;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,KAAK,iBAAiB;AACzB,wCAAgC;AAAA,MAClC;AACA,UAAI,CAAC,KAAK,OAAO;AACf,kCAA0B;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,yBAAyB;AACvB,SAAK,QAAQ,kBAAkB,MAAM;AACnC,gBAAU,KAAK,MAAM,eAAe,WAAW,EAAE,KAAK,OAAO,CAAC,GAAG,UAAU,QAAQ,gCAAgC,CAAC,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,WAAS;AACpK,aAAK,QAAQ,KAAK;AAAA,UAChB,GAAG,MAAM;AAAA,UACT,GAAG,MAAM;AAAA,QACX,CAAC;AACD,YAAI,KAAK,QAAQ,SAAS,YAAY;AACpC,eAAK,QAAQ,MAAM;AAAA,QACrB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAe;AAAA,IAClD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,eAAc;AAAA,IACzB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,MACxC,UAAU,CAAC,kBAAkB;AAAA,MAC7B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC,CAAC,CAAC;AAAA,IACL,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,SAAS,2BAA2B,YAAY,OAAO;AAErD,MAAI,CAAC,MAAM,WAAW;AACpB,WAAO;AAAA,EACT;AACA,QAAM,KAAK,WAAW;AACtB,QAAM,UAAU,MAAM;AAEtB,MAAI,GAAG,aAAa,YAAY,CAAC,GAAG,UAAU;AAC5C,WAAO,YAAY,SAAS,YAAY;AAAA,EAC1C;AAEA,MAAI,GAAG,aAAa,KAAK;AACvB,WAAO,YAAY;AAAA,EACrB;AAEA,SAAO;AACT;AASA,IAAM,iBAAN,MAAM,wBAAuB,mBAAmB;AAAA,EAC9C,cAAc;AACZ,UAAM;AACN,SAAK,cAAc,OAAO,UAAU;AACpC,SAAK,WAAW,OAAO,OAAO;AAC9B,SAAK,UAAU,OAAO,MAAM;AAC5B,SAAK,qBAAqB,OAAO,iBAAiB;AAClD,SAAK,yBAAyB,OAAO,qBAAqB;AAC1D,SAAK,kBAAkB,OAAO,gBAAgB;AAAA,MAC5C,UAAU;AAAA,IACZ,CAAC;AAED,SAAK,cAAc,OAAO,UAAU;AAAA,MAClC,UAAU;AAAA,IACZ,CAAC;AAED,SAAK,WAAW,OAAO,UAAU;AAAA,MAC/B,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,SAAS;AACd,SAAK,sBAAsB;AAC3B,SAAK,4BAA4B;AACjC,SAAK,uBAAuB;AAC5B,SAAK,8BAA8B;AACnC,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA,EAEA,SAAS;AACP,SAAK,OAAO,IAAI,KAAK,MAAM,IAAI,KAAK,KAAK;AAAA,EAC3C;AAAA;AAAA,EAEA,OAAO;AACL,QAAI,CAAC,KAAK,OAAO,KAAK,KAAK,mBAAmB,MAAM;AAClD,WAAK,OAAO,KAAK;AACjB,WAAK,aAAa,KAAK,cAAc,KAAK,SAAS,OAAO,KAAK,kBAAkB,CAAC;AAClF,WAAK,WAAW,OAAO,KAAK,qBAAqB,CAAC;AAClD,WAAK,mBAAmB,aAAa;AACrC,WAAK,0BAA0B;AAAA,IACjC;AAAA,EACF;AAAA;AAAA,EAEA,QAAQ;AACN,QAAI,KAAK,OAAO,GAAG;AACjB,WAAK,OAAO,KAAK;AACjB,WAAK,WAAW,OAAO;AACvB,WAAK,mBAAmB,aAAa;AAAA,IACvC;AACA,SAAK,sBAAsB;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,OAAO;AACtB,UAAM,mBAAmB,KAAK,aAAa,gBAAgB;AAC3D,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AAAA,MACL,KAAK;AAEH,YAAI,CAAC,eAAe,KAAK,KAAK,CAAC,2BAA2B,KAAK,aAAa,KAAK,GAAG;AAClF,eAAK,OAAO;AACZ,eAAK,WAAW,eAAe,UAAU;AAAA,QAC3C;AACA;AAAA,MACF,KAAK;AACH,YAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,cAAI,KAAK,eAAe,oBAAoB,KAAK,iBAAiB,UAAU,OAAO;AACjF,kBAAM,eAAe;AACrB,iBAAK,KAAK;AACV,iBAAK,WAAW,eAAe,UAAU;AAAA,UAC3C;AAAA,QACF;AACA;AAAA,MACF,KAAK;AACH,YAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,cAAI,KAAK,eAAe,oBAAoB,KAAK,iBAAiB,UAAU,OAAO;AACjF,kBAAM,eAAe;AACrB,iBAAK,KAAK;AACV,iBAAK,WAAW,eAAe,UAAU;AAAA,UAC3C;AAAA,QACF;AACA;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,YAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,cAAI,CAAC,kBAAkB;AACrB,kBAAM,eAAe;AACrB,iBAAK,KAAK;AACV,kBAAM,YAAY,aAAa,KAAK,WAAW,eAAe,UAAU,IAAI,KAAK,WAAW,cAAc,UAAU;AAAA,UACtH;AAAA,QACF;AACA;AAAA,IACJ;AAAA,EACF;AAAA;AAAA,EAEA,eAAe;AACb,SAAK,OAAO;AACZ,SAAK,WAAW,eAAe,OAAO;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,UAAU;AACrB,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,UAAU,YAAY,QAAQ;AAAA,IACrC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB;AACvB,SAAK,QAAQ,kBAAkB,MAAM;AACnC,gBAAU,KAAK,YAAY,eAAe,YAAY,EAAE,KAAK,OAAO,MAAM;AACxE;AAAA;AAAA,UAEE,KAAK,uBAAuB,uBAAuB,WAAW,CAAC,KAAK,UAAU,QAAQ,KAAK,CAAC,KAAK,OAAO;AAAA;AAAA,MAE5G,CAAC,GAAG,UAAU,KAAK,SAAS,CAAC,EAAE,UAAU,MAAM;AAE7C,cAAM,cAAc,MAAM,KAAK,QAAQ,IAAI,MAAM;AAC/C,eAAK,sBAAsB;AAC3B,eAAK,KAAK;AAAA,QACZ,CAAC;AACD,YAAI,KAAK,UAAU;AACjB,eAAK,SAAS,OAAO,WAAW;AAAA,QAClC,OAAO;AACL,sBAAY;AAAA,QACd;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,wBAAwB;AACtB,QAAI,KAAK,aAAa;AAIpB,YAAM,kBAAkB,CAAC,KAAK,UAAU,eAAe,KAAK,WAAW,KAAK,KAAK,UAAU,KAAK,MAAM,KAAK;AAC3G,UAAI,iBAAiB;AACnB,aAAK,UAAU,SAAS;AAAA,MAC1B;AAAA,IACF,OAAO;AACL,WAAK,UAAU,SAAS;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB;AAClB,WAAO,IAAI,cAAc;AAAA,MACvB,kBAAkB,KAAK,4BAA4B;AAAA,MACnD,gBAAgB,KAAK,mBAAmB;AAAA,MACxC,WAAW,KAAK,mBAAmB;AAAA,IACrC,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,8BAA8B;AAC5B,WAAO,KAAK,SAAS,SAAS,EAAE,oBAAoB,KAAK,WAAW,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,cAAc,KAAK,qBAAqB,CAAC;AAAA,EAC1J;AAAA;AAAA,EAEA,uBAAuB;AACrB,WAAO,KAAK,iBAAiB,CAAC,KAAK,eAAe,KAAK,YAAY,gBAAgB,eAAe,oCAAoC;AAAA,EACxI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,UAAU,OAAO,KAAK,UAAU,KAAK,SAAS,CAAC,EAAE,UAAU,CAAC;AAAA,QAC/D;AAAA,MACF,MAAM;AACJ,YAAI,SAAS,KAAK,WAAW;AAC3B,eAAK,MAAM;AAAA,QACb;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,4BAA4B;AAC1B,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,qBAAqB,EAAE,KAAK,UAAU,KAAK,yBAAyB,CAAC,EAAE,UAAU,WAAS;AACxG,cAAM,SAAS,gBAAgB,KAAK;AACpC,cAAM,UAAU,KAAK,YAAY;AACjC,YAAI,WAAW,WAAW,CAAC,QAAQ,SAAS,MAAM,GAAG;AACnD,cAAI,CAAC,KAAK,yBAAyB,MAAM,GAAG;AAC1C,iBAAK,UAAU,SAAS;AAAA,UAC1B,OAAO;AACL,iBAAK,sBAAsB;AAAA,UAC7B;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,gCAAgC;AAC9B,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,UAAU,SAAS,KAAK,UAAU,KAAK,SAAS,CAAC,EAAE,UAAU,cAAY;AAC5E,YAAI,CAAC,UAAU;AACb,eAAK,UAAU,SAAS;AAAA,QAC1B;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,8BAA8B;AAC5B,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,UAAU,OAAO,UAAU,CAAC;AAAA,QAC/B;AAAA,MACF,MAAM;AACJ,YAAI,sBAAsB,CAAC,KAAK,UAAU,OAAO,GAAG;AAClD,eAAK,YAAY,cAAc,MAAM;AAAA,QACvC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,WAAW;AAGT,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,YAAY,cAAc,aAAa,QAAQ,QAAQ;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA,EAEA,WAAW;AACT,UAAM,UAAU,KAAK,YAAY;AACjC,QAAI,QAAQ,aAAa,YAAY,CAAC,QAAQ,aAAa,MAAM,GAAG;AAElE,cAAQ,aAAa,QAAQ,QAAQ;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAgB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,MACzC,WAAW,CAAC,GAAG,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,cAAc,SAAS,4BAA4B,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,SAAS,4CAA4C;AAC5E,mBAAO,IAAI,aAAa,IAAI;AAAA,UAC9B,CAAC,EAAE,YAAY,SAAS,6CAA6C;AACnE,mBAAO,IAAI,aAAa,KAAK;AAAA,UAC/B,CAAC,EAAE,WAAW,SAAS,0CAA0C,QAAQ;AACvE,mBAAO,IAAI,iBAAiB,MAAM;AAAA,UACpC,CAAC,EAAE,SAAS,SAAS,0CAA0C;AAC7D,mBAAO,IAAI,aAAa;AAAA,UAC1B,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,iBAAiB,IAAI,kBAAkB,SAAS,IAAI,EAAE,iBAAiB,IAAI,mBAAmB,OAAO,OAAO,IAAI,OAAO,CAAC;AAAA,QACzI;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,iBAAiB,CAAC,GAAG,qBAAqB,iBAAiB;AAAA,QAC3D,cAAc,CAAC,GAAG,mBAAmB,cAAc;AAAA,QACnD,UAAU,CAAC,GAAG,sBAAsB,UAAU;AAAA,MAChD;AAAA,MACA,SAAS;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV;AAAA,MACA,UAAU,CAAC,mBAAmB;AAAA,MAC9B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG,iCAAiC,CAAC,GAAM,0BAA0B;AAAA,IACvE,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,aAAa;AAAA,QACb,WAAW;AAAA,MACb;AAAA,MACA,QAAQ,CAAC;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,MACT,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,MACD,SAAS,CAAC,yBAAyB,uBAAuB;AAAA,MAC1D,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG,iCAAiC;AAAA,IACtC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAOH,IAAM,cAAN,MAAM,aAAY;AAAA;AAAA,EAEhB,IAAI,UAAU;AACZ,WAAO,KAAK,cAAc,mBAAmB;AAAA,EAC/C;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,OAAO,gBAAgB;AAAA,MACjC,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,cAAc,OAAO,UAAU;AACpC,SAAK,UAAU,OAAO,MAAM;AAC5B,SAAK,yBAAyB,OAAO,qBAAqB;AAE1D,SAAK,WAAW,OAAO,UAAU;AAAA,MAC/B,UAAU;AAAA,IACZ,CAAC;AAED,SAAK,aAAa,OAAO,UAAU;AAEnC,SAAK,cAAc,OAAO,UAAU;AAAA,MAClC,UAAU;AAAA,IACZ,CAAC;AAED,SAAK,eAAe,OAAO,gBAAgB;AAAA,MACzC,UAAU;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAED,SAAK,WAAW;AAKhB,SAAK,YAAY,IAAI,aAAa;AAKlC,SAAK,YAAY;AAEjB,SAAK,yBAAyB;AAE9B,SAAK,YAAY,IAAI,QAAQ;AAC7B,SAAK,iBAAiB;AACtB,SAAK,SAAS;AACd,QAAI,KAAK,kBAAkB,GAAG;AAC5B,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,KAAK;AACpB,SAAK,UAAU,SAAS;AAAA,EAC1B;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,YAAY,cAAc,MAAM;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,SAAS;AACf,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,mBACC;AAEL,QAAI,CAAC,KAAK,YAAY,CAAC,KAAK,SAAS;AACnC,WAAK,UAAU,KAAK;AACpB,UAAI,CAAC,UAAU;AACb,aAAK,WAAW,SAAS;AAAA,UACvB,oBAAoB;AAAA,QACtB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,CAAC,CAAC,KAAK,cAAc,OAAO;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,WAAO,KAAK,cAAc,QAAQ;AAAA,EACpC;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,KAAK,kBAAkB,KAAK,YAAY,cAAc,aAAa,KAAK,KAAK;AAAA,EACtF;AAAA;AAAA,EAEA,iBAAiB;AACf,QAAI,CAAC,KAAK,kBAAkB,GAAG;AAC7B,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,OAAO;AAClB,QAAI,KAAK,UAAU;AACjB;AAAA,IACF;AAEA,QAAI,CAAC,SAAS,CAAC,KAAK,WAAW,QAAQ,GAAG;AACxC,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,OAAO;AAChB,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AAAA,MACL,KAAK;AAEH,YAAI,CAAC,eAAe,KAAK,KAAK,CAAC,2BAA2B,KAAK,aAAa,KAAK,GAAG;AAClF,eAAK,QAAQ;AAAA,YACX,UAAU,MAAM,YAAY,SAAS,CAAC,KAAK;AAAA,UAC7C,CAAC;AAAA,QACH;AACA;AAAA,MACF,KAAK;AACH,YAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,cAAI,KAAK,eAAe,KAAK,kBAAkB,GAAG;AAChD,gBAAI,KAAK,MAAM,UAAU,OAAO;AAC9B,mBAAK,qBAAqB,KAAK;AAAA,YACjC,OAAO;AACL,mBAAK,kBAAkB,KAAK;AAAA,YAC9B;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF,KAAK;AACH,YAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,cAAI,KAAK,eAAe,KAAK,kBAAkB,GAAG;AAChD,gBAAI,KAAK,MAAM,UAAU,OAAO;AAC9B,mBAAK,kBAAkB,KAAK;AAAA,YAC9B,OAAO;AACL,mBAAK,qBAAqB,KAAK;AAAA,YACjC;AAAA,UACF;AAAA,QACF;AACA;AAAA,IACJ;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB;AAClB,WAAO,CAAC,KAAK;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,OAAO;AACvB,UAAM,aAAa,KAAK;AACxB,QAAI,KAAK,WAAW,cAAc,KAAK,KAAK,WAAW,OAAO,IAAI,GAAG;AACnE,YAAM,eAAe;AACrB,WAAK,WAAW,MAAM,YAAY;AAAA,QAChC,kBAAkB,KAAK,WAAW,sBAAsB,MAAM,eAAe,UAAU,eAAe,UAAU;AAAA,QAChH,oBAAoB;AAAA,MACtB,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,OAAO;AAC1B,QAAI,CAAC,KAAK,WAAW,KAAK,WAAW,sBAAsB,MAAM,cAAc;AAC7E,YAAM,eAAe;AACrB,WAAK,WAAW,SAAS;AAAA,QACvB,kBAAkB,UAAU;AAAA,QAC5B,oBAAoB;AAAA,MACtB,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,QAAI,CAAC,KAAK,kBAAkB,GAAG;AAC7B,YAAM,oBAAoB,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,WAAW,eAAe,KAAK,WAAW,CAAC;AACvG,WAAK,QAAQ,kBAAkB,MAAM,UAAU,KAAK,YAAY,eAAe,YAAY,EAAE,KAAK,OAAO,MAAM;AAC7G;AAAA;AAAA,UAEE,KAAK,uBAAuB,uBAAuB,WAAW,CAAC,KAAK,WAAW,QAAQ,KAAK,CAAC,KAAK;AAAA;AAAA,MAEtG,CAAC,GAAG,UAAU,KAAK,SAAS,CAAC,EAAE,UAAU,MAAM;AAC7C,YAAI,KAAK,UAAU;AACjB,eAAK,SAAS,OAAO,iBAAiB;AAAA,QACxC,OAAO;AACL,4BAAkB;AAAA,QACpB;AAAA,MACF,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAClB,WAAO,KAAK,aAAa,gBAAgB;AAAA,EAC3C;AAAA;AAAA,EAEA,WAAW;AACT,UAAM,UAAU,KAAK,YAAY;AACjC,QAAI,QAAQ,aAAa,YAAY,CAAC,QAAQ,aAAa,MAAM,GAAG;AAElE,cAAQ,aAAa,QAAQ,QAAQ;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,mBAAmB;AAC1D,aAAO,KAAK,qBAAqB,cAAa;AAAA,IAChD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,MACnC,WAAW,CAAC,QAAQ,YAAY,GAAG,eAAe;AAAA,MAClD,UAAU;AAAA,MACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,QAAQ,SAAS,sCAAsC;AACnE,mBAAO,IAAI,eAAe;AAAA,UAC5B,CAAC,EAAE,SAAS,SAAS,uCAAuC;AAC1D,mBAAO,IAAI,aAAa;AAAA,UAC1B,CAAC,EAAE,SAAS,SAAS,uCAAuC;AAC1D,mBAAO,IAAI,QAAQ;AAAA,UACrB,CAAC,EAAE,WAAW,SAAS,uCAAuC,QAAQ;AACpE,mBAAO,IAAI,WAAW,MAAM;AAAA,UAC9B,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,YAAY,IAAI,SAAS;AAC3C,UAAG,YAAY,iBAAiB,IAAI,YAAY,IAAI;AAAA,QACtD;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU,CAAC,GAAG,uBAAuB,YAAY,gBAAgB;AAAA,QACjE,gBAAgB,CAAC,GAAG,6BAA6B,gBAAgB;AAAA,MACnE;AAAA,MACA,SAAS;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,UAAU,CAAC,aAAa;AAAA,MACxB,YAAY;AAAA,MACZ,UAAU,CAAI,wBAAwB;AAAA,IACxC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,IAC/B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,sBAAN,MAA0B;AAAA,EACxB,YACA,QAAQ;AACN,SAAK,SAAS;AAEd,SAAK,UAAU,KAAK,uBAAuB;AAE3C,SAAK,SAAS,KAAK,qBAAqB;AAExC,SAAK,aAAa,IAAI,QAAQ;AAC9B,SAAK,QAAQ,UAAU,aAAW,KAAK,gBAAgB,OAAO;AAC9D,SAAK,OAAO,UAAU,MAAM;AAC1B,WAAK,kBAAkB,KAAK;AAC5B,WAAK,gBAAgB;AAAA,IACvB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB;AACvB,WAAO,MAAM,MAAM,KAAK,OAAO,QAAQ,KAAK,UAAU,KAAK,MAAM,GAAG,SAAS,UAAQ,KAAK,IAAI,aAAW,UAAU,QAAQ,YAAY,eAAe,YAAY,EAAE,KAAK,MAAM,OAAO,GAAG,UAAU,KAAK,OAAO,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;AAAA,EACzO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,WAAO,MAAM,MAAM,KAAK,OAAO,QAAQ,KAAK,UAAU,KAAK,MAAM,GAAG,SAAS,UAAQ,KAAK,IAAI,aAAW,UAAU,QAAQ,YAAY,eAAe,UAAU,EAAE,KAAK,MAAM,OAAO,GAAG,UAAU,KAAK,OAAO,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;AAAA,EACvO;AACF;AAGA,IAAI,WAAW;AAKf,IAAM,cAAN,MAAM,qBAAoB,aAAa;AAAA,EACrC,cAAc;AACZ,UAAM,GAAG,SAAS;AAElB,SAAK,gBAAgB,OAAO,UAAU,EAAE;AAExC,SAAK,SAAS,OAAO,MAAM;AAE3B,SAAK,YAAY,OAAO,UAAU;AAElC,SAAK,UAAU,OAAO,UAAU;AAAA,MAC9B,UAAU;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAED,SAAK,MAAM,OAAO,gBAAgB;AAAA,MAChC,UAAU;AAAA,IACZ,CAAC;AAED,SAAK,KAAK,YAAY,UAAU;AAEhC,SAAK,cAAc;AAKnB,SAAK,WAAW;AAEhB,SAAK,YAAY,IAAI,QAAQ;AAE7B,SAAK,qBAAqB,OAAO,KAAK;AACtC,SAAK,kBAAkB,SAAS,MAAM;AACpC,YAAM,mBAAmB,KAAK,mBAAmB,IAAI,KAAK;AAC1D,aAAO,KAAK,WAAW,mBAAmB;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,UAAU,KAAK,IAAI;AAAA,IAC1B;AACA,SAAK,eAAe;AACpB,SAAK,8BAA8B;AACnC,SAAK,qBAAqB;AAC1B,SAAK,4BAA4B;AACjC,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,cAAc;AACZ,SAAK,YAAY,QAAQ;AACzB,SAAK,UAAU,KAAK;AACpB,SAAK,UAAU,SAAS;AACxB,SAAK,gBAAgB,QAAQ;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,cAAc,WAAW;AACtC,SAAK,WAAW,eAAe,WAAW;AAC1C,SAAK,WAAW,mBAAmB;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,cAAc,WAAW;AACrC,SAAK,WAAW,eAAe,WAAW;AAC1C,SAAK,WAAW,kBAAkB;AAAA,EACpC;AAAA;AAAA,EAEA,eAAe;AACb,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,MAAM,SAAS;AAC3B,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,mBACC;AAEL,UAAM,aAAa,KAAK;AACxB,UAAM,UAAU,KAAK;AACrB,QAAI,SAAS,SAAS,eAAe,GAAG,QAAQ,GAAG;AACjD,eAAS,eAAe,GAAG,MAAM;AAGjC,UAAI,oBAAoB;AACtB,YAAI,SAAS;AACX,qBAAW,cAAc,OAAO;AAAA,QAClC,OAAO;AACL,qBAAW,mBAAmB;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,aAAa,IAAI,gBAAgB,KAAK,KAAK,EAAE,SAAS,EAAE,cAAc,EAAE,eAAe;AAC5F,QAAI,KAAK,gBAAgB,cAAc;AACrC,WAAK,WAAW,0BAA0B,KAAK,KAAK,SAAS,KAAK;AAAA,IACpE,OAAO;AACL,WAAK,WAAW,wBAAwB;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,UAAM,gBAAgB,MAAM,KAAK,MAAM,SAAS,KAAK,SAAS;AAC9D,SAAK,MAAM,QAAQ,KAAK,UAAU,KAAK,KAAK,GAAG,SAAS,UAAQ,KAAK,OAAO,UAAQ,KAAK,OAAO,EAAE,IAAI,UAAQ,KAAK,eAAe,EAAE,OAAO,KAAK,MAAM,IAAI,GAAG,UAAU,aAAa,CAAC,CAAC,CAAC,GAAG,SAAS,GAAG,UAAU,UAAQ;AACtN,WAAK,cAAc;AACnB,aAAO,KAAK,eAAe,EAAE;AAAA,IAC/B,CAAC,GAAG,UAAU,KAAK,SAAS,CAAC,EAAE,UAAU,MAAM,KAAK,cAAc,MAAS;AAAA,EAC7E;AAAA;AAAA,EAEA,8BAA8B;AAC5B,SAAK,UAAU,OAAO,KAAK,UAAU,KAAK,SAAS,CAAC,EAAE,UAAU,CAAC;AAAA,MAC/D;AAAA,MACA;AAAA,IACF,MAAM,KAAK,cAAc,MAAM;AAAA,MAC7B;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA,EAEA,gCAAgC;AAC9B,QAAI,KAAK,UAAU;AACjB,WAAK,UAAU,SAAS,KAAK,UAAU,KAAK,SAAS,CAAC,EAAE,UAAU,cAAY;AAC5E,aAAK,mBAAmB,IAAI,QAAQ;AAAA,MACtC,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,QAAI,KAAK,SAAS;AAChB,WAAK,OAAO,kBAAkB,MAAM;AAClC,aAAK,iBAAiB,IAAI,oBAAoB,KAAK,KAAK;AAAA,MAC1D,CAAC;AACD,WAAK,QAAQ,WAAW,MAAM,KAAK,cAAc;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAuB,uBAAM;AAChC,UAAI;AACJ,aAAO,SAAS,oBAAoB,mBAAmB;AACrD,gBAAQ,6BAA6B,2BAA8B,sBAAsB,YAAW,IAAI,qBAAqB,YAAW;AAAA,MAC1I;AAAA,IACF,GAAG;AAAA,EACL;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,gBAAgB,SAAS,2BAA2B,IAAI,KAAK,UAAU;AACrE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,aAAa,CAAC;AAAA,QAC5C;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ;AAAA,QAC3D;AAAA,MACF;AAAA,MACA,WAAW,CAAC,QAAQ,MAAM;AAAA,MAC1B,UAAU;AAAA,MACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,uCAAuC;AACrE,mBAAO,IAAI,eAAe;AAAA,UAC5B,CAAC,EAAE,WAAW,SAAS,yCAAyC;AAC9D,mBAAO,IAAI,UAAU,YAAY,IAAI;AAAA,UACvC,CAAC,EAAE,YAAY,SAAS,0CAA0C;AAChE,mBAAO,IAAI,UAAU,YAAY,KAAK;AAAA,UACxC,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,YAAY,IAAI,aAAa,CAAC,EAAE,MAAM,IAAI,EAAE;AAC9D,UAAG,YAAY,oBAAoB,IAAI,WAAW,EAAE,0BAA0B,IAAI,UAAU,EAAE;AAAA,QAChG;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,IAAI;AAAA,MACN;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA0B;AAAA,IAC1C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS;AAAA;AAAA,QAET,cAAc;AAAA,QACd,QAAQ;AAAA,QACR,2BAA2B;AAAA,QAC3B,iCAAiC;AAAA,QACjC,WAAW;AAAA,QACX,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AASH,IAAM,UAAN,MAAM,iBAAgB,YAAY;AAAA,EAChC,cAAc;AACZ,UAAM;AACN,SAAK,iBAAiB,OAAO,cAAc;AAAA,MACzC,UAAU;AAAA,IACZ,CAAC;AAED,SAAK,SAAS,IAAI,aAAa;AAE/B,SAAK,cAAc;AAEnB,SAAK,WAAW,CAAC,KAAK;AACtB,SAAK,UAAU,UAAU,KAAK,MAAM;AACpC,SAAK,gBAAgB,kBAAkB,IAAI;AAAA,EAC7C;AAAA,EACA,qBAAqB;AACnB,UAAM,mBAAmB;AACzB,SAAK,6BAA6B;AAAA,EACpC;AAAA,EACA,cAAc;AACZ,UAAM,YAAY;AAClB,SAAK,OAAO,SAAS;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,OAAO;AACrB,UAAM,aAAa,KAAK;AACxB,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AAAA,MACL,KAAK;AACH,YAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,gBAAM,eAAe;AACrB,qBAAW,eAAe,UAAU;AACpC,qBAAW,UAAU,KAAK;AAAA,QAC5B;AACA;AAAA,MACF,KAAK;AACH,YAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,gBAAM,eAAe;AACrB,eAAK,UAAU,MAAM,MAAM;AAAA,YACzB,kBAAkB,UAAU;AAAA,YAC5B,oBAAoB;AAAA,UACtB,CAAC;AAAA,QACH;AACA;AAAA,MACF,KAAK;AACH,YAAI,CAAC,eAAe,OAAO,UAAU,WAAW,SAAS,GAAG;AAC1D,eAAK,UAAU,SAAS;AAAA,YACtB,oBAAoB;AAAA,UACtB,CAAC;AAAA,QACH;AACA;AAAA,MACF;AACE,mBAAW,UAAU,KAAK;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,WAAW;AAC1B,UAAM,aAAa,KAAK;AACxB,YAAQ,WAAW;AAAA,MACjB,KAAK,UAAU;AACb,mBAAW,eAAe,UAAU;AACpC,mBAAW,kBAAkB;AAC7B;AAAA,MACF,KAAK,UAAU;AACb,mBAAW,eAAe,UAAU;AACpC,mBAAW,sBAAsB;AACjC;AAAA,MACF,KAAK,UAAU;AACb,YAAI,WAAW,YAAY;AACzB,qBAAW,eAAe,UAAU;AACpC,qBAAW,cAAc,WAAW,UAAU;AAAA,QAChD;AACA;AAAA,IACJ;AAAA,EACF;AAAA;AAAA,EAEA,+BAA+B;AAC7B,SAAK,UAAU,QAAQ,KAAK,UAAU,KAAK,SAAS,CAAC,EAAE,UAAU,WAAS,KAAK,iBAAiB,KAAK,CAAC;AAAA,EACxG;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gBAAgB,mBAAmB;AACtD,aAAO,KAAK,qBAAqB,UAAS;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,MAC/B,WAAW,CAAC,QAAQ,QAAQ,GAAG,UAAU;AAAA,MACzC,UAAU;AAAA,MACV,cAAc,SAAS,qBAAqB,IAAI,KAAK;AACnD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,SAAS,mCAAmC,QAAQ;AAC3E,mBAAO,IAAI,gBAAgB,MAAM;AAAA,UACnC,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,mBAAmB,IAAI,QAAQ;AAAA,QAChD;AAAA,MACF;AAAA,MACA,SAAS;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,MACA,UAAU,CAAC,SAAS;AAAA,MACpB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG,yCAAyC,UAAU,CAAC,CAAC,GAAM,0BAA0B;AAAA,IAC1F,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,2BAA2B;AAAA,QAC3B,aAAa;AAAA,MACf;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG,yCAAyC,UAAU,CAAC;AAAA,IACzD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAQH,IAAM,aAAN,MAAM,oBAAmB,YAAY;AAAA,EACnC,cAAc;AACZ,UAAM,GAAG,SAAS;AAElB,SAAK,cAAc;AAEnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,qBAAqB;AACnB,UAAM,mBAAmB;AACzB,SAAK,6BAA6B;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,OAAO;AACrB,UAAM,aAAa,KAAK;AACxB,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,YAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,gBAAM,mBAAmB,MAAM,YAAY,cAAc,MAAM,YAAY;AAI3E,cAAI,kBAAkB;AACpB,kBAAM,eAAe;AACrB,kBAAM,aAAa,WAAW,YAAY,WAAW;AACrD,uBAAW,YAAY,eAAe,GAAG,MAAM;AAC/C,uBAAW,eAAe,UAAU;AACpC,uBAAW,UAAU,KAAK;AAC1B,gBAAI,YAAY;AACd,yBAAW,YAAY,eAAe,GAAG,KAAK;AAAA,YAChD;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF,KAAK;AACH,YAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,gBAAM,eAAe;AACrB,qBAAW,YAAY,eAAe,GAAG,MAAM;AAAA,QACjD;AACA;AAAA,MACF,KAAK;AACH,YAAI,CAAC,eAAe,OAAO,UAAU,WAAW,SAAS,GAAG;AAC1D,qBAAW,YAAY,eAAe,GAAG,MAAM;AAAA,QACjD;AACA;AAAA,MACF;AACE,mBAAW,UAAU,KAAK;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,WAAW;AACzB,UAAM,aAAa,KAAK;AACxB,YAAQ,WAAW;AAAA,MACjB,KAAK,UAAU;AACb,mBAAW,eAAe,UAAU;AACpC,mBAAW,kBAAkB;AAC7B,mBAAW,YAAY,eAAe,GAAG,KAAK;AAC9C;AAAA,MACF,KAAK,UAAU;AACb,mBAAW,eAAe,UAAU;AACpC,mBAAW,sBAAsB;AACjC,mBAAW,YAAY,eAAe,GAAG,KAAK;AAC9C;AAAA,MACF,KAAK,UAAU;AACb,YAAI,WAAW,YAAY;AACzB,qBAAW,eAAe,UAAU;AACpC,qBAAW,cAAc,WAAW,UAAU;AAAA,QAChD;AACA;AAAA,IACJ;AAAA,EACF;AAAA;AAAA,EAEA,+BAA+B;AAC7B,SAAK,WAAW,QAAQ,KAAK,UAAU,KAAK,SAAS,CAAC,EAAE,UAAU,WAAS,KAAK,gBAAgB,KAAK,CAAC;AAAA,EACxG;AAAA,EACA,OAAO;AACL,SAAK,OAAuB,uBAAM;AAChC,UAAI;AACJ,aAAO,SAAS,mBAAmB,mBAAmB;AACpD,gBAAQ,4BAA4B,0BAA6B,sBAAsB,WAAU,IAAI,qBAAqB,WAAU;AAAA,MACtI;AAAA,IACF,GAAG;AAAA,EACL;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,MAClC,WAAW,CAAC,QAAQ,WAAW,GAAG,cAAc;AAAA,MAChD,cAAc,SAAS,wBAAwB,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,SAAS,sCAAsC,QAAQ;AAC9E,mBAAO,IAAI,gBAAgB,MAAM;AAAA,UACnC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,UAAU,CAAC,YAAY;AAAA,MACvB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,YAAY,MAAM,UAAU,OAAO,YAAY;AAAA,MACjD,CAAC,CAAC,GAAM,0BAA0B;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,YAAY,MAAM,UAAU,OAAO,YAAY;AAAA,MACjD,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,wBAAN,MAAM,+BAA8B,YAAY;AAAA,EAC9C,cAAc;AACZ,UAAM,GAAG,SAAS;AAElB,SAAK,UAAU;AAEf,SAAK,yBAAyB;AAAA,EAChC;AAAA,EACA,OAAO;AACL,SAAK,OAAuB,uBAAM;AAChC,UAAI;AACJ,aAAO,SAAS,8BAA8B,mBAAmB;AAC/D,gBAAQ,uCAAuC,qCAAwC,sBAAsB,sBAAqB,IAAI,qBAAqB,sBAAqB;AAAA,MAClL;AAAA,IACF,GAAG;AAAA,EACL;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,UAAU;AAAA,MACV,cAAc,SAAS,mCAAmC,IAAI,KAAK;AACjE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,gBAAgB,CAAC,CAAC,IAAI,OAAO,EAAE,iBAAiB,IAAI,YAAY,IAAI;AAAA,QACrF;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,SAAS,CAAC,GAAG,sBAAsB,WAAW,gBAAgB;AAAA,MAChE;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA6B,0BAA0B;AAAA,IACvE,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,QACJ,uBAAuB;AAAA,QACvB,wBAAwB;AAAA,MAC1B;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAI,SAAS;AAMb,IAAM,mBAAN,MAAM,0BAAyB,sBAAsB;AAAA,EACnD,cAAc;AACZ,UAAM;AAEN,SAAK,uBAAuB,OAAO,yBAAyB;AAE5D,SAAK,MAAM,GAAG,QAAQ;AACtB,SAAK,4BAA4B;AAAA,EACnC;AAAA,EACA,cAAc;AACZ,UAAM,YAAY;AAClB,SAAK,0BAA0B;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,SAAS;AACf,UAAM,QAAQ,OAAO;AACrB,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,qBAAqB,OAAO,KAAK,KAAK,EAAE;AAAA,IAC/C;AAAA,EACF;AAAA;AAAA,EAEA,8BAA8B;AAC5B,SAAK,4BAA4B,KAAK,qBAAqB,OAAO,QAAM;AACtE,WAAK,UAAU,KAAK,QAAQ;AAAA,IAC9B,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,MACxC,WAAW,CAAC,QAAQ,eAAe;AAAA,MACnC,UAAU;AAAA,MACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,uBAAuB,IAAI;AAAA,QAC5C;AAAA,MACF;AAAA,MACA,UAAU,CAAC,kBAAkB;AAAA,MAC7B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,+BAA+B;AAAA,MACjC;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAMH,IAAM,sBAAN,MAAM,6BAA4B,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtD,QAAQ,SAAS;AACf,UAAM,QAAQ,OAAO;AACrB,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,UAAU,CAAC,KAAK;AAAA,IACvB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAuB,uBAAM;AAChC,UAAI;AACJ,aAAO,SAAS,4BAA4B,mBAAmB;AAC7D,gBAAQ,qCAAqC,mCAAsC,sBAAsB,oBAAmB,IAAI,qBAAqB,oBAAmB;AAAA,MAC1K;AAAA,IACF,GAAG;AAAA,EACL;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,uBAAuB,EAAE,CAAC;AAAA,MAC3C,WAAW,CAAC,QAAQ,kBAAkB;AAAA,MACtC,UAAU;AAAA,MACV,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,0BAA0B,IAAI;AAAA,QAC/C;AAAA,MACF;AAAA,MACA,UAAU,CAAC,qBAAqB;AAAA,MAChC,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,kCAAkC;AAAA,MACpC;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,yBAAyB,kCAAkC,IAAI,cAAY;AAG/E,QAAM,UAAU,SAAS,aAAa,UAAU,IAAI;AACpD,QAAM,UAAU,SAAS,aAAa,QAAQ,IAAI;AAClD,SAAO,iCACF,WADE;AAAA,IAEL;AAAA,IACA;AAAA,EACF;AACF,CAAC;AAED,IAAM,qBAAN,MAAM,oBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,OAAO,SAAS;AACd,QAAI,oBAAmB,4BAA4B,SAAS;AAC1D,0BAAmB,yBAAyB,MAAM;AAClD,0BAAmB,0BAA0B;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAoB;AAAA,IACvD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,oBAAmB;AAAA,MAC5B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,wBAAN,MAAM,+BAA8B,mBAAmB;AAAA,EACrD,cAAc;AACZ,UAAM;AAEN,SAAK,WAAW,OAAO,OAAO;AAE9B,SAAK,kBAAkB,OAAO,gBAAgB;AAAA,MAC5C,UAAU;AAAA,IACZ,CAAC;AAED,SAAK,sBAAsB,OAAO,kBAAkB;AACpD,SAAK,qBAAqB,OAAO,iBAAiB;AAElD,SAAK,WAAW;AAChB,SAAK,2BAA2B;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,aAAa;AAChB,SAAK,MAAM,MAAM,WAAW;AAC5B,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,UAAU,SAAS;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,OAAO;AACxB,QAAI,CAAC,KAAK,UAAU;AAElB,YAAM,eAAe;AAIrB,YAAM,gBAAgB;AACtB,WAAK,oBAAoB,OAAO,IAAI;AACpC,WAAK,MAAM,OAAO;AAAA,QAChB,GAAG,MAAM;AAAA,QACT,GAAG,MAAM;AAAA,MACX,CAAC;AAED,UAAI,MAAM,WAAW,GAAG;AACtB,aAAK,WAAW,eAAe,OAAO;AAAA,MACxC,WAAW,MAAM,WAAW,GAAG;AAC7B,aAAK,WAAW,eAAe,UAAU;AAAA,MAC3C,OAAO;AACL,aAAK,WAAW,eAAe,SAAS;AAAA,MAC1C;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,aAAa;AAC7B,WAAO,IAAI,cAAc;AAAA,MACvB,kBAAkB,KAAK,4BAA4B,WAAW;AAAA,MAC9D,gBAAgB,KAAK,mBAAmB;AAAA,MACxC,WAAW,KAAK,mBAAmB;AAAA,IACrC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,4BAA4B,aAAa;AACvC,WAAO,KAAK,SAAS,SAAS,EAAE,oBAAoB,WAAW,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,cAAc,KAAK,gBAAgB,sBAAsB;AAAA,EACrK;AAAA;AAAA,EAEA,6BAA6B;AAC3B,SAAK,UAAU,OAAO,KAAK,UAAU,KAAK,SAAS,CAAC,EAAE,UAAU,CAAC;AAAA,MAC/D;AAAA,IACF,MAAM;AACJ,UAAI,SAAS,KAAK,aAAa,KAAK,OAAO,GAAG;AAC5C,aAAK,OAAO,KAAK;AACjB,aAAK,WAAW,OAAO;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,0BAA0B,WAAW;AACnC,QAAI,KAAK,YAAY;AACnB,UAAI,gBAAgB,KAAK,WAAW,qBAAqB;AACzD,UAAI,WAAW;AACb,cAAM,CAAC,WAAW,YAAY,IAAI,UAAU,eAAe,CAAC;AAAA,UAC1D;AAAA,QACF,MAAM,SAAS,UAAU;AACzB,wBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOhB,aAAa,KAAK,UAAU,CAAC,OAAO,UAAU,UAAU,WAAW,UAAU,KAAK,MAAM,OAAO,CAAC;AAAA;AAAA;AAAA,UAGhG,UAAU,KAAK,KAAK,CAAC,CAAC;AAAA,QAAC;AAAA,MACzB;AACA,oBAAc,KAAK,UAAU,KAAK,yBAAyB,CAAC,EAAE,UAAU,WAAS;AAC/E,YAAI,CAAC,KAAK,yBAAyB,gBAAgB,KAAK,CAAC,GAAG;AAC1D,eAAK,UAAU,SAAS;AAAA,QAC1B;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,WAAW,aAAa;AAC5B,QAAI,KAAK,UAAU;AACjB;AAAA,IACF;AACA,QAAI,KAAK,OAAO,GAAG;AAGjB,WAAK,UAAU,eAAe,KAAK,SAAS;AAC5C,WAAK,WAAW,UAAU,EAAE,iBAAiB,UAAU,WAAW;AAClE,WAAK,WAAW,eAAe;AAAA,IACjC,OAAO;AACL,WAAK,OAAO,KAAK;AACjB,UAAI,KAAK,YAAY;AACnB,aAAK,WAAW,UAAU,EAAE,iBAAiB,UAAU,WAAW;AAClE,aAAK,WAAW,eAAe;AAAA,MACjC,OAAO;AACL,aAAK,aAAa,KAAK,SAAS,OAAO,KAAK,kBAAkB,WAAW,CAAC;AAAA,MAC5E;AACA,WAAK,WAAW,OAAO,KAAK,qBAAqB,CAAC;AAClD,WAAK,0BAA0B,SAAS;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,mBAAmB;AACpE,aAAO,KAAK,qBAAqB,wBAAuB;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,4BAA4B,EAAE,CAAC;AAAA,MAChD,UAAU;AAAA,MACV,cAAc,SAAS,mCAAmC,IAAI,KAAK;AACjE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,eAAe,SAAS,qDAAqD,QAAQ;AACjG,mBAAO,IAAI,mBAAmB,MAAM;AAAA,UACtC,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,0BAA0B,IAAI;AAAA,QAC/C;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,iBAAiB,CAAC,GAAG,4BAA4B,iBAAiB;AAAA,QAClE,cAAc,CAAC,GAAG,0BAA0B,cAAc;AAAA,QAC1D,UAAU,CAAC,GAAG,6BAA6B,UAAU;AAAA,QACrD,UAAU,CAAC,GAAG,0BAA0B,YAAY,gBAAgB;AAAA,MACtE;AAAA,MACA,SAAS;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV;AAAA,MACA,UAAU,CAAC,0BAA0B;AAAA,MACrC,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC,CAAC,GAAM,0BAA6B,0BAA0B;AAAA,IACjE,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,iCAAiC;AAAA,QACjC,iBAAiB;AAAA,MACnB;AAAA,MACA,QAAQ,CAAC;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,MACT,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,MACD,SAAS,CAAC,gCAAgC,8BAA8B;AAAA,MACxE,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAkB,CAAC,YAAY,SAAS,aAAa,kBAAkB,qBAAqB,gBAAgB,cAAc,uBAAuB,gBAAgB;AAEvK,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAe;AAAA,IAClD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,eAAe,YAAY,SAAS,aAAa,kBAAkB,qBAAqB,gBAAgB,cAAc,uBAAuB,gBAAgB;AAAA,MACvK,SAAS,CAAC,YAAY,SAAS,aAAa,kBAAkB,qBAAqB,gBAAgB,cAAc,uBAAuB,gBAAgB;AAAA,IAC1J,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,aAAa;AAAA,IACzB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,eAAe,GAAG,eAAe;AAAA,MAC3C,SAAS;AAAA,IACX,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["FocusNext"]}