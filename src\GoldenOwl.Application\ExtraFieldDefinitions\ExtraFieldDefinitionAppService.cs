﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoldenOwl.ExtraFieldDefinitions.DTO;
using GoldenOwl.ExtraFields.ExtraFieldDefinitions;
using GoldenOwl.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace GoldenOwl.ExtraFieldDefinitions;

[Authorize(GoldenOwlPermissions.ExtraFieldDefinitionManagement)]
public class ExtraFieldDefinitionAppService(
    IRepository<ExtraFieldDefinition, Guid> extraFieldDefinitionRepository,
    IExtraFieldDefinitionManager fieldDefinitionManager)
    : GoldenOwlAppService, IExtraFieldDefinitionAppService
{
    public async Task<PagedResultDto<ExtraFieldDefinitionDto>> GetListAsync(PagedResultRequestDto input,
        EntityType? entityType = null)
    {
        var queryable = await extraFieldDefinitionRepository.GetQueryableAsync();

        queryable = queryable.WhereIf(entityType.HasValue, x => x.EntityType == entityType);
        var totalCount = await
            AsyncExecuter.CountAsync(queryable);
        var extraFieldDefinitions = await AsyncExecuter.ToListAsync(
            queryable
                .OrderByDescending(x => x.CreationTime)
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
        );
        var data =
            ObjectMapper.Map<List<ExtraFieldDefinition>, List<ExtraFieldDefinitionDto>>
                (extraFieldDefinitions);
        return new PagedResultDto<ExtraFieldDefinitionDto>(totalCount, data);
    }


    public async Task<Guid> CreateAsync(ExtraFieldDefinitionDto definitionDto)
    {
        return await fieldDefinitionManager.CreateAsync
        (
            definitionDto.FieldName,
            definitionDto.EntityType,
            definitionDto.FieldType,
            definitionDto.DefaultValue,
            definitionDto.SelectListValues,
            definitionDto.Code
        );
    }

    public async Task UpdateAsync(ExtraFieldDefinitionDto definitionDto)
    {
        await fieldDefinitionManager.UpdateAsync
        (
            definitionDto.Id,
            definitionDto.FieldName,
            definitionDto.EntityType,
            definitionDto.FieldType,
            definitionDto.DefaultValue,
            definitionDto.SelectListValues,
            definitionDto.Code
        );
    }

    public async Task DeleteAsync(Guid id)
    {
        await extraFieldDefinitionRepository.HardDeleteAsync(x => x.Id == id);
    }

    public async Task<List<ExtraFieldDefinitionDto>> GetListByEntityTypeAsync(EntityType entityType)
    {
        var definitions = await extraFieldDefinitionRepository.GetListAsync(x => x.EntityType == entityType);
        return ObjectMapper.Map<List<ExtraFieldDefinition>, List<ExtraFieldDefinitionDto>>(definitions);
    }

    public async Task<bool> HasAssociatedValuesAsync(Guid id)
    {
        return await fieldDefinitionManager.HasAssociatedValuesAsync(id);
    }

    public async Task<bool> IsKeyUniqueAsync(string key, Guid? id = null)
    {
        var existingDefinition = await extraFieldDefinitionRepository.FirstOrDefaultAsync(x => x.Code == key);
        return existingDefinition == null || existingDefinition.Id == id;
    }
}