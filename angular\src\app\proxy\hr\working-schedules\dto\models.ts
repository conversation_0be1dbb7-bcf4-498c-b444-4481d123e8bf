import type { EntityDto } from '@abp/ng.core';

export interface CreateOrUpdateWorkingScheduleDto {
  name?: string;
  workingHours: WorkingHoursDto[];
}

export interface WorkingHoursDto extends EntityDto<number> {
  day: any;
  from?: string;
  to?: string;
  expectedAttendance?: string;
}

export interface WorkingScheduleDto extends EntityDto<string> {
  name?: string;
  workingHours: WorkingHoursDto[];
  averageHourNumber: number;
}

export interface WorkingScheduleNameDto {
  name?: string;
}
