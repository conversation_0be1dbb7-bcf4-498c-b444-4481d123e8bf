
export interface AttachmentRequestDto {
  fileName?: string;
  file: number[];
  attachmentTypeId?: string;
  expiryDate?: string;
}

export interface AttachmentResponseDto {
  id?: string;
  bindEntityId?: string;
  attachmentTypeId?: string;
  fileName?: string;
  fileSize: number;
  creationTime?: string;
  expiryDate?: string;
}

export interface SaveTempDto {
  attachmentId?: string;
  bindEntityId?: string;
}
