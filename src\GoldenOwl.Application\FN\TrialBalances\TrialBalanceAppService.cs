using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoldenOwl.FN.Entries;
using GoldenOwl.FN.FinancialPeriods;
using GoldenOwl.FN.TrialBalances.DTO;
using GoldenOwl.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;

namespace GoldenOwl.FN.TrialBalances;

public class TrialBalanceAppService : GoldenOwlAppService, ITrialBalanceAppService
{
    private readonly IEntryRepository _entryRepository;
    private readonly IFinancialPeriodManager _financialPeriodManager;

    public TrialBalanceAppService(IEntryRepository entryRepository, IFinancialPeriodManager financialPeriodManager)
    {
        _entryRepository = entryRepository;
        _financialPeriodManager = financialPeriodManager;
    }

    [Authorize(GoldenOwlPermissions.EntryIndex)]
    public virtual async Task<TrialBalanceDto> PostGetTrialBalanceReport(GetTrialBalanceRequestDto input)
    {
        if (input.CurrencyCode != GoldenOwlConsts.DefaultCurrencyCode)
        {
            throw new UserFriendlyException(L[GoldenOwlApplicationErrorCodes.CurrencyCodeMustBeEqualToMainCurrency]);
        }

        FinancialPeriod activeFinancialPeriod = await _financialPeriodManager.GetActiveFinancialPeriodAsync();
        if (input.FromDate < activeFinancialPeriod.StartDate || input.ToDate > activeFinancialPeriod.EndDate)
            throw new UserFriendlyException(L[GoldenOwlApplicationErrorCodes.DatesMustBeInsideActiveFinancialPeriod]);

        var query = await _entryRepository.WithDetailsEntryAsync();
        query = query.Where(e => e.EntryDate >= input.FromDate && e.EntryDate <= input.ToDate && e.IsPosted);

        if (input.AccountId != null && input.AccountId != Guid.Empty)
        {
            query = query.Where(e => e.EntryItems.Any(ei => ei.AccountId == input.AccountId));
        }

        List<TrialBalanceItemDto> trialBalanceItems = query
            .SelectMany(e => e.EntryItems)
            .GroupBy(item => new { item.AccountId, item.Account.Name, item.Account.Code, item.CurrencyCode })
            .Select(group => new TrialBalanceItemDto
            {
                AccountId = group.Key.AccountId,
                AccountName = group.Key.Name,
                AccountCode = group.Key.Code,
                CurrencyCode = group.Key.CurrencyCode,
                TotalDebit = group.Sum(x => x.Debit),
                TotalCredit = group.Sum(x => x.Credit),
                Balance = group.Sum(x => x.Debit) - group.Sum(x => x.Credit)
            })
            .OrderBy(e => e.AccountCode)
            .ToList();

        TrialBalanceDto trialBalanceDto = new TrialBalanceDto()
        {
            BalanceItems = trialBalanceItems,
            CurrencyCode = input.CurrencyCode,
            FromDate = input.FromDate,
            ToDate = input.ToDate,
            TotalCreditBalance = trialBalanceItems.Sum(x => x.TotalCredit),
            TotalDebitBalance = trialBalanceItems.Sum(x => x.TotalDebit)
        };

        return trialBalanceDto;
    }
}