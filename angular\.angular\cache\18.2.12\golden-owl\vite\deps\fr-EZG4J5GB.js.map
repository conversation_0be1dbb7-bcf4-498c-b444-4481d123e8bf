{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/fr.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    v = val.toString().replace(/^[^.]*\\.?/, '').length,\n    e = parseInt(val.toString().replace(/^[^e]*(e([-+]?\\d+))?/, '$2')) || 0;\n  if (i === 0 || i === 1) return 1;\n  if (e === 0 && !(i === 0) && i % 1000000 === 0 && v === 0 || !(e >= 0 && e <= 5)) return 4;\n  return 5;\n}\nexport default [\"fr\", [[\"AM\", \"PM\"], u, u], u, [[\"D\", \"L\", \"M\", \"M\", \"J\", \"V\", \"S\"], [\"dim.\", \"lun.\", \"mar.\", \"mer.\", \"jeu.\", \"ven.\", \"sam.\"], [\"dimanche\", \"lundi\", \"mardi\", \"mercredi\", \"jeudi\", \"vendredi\", \"samedi\"], [\"di\", \"lu\", \"ma\", \"me\", \"je\", \"ve\", \"sa\"]], u, [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"janv.\", \"févr.\", \"mars\", \"avr.\", \"mai\", \"juin\", \"juil.\", \"août\", \"sept.\", \"oct.\", \"nov.\", \"déc.\"], [\"janvier\", \"février\", \"mars\", \"avril\", \"mai\", \"juin\", \"juillet\", \"août\", \"septembre\", \"octobre\", \"novembre\", \"décembre\"]], u, [[\"av. J.-C.\", \"ap. J.-C.\"], u, [\"avant Jésus-Christ\", \"après Jésus-Christ\"]], 1, [6, 0], [\"dd/MM/y\", \"d MMM y\", \"d MMMM y\", \"EEEE d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", \"{1}, {0}\", \"{1} 'à' {0}\", u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"euro\", {\n  \"ARS\": [\"$AR\", \"$\"],\n  \"AUD\": [\"$AU\", \"$\"],\n  \"BEF\": [\"FB\"],\n  \"BMD\": [\"$BM\", \"$\"],\n  \"BND\": [\"$BN\", \"$\"],\n  \"BYN\": [u, \"р.\"],\n  \"BZD\": [\"$BZ\", \"$\"],\n  \"CAD\": [\"$CA\", \"$\"],\n  \"CLP\": [\"$CL\", \"$\"],\n  \"CNY\": [u, \"¥\"],\n  \"COP\": [\"$CO\", \"$\"],\n  \"CYP\": [\"£CY\"],\n  \"EGP\": [u, \"£E\"],\n  \"FJD\": [\"$FJ\", \"$\"],\n  \"FKP\": [\"£FK\", \"£\"],\n  \"FRF\": [\"F\"],\n  \"GBP\": [\"£GB\", \"£\"],\n  \"GIP\": [\"£GI\", \"£\"],\n  \"HKD\": [u, \"$\"],\n  \"IEP\": [\"£IE\"],\n  \"ILP\": [\"£IL\"],\n  \"ITL\": [\"₤IT\"],\n  \"JPY\": [u, \"¥\"],\n  \"KMF\": [u, \"FC\"],\n  \"LBP\": [\"£LB\", \"£L\"],\n  \"MTP\": [\"£MT\"],\n  \"MXN\": [\"$MX\", \"$\"],\n  \"NAD\": [\"$NA\", \"$\"],\n  \"NIO\": [u, \"$C\"],\n  \"NZD\": [\"$NZ\", \"$\"],\n  \"PHP\": [u, \"₱\"],\n  \"RHD\": [\"$RH\"],\n  \"RON\": [u, \"L\"],\n  \"RWF\": [u, \"FR\"],\n  \"SBD\": [\"$SB\", \"$\"],\n  \"SGD\": [\"$SG\", \"$\"],\n  \"SRD\": [\"$SR\", \"$\"],\n  \"TOP\": [u, \"$T\"],\n  \"TTD\": [\"$TT\", \"$\"],\n  \"TWD\": [u, \"NT$\"],\n  \"USD\": [\"$US\", \"$\"],\n  \"UYU\": [\"$UY\", \"$\"],\n  \"WST\": [\"$WS\"],\n  \"XCD\": [u, \"$\"],\n  \"XPF\": [\"FCFP\"],\n  \"ZMW\": [u, \"Kw\"]\n}, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACnB,QAAM,IAAI,KACR,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,GAC5B,IAAI,IAAI,SAAS,EAAE,QAAQ,aAAa,EAAE,EAAE,QAC5C,IAAI,SAAS,IAAI,SAAS,EAAE,QAAQ,wBAAwB,IAAI,CAAC,KAAK;AACxE,MAAI,MAAM,KAAK,MAAM,EAAG,QAAO;AAC/B,MAAI,MAAM,KAAK,EAAE,MAAM,MAAM,IAAI,QAAY,KAAK,MAAM,KAAK,EAAE,KAAK,KAAK,KAAK,GAAI,QAAO;AACzF,SAAO;AACT;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,GAAG,CAAC,YAAY,SAAS,SAAS,YAAY,SAAS,YAAY,QAAQ,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,SAAS,SAAS,QAAQ,QAAQ,OAAO,QAAQ,SAAS,QAAQ,SAAS,QAAQ,QAAQ,MAAM,GAAG,CAAC,WAAW,WAAW,QAAQ,SAAS,OAAO,QAAQ,WAAW,QAAQ,aAAa,WAAW,YAAY,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,aAAa,WAAW,GAAG,GAAG,CAAC,sBAAsB,oBAAoB,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,WAAW,YAAY,eAAe,GAAG,CAAC,SAAS,YAAY,cAAc,eAAe,GAAG,CAAC,WAAW,YAAY,eAAe,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,WAAW,cAAc,KAAK,GAAG,OAAO,KAAK,QAAQ;AAAA,EAC95B,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,IAAI;AAAA,EACZ,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,GAAG;AAAA,EACX,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,OAAO,IAAI;AAAA,EACnB,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,MAAM;AAAA,EACd,OAAO,CAAC,GAAG,IAAI;AACjB,GAAG,OAAO,MAAM;", "names": []}