import { Component, inject } from '@angular/core';
import {
  fields,
  LanguagePipe,
  requiredValidator,
  takeOptions,
  TtwrFormComponent
} from '@ttwr-framework/ngx-main-visuals';
import { workingHour } from '../../working-schedules.model';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { WorkingHoursDto } from '@proxy/hr/working-schedules/dto';
import { of } from 'rxjs';
import { TimePeriodCustomInputComponent, timeToIso } from '@shared';
import { DayOfWeek } from '../../day-of-week.enum';

@Component({
  selector: 'app-working-hour-form-dialog',
  standalone: true,
  imports: [
    TtwrFormComponent,
    MatDialogContent,
    MatDialogTitle,
    LanguagePipe
  ],
  template: `
    <h1 mat-dialog-title>
      {{ '::GoldenOwl:WorkingHours' | i18n }}
    </h1>
    <mat-dialog-content>
      <ttwr-form [config]="config"/>
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class WorkingHourFormDialogComponent {
  private data = inject<WorkingHoursDto | undefined>(MAT_DIALOG_DATA);
  private ref = inject(MatDialogRef);

  protected config = workingHour.exclude({
    day: true,
  }).extend({
    day: fields.select(this.data ? 'single' : 'multiple', takeOptions(DayOfWeek))
  }).select({
    // reordering
    day: true,
    from: true,
    to: true,
    expectedAttendance: true,
  }).form({
    initialRequired: true,
    submitAction: {
      onSubmit: value => this.ref.close(value),
    },
    fields: {
      day: {
        label: '::GoldenOwl:Day',
      },
      from: {
        label: '::GoldenOwl:From',
        pickerType: 'time',
        mode: 'portrait',
        twelveHour: true,
        onChange: (value, _, parentGroup) => {
          if (
            !value ||
            parentGroup.untouched ||
            !parentGroup.controls.to.value
          ) return;

          const difference = this.getDateDiff(value, parentGroup.controls.to.value);

          parentGroup.controls.expectedAttendance.setValue(difference as any);
        },
      },
      to: {
        label: '::GoldenOwl:To',
        pickerType: 'time',
        mode: 'portrait',
        twelveHour: true,
        onChange: (value, _, parentGroup) => {
          if (
            !value ||
            parentGroup.untouched ||
            !parentGroup.controls.from.value
          ) return;

          const difference = this.getDateDiff(parentGroup.controls.from.value, value);

          parentGroup.controls.expectedAttendance.setValue(difference as any);
        },
      },
      expectedAttendance: {
        label: '::GoldenOwl:ExpectedAttendance',
        customInputComponent: TimePeriodCustomInputComponent,
        validators: [
          requiredValidator,
          {
            name: 'invalidExpectedAttendance',
            message: '::InvalidExpectedAttendance',
            validator: (control) => {
              if (!control.value) return null;

              const from = (control.parent!.controls as any).from.value;
              const to = (control.parent!.controls as any).to.value;

              if (!from || !to) return null;

              // here we passed them
              const differenceDate = this.getDateDiff(from, to);

              if (new Date(differenceDate) < control.value) return {
                'invalidExpectedAttendance': true
              }

              return null
            },
          }
        ],
      },
    },
    viewFunc: () => of(this.data),
  })

  getDateDiff(from: Date | string, to: Date | string) {
    const toDate = to instanceof Date ? to : new Date(to);
    const fromDate = from instanceof Date ? from : new Date(from);

    const hours = Math.floor(Math.abs(toDate.getTime() - fromDate.getTime()) / 3_600_000);
    const minutes = Math.floor(Math.abs(toDate.getTime() - fromDate.getTime()) / 60_000) % 60;

    const formattedHours = hours >= 10 ? hours.toString() : '0' + hours.toString();
    const formattedMinutes = minutes >= 10 ? minutes.toString() : '0' + minutes.toString();

    return timeToIso(`${formattedHours}:${formattedMinutes}`);
  }
}
