import { Component, DestroyRef, inject, signal } from '@angular/core';
import { AlertService, LanguagePipe, LOADING, TtwrFormComponent } from '@ttwr-framework/ngx-main-visuals';
import { finalize, map } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatCard, MatCardContent } from '@angular/material/card';
import { ActivatedRoute, Router } from '@angular/router';
import { LeaveAllocationService } from '@proxy/hr/leave-allocations';
import { leaveAllocations } from '../../leave-allocations.model';
import { RequestUnit } from '@proxy/hr/leave-types';
import { combineTwoDates, DateRangeCustomInputComponent, extractTwoDates } from '@shared';

@Component({
  selector: 'app-leave-allocations-update',
  standalone: true,
  templateUrl: './leave-allocations-update.component.html',
  styleUrl: './leave-allocations-update.component.scss',
  imports: [
    Mat<PERSON><PERSON>,
    MatCardContent,
    TtwrFormComponent,
    LanguagePipe
  ],
})
export class LeaveAllocationsUpdateComponent {
  private leaveAllocation = inject(LeaveAllocationService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private loading = inject(LOADING);

  private leaveTypeIdToRequestUnitMap = new Map<string, RequestUnit>();

  private numberOfDaysHiddenSignal = signal(false);
  private numberOfHoursHiddenSignal = signal(false);

  protected config = leaveAllocations(leaveTypes => {
    leaveTypes.forEach(leaveType => {
      this.leaveTypeIdToRequestUnitMap.set(leaveType.id, leaveType.requestUnit);
    });

    // because the options maybe come after the main viewFunc
    this.updateSignals(this.config.fields.leaveTypeId.control.value);
  }).exclude({
    leaveTypeName: true,
    employeeName: true,
  }).form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        const { start, end } = extractTwoDates(body.validityDateRange);

        this.leaveAllocation.update(this.route.snapshot.params['id'], {
          ...body,
          validityFrom: start,
          validityTo: end,
          numberOfDays: this.numberOfDaysHiddenSignal() ? 0 : body.numberOfDays,
          numberOfHours: this.numberOfHoursHiddenSignal() ? 0 : body.numberOfHours,
        })
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::UpdatedSuccessfully');
            this.router.navigate(['.'], { relativeTo: this.route.parent });
          })
      },
    },
    fields: {
      employeeId: {
        label: '::GoldenOwl:Employee',
        search: true,
      },
      leaveTypeId: {
        label: '::GoldenOwl:LeaveType',
        search: true,
        onChange: value => this.updateSignals(value),
      },
      numberOfDays: {
        label: '::Days',
        hiddenSignal: this.numberOfDaysHiddenSignal,
        defaultValue: 0,
      },
      numberOfHours: {
        label: '::Hours',
        hiddenSignal: this.numberOfHoursHiddenSignal,
        defaultValue: 0,
      },
      validityDateRange: {
        customInputComponent: DateRangeCustomInputComponent,
      },
    },
    viewFunc: () => this.leaveAllocation.get(this.route.snapshot.params['id']).pipe(
      map(allocation => ({
        ...allocation,
        validityDateRange: combineTwoDates(allocation.validityFrom, allocation.validityTo),
      }))
    ),
  })

  private updateSignals(value: string | null) {
    if (!value) return;

    const requestUnit = this.leaveTypeIdToRequestUnitMap.get(value);

    if (requestUnit === undefined) return;

    this.numberOfDaysHiddenSignal.set(requestUnit === RequestUnit.Hours);
    this.numberOfHoursHiddenSignal.set(requestUnit === RequestUnit.Days);
  }
}
