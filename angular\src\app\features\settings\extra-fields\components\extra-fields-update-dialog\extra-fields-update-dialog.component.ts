import { Component, DestroyRef, inject, InjectionToken, signal, Signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { AlertService, LanguagePipe, LOADING, TtwrFormComponent } from '@ttwr-framework/ngx-main-visuals';
import { finalize, of } from 'rxjs';
import { extraFields } from '../../extra-fields.model';
import { ExtraFieldDefinitionService, FieldType } from '@proxy/extra-field-definitions';
import { DefaultValueCustomInputComponent } from '../default-value-custom-input/default-value-custom-input.component';
import { UniqueCodeCustomInputComponent } from '../unique-code-custom-input/unique-code-custom-input.component';
import { clearDate } from '@shared';
import { EntityType } from '@proxy/notifications';

/**
 * Injection token for select options
 * in update dialog initial setup
 */
export const CURRENT_SELECT_OPTIONS = new InjectionToken<Signal<string[]>>('current select options');

const _currentSelectOptionsSignal = signal<string[]>([]);

@Component({
  selector: 'app-extra-fields-update-dialog',
  standalone: true,
  imports: [TtwrFormComponent, MatDialogTitle, MatDialogContent, LanguagePipe],
  template: `
    <h1 mat-dialog-title>{{ '::EditExtraDefinition' | i18n }}</h1>
    <mat-dialog-content>
      <ttwr-form [config]="config"/>
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-grid-template-columns: 1fr 1fr;
      --ttwr-form-gap: 1rem;
    }
  `,
  providers: [
    {
      provide: CURRENT_SELECT_OPTIONS,
      useValue: _currentSelectOptionsSignal,
    }
  ],
})
export class ExtraFieldsUpdateDialogComponent {
  private extraFieldDefinition = inject(ExtraFieldDefinitionService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private data = inject<DialogData>(MAT_DIALOG_DATA);
  private dialogRef = inject(MatDialogRef);
  private loading = inject(LOADING);

  protected config = extraFields.form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        let defaultValue: string;
        if (body.fieldType === FieldType.Int || body.fieldType === FieldType.Decimal) {
          defaultValue = JSON.stringify(body.defaultValue).replace(/"/g, '');
        } else if (body.fieldType === FieldType.DateOnly) {
          defaultValue = JSON.stringify(clearDate(body.defaultValue));
        } else if (body.fieldType === FieldType.Bool) {
          defaultValue = JSON.stringify(Boolean(body.defaultValue));
        } else if (body.fieldType === FieldType.Selection) {
          defaultValue = JSON.stringify(body.defaultValue[0]);
        } else {
          defaultValue = JSON.stringify(body.defaultValue);
        }

        this.extraFieldDefinition
          .update({
            ...body,
            defaultValue,
            id: this.data.id,
            selectListValues: body.fieldType === FieldType.Selection ? body.defaultValue as any : [],
          })
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::CreatedSuccessfully');
            this.dialogRef.close(true);
          })
      },
    },
    viewFunc: () => {
      if (this.data.fieldType === FieldType.Selection) {
        // making sure the first value is the default
        const options = this.data.selectListValues.filter(option => option !== this.data.defaultValue);

        _currentSelectOptionsSignal.set([
          this.data.defaultValue,
          ...options,
        ])
      }
      return of(this.data as any);
    },
    fields: {
      code: {
        label: '::Code',
        customInputComponent: UniqueCodeCustomInputComponent,
        customInputComponentExtraInputs: {
          self: true,
        },
      },
      defaultValue: {
        customInputComponent: DefaultValueCustomInputComponent,
        label: '::DefaultValue',
      },
      fieldType: {
        label: '::FieldType',
      },
      fieldName: {
        label: '::FieldName',
      },
      entityType: {
        label: '::EntityType',
      },
    },
  });
}


interface DialogData {
  id: string;
  defaultValue: string;
  fieldName: string;
  code: string;
  entityType: EntityType;
  fieldType: FieldType;
  selectListValues: string[];
}
