using Elsa.Extensions;
using Elsa.Workflows;
using Elsa.Workflows.Activities.Flowchart.Attributes;
using Elsa.Workflows.Attributes;
using ElsaWorkflows.GoldenOwl.Common.Requests.LeaveRequests;

namespace ElsaWorkflows.Domain.Activities.Requests.LeaveRequests;

[Activity("Requests.LeaveRequest",
    "Validates whether the employee has enough leave balance for the request (leave request), workflow input name: \"RequestId\"")]
[FlowNode("Pass", "InsufficientBalance")]
public class ValidateLeaveBalanceForEmployee : CodeActivity
{
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        var leaveRequestElsaHelper = context.GetRequiredService<ILeaveRequestsElsaHelper>();

        var requestId = Guid.Parse(context.GetWorkflowInput<string>("RequestId"));

        var hasSufficientBalance =
            await leaveRequestElsaHelper.ValidateEmployeeHasEnoughBalanceForRequestAsync(requestId);

        await context.CompleteActivityWithOutcomesAsync(hasSufficientBalance ? "Pass" : "InsufficientBalance");
    }
}