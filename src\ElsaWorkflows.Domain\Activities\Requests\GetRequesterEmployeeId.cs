using Elsa.Extensions;
using Elsa.Workflows;
using Elsa.Workflows.Attributes;

namespace ElsaWorkflows.Domain.Activities.Requests;

[Activity("Requests",
    "Gets the requester's employee id from workflow inputs, workflow input name: \"EmployeeId\" - output: employeeId")]
public class GetRequesterEmployeeId : CodeActivity<Guid>
{
    protected override void Execute(ActivityExecutionContext context)
    {
        if (!context.WorkflowInput.TryGetValue("EmployeeId", out var input))
        {
            throw new InvalidOperationException(
                "The Input \"EmployeeId\" is not set, please set it in the designer");
        }

        if (!Guid.TryParse(input.ToString(), out var requesterEmployeeId) || requesterEmployeeId == Guid.Empty)
        {
            throw new InvalidOperationException(
                "The Input \"EmployeeId\" is empty or invalid when you try to access it");
        }

        context.SetResult(requesterEmployeeId);
    }
}