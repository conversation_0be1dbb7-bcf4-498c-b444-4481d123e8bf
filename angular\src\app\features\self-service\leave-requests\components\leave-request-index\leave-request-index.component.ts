import { Component, DestroyRef, inject, signal } from '@angular/core';
import {
  dateToISO,
  extraGridFilter,
  GridFilterOperation,
  LOADING,
  pagedMap,
  takeOptions,
  TtwrGridComponent
} from '@ttwr-framework/ngx-main-visuals';
import { leaveRequests } from '../../leave-requests.model';
import { ActivatedRoute, Router } from '@angular/router';
import {finalize, map, of, Subject} from 'rxjs';
import {requireAllOperator} from '@shared/functions';
import {LeaveRequestService, LeaveRequestState, LeaveUnit} from '@proxy/hr/leave-requests';
import {extractTwoDates} from '@shared/components';
import {DateRangeCustomInputComponent} from '@shared';
import {AttendanceRequestState} from '@proxy/self-service/attendance-requests';
import {LocalizationService, PagedResultDto} from '@abp/ng.core';
import {takeUntilDestroyed} from '@angular/core/rxjs-interop';
import {getLabelOfValue} from '@shared/functions/enum-labels';
import {DatePipe} from '@angular/common';

@Component({
  selector: 'app-leave-requests-index',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `
    <ttwr-grid [config]="latestConfig" />
    <ttwr-grid [config]="config" />
  `,
})
export class LeaveRequestsIndexComponent {
  private leaveRequestService = inject(LeaveRequestService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private loading = inject(LOADING);
  private localizationService = inject(LocalizationService);
  private datePipe = inject(DatePipe);

  constructor() {}

  private refreshSubject = new Subject<void>();

  protected config = leaveRequests.exclude({
    from: true,
    to: true,
    unit: true,
    duration: true
  }).grid({
    title: '::Menu:MyLeaveRequests',
    refreshSubject: this.refreshSubject,
    dataFunc: (pagination, _, filters) => {

      const dateAndTimeRange = filters.find(f => f.attribute === 'dateAndTimeRange')?.value;
      const state = filters.find(f => f.attribute === 'state')?.value;

      const { start, end } = dateAndTimeRange ? extractTwoDates(dateAndTimeRange) : { start: '', end: '' };

      return this.leaveRequestService.getList({
        skipCount: pagination.pageSize * pagination.pageIndex,
        maxResultCount: pagination.pageSize,
      }).pipe(requireAllOperator(),
        pagedMap(item => ({
          ...item,
          dateAndTimeRange: this.datePipe.transform(item.from, "yyyy/MM/dd hh:mm a") +" - " + this.datePipe.transform(item.to, "yyyy/MM/dd hh:mm a"),
          durationUnit: item.duration + " " + this.localizationService.instant('::'+ getLabelOfValue(LeaveUnit, item.unit))
        })));
    },
    extraFilters: [
        extraGridFilter({
          name: 'dateAndTimeRange',
          type: 'text',
          customInputComponent: DateRangeCustomInputComponent,
          customInputComponentExtraInputs: {
            disableDateFilter: true,
          },
          searchBarOperation: GridFilterOperation.In,
        }),
        extraGridFilter({
          name: 'state',
          type: 'select',
          options: takeOptions(LeaveRequestState),
          multiple: false,
          searchBarOperation: GridFilterOperation.Equal,
        })
      ],
    actions: [
      {
        tooltip: 'Add',
        matIcon: 'add',
        delegateFunc: () => this.router.navigate(['create'], { relativeTo: this.route }),
      }
    ],
    fields: {
      leaveTypeName: {
        columnName: '::GoldenOwl:LeaveType',
      },
      durationUnit: {
        columnName: '::GoldenOwl:Duration',
      },
      state: {
        columnName: '::State',
      },
      dateAndTimeRange: {
        columnName: '::GoldenOwl:DateAndTimeRange',
      },
    },
    fieldActions: [
      {
        label: '::Cancel',
        showFunc: obj => obj.state === LeaveRequestState.PendingApproval,
        confirmation: {
          title: '::Warning',
          message: '::AreYouSureYouWantToCancelLeaveRequest?',
        },
        delegateFunc: obj => {
          this.loading.set(true);
          this.leaveRequestService.cancel(obj.id).pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          ).subscribe(() => {
            this.refreshSubject.next();
          });
        }
      }
    ],
  });

  protected latestConfig = leaveRequests.exclude({
    from: true,
    to: true,
    unit: true,
    duration: true
  }).grid({
    title: '::GoldenOwl:RecentlyRequests',
    refreshSubject: this.refreshSubject,
    dataFunc: (pagination, _, filters) => {
      return this.leaveRequestService.getPendingApproval().pipe(requireAllOperator(),
        map((items: any[]) => ({
          items: items.map((item: any) => ({
            ...item,
            dateAndTimeRange: this.datePipe.transform(item.from, "yyyy/MM/dd hh:mm a") + " - " + this.datePipe.transform(item.to, "yyyy/MM/dd hh:mm a"),
            durationUnit: item.duration + " " + this.localizationService.instant('::' + getLabelOfValue(LeaveUnit, item.unit))
          })),
          totalCount: items.length  // Calculate total count from the array length
        }))
        );
    },
    extraFilters: [
    ],
    actions: [
    ],
    fields: {
      leaveTypeName: {
        columnName: '::GoldenOwl:LeaveType',
      },
      durationUnit: {
        columnName: '::GoldenOwl:Duration',
      },
      state: {
        columnName: '::State',
      },
      dateAndTimeRange: {
        columnName: '::GoldenOwl:DateAndTimeRange',
      },
    },
    fieldActions: [
    ],
  });
}
