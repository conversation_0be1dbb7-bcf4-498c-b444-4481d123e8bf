{"version": 3, "sources": ["../../../../../../node_modules/@codemirror/commands/dist/index.js", "../../../../../../node_modules/crelt/index.js", "../../../../../../node_modules/@codemirror/search/dist/index.js", "../../../../../../node_modules/@codemirror/lint/dist/index.js", "../../../../../../node_modules/codemirror/dist/index.js"], "sourcesContent": ["import { Annotation, Facet, combineConfig, StateField, Transaction, ChangeSet, ChangeDesc, EditorSelection, StateEffect, Text, findClusterBreak, countColumn, CharCategory } from '@codemirror/state';\nimport { EditorView, Direction } from '@codemirror/view';\nimport { IndentContext, getIndentation, indentString, matchBrackets, syntaxTree, getIndentUnit, indentUnit } from '@codemirror/language';\nimport { NodeProp } from '@lezer/common';\n\n/**\nComment or uncomment the current selection. Will use line comments\nif available, otherwise falling back to block comments.\n*/\nconst toggleComment = target => {\n  let {\n      state\n    } = target,\n    line = state.doc.lineAt(state.selection.main.from),\n    config = getConfig(target.state, line.from);\n  return config.line ? toggleLineComment(target) : config.block ? toggleBlockCommentByLine(target) : false;\n};\nfunction command(f, option) {\n  return ({\n    state,\n    dispatch\n  }) => {\n    if (state.readOnly) return false;\n    let tr = f(option, state);\n    if (!tr) return false;\n    dispatch(state.update(tr));\n    return true;\n  };\n}\n/**\nComment or uncomment the current selection using line comments.\nThe line comment syntax is taken from the\n[`commentTokens`](https://codemirror.net/6/docs/ref/#commands.CommentTokens) [language\ndata](https://codemirror.net/6/docs/ref/#state.EditorState.languageDataAt).\n*/\nconst toggleLineComment = /*@__PURE__*/command(changeLineComment, 0 /* CommentOption.Toggle */);\n/**\nComment the current selection using line comments.\n*/\nconst lineComment = /*@__PURE__*/command(changeLineComment, 1 /* CommentOption.Comment */);\n/**\nUncomment the current selection using line comments.\n*/\nconst lineUncomment = /*@__PURE__*/command(changeLineComment, 2 /* CommentOption.Uncomment */);\n/**\nComment or uncomment the current selection using block comments.\nThe block comment syntax is taken from the\n[`commentTokens`](https://codemirror.net/6/docs/ref/#commands.CommentTokens) [language\ndata](https://codemirror.net/6/docs/ref/#state.EditorState.languageDataAt).\n*/\nconst toggleBlockComment = /*@__PURE__*/command(changeBlockComment, 0 /* CommentOption.Toggle */);\n/**\nComment the current selection using block comments.\n*/\nconst blockComment = /*@__PURE__*/command(changeBlockComment, 1 /* CommentOption.Comment */);\n/**\nUncomment the current selection using block comments.\n*/\nconst blockUncomment = /*@__PURE__*/command(changeBlockComment, 2 /* CommentOption.Uncomment */);\n/**\nComment or uncomment the lines around the current selection using\nblock comments.\n*/\nconst toggleBlockCommentByLine = /*@__PURE__*/command((o, s) => changeBlockComment(o, s, selectedLineRanges(s)), 0 /* CommentOption.Toggle */);\nfunction getConfig(state, pos) {\n  let data = state.languageDataAt(\"commentTokens\", pos);\n  return data.length ? data[0] : {};\n}\nconst SearchMargin = 50;\n/**\nDetermines if the given range is block-commented in the given\nstate.\n*/\nfunction findBlockComment(state, {\n  open,\n  close\n}, from, to) {\n  let textBefore = state.sliceDoc(from - SearchMargin, from);\n  let textAfter = state.sliceDoc(to, to + SearchMargin);\n  let spaceBefore = /\\s*$/.exec(textBefore)[0].length,\n    spaceAfter = /^\\s*/.exec(textAfter)[0].length;\n  let beforeOff = textBefore.length - spaceBefore;\n  if (textBefore.slice(beforeOff - open.length, beforeOff) == open && textAfter.slice(spaceAfter, spaceAfter + close.length) == close) {\n    return {\n      open: {\n        pos: from - spaceBefore,\n        margin: spaceBefore && 1\n      },\n      close: {\n        pos: to + spaceAfter,\n        margin: spaceAfter && 1\n      }\n    };\n  }\n  let startText, endText;\n  if (to - from <= 2 * SearchMargin) {\n    startText = endText = state.sliceDoc(from, to);\n  } else {\n    startText = state.sliceDoc(from, from + SearchMargin);\n    endText = state.sliceDoc(to - SearchMargin, to);\n  }\n  let startSpace = /^\\s*/.exec(startText)[0].length,\n    endSpace = /\\s*$/.exec(endText)[0].length;\n  let endOff = endText.length - endSpace - close.length;\n  if (startText.slice(startSpace, startSpace + open.length) == open && endText.slice(endOff, endOff + close.length) == close) {\n    return {\n      open: {\n        pos: from + startSpace + open.length,\n        margin: /\\s/.test(startText.charAt(startSpace + open.length)) ? 1 : 0\n      },\n      close: {\n        pos: to - endSpace - close.length,\n        margin: /\\s/.test(endText.charAt(endOff - 1)) ? 1 : 0\n      }\n    };\n  }\n  return null;\n}\nfunction selectedLineRanges(state) {\n  let ranges = [];\n  for (let r of state.selection.ranges) {\n    let fromLine = state.doc.lineAt(r.from);\n    let toLine = r.to <= fromLine.to ? fromLine : state.doc.lineAt(r.to);\n    if (toLine.from > fromLine.from && toLine.from == r.to) toLine = r.to == fromLine.to + 1 ? fromLine : state.doc.lineAt(r.to - 1);\n    let last = ranges.length - 1;\n    if (last >= 0 && ranges[last].to > fromLine.from) ranges[last].to = toLine.to;else ranges.push({\n      from: fromLine.from + /^\\s*/.exec(fromLine.text)[0].length,\n      to: toLine.to\n    });\n  }\n  return ranges;\n}\n// Performs toggle, comment and uncomment of block comments in\n// languages that support them.\nfunction changeBlockComment(option, state, ranges = state.selection.ranges) {\n  let tokens = ranges.map(r => getConfig(state, r.from).block);\n  if (!tokens.every(c => c)) return null;\n  let comments = ranges.map((r, i) => findBlockComment(state, tokens[i], r.from, r.to));\n  if (option != 2 /* CommentOption.Uncomment */ && !comments.every(c => c)) {\n    return {\n      changes: state.changes(ranges.map((range, i) => {\n        if (comments[i]) return [];\n        return [{\n          from: range.from,\n          insert: tokens[i].open + \" \"\n        }, {\n          from: range.to,\n          insert: \" \" + tokens[i].close\n        }];\n      }))\n    };\n  } else if (option != 1 /* CommentOption.Comment */ && comments.some(c => c)) {\n    let changes = [];\n    for (let i = 0, comment; i < comments.length; i++) if (comment = comments[i]) {\n      let token = tokens[i],\n        {\n          open,\n          close\n        } = comment;\n      changes.push({\n        from: open.pos - token.open.length,\n        to: open.pos + open.margin\n      }, {\n        from: close.pos - close.margin,\n        to: close.pos + token.close.length\n      });\n    }\n    return {\n      changes\n    };\n  }\n  return null;\n}\n// Performs toggle, comment and uncomment of line comments.\nfunction changeLineComment(option, state, ranges = state.selection.ranges) {\n  let lines = [];\n  let prevLine = -1;\n  for (let {\n    from,\n    to\n  } of ranges) {\n    let startI = lines.length,\n      minIndent = 1e9;\n    let token = getConfig(state, from).line;\n    if (!token) continue;\n    for (let pos = from; pos <= to;) {\n      let line = state.doc.lineAt(pos);\n      if (line.from > prevLine && (from == to || to > line.from)) {\n        prevLine = line.from;\n        let indent = /^\\s*/.exec(line.text)[0].length;\n        let empty = indent == line.length;\n        let comment = line.text.slice(indent, indent + token.length) == token ? indent : -1;\n        if (indent < line.text.length && indent < minIndent) minIndent = indent;\n        lines.push({\n          line,\n          comment,\n          token,\n          indent,\n          empty,\n          single: false\n        });\n      }\n      pos = line.to + 1;\n    }\n    if (minIndent < 1e9) for (let i = startI; i < lines.length; i++) if (lines[i].indent < lines[i].line.text.length) lines[i].indent = minIndent;\n    if (lines.length == startI + 1) lines[startI].single = true;\n  }\n  if (option != 2 /* CommentOption.Uncomment */ && lines.some(l => l.comment < 0 && (!l.empty || l.single))) {\n    let changes = [];\n    for (let {\n      line,\n      token,\n      indent,\n      empty,\n      single\n    } of lines) if (single || !empty) changes.push({\n      from: line.from + indent,\n      insert: token + \" \"\n    });\n    let changeSet = state.changes(changes);\n    return {\n      changes: changeSet,\n      selection: state.selection.map(changeSet, 1)\n    };\n  } else if (option != 1 /* CommentOption.Comment */ && lines.some(l => l.comment >= 0)) {\n    let changes = [];\n    for (let {\n      line,\n      comment,\n      token\n    } of lines) if (comment >= 0) {\n      let from = line.from + comment,\n        to = from + token.length;\n      if (line.text[to - line.from] == \" \") to++;\n      changes.push({\n        from,\n        to\n      });\n    }\n    return {\n      changes\n    };\n  }\n  return null;\n}\nconst fromHistory = /*@__PURE__*/Annotation.define();\n/**\nTransaction annotation that will prevent that transaction from\nbeing combined with other transactions in the undo history. Given\n`\"before\"`, it'll prevent merging with previous transactions. With\n`\"after\"`, subsequent transactions won't be combined with this\none. With `\"full\"`, the transaction is isolated on both sides.\n*/\nconst isolateHistory = /*@__PURE__*/Annotation.define();\n/**\nThis facet provides a way to register functions that, given a\ntransaction, provide a set of effects that the history should\nstore when inverting the transaction. This can be used to\nintegrate some kinds of effects in the history, so that they can\nbe undone (and redone again).\n*/\nconst invertedEffects = /*@__PURE__*/Facet.define();\nconst historyConfig = /*@__PURE__*/Facet.define({\n  combine(configs) {\n    return combineConfig(configs, {\n      minDepth: 100,\n      newGroupDelay: 500,\n      joinToEvent: (_t, isAdjacent) => isAdjacent\n    }, {\n      minDepth: Math.max,\n      newGroupDelay: Math.min,\n      joinToEvent: (a, b) => (tr, adj) => a(tr, adj) || b(tr, adj)\n    });\n  }\n});\nconst historyField_ = /*@__PURE__*/StateField.define({\n  create() {\n    return HistoryState.empty;\n  },\n  update(state, tr) {\n    let config = tr.state.facet(historyConfig);\n    let fromHist = tr.annotation(fromHistory);\n    if (fromHist) {\n      let item = HistEvent.fromTransaction(tr, fromHist.selection),\n        from = fromHist.side;\n      let other = from == 0 /* BranchName.Done */ ? state.undone : state.done;\n      if (item) other = updateBranch(other, other.length, config.minDepth, item);else other = addSelection(other, tr.startState.selection);\n      return new HistoryState(from == 0 /* BranchName.Done */ ? fromHist.rest : other, from == 0 /* BranchName.Done */ ? other : fromHist.rest);\n    }\n    let isolate = tr.annotation(isolateHistory);\n    if (isolate == \"full\" || isolate == \"before\") state = state.isolate();\n    if (tr.annotation(Transaction.addToHistory) === false) return !tr.changes.empty ? state.addMapping(tr.changes.desc) : state;\n    let event = HistEvent.fromTransaction(tr);\n    let time = tr.annotation(Transaction.time),\n      userEvent = tr.annotation(Transaction.userEvent);\n    if (event) state = state.addChanges(event, time, userEvent, config, tr);else if (tr.selection) state = state.addSelection(tr.startState.selection, time, userEvent, config.newGroupDelay);\n    if (isolate == \"full\" || isolate == \"after\") state = state.isolate();\n    return state;\n  },\n  toJSON(value) {\n    return {\n      done: value.done.map(e => e.toJSON()),\n      undone: value.undone.map(e => e.toJSON())\n    };\n  },\n  fromJSON(json) {\n    return new HistoryState(json.done.map(HistEvent.fromJSON), json.undone.map(HistEvent.fromJSON));\n  }\n});\n/**\nCreate a history extension with the given configuration.\n*/\nfunction history(config = {}) {\n  return [historyField_, historyConfig.of(config), EditorView.domEventHandlers({\n    beforeinput(e, view) {\n      let command = e.inputType == \"historyUndo\" ? undo : e.inputType == \"historyRedo\" ? redo : null;\n      if (!command) return false;\n      e.preventDefault();\n      return command(view);\n    }\n  })];\n}\n/**\nThe state field used to store the history data. Should probably\nonly be used when you want to\n[serialize](https://codemirror.net/6/docs/ref/#state.EditorState.toJSON) or\n[deserialize](https://codemirror.net/6/docs/ref/#state.EditorState^fromJSON) state objects in a way\nthat preserves history.\n*/\nconst historyField = historyField_;\nfunction cmd(side, selection) {\n  return function ({\n    state,\n    dispatch\n  }) {\n    if (!selection && state.readOnly) return false;\n    let historyState = state.field(historyField_, false);\n    if (!historyState) return false;\n    let tr = historyState.pop(side, state, selection);\n    if (!tr) return false;\n    dispatch(tr);\n    return true;\n  };\n}\n/**\nUndo a single group of history events. Returns false if no group\nwas available.\n*/\nconst undo = /*@__PURE__*/cmd(0 /* BranchName.Done */, false);\n/**\nRedo a group of history events. Returns false if no group was\navailable.\n*/\nconst redo = /*@__PURE__*/cmd(1 /* BranchName.Undone */, false);\n/**\nUndo a change or selection change.\n*/\nconst undoSelection = /*@__PURE__*/cmd(0 /* BranchName.Done */, true);\n/**\nRedo a change or selection change.\n*/\nconst redoSelection = /*@__PURE__*/cmd(1 /* BranchName.Undone */, true);\nfunction depth(side) {\n  return function (state) {\n    let histState = state.field(historyField_, false);\n    if (!histState) return 0;\n    let branch = side == 0 /* BranchName.Done */ ? histState.done : histState.undone;\n    return branch.length - (branch.length && !branch[0].changes ? 1 : 0);\n  };\n}\n/**\nThe amount of undoable change events available in a given state.\n*/\nconst undoDepth = /*@__PURE__*/depth(0 /* BranchName.Done */);\n/**\nThe amount of redoable change events available in a given state.\n*/\nconst redoDepth = /*@__PURE__*/depth(1 /* BranchName.Undone */);\n// History events store groups of changes or effects that need to be\n// undone/redone together.\nclass HistEvent {\n  constructor(\n  // The changes in this event. Normal events hold at least one\n  // change or effect. But it may be necessary to store selection\n  // events before the first change, in which case a special type of\n  // instance is created which doesn't hold any changes, with\n  // changes == startSelection == undefined\n  changes,\n  // The effects associated with this event\n  effects,\n  // Accumulated mapping (from addToHistory==false) that should be\n  // applied to events below this one.\n  mapped,\n  // The selection before this event\n  startSelection,\n  // Stores selection changes after this event, to be used for\n  // selection undo/redo.\n  selectionsAfter) {\n    this.changes = changes;\n    this.effects = effects;\n    this.mapped = mapped;\n    this.startSelection = startSelection;\n    this.selectionsAfter = selectionsAfter;\n  }\n  setSelAfter(after) {\n    return new HistEvent(this.changes, this.effects, this.mapped, this.startSelection, after);\n  }\n  toJSON() {\n    var _a, _b, _c;\n    return {\n      changes: (_a = this.changes) === null || _a === void 0 ? void 0 : _a.toJSON(),\n      mapped: (_b = this.mapped) === null || _b === void 0 ? void 0 : _b.toJSON(),\n      startSelection: (_c = this.startSelection) === null || _c === void 0 ? void 0 : _c.toJSON(),\n      selectionsAfter: this.selectionsAfter.map(s => s.toJSON())\n    };\n  }\n  static fromJSON(json) {\n    return new HistEvent(json.changes && ChangeSet.fromJSON(json.changes), [], json.mapped && ChangeDesc.fromJSON(json.mapped), json.startSelection && EditorSelection.fromJSON(json.startSelection), json.selectionsAfter.map(EditorSelection.fromJSON));\n  }\n  // This does not check `addToHistory` and such, it assumes the\n  // transaction needs to be converted to an item. Returns null when\n  // there are no changes or effects in the transaction.\n  static fromTransaction(tr, selection) {\n    let effects = none;\n    for (let invert of tr.startState.facet(invertedEffects)) {\n      let result = invert(tr);\n      if (result.length) effects = effects.concat(result);\n    }\n    if (!effects.length && tr.changes.empty) return null;\n    return new HistEvent(tr.changes.invert(tr.startState.doc), effects, undefined, selection || tr.startState.selection, none);\n  }\n  static selection(selections) {\n    return new HistEvent(undefined, none, undefined, undefined, selections);\n  }\n}\nfunction updateBranch(branch, to, maxLen, newEvent) {\n  let start = to + 1 > maxLen + 20 ? to - maxLen - 1 : 0;\n  let newBranch = branch.slice(start, to);\n  newBranch.push(newEvent);\n  return newBranch;\n}\nfunction isAdjacent(a, b) {\n  let ranges = [],\n    isAdjacent = false;\n  a.iterChangedRanges((f, t) => ranges.push(f, t));\n  b.iterChangedRanges((_f, _t, f, t) => {\n    for (let i = 0; i < ranges.length;) {\n      let from = ranges[i++],\n        to = ranges[i++];\n      if (t >= from && f <= to) isAdjacent = true;\n    }\n  });\n  return isAdjacent;\n}\nfunction eqSelectionShape(a, b) {\n  return a.ranges.length == b.ranges.length && a.ranges.filter((r, i) => r.empty != b.ranges[i].empty).length === 0;\n}\nfunction conc(a, b) {\n  return !a.length ? b : !b.length ? a : a.concat(b);\n}\nconst none = [];\nconst MaxSelectionsPerEvent = 200;\nfunction addSelection(branch, selection) {\n  if (!branch.length) {\n    return [HistEvent.selection([selection])];\n  } else {\n    let lastEvent = branch[branch.length - 1];\n    let sels = lastEvent.selectionsAfter.slice(Math.max(0, lastEvent.selectionsAfter.length - MaxSelectionsPerEvent));\n    if (sels.length && sels[sels.length - 1].eq(selection)) return branch;\n    sels.push(selection);\n    return updateBranch(branch, branch.length - 1, 1e9, lastEvent.setSelAfter(sels));\n  }\n}\n// Assumes the top item has one or more selectionAfter values\nfunction popSelection(branch) {\n  let last = branch[branch.length - 1];\n  let newBranch = branch.slice();\n  newBranch[branch.length - 1] = last.setSelAfter(last.selectionsAfter.slice(0, last.selectionsAfter.length - 1));\n  return newBranch;\n}\n// Add a mapping to the top event in the given branch. If this maps\n// away all the changes and effects in that item, drop it and\n// propagate the mapping to the next item.\nfunction addMappingToBranch(branch, mapping) {\n  if (!branch.length) return branch;\n  let length = branch.length,\n    selections = none;\n  while (length) {\n    let event = mapEvent(branch[length - 1], mapping, selections);\n    if (event.changes && !event.changes.empty || event.effects.length) {\n      // Event survived mapping\n      let result = branch.slice(0, length);\n      result[length - 1] = event;\n      return result;\n    } else {\n      // Drop this event, since there's no changes or effects left\n      mapping = event.mapped;\n      length--;\n      selections = event.selectionsAfter;\n    }\n  }\n  return selections.length ? [HistEvent.selection(selections)] : none;\n}\nfunction mapEvent(event, mapping, extraSelections) {\n  let selections = conc(event.selectionsAfter.length ? event.selectionsAfter.map(s => s.map(mapping)) : none, extraSelections);\n  // Change-less events don't store mappings (they are always the last event in a branch)\n  if (!event.changes) return HistEvent.selection(selections);\n  let mappedChanges = event.changes.map(mapping),\n    before = mapping.mapDesc(event.changes, true);\n  let fullMapping = event.mapped ? event.mapped.composeDesc(before) : before;\n  return new HistEvent(mappedChanges, StateEffect.mapEffects(event.effects, mapping), fullMapping, event.startSelection.map(before), selections);\n}\nconst joinableUserEvent = /^(input\\.type|delete)($|\\.)/;\nclass HistoryState {\n  constructor(done, undone, prevTime = 0, prevUserEvent = undefined) {\n    this.done = done;\n    this.undone = undone;\n    this.prevTime = prevTime;\n    this.prevUserEvent = prevUserEvent;\n  }\n  isolate() {\n    return this.prevTime ? new HistoryState(this.done, this.undone) : this;\n  }\n  addChanges(event, time, userEvent, config, tr) {\n    let done = this.done,\n      lastEvent = done[done.length - 1];\n    if (lastEvent && lastEvent.changes && !lastEvent.changes.empty && event.changes && (!userEvent || joinableUserEvent.test(userEvent)) && (!lastEvent.selectionsAfter.length && time - this.prevTime < config.newGroupDelay && config.joinToEvent(tr, isAdjacent(lastEvent.changes, event.changes)) ||\n    // For compose (but not compose.start) events, always join with previous event\n    userEvent == \"input.type.compose\")) {\n      done = updateBranch(done, done.length - 1, config.minDepth, new HistEvent(event.changes.compose(lastEvent.changes), conc(StateEffect.mapEffects(event.effects, lastEvent.changes), lastEvent.effects), lastEvent.mapped, lastEvent.startSelection, none));\n    } else {\n      done = updateBranch(done, done.length, config.minDepth, event);\n    }\n    return new HistoryState(done, none, time, userEvent);\n  }\n  addSelection(selection, time, userEvent, newGroupDelay) {\n    let last = this.done.length ? this.done[this.done.length - 1].selectionsAfter : none;\n    if (last.length > 0 && time - this.prevTime < newGroupDelay && userEvent == this.prevUserEvent && userEvent && /^select($|\\.)/.test(userEvent) && eqSelectionShape(last[last.length - 1], selection)) return this;\n    return new HistoryState(addSelection(this.done, selection), this.undone, time, userEvent);\n  }\n  addMapping(mapping) {\n    return new HistoryState(addMappingToBranch(this.done, mapping), addMappingToBranch(this.undone, mapping), this.prevTime, this.prevUserEvent);\n  }\n  pop(side, state, onlySelection) {\n    let branch = side == 0 /* BranchName.Done */ ? this.done : this.undone;\n    if (branch.length == 0) return null;\n    let event = branch[branch.length - 1],\n      selection = event.selectionsAfter[0] || state.selection;\n    if (onlySelection && event.selectionsAfter.length) {\n      return state.update({\n        selection: event.selectionsAfter[event.selectionsAfter.length - 1],\n        annotations: fromHistory.of({\n          side,\n          rest: popSelection(branch),\n          selection\n        }),\n        userEvent: side == 0 /* BranchName.Done */ ? \"select.undo\" : \"select.redo\",\n        scrollIntoView: true\n      });\n    } else if (!event.changes) {\n      return null;\n    } else {\n      let rest = branch.length == 1 ? none : branch.slice(0, branch.length - 1);\n      if (event.mapped) rest = addMappingToBranch(rest, event.mapped);\n      return state.update({\n        changes: event.changes,\n        selection: event.startSelection,\n        effects: event.effects,\n        annotations: fromHistory.of({\n          side,\n          rest,\n          selection\n        }),\n        filter: false,\n        userEvent: side == 0 /* BranchName.Done */ ? \"undo\" : \"redo\",\n        scrollIntoView: true\n      });\n    }\n  }\n}\nHistoryState.empty = /*@__PURE__*/new HistoryState(none, none);\n/**\nDefault key bindings for the undo history.\n\n- Mod-z: [`undo`](https://codemirror.net/6/docs/ref/#commands.undo).\n- Mod-y (Mod-Shift-z on macOS) + Ctrl-Shift-z on Linux: [`redo`](https://codemirror.net/6/docs/ref/#commands.redo).\n- Mod-u: [`undoSelection`](https://codemirror.net/6/docs/ref/#commands.undoSelection).\n- Alt-u (Mod-Shift-u on macOS): [`redoSelection`](https://codemirror.net/6/docs/ref/#commands.redoSelection).\n*/\nconst historyKeymap = [{\n  key: \"Mod-z\",\n  run: undo,\n  preventDefault: true\n}, {\n  key: \"Mod-y\",\n  mac: \"Mod-Shift-z\",\n  run: redo,\n  preventDefault: true\n}, {\n  linux: \"Ctrl-Shift-z\",\n  run: redo,\n  preventDefault: true\n}, {\n  key: \"Mod-u\",\n  run: undoSelection,\n  preventDefault: true\n}, {\n  key: \"Alt-u\",\n  mac: \"Mod-Shift-u\",\n  run: redoSelection,\n  preventDefault: true\n}];\nfunction updateSel(sel, by) {\n  return EditorSelection.create(sel.ranges.map(by), sel.mainIndex);\n}\nfunction setSel(state, selection) {\n  return state.update({\n    selection,\n    scrollIntoView: true,\n    userEvent: \"select\"\n  });\n}\nfunction moveSel({\n  state,\n  dispatch\n}, how) {\n  let selection = updateSel(state.selection, how);\n  if (selection.eq(state.selection, true)) return false;\n  dispatch(setSel(state, selection));\n  return true;\n}\nfunction rangeEnd(range, forward) {\n  return EditorSelection.cursor(forward ? range.to : range.from);\n}\nfunction cursorByChar(view, forward) {\n  return moveSel(view, range => range.empty ? view.moveByChar(range, forward) : rangeEnd(range, forward));\n}\nfunction ltrAtCursor(view) {\n  return view.textDirectionAt(view.state.selection.main.head) == Direction.LTR;\n}\n/**\nMove the selection one character to the left (which is backward in\nleft-to-right text, forward in right-to-left text).\n*/\nconst cursorCharLeft = view => cursorByChar(view, !ltrAtCursor(view));\n/**\nMove the selection one character to the right.\n*/\nconst cursorCharRight = view => cursorByChar(view, ltrAtCursor(view));\n/**\nMove the selection one character forward.\n*/\nconst cursorCharForward = view => cursorByChar(view, true);\n/**\nMove the selection one character backward.\n*/\nconst cursorCharBackward = view => cursorByChar(view, false);\nfunction byCharLogical(state, range, forward) {\n  let pos = range.head,\n    line = state.doc.lineAt(pos);\n  if (pos == (forward ? line.to : line.from)) pos = forward ? Math.min(state.doc.length, line.to + 1) : Math.max(0, line.from - 1);else pos = line.from + findClusterBreak(line.text, pos - line.from, forward);\n  return EditorSelection.cursor(pos, forward ? -1 : 1);\n}\nfunction moveByCharLogical(target, forward) {\n  return moveSel(target, range => range.empty ? byCharLogical(target.state, range, forward) : rangeEnd(range, forward));\n}\n/**\nMove the selection one character forward, in logical\n(non-text-direction-aware) string index order.\n*/\nconst cursorCharForwardLogical = target => moveByCharLogical(target, true);\n/**\nMove the selection one character backward, in logical string index\norder.\n*/\nconst cursorCharBackwardLogical = target => moveByCharLogical(target, false);\nfunction cursorByGroup(view, forward) {\n  return moveSel(view, range => range.empty ? view.moveByGroup(range, forward) : rangeEnd(range, forward));\n}\n/**\nMove the selection to the left across one group of word or\nnon-word (but also non-space) characters.\n*/\nconst cursorGroupLeft = view => cursorByGroup(view, !ltrAtCursor(view));\n/**\nMove the selection one group to the right.\n*/\nconst cursorGroupRight = view => cursorByGroup(view, ltrAtCursor(view));\n/**\nMove the selection one group forward.\n*/\nconst cursorGroupForward = view => cursorByGroup(view, true);\n/**\nMove the selection one group backward.\n*/\nconst cursorGroupBackward = view => cursorByGroup(view, false);\nfunction toGroupStart(view, pos, start) {\n  let categorize = view.state.charCategorizer(pos);\n  let cat = categorize(start),\n    initial = cat != CharCategory.Space;\n  return next => {\n    let nextCat = categorize(next);\n    if (nextCat != CharCategory.Space) return initial && nextCat == cat;\n    initial = false;\n    return true;\n  };\n}\n/**\nMove the cursor one group forward in the default Windows style,\nwhere it moves to the start of the next group.\n*/\nconst cursorGroupForwardWin = view => {\n  return moveSel(view, range => range.empty ? view.moveByChar(range, true, start => toGroupStart(view, range.head, start)) : rangeEnd(range, true));\n};\nconst segmenter = typeof Intl != \"undefined\" && Intl.Segmenter ? /*@__PURE__*/new Intl.Segmenter(undefined, {\n  granularity: \"word\"\n}) : null;\nfunction moveBySubword(view, range, forward) {\n  let categorize = view.state.charCategorizer(range.from);\n  let cat = CharCategory.Space,\n    pos = range.from,\n    steps = 0;\n  let done = false,\n    sawUpper = false,\n    sawLower = false;\n  let step = next => {\n    if (done) return false;\n    pos += forward ? next.length : -next.length;\n    let nextCat = categorize(next),\n      ahead;\n    if (nextCat == CharCategory.Word && next.charCodeAt(0) < 128 && /[\\W_]/.test(next)) nextCat = -1; // Treat word punctuation specially\n    if (cat == CharCategory.Space) cat = nextCat;\n    if (cat != nextCat) return false;\n    if (cat == CharCategory.Word) {\n      if (next.toLowerCase() == next) {\n        if (!forward && sawUpper) return false;\n        sawLower = true;\n      } else if (sawLower) {\n        if (forward) return false;\n        done = true;\n      } else {\n        if (sawUpper && forward && categorize(ahead = view.state.sliceDoc(pos, pos + 1)) == CharCategory.Word && ahead.toLowerCase() == ahead) return false;\n        sawUpper = true;\n      }\n    }\n    steps++;\n    return true;\n  };\n  let end = view.moveByChar(range, forward, start => {\n    step(start);\n    return step;\n  });\n  if (segmenter && cat == CharCategory.Word && end.from == range.from + steps * (forward ? 1 : -1)) {\n    let from = Math.min(range.head, end.head),\n      to = Math.max(range.head, end.head);\n    let skipped = view.state.sliceDoc(from, to);\n    if (skipped.length > 1 && /[\\u4E00-\\uffff]/.test(skipped)) {\n      let segments = Array.from(segmenter.segment(skipped));\n      if (segments.length > 1) {\n        if (forward) return EditorSelection.cursor(range.head + segments[1].index, -1);\n        return EditorSelection.cursor(end.head + segments[segments.length - 1].index, 1);\n      }\n    }\n  }\n  return end;\n}\nfunction cursorBySubword(view, forward) {\n  return moveSel(view, range => range.empty ? moveBySubword(view, range, forward) : rangeEnd(range, forward));\n}\n/**\nMove the selection one group or camel-case subword forward.\n*/\nconst cursorSubwordForward = view => cursorBySubword(view, true);\n/**\nMove the selection one group or camel-case subword backward.\n*/\nconst cursorSubwordBackward = view => cursorBySubword(view, false);\nfunction interestingNode(state, node, bracketProp) {\n  if (node.type.prop(bracketProp)) return true;\n  let len = node.to - node.from;\n  return len && (len > 2 || /[^\\s,.;:]/.test(state.sliceDoc(node.from, node.to))) || node.firstChild;\n}\nfunction moveBySyntax(state, start, forward) {\n  let pos = syntaxTree(state).resolveInner(start.head);\n  let bracketProp = forward ? NodeProp.closedBy : NodeProp.openedBy;\n  // Scan forward through child nodes to see if there's an interesting\n  // node ahead.\n  for (let at = start.head;;) {\n    let next = forward ? pos.childAfter(at) : pos.childBefore(at);\n    if (!next) break;\n    if (interestingNode(state, next, bracketProp)) pos = next;else at = forward ? next.to : next.from;\n  }\n  let bracket = pos.type.prop(bracketProp),\n    match,\n    newPos;\n  if (bracket && (match = forward ? matchBrackets(state, pos.from, 1) : matchBrackets(state, pos.to, -1)) && match.matched) newPos = forward ? match.end.to : match.end.from;else newPos = forward ? pos.to : pos.from;\n  return EditorSelection.cursor(newPos, forward ? -1 : 1);\n}\n/**\nMove the cursor over the next syntactic element to the left.\n*/\nconst cursorSyntaxLeft = view => moveSel(view, range => moveBySyntax(view.state, range, !ltrAtCursor(view)));\n/**\nMove the cursor over the next syntactic element to the right.\n*/\nconst cursorSyntaxRight = view => moveSel(view, range => moveBySyntax(view.state, range, ltrAtCursor(view)));\nfunction cursorByLine(view, forward) {\n  return moveSel(view, range => {\n    if (!range.empty) return rangeEnd(range, forward);\n    let moved = view.moveVertically(range, forward);\n    return moved.head != range.head ? moved : view.moveToLineBoundary(range, forward);\n  });\n}\n/**\nMove the selection one line up.\n*/\nconst cursorLineUp = view => cursorByLine(view, false);\n/**\nMove the selection one line down.\n*/\nconst cursorLineDown = view => cursorByLine(view, true);\nfunction pageInfo(view) {\n  let selfScroll = view.scrollDOM.clientHeight < view.scrollDOM.scrollHeight - 2;\n  let marginTop = 0,\n    marginBottom = 0,\n    height;\n  if (selfScroll) {\n    for (let source of view.state.facet(EditorView.scrollMargins)) {\n      let margins = source(view);\n      if (margins === null || margins === void 0 ? void 0 : margins.top) marginTop = Math.max(margins === null || margins === void 0 ? void 0 : margins.top, marginTop);\n      if (margins === null || margins === void 0 ? void 0 : margins.bottom) marginBottom = Math.max(margins === null || margins === void 0 ? void 0 : margins.bottom, marginBottom);\n    }\n    height = view.scrollDOM.clientHeight - marginTop - marginBottom;\n  } else {\n    height = (view.dom.ownerDocument.defaultView || window).innerHeight;\n  }\n  return {\n    marginTop,\n    marginBottom,\n    selfScroll,\n    height: Math.max(view.defaultLineHeight, height - 5)\n  };\n}\nfunction cursorByPage(view, forward) {\n  let page = pageInfo(view);\n  let {\n      state\n    } = view,\n    selection = updateSel(state.selection, range => {\n      return range.empty ? view.moveVertically(range, forward, page.height) : rangeEnd(range, forward);\n    });\n  if (selection.eq(state.selection)) return false;\n  let effect;\n  if (page.selfScroll) {\n    let startPos = view.coordsAtPos(state.selection.main.head);\n    let scrollRect = view.scrollDOM.getBoundingClientRect();\n    let scrollTop = scrollRect.top + page.marginTop,\n      scrollBottom = scrollRect.bottom - page.marginBottom;\n    if (startPos && startPos.top > scrollTop && startPos.bottom < scrollBottom) effect = EditorView.scrollIntoView(selection.main.head, {\n      y: \"start\",\n      yMargin: startPos.top - scrollTop\n    });\n  }\n  view.dispatch(setSel(state, selection), {\n    effects: effect\n  });\n  return true;\n}\n/**\nMove the selection one page up.\n*/\nconst cursorPageUp = view => cursorByPage(view, false);\n/**\nMove the selection one page down.\n*/\nconst cursorPageDown = view => cursorByPage(view, true);\nfunction moveByLineBoundary(view, start, forward) {\n  let line = view.lineBlockAt(start.head),\n    moved = view.moveToLineBoundary(start, forward);\n  if (moved.head == start.head && moved.head != (forward ? line.to : line.from)) moved = view.moveToLineBoundary(start, forward, false);\n  if (!forward && moved.head == line.from && line.length) {\n    let space = /^\\s*/.exec(view.state.sliceDoc(line.from, Math.min(line.from + 100, line.to)))[0].length;\n    if (space && start.head != line.from + space) moved = EditorSelection.cursor(line.from + space);\n  }\n  return moved;\n}\n/**\nMove the selection to the next line wrap point, or to the end of\nthe line if there isn't one left on this line.\n*/\nconst cursorLineBoundaryForward = view => moveSel(view, range => moveByLineBoundary(view, range, true));\n/**\nMove the selection to previous line wrap point, or failing that to\nthe start of the line. If the line is indented, and the cursor\nisn't already at the end of the indentation, this will move to the\nend of the indentation instead of the start of the line.\n*/\nconst cursorLineBoundaryBackward = view => moveSel(view, range => moveByLineBoundary(view, range, false));\n/**\nMove the selection one line wrap point to the left.\n*/\nconst cursorLineBoundaryLeft = view => moveSel(view, range => moveByLineBoundary(view, range, !ltrAtCursor(view)));\n/**\nMove the selection one line wrap point to the right.\n*/\nconst cursorLineBoundaryRight = view => moveSel(view, range => moveByLineBoundary(view, range, ltrAtCursor(view)));\n/**\nMove the selection to the start of the line.\n*/\nconst cursorLineStart = view => moveSel(view, range => EditorSelection.cursor(view.lineBlockAt(range.head).from, 1));\n/**\nMove the selection to the end of the line.\n*/\nconst cursorLineEnd = view => moveSel(view, range => EditorSelection.cursor(view.lineBlockAt(range.head).to, -1));\nfunction toMatchingBracket(state, dispatch, extend) {\n  let found = false,\n    selection = updateSel(state.selection, range => {\n      let matching = matchBrackets(state, range.head, -1) || matchBrackets(state, range.head, 1) || range.head > 0 && matchBrackets(state, range.head - 1, 1) || range.head < state.doc.length && matchBrackets(state, range.head + 1, -1);\n      if (!matching || !matching.end) return range;\n      found = true;\n      let head = matching.start.from == range.head ? matching.end.to : matching.end.from;\n      return extend ? EditorSelection.range(range.anchor, head) : EditorSelection.cursor(head);\n    });\n  if (!found) return false;\n  dispatch(setSel(state, selection));\n  return true;\n}\n/**\nMove the selection to the bracket matching the one it is currently\non, if any.\n*/\nconst cursorMatchingBracket = ({\n  state,\n  dispatch\n}) => toMatchingBracket(state, dispatch, false);\n/**\nExtend the selection to the bracket matching the one the selection\nhead is currently on, if any.\n*/\nconst selectMatchingBracket = ({\n  state,\n  dispatch\n}) => toMatchingBracket(state, dispatch, true);\nfunction extendSel(target, how) {\n  let selection = updateSel(target.state.selection, range => {\n    let head = how(range);\n    return EditorSelection.range(range.anchor, head.head, head.goalColumn, head.bidiLevel || undefined);\n  });\n  if (selection.eq(target.state.selection)) return false;\n  target.dispatch(setSel(target.state, selection));\n  return true;\n}\nfunction selectByChar(view, forward) {\n  return extendSel(view, range => view.moveByChar(range, forward));\n}\n/**\nMove the selection head one character to the left, while leaving\nthe anchor in place.\n*/\nconst selectCharLeft = view => selectByChar(view, !ltrAtCursor(view));\n/**\nMove the selection head one character to the right.\n*/\nconst selectCharRight = view => selectByChar(view, ltrAtCursor(view));\n/**\nMove the selection head one character forward.\n*/\nconst selectCharForward = view => selectByChar(view, true);\n/**\nMove the selection head one character backward.\n*/\nconst selectCharBackward = view => selectByChar(view, false);\n/**\nMove the selection head one character forward by logical\n(non-direction aware) string index order.\n*/\nconst selectCharForwardLogical = target => extendSel(target, range => byCharLogical(target.state, range, true));\n/**\nMove the selection head one character backward by logical string\nindex order.\n*/\nconst selectCharBackwardLogical = target => extendSel(target, range => byCharLogical(target.state, range, false));\nfunction selectByGroup(view, forward) {\n  return extendSel(view, range => view.moveByGroup(range, forward));\n}\n/**\nMove the selection head one [group](https://codemirror.net/6/docs/ref/#commands.cursorGroupLeft) to\nthe left.\n*/\nconst selectGroupLeft = view => selectByGroup(view, !ltrAtCursor(view));\n/**\nMove the selection head one group to the right.\n*/\nconst selectGroupRight = view => selectByGroup(view, ltrAtCursor(view));\n/**\nMove the selection head one group forward.\n*/\nconst selectGroupForward = view => selectByGroup(view, true);\n/**\nMove the selection head one group backward.\n*/\nconst selectGroupBackward = view => selectByGroup(view, false);\n/**\nMove the selection head one group forward in the default Windows\nstyle, skipping to the start of the next group.\n*/\nconst selectGroupForwardWin = view => {\n  return extendSel(view, range => view.moveByChar(range, true, start => toGroupStart(view, range.head, start)));\n};\nfunction selectBySubword(view, forward) {\n  return extendSel(view, range => moveBySubword(view, range, forward));\n}\n/**\nMove the selection head one group or camel-case subword forward.\n*/\nconst selectSubwordForward = view => selectBySubword(view, true);\n/**\nMove the selection head one group or subword backward.\n*/\nconst selectSubwordBackward = view => selectBySubword(view, false);\n/**\nMove the selection head over the next syntactic element to the left.\n*/\nconst selectSyntaxLeft = view => extendSel(view, range => moveBySyntax(view.state, range, !ltrAtCursor(view)));\n/**\nMove the selection head over the next syntactic element to the right.\n*/\nconst selectSyntaxRight = view => extendSel(view, range => moveBySyntax(view.state, range, ltrAtCursor(view)));\nfunction selectByLine(view, forward) {\n  return extendSel(view, range => view.moveVertically(range, forward));\n}\n/**\nMove the selection head one line up.\n*/\nconst selectLineUp = view => selectByLine(view, false);\n/**\nMove the selection head one line down.\n*/\nconst selectLineDown = view => selectByLine(view, true);\nfunction selectByPage(view, forward) {\n  return extendSel(view, range => view.moveVertically(range, forward, pageInfo(view).height));\n}\n/**\nMove the selection head one page up.\n*/\nconst selectPageUp = view => selectByPage(view, false);\n/**\nMove the selection head one page down.\n*/\nconst selectPageDown = view => selectByPage(view, true);\n/**\nMove the selection head to the next line boundary.\n*/\nconst selectLineBoundaryForward = view => extendSel(view, range => moveByLineBoundary(view, range, true));\n/**\nMove the selection head to the previous line boundary.\n*/\nconst selectLineBoundaryBackward = view => extendSel(view, range => moveByLineBoundary(view, range, false));\n/**\nMove the selection head one line boundary to the left.\n*/\nconst selectLineBoundaryLeft = view => extendSel(view, range => moveByLineBoundary(view, range, !ltrAtCursor(view)));\n/**\nMove the selection head one line boundary to the right.\n*/\nconst selectLineBoundaryRight = view => extendSel(view, range => moveByLineBoundary(view, range, ltrAtCursor(view)));\n/**\nMove the selection head to the start of the line.\n*/\nconst selectLineStart = view => extendSel(view, range => EditorSelection.cursor(view.lineBlockAt(range.head).from));\n/**\nMove the selection head to the end of the line.\n*/\nconst selectLineEnd = view => extendSel(view, range => EditorSelection.cursor(view.lineBlockAt(range.head).to));\n/**\nMove the selection to the start of the document.\n*/\nconst cursorDocStart = ({\n  state,\n  dispatch\n}) => {\n  dispatch(setSel(state, {\n    anchor: 0\n  }));\n  return true;\n};\n/**\nMove the selection to the end of the document.\n*/\nconst cursorDocEnd = ({\n  state,\n  dispatch\n}) => {\n  dispatch(setSel(state, {\n    anchor: state.doc.length\n  }));\n  return true;\n};\n/**\nMove the selection head to the start of the document.\n*/\nconst selectDocStart = ({\n  state,\n  dispatch\n}) => {\n  dispatch(setSel(state, {\n    anchor: state.selection.main.anchor,\n    head: 0\n  }));\n  return true;\n};\n/**\nMove the selection head to the end of the document.\n*/\nconst selectDocEnd = ({\n  state,\n  dispatch\n}) => {\n  dispatch(setSel(state, {\n    anchor: state.selection.main.anchor,\n    head: state.doc.length\n  }));\n  return true;\n};\n/**\nSelect the entire document.\n*/\nconst selectAll = ({\n  state,\n  dispatch\n}) => {\n  dispatch(state.update({\n    selection: {\n      anchor: 0,\n      head: state.doc.length\n    },\n    userEvent: \"select\"\n  }));\n  return true;\n};\n/**\nExpand the selection to cover entire lines.\n*/\nconst selectLine = ({\n  state,\n  dispatch\n}) => {\n  let ranges = selectedLineBlocks(state).map(({\n    from,\n    to\n  }) => EditorSelection.range(from, Math.min(to + 1, state.doc.length)));\n  dispatch(state.update({\n    selection: EditorSelection.create(ranges),\n    userEvent: \"select\"\n  }));\n  return true;\n};\n/**\nSelect the next syntactic construct that is larger than the\nselection. Note that this will only work insofar as the language\n[provider](https://codemirror.net/6/docs/ref/#language.language) you use builds up a full\nsyntax tree.\n*/\nconst selectParentSyntax = ({\n  state,\n  dispatch\n}) => {\n  let selection = updateSel(state.selection, range => {\n    let tree = syntaxTree(state),\n      stack = tree.resolveStack(range.from, 1);\n    if (range.empty) {\n      let stackBefore = tree.resolveStack(range.from, -1);\n      if (stackBefore.node.from >= stack.node.from && stackBefore.node.to <= stack.node.to) stack = stackBefore;\n    }\n    for (let cur = stack; cur; cur = cur.next) {\n      let {\n        node\n      } = cur;\n      if ((node.from < range.from && node.to >= range.to || node.to > range.to && node.from <= range.from) && cur.next) return EditorSelection.range(node.to, node.from);\n    }\n    return range;\n  });\n  if (selection.eq(state.selection)) return false;\n  dispatch(setSel(state, selection));\n  return true;\n};\n/**\nSimplify the current selection. When multiple ranges are selected,\nreduce it to its main range. Otherwise, if the selection is\nnon-empty, convert it to a cursor selection.\n*/\nconst simplifySelection = ({\n  state,\n  dispatch\n}) => {\n  let cur = state.selection,\n    selection = null;\n  if (cur.ranges.length > 1) selection = EditorSelection.create([cur.main]);else if (!cur.main.empty) selection = EditorSelection.create([EditorSelection.cursor(cur.main.head)]);\n  if (!selection) return false;\n  dispatch(setSel(state, selection));\n  return true;\n};\nfunction deleteBy(target, by) {\n  if (target.state.readOnly) return false;\n  let event = \"delete.selection\",\n    {\n      state\n    } = target;\n  let changes = state.changeByRange(range => {\n    let {\n      from,\n      to\n    } = range;\n    if (from == to) {\n      let towards = by(range);\n      if (towards < from) {\n        event = \"delete.backward\";\n        towards = skipAtomic(target, towards, false);\n      } else if (towards > from) {\n        event = \"delete.forward\";\n        towards = skipAtomic(target, towards, true);\n      }\n      from = Math.min(from, towards);\n      to = Math.max(to, towards);\n    } else {\n      from = skipAtomic(target, from, false);\n      to = skipAtomic(target, to, true);\n    }\n    return from == to ? {\n      range\n    } : {\n      changes: {\n        from,\n        to\n      },\n      range: EditorSelection.cursor(from, from < range.head ? -1 : 1)\n    };\n  });\n  if (changes.changes.empty) return false;\n  target.dispatch(state.update(changes, {\n    scrollIntoView: true,\n    userEvent: event,\n    effects: event == \"delete.selection\" ? EditorView.announce.of(state.phrase(\"Selection deleted\")) : undefined\n  }));\n  return true;\n}\nfunction skipAtomic(target, pos, forward) {\n  if (target instanceof EditorView) for (let ranges of target.state.facet(EditorView.atomicRanges).map(f => f(target))) ranges.between(pos, pos, (from, to) => {\n    if (from < pos && to > pos) pos = forward ? to : from;\n  });\n  return pos;\n}\nconst deleteByChar = (target, forward, byIndentUnit) => deleteBy(target, range => {\n  let pos = range.from,\n    {\n      state\n    } = target,\n    line = state.doc.lineAt(pos),\n    before,\n    targetPos;\n  if (byIndentUnit && !forward && pos > line.from && pos < line.from + 200 && !/[^ \\t]/.test(before = line.text.slice(0, pos - line.from))) {\n    if (before[before.length - 1] == \"\\t\") return pos - 1;\n    let col = countColumn(before, state.tabSize),\n      drop = col % getIndentUnit(state) || getIndentUnit(state);\n    for (let i = 0; i < drop && before[before.length - 1 - i] == \" \"; i++) pos--;\n    targetPos = pos;\n  } else {\n    targetPos = findClusterBreak(line.text, pos - line.from, forward, forward) + line.from;\n    if (targetPos == pos && line.number != (forward ? state.doc.lines : 1)) targetPos += forward ? 1 : -1;else if (!forward && /[\\ufe00-\\ufe0f]/.test(line.text.slice(targetPos - line.from, pos - line.from))) targetPos = findClusterBreak(line.text, targetPos - line.from, false, false) + line.from;\n  }\n  return targetPos;\n});\n/**\nDelete the selection, or, for cursor selections, the character or\nindentation unit before the cursor.\n*/\nconst deleteCharBackward = view => deleteByChar(view, false, true);\n/**\nDelete the selection or the character before the cursor. Does not\nimplement any extended behavior like deleting whole indentation\nunits in one go.\n*/\nconst deleteCharBackwardStrict = view => deleteByChar(view, false, false);\n/**\nDelete the selection or the character after the cursor.\n*/\nconst deleteCharForward = view => deleteByChar(view, true, false);\nconst deleteByGroup = (target, forward) => deleteBy(target, range => {\n  let pos = range.head,\n    {\n      state\n    } = target,\n    line = state.doc.lineAt(pos);\n  let categorize = state.charCategorizer(pos);\n  for (let cat = null;;) {\n    if (pos == (forward ? line.to : line.from)) {\n      if (pos == range.head && line.number != (forward ? state.doc.lines : 1)) pos += forward ? 1 : -1;\n      break;\n    }\n    let next = findClusterBreak(line.text, pos - line.from, forward) + line.from;\n    let nextChar = line.text.slice(Math.min(pos, next) - line.from, Math.max(pos, next) - line.from);\n    let nextCat = categorize(nextChar);\n    if (cat != null && nextCat != cat) break;\n    if (nextChar != \" \" || pos != range.head) cat = nextCat;\n    pos = next;\n  }\n  return pos;\n});\n/**\nDelete the selection or backward until the end of the next\n[group](https://codemirror.net/6/docs/ref/#view.EditorView.moveByGroup), only skipping groups of\nwhitespace when they consist of a single space.\n*/\nconst deleteGroupBackward = target => deleteByGroup(target, false);\n/**\nDelete the selection or forward until the end of the next group.\n*/\nconst deleteGroupForward = target => deleteByGroup(target, true);\n/**\nDelete the selection, or, if it is a cursor selection, delete to\nthe end of the line. If the cursor is directly at the end of the\nline, delete the line break after it.\n*/\nconst deleteToLineEnd = view => deleteBy(view, range => {\n  let lineEnd = view.lineBlockAt(range.head).to;\n  return range.head < lineEnd ? lineEnd : Math.min(view.state.doc.length, range.head + 1);\n});\n/**\nDelete the selection, or, if it is a cursor selection, delete to\nthe start of the line. If the cursor is directly at the start of the\nline, delete the line break before it.\n*/\nconst deleteToLineStart = view => deleteBy(view, range => {\n  let lineStart = view.lineBlockAt(range.head).from;\n  return range.head > lineStart ? lineStart : Math.max(0, range.head - 1);\n});\n/**\nDelete the selection, or, if it is a cursor selection, delete to\nthe start of the line or the next line wrap before the cursor.\n*/\nconst deleteLineBoundaryBackward = view => deleteBy(view, range => {\n  let lineStart = view.moveToLineBoundary(range, false).head;\n  return range.head > lineStart ? lineStart : Math.max(0, range.head - 1);\n});\n/**\nDelete the selection, or, if it is a cursor selection, delete to\nthe end of the line or the next line wrap after the cursor.\n*/\nconst deleteLineBoundaryForward = view => deleteBy(view, range => {\n  let lineStart = view.moveToLineBoundary(range, true).head;\n  return range.head < lineStart ? lineStart : Math.min(view.state.doc.length, range.head + 1);\n});\n/**\nDelete all whitespace directly before a line end from the\ndocument.\n*/\nconst deleteTrailingWhitespace = ({\n  state,\n  dispatch\n}) => {\n  if (state.readOnly) return false;\n  let changes = [];\n  for (let pos = 0, prev = \"\", iter = state.doc.iter();;) {\n    iter.next();\n    if (iter.lineBreak || iter.done) {\n      let trailing = prev.search(/\\s+$/);\n      if (trailing > -1) changes.push({\n        from: pos - (prev.length - trailing),\n        to: pos\n      });\n      if (iter.done) break;\n      prev = \"\";\n    } else {\n      prev = iter.value;\n    }\n    pos += iter.value.length;\n  }\n  if (!changes.length) return false;\n  dispatch(state.update({\n    changes,\n    userEvent: \"delete\"\n  }));\n  return true;\n};\n/**\nReplace each selection range with a line break, leaving the cursor\non the line before the break.\n*/\nconst splitLine = ({\n  state,\n  dispatch\n}) => {\n  if (state.readOnly) return false;\n  let changes = state.changeByRange(range => {\n    return {\n      changes: {\n        from: range.from,\n        to: range.to,\n        insert: Text.of([\"\", \"\"])\n      },\n      range: EditorSelection.cursor(range.from)\n    };\n  });\n  dispatch(state.update(changes, {\n    scrollIntoView: true,\n    userEvent: \"input\"\n  }));\n  return true;\n};\n/**\nFlip the characters before and after the cursor(s).\n*/\nconst transposeChars = ({\n  state,\n  dispatch\n}) => {\n  if (state.readOnly) return false;\n  let changes = state.changeByRange(range => {\n    if (!range.empty || range.from == 0 || range.from == state.doc.length) return {\n      range\n    };\n    let pos = range.from,\n      line = state.doc.lineAt(pos);\n    let from = pos == line.from ? pos - 1 : findClusterBreak(line.text, pos - line.from, false) + line.from;\n    let to = pos == line.to ? pos + 1 : findClusterBreak(line.text, pos - line.from, true) + line.from;\n    return {\n      changes: {\n        from,\n        to,\n        insert: state.doc.slice(pos, to).append(state.doc.slice(from, pos))\n      },\n      range: EditorSelection.cursor(to)\n    };\n  });\n  if (changes.changes.empty) return false;\n  dispatch(state.update(changes, {\n    scrollIntoView: true,\n    userEvent: \"move.character\"\n  }));\n  return true;\n};\nfunction selectedLineBlocks(state) {\n  let blocks = [],\n    upto = -1;\n  for (let range of state.selection.ranges) {\n    let startLine = state.doc.lineAt(range.from),\n      endLine = state.doc.lineAt(range.to);\n    if (!range.empty && range.to == endLine.from) endLine = state.doc.lineAt(range.to - 1);\n    if (upto >= startLine.number) {\n      let prev = blocks[blocks.length - 1];\n      prev.to = endLine.to;\n      prev.ranges.push(range);\n    } else {\n      blocks.push({\n        from: startLine.from,\n        to: endLine.to,\n        ranges: [range]\n      });\n    }\n    upto = endLine.number + 1;\n  }\n  return blocks;\n}\nfunction moveLine(state, dispatch, forward) {\n  if (state.readOnly) return false;\n  let changes = [],\n    ranges = [];\n  for (let block of selectedLineBlocks(state)) {\n    if (forward ? block.to == state.doc.length : block.from == 0) continue;\n    let nextLine = state.doc.lineAt(forward ? block.to + 1 : block.from - 1);\n    let size = nextLine.length + 1;\n    if (forward) {\n      changes.push({\n        from: block.to,\n        to: nextLine.to\n      }, {\n        from: block.from,\n        insert: nextLine.text + state.lineBreak\n      });\n      for (let r of block.ranges) ranges.push(EditorSelection.range(Math.min(state.doc.length, r.anchor + size), Math.min(state.doc.length, r.head + size)));\n    } else {\n      changes.push({\n        from: nextLine.from,\n        to: block.from\n      }, {\n        from: block.to,\n        insert: state.lineBreak + nextLine.text\n      });\n      for (let r of block.ranges) ranges.push(EditorSelection.range(r.anchor - size, r.head - size));\n    }\n  }\n  if (!changes.length) return false;\n  dispatch(state.update({\n    changes,\n    scrollIntoView: true,\n    selection: EditorSelection.create(ranges, state.selection.mainIndex),\n    userEvent: \"move.line\"\n  }));\n  return true;\n}\n/**\nMove the selected lines up one line.\n*/\nconst moveLineUp = ({\n  state,\n  dispatch\n}) => moveLine(state, dispatch, false);\n/**\nMove the selected lines down one line.\n*/\nconst moveLineDown = ({\n  state,\n  dispatch\n}) => moveLine(state, dispatch, true);\nfunction copyLine(state, dispatch, forward) {\n  if (state.readOnly) return false;\n  let changes = [];\n  for (let block of selectedLineBlocks(state)) {\n    if (forward) changes.push({\n      from: block.from,\n      insert: state.doc.slice(block.from, block.to) + state.lineBreak\n    });else changes.push({\n      from: block.to,\n      insert: state.lineBreak + state.doc.slice(block.from, block.to)\n    });\n  }\n  dispatch(state.update({\n    changes,\n    scrollIntoView: true,\n    userEvent: \"input.copyline\"\n  }));\n  return true;\n}\n/**\nCreate a copy of the selected lines. Keep the selection in the top copy.\n*/\nconst copyLineUp = ({\n  state,\n  dispatch\n}) => copyLine(state, dispatch, false);\n/**\nCreate a copy of the selected lines. Keep the selection in the bottom copy.\n*/\nconst copyLineDown = ({\n  state,\n  dispatch\n}) => copyLine(state, dispatch, true);\n/**\nDelete selected lines.\n*/\nconst deleteLine = view => {\n  if (view.state.readOnly) return false;\n  let {\n      state\n    } = view,\n    changes = state.changes(selectedLineBlocks(state).map(({\n      from,\n      to\n    }) => {\n      if (from > 0) from--;else if (to < state.doc.length) to++;\n      return {\n        from,\n        to\n      };\n    }));\n  let selection = updateSel(state.selection, range => {\n    let dist = undefined;\n    if (view.lineWrapping) {\n      let block = view.lineBlockAt(range.head),\n        pos = view.coordsAtPos(range.head, range.assoc || 1);\n      if (pos) dist = block.bottom + view.documentTop - pos.bottom + view.defaultLineHeight / 2;\n    }\n    return view.moveVertically(range, true, dist);\n  }).map(changes);\n  view.dispatch({\n    changes,\n    selection,\n    scrollIntoView: true,\n    userEvent: \"delete.line\"\n  });\n  return true;\n};\n/**\nReplace the selection with a newline.\n*/\nconst insertNewline = ({\n  state,\n  dispatch\n}) => {\n  dispatch(state.update(state.replaceSelection(state.lineBreak), {\n    scrollIntoView: true,\n    userEvent: \"input\"\n  }));\n  return true;\n};\n/**\nReplace the selection with a newline and the same amount of\nindentation as the line above.\n*/\nconst insertNewlineKeepIndent = ({\n  state,\n  dispatch\n}) => {\n  dispatch(state.update(state.changeByRange(range => {\n    let indent = /^\\s*/.exec(state.doc.lineAt(range.from).text)[0];\n    return {\n      changes: {\n        from: range.from,\n        to: range.to,\n        insert: state.lineBreak + indent\n      },\n      range: EditorSelection.cursor(range.from + indent.length + 1)\n    };\n  }), {\n    scrollIntoView: true,\n    userEvent: \"input\"\n  }));\n  return true;\n};\nfunction isBetweenBrackets(state, pos) {\n  if (/\\(\\)|\\[\\]|\\{\\}/.test(state.sliceDoc(pos - 1, pos + 1))) return {\n    from: pos,\n    to: pos\n  };\n  let context = syntaxTree(state).resolveInner(pos);\n  let before = context.childBefore(pos),\n    after = context.childAfter(pos),\n    closedBy;\n  if (before && after && before.to <= pos && after.from >= pos && (closedBy = before.type.prop(NodeProp.closedBy)) && closedBy.indexOf(after.name) > -1 && state.doc.lineAt(before.to).from == state.doc.lineAt(after.from).from && !/\\S/.test(state.sliceDoc(before.to, after.from))) return {\n    from: before.to,\n    to: after.from\n  };\n  return null;\n}\n/**\nReplace the selection with a newline and indent the newly created\nline(s). If the current line consists only of whitespace, this\nwill also delete that whitespace. When the cursor is between\nmatching brackets, an additional newline will be inserted after\nthe cursor.\n*/\nconst insertNewlineAndIndent = /*@__PURE__*/newlineAndIndent(false);\n/**\nCreate a blank, indented line below the current line.\n*/\nconst insertBlankLine = /*@__PURE__*/newlineAndIndent(true);\nfunction newlineAndIndent(atEof) {\n  return ({\n    state,\n    dispatch\n  }) => {\n    if (state.readOnly) return false;\n    let changes = state.changeByRange(range => {\n      let {\n          from,\n          to\n        } = range,\n        line = state.doc.lineAt(from);\n      let explode = !atEof && from == to && isBetweenBrackets(state, from);\n      if (atEof) from = to = (to <= line.to ? line : state.doc.lineAt(to)).to;\n      let cx = new IndentContext(state, {\n        simulateBreak: from,\n        simulateDoubleBreak: !!explode\n      });\n      let indent = getIndentation(cx, from);\n      if (indent == null) indent = countColumn(/^\\s*/.exec(state.doc.lineAt(from).text)[0], state.tabSize);\n      while (to < line.to && /\\s/.test(line.text[to - line.from])) to++;\n      if (explode) ({\n        from,\n        to\n      } = explode);else if (from > line.from && from < line.from + 100 && !/\\S/.test(line.text.slice(0, from))) from = line.from;\n      let insert = [\"\", indentString(state, indent)];\n      if (explode) insert.push(indentString(state, cx.lineIndent(line.from, -1)));\n      return {\n        changes: {\n          from,\n          to,\n          insert: Text.of(insert)\n        },\n        range: EditorSelection.cursor(from + 1 + insert[1].length)\n      };\n    });\n    dispatch(state.update(changes, {\n      scrollIntoView: true,\n      userEvent: \"input\"\n    }));\n    return true;\n  };\n}\nfunction changeBySelectedLine(state, f) {\n  let atLine = -1;\n  return state.changeByRange(range => {\n    let changes = [];\n    for (let pos = range.from; pos <= range.to;) {\n      let line = state.doc.lineAt(pos);\n      if (line.number > atLine && (range.empty || range.to > line.from)) {\n        f(line, changes, range);\n        atLine = line.number;\n      }\n      pos = line.to + 1;\n    }\n    let changeSet = state.changes(changes);\n    return {\n      changes,\n      range: EditorSelection.range(changeSet.mapPos(range.anchor, 1), changeSet.mapPos(range.head, 1))\n    };\n  });\n}\n/**\nAuto-indent the selected lines. This uses the [indentation service\nfacet](https://codemirror.net/6/docs/ref/#language.indentService) as source for auto-indent\ninformation.\n*/\nconst indentSelection = ({\n  state,\n  dispatch\n}) => {\n  if (state.readOnly) return false;\n  let updated = Object.create(null);\n  let context = new IndentContext(state, {\n    overrideIndentation: start => {\n      let found = updated[start];\n      return found == null ? -1 : found;\n    }\n  });\n  let changes = changeBySelectedLine(state, (line, changes, range) => {\n    let indent = getIndentation(context, line.from);\n    if (indent == null) return;\n    if (!/\\S/.test(line.text)) indent = 0;\n    let cur = /^\\s*/.exec(line.text)[0];\n    let norm = indentString(state, indent);\n    if (cur != norm || range.from < line.from + cur.length) {\n      updated[line.from] = indent;\n      changes.push({\n        from: line.from,\n        to: line.from + cur.length,\n        insert: norm\n      });\n    }\n  });\n  if (!changes.changes.empty) dispatch(state.update(changes, {\n    userEvent: \"indent\"\n  }));\n  return true;\n};\n/**\nAdd a [unit](https://codemirror.net/6/docs/ref/#language.indentUnit) of indentation to all selected\nlines.\n*/\nconst indentMore = ({\n  state,\n  dispatch\n}) => {\n  if (state.readOnly) return false;\n  dispatch(state.update(changeBySelectedLine(state, (line, changes) => {\n    changes.push({\n      from: line.from,\n      insert: state.facet(indentUnit)\n    });\n  }), {\n    userEvent: \"input.indent\"\n  }));\n  return true;\n};\n/**\nRemove a [unit](https://codemirror.net/6/docs/ref/#language.indentUnit) of indentation from all\nselected lines.\n*/\nconst indentLess = ({\n  state,\n  dispatch\n}) => {\n  if (state.readOnly) return false;\n  dispatch(state.update(changeBySelectedLine(state, (line, changes) => {\n    let space = /^\\s*/.exec(line.text)[0];\n    if (!space) return;\n    let col = countColumn(space, state.tabSize),\n      keep = 0;\n    let insert = indentString(state, Math.max(0, col - getIndentUnit(state)));\n    while (keep < space.length && keep < insert.length && space.charCodeAt(keep) == insert.charCodeAt(keep)) keep++;\n    changes.push({\n      from: line.from + keep,\n      to: line.from + space.length,\n      insert: insert.slice(keep)\n    });\n  }), {\n    userEvent: \"delete.dedent\"\n  }));\n  return true;\n};\n/**\nEnables or disables\n[tab-focus mode](https://codemirror.net/6/docs/ref/#view.EditorView.setTabFocusMode). While on, this\nprevents the editor's key bindings from capturing Tab or\nShift-Tab, making it possible for the user to move focus out of\nthe editor with the keyboard.\n*/\nconst toggleTabFocusMode = view => {\n  view.setTabFocusMode();\n  return true;\n};\n/**\nTemporarily enables [tab-focus\nmode](https://codemirror.net/6/docs/ref/#view.EditorView.setTabFocusMode) for two seconds or until\nanother key is pressed.\n*/\nconst temporarilySetTabFocusMode = view => {\n  view.setTabFocusMode(2000);\n  return true;\n};\n/**\nInsert a tab character at the cursor or, if something is selected,\nuse [`indentMore`](https://codemirror.net/6/docs/ref/#commands.indentMore) to indent the entire\nselection.\n*/\nconst insertTab = ({\n  state,\n  dispatch\n}) => {\n  if (state.selection.ranges.some(r => !r.empty)) return indentMore({\n    state,\n    dispatch\n  });\n  dispatch(state.update(state.replaceSelection(\"\\t\"), {\n    scrollIntoView: true,\n    userEvent: \"input\"\n  }));\n  return true;\n};\n/**\nArray of key bindings containing the Emacs-style bindings that are\navailable on macOS by default.\n\n - Ctrl-b: [`cursorCharLeft`](https://codemirror.net/6/docs/ref/#commands.cursorCharLeft) ([`selectCharLeft`](https://codemirror.net/6/docs/ref/#commands.selectCharLeft) with Shift)\n - Ctrl-f: [`cursorCharRight`](https://codemirror.net/6/docs/ref/#commands.cursorCharRight) ([`selectCharRight`](https://codemirror.net/6/docs/ref/#commands.selectCharRight) with Shift)\n - Ctrl-p: [`cursorLineUp`](https://codemirror.net/6/docs/ref/#commands.cursorLineUp) ([`selectLineUp`](https://codemirror.net/6/docs/ref/#commands.selectLineUp) with Shift)\n - Ctrl-n: [`cursorLineDown`](https://codemirror.net/6/docs/ref/#commands.cursorLineDown) ([`selectLineDown`](https://codemirror.net/6/docs/ref/#commands.selectLineDown) with Shift)\n - Ctrl-a: [`cursorLineStart`](https://codemirror.net/6/docs/ref/#commands.cursorLineStart) ([`selectLineStart`](https://codemirror.net/6/docs/ref/#commands.selectLineStart) with Shift)\n - Ctrl-e: [`cursorLineEnd`](https://codemirror.net/6/docs/ref/#commands.cursorLineEnd) ([`selectLineEnd`](https://codemirror.net/6/docs/ref/#commands.selectLineEnd) with Shift)\n - Ctrl-d: [`deleteCharForward`](https://codemirror.net/6/docs/ref/#commands.deleteCharForward)\n - Ctrl-h: [`deleteCharBackward`](https://codemirror.net/6/docs/ref/#commands.deleteCharBackward)\n - Ctrl-k: [`deleteToLineEnd`](https://codemirror.net/6/docs/ref/#commands.deleteToLineEnd)\n - Ctrl-Alt-h: [`deleteGroupBackward`](https://codemirror.net/6/docs/ref/#commands.deleteGroupBackward)\n - Ctrl-o: [`splitLine`](https://codemirror.net/6/docs/ref/#commands.splitLine)\n - Ctrl-t: [`transposeChars`](https://codemirror.net/6/docs/ref/#commands.transposeChars)\n - Ctrl-v: [`cursorPageDown`](https://codemirror.net/6/docs/ref/#commands.cursorPageDown)\n - Alt-v: [`cursorPageUp`](https://codemirror.net/6/docs/ref/#commands.cursorPageUp)\n*/\nconst emacsStyleKeymap = [{\n  key: \"Ctrl-b\",\n  run: cursorCharLeft,\n  shift: selectCharLeft,\n  preventDefault: true\n}, {\n  key: \"Ctrl-f\",\n  run: cursorCharRight,\n  shift: selectCharRight\n}, {\n  key: \"Ctrl-p\",\n  run: cursorLineUp,\n  shift: selectLineUp\n}, {\n  key: \"Ctrl-n\",\n  run: cursorLineDown,\n  shift: selectLineDown\n}, {\n  key: \"Ctrl-a\",\n  run: cursorLineStart,\n  shift: selectLineStart\n}, {\n  key: \"Ctrl-e\",\n  run: cursorLineEnd,\n  shift: selectLineEnd\n}, {\n  key: \"Ctrl-d\",\n  run: deleteCharForward\n}, {\n  key: \"Ctrl-h\",\n  run: deleteCharBackward\n}, {\n  key: \"Ctrl-k\",\n  run: deleteToLineEnd\n}, {\n  key: \"Ctrl-Alt-h\",\n  run: deleteGroupBackward\n}, {\n  key: \"Ctrl-o\",\n  run: splitLine\n}, {\n  key: \"Ctrl-t\",\n  run: transposeChars\n}, {\n  key: \"Ctrl-v\",\n  run: cursorPageDown\n}];\n/**\nAn array of key bindings closely sticking to platform-standard or\nwidely used bindings. (This includes the bindings from\n[`emacsStyleKeymap`](https://codemirror.net/6/docs/ref/#commands.emacsStyleKeymap), with their `key`\nproperty changed to `mac`.)\n\n - ArrowLeft: [`cursorCharLeft`](https://codemirror.net/6/docs/ref/#commands.cursorCharLeft) ([`selectCharLeft`](https://codemirror.net/6/docs/ref/#commands.selectCharLeft) with Shift)\n - ArrowRight: [`cursorCharRight`](https://codemirror.net/6/docs/ref/#commands.cursorCharRight) ([`selectCharRight`](https://codemirror.net/6/docs/ref/#commands.selectCharRight) with Shift)\n - Ctrl-ArrowLeft (Alt-ArrowLeft on macOS): [`cursorGroupLeft`](https://codemirror.net/6/docs/ref/#commands.cursorGroupLeft) ([`selectGroupLeft`](https://codemirror.net/6/docs/ref/#commands.selectGroupLeft) with Shift)\n - Ctrl-ArrowRight (Alt-ArrowRight on macOS): [`cursorGroupRight`](https://codemirror.net/6/docs/ref/#commands.cursorGroupRight) ([`selectGroupRight`](https://codemirror.net/6/docs/ref/#commands.selectGroupRight) with Shift)\n - Cmd-ArrowLeft (on macOS): [`cursorLineStart`](https://codemirror.net/6/docs/ref/#commands.cursorLineStart) ([`selectLineStart`](https://codemirror.net/6/docs/ref/#commands.selectLineStart) with Shift)\n - Cmd-ArrowRight (on macOS): [`cursorLineEnd`](https://codemirror.net/6/docs/ref/#commands.cursorLineEnd) ([`selectLineEnd`](https://codemirror.net/6/docs/ref/#commands.selectLineEnd) with Shift)\n - ArrowUp: [`cursorLineUp`](https://codemirror.net/6/docs/ref/#commands.cursorLineUp) ([`selectLineUp`](https://codemirror.net/6/docs/ref/#commands.selectLineUp) with Shift)\n - ArrowDown: [`cursorLineDown`](https://codemirror.net/6/docs/ref/#commands.cursorLineDown) ([`selectLineDown`](https://codemirror.net/6/docs/ref/#commands.selectLineDown) with Shift)\n - Cmd-ArrowUp (on macOS): [`cursorDocStart`](https://codemirror.net/6/docs/ref/#commands.cursorDocStart) ([`selectDocStart`](https://codemirror.net/6/docs/ref/#commands.selectDocStart) with Shift)\n - Cmd-ArrowDown (on macOS): [`cursorDocEnd`](https://codemirror.net/6/docs/ref/#commands.cursorDocEnd) ([`selectDocEnd`](https://codemirror.net/6/docs/ref/#commands.selectDocEnd) with Shift)\n - Ctrl-ArrowUp (on macOS): [`cursorPageUp`](https://codemirror.net/6/docs/ref/#commands.cursorPageUp) ([`selectPageUp`](https://codemirror.net/6/docs/ref/#commands.selectPageUp) with Shift)\n - Ctrl-ArrowDown (on macOS): [`cursorPageDown`](https://codemirror.net/6/docs/ref/#commands.cursorPageDown) ([`selectPageDown`](https://codemirror.net/6/docs/ref/#commands.selectPageDown) with Shift)\n - PageUp: [`cursorPageUp`](https://codemirror.net/6/docs/ref/#commands.cursorPageUp) ([`selectPageUp`](https://codemirror.net/6/docs/ref/#commands.selectPageUp) with Shift)\n - PageDown: [`cursorPageDown`](https://codemirror.net/6/docs/ref/#commands.cursorPageDown) ([`selectPageDown`](https://codemirror.net/6/docs/ref/#commands.selectPageDown) with Shift)\n - Home: [`cursorLineBoundaryBackward`](https://codemirror.net/6/docs/ref/#commands.cursorLineBoundaryBackward) ([`selectLineBoundaryBackward`](https://codemirror.net/6/docs/ref/#commands.selectLineBoundaryBackward) with Shift)\n - End: [`cursorLineBoundaryForward`](https://codemirror.net/6/docs/ref/#commands.cursorLineBoundaryForward) ([`selectLineBoundaryForward`](https://codemirror.net/6/docs/ref/#commands.selectLineBoundaryForward) with Shift)\n - Ctrl-Home (Cmd-Home on macOS): [`cursorDocStart`](https://codemirror.net/6/docs/ref/#commands.cursorDocStart) ([`selectDocStart`](https://codemirror.net/6/docs/ref/#commands.selectDocStart) with Shift)\n - Ctrl-End (Cmd-Home on macOS): [`cursorDocEnd`](https://codemirror.net/6/docs/ref/#commands.cursorDocEnd) ([`selectDocEnd`](https://codemirror.net/6/docs/ref/#commands.selectDocEnd) with Shift)\n - Enter and Shift-Enter: [`insertNewlineAndIndent`](https://codemirror.net/6/docs/ref/#commands.insertNewlineAndIndent)\n - Ctrl-a (Cmd-a on macOS): [`selectAll`](https://codemirror.net/6/docs/ref/#commands.selectAll)\n - Backspace: [`deleteCharBackward`](https://codemirror.net/6/docs/ref/#commands.deleteCharBackward)\n - Delete: [`deleteCharForward`](https://codemirror.net/6/docs/ref/#commands.deleteCharForward)\n - Ctrl-Backspace (Alt-Backspace on macOS): [`deleteGroupBackward`](https://codemirror.net/6/docs/ref/#commands.deleteGroupBackward)\n - Ctrl-Delete (Alt-Delete on macOS): [`deleteGroupForward`](https://codemirror.net/6/docs/ref/#commands.deleteGroupForward)\n - Cmd-Backspace (macOS): [`deleteLineBoundaryBackward`](https://codemirror.net/6/docs/ref/#commands.deleteLineBoundaryBackward).\n - Cmd-Delete (macOS): [`deleteLineBoundaryForward`](https://codemirror.net/6/docs/ref/#commands.deleteLineBoundaryForward).\n*/\nconst standardKeymap = /*@__PURE__*/[{\n  key: \"ArrowLeft\",\n  run: cursorCharLeft,\n  shift: selectCharLeft,\n  preventDefault: true\n}, {\n  key: \"Mod-ArrowLeft\",\n  mac: \"Alt-ArrowLeft\",\n  run: cursorGroupLeft,\n  shift: selectGroupLeft,\n  preventDefault: true\n}, {\n  mac: \"Cmd-ArrowLeft\",\n  run: cursorLineBoundaryLeft,\n  shift: selectLineBoundaryLeft,\n  preventDefault: true\n}, {\n  key: \"ArrowRight\",\n  run: cursorCharRight,\n  shift: selectCharRight,\n  preventDefault: true\n}, {\n  key: \"Mod-ArrowRight\",\n  mac: \"Alt-ArrowRight\",\n  run: cursorGroupRight,\n  shift: selectGroupRight,\n  preventDefault: true\n}, {\n  mac: \"Cmd-ArrowRight\",\n  run: cursorLineBoundaryRight,\n  shift: selectLineBoundaryRight,\n  preventDefault: true\n}, {\n  key: \"ArrowUp\",\n  run: cursorLineUp,\n  shift: selectLineUp,\n  preventDefault: true\n}, {\n  mac: \"Cmd-ArrowUp\",\n  run: cursorDocStart,\n  shift: selectDocStart\n}, {\n  mac: \"Ctrl-ArrowUp\",\n  run: cursorPageUp,\n  shift: selectPageUp\n}, {\n  key: \"ArrowDown\",\n  run: cursorLineDown,\n  shift: selectLineDown,\n  preventDefault: true\n}, {\n  mac: \"Cmd-ArrowDown\",\n  run: cursorDocEnd,\n  shift: selectDocEnd\n}, {\n  mac: \"Ctrl-ArrowDown\",\n  run: cursorPageDown,\n  shift: selectPageDown\n}, {\n  key: \"PageUp\",\n  run: cursorPageUp,\n  shift: selectPageUp\n}, {\n  key: \"PageDown\",\n  run: cursorPageDown,\n  shift: selectPageDown\n}, {\n  key: \"Home\",\n  run: cursorLineBoundaryBackward,\n  shift: selectLineBoundaryBackward,\n  preventDefault: true\n}, {\n  key: \"Mod-Home\",\n  run: cursorDocStart,\n  shift: selectDocStart\n}, {\n  key: \"End\",\n  run: cursorLineBoundaryForward,\n  shift: selectLineBoundaryForward,\n  preventDefault: true\n}, {\n  key: \"Mod-End\",\n  run: cursorDocEnd,\n  shift: selectDocEnd\n}, {\n  key: \"Enter\",\n  run: insertNewlineAndIndent,\n  shift: insertNewlineAndIndent\n}, {\n  key: \"Mod-a\",\n  run: selectAll\n}, {\n  key: \"Backspace\",\n  run: deleteCharBackward,\n  shift: deleteCharBackward\n}, {\n  key: \"Delete\",\n  run: deleteCharForward\n}, {\n  key: \"Mod-Backspace\",\n  mac: \"Alt-Backspace\",\n  run: deleteGroupBackward\n}, {\n  key: \"Mod-Delete\",\n  mac: \"Alt-Delete\",\n  run: deleteGroupForward\n}, {\n  mac: \"Mod-Backspace\",\n  run: deleteLineBoundaryBackward\n}, {\n  mac: \"Mod-Delete\",\n  run: deleteLineBoundaryForward\n}].concat(/*@__PURE__*/emacsStyleKeymap.map(b => ({\n  mac: b.key,\n  run: b.run,\n  shift: b.shift\n})));\n/**\nThe default keymap. Includes all bindings from\n[`standardKeymap`](https://codemirror.net/6/docs/ref/#commands.standardKeymap) plus the following:\n\n- Alt-ArrowLeft (Ctrl-ArrowLeft on macOS): [`cursorSyntaxLeft`](https://codemirror.net/6/docs/ref/#commands.cursorSyntaxLeft) ([`selectSyntaxLeft`](https://codemirror.net/6/docs/ref/#commands.selectSyntaxLeft) with Shift)\n- Alt-ArrowRight (Ctrl-ArrowRight on macOS): [`cursorSyntaxRight`](https://codemirror.net/6/docs/ref/#commands.cursorSyntaxRight) ([`selectSyntaxRight`](https://codemirror.net/6/docs/ref/#commands.selectSyntaxRight) with Shift)\n- Alt-ArrowUp: [`moveLineUp`](https://codemirror.net/6/docs/ref/#commands.moveLineUp)\n- Alt-ArrowDown: [`moveLineDown`](https://codemirror.net/6/docs/ref/#commands.moveLineDown)\n- Shift-Alt-ArrowUp: [`copyLineUp`](https://codemirror.net/6/docs/ref/#commands.copyLineUp)\n- Shift-Alt-ArrowDown: [`copyLineDown`](https://codemirror.net/6/docs/ref/#commands.copyLineDown)\n- Escape: [`simplifySelection`](https://codemirror.net/6/docs/ref/#commands.simplifySelection)\n- Ctrl-Enter (Cmd-Enter on macOS): [`insertBlankLine`](https://codemirror.net/6/docs/ref/#commands.insertBlankLine)\n- Alt-l (Ctrl-l on macOS): [`selectLine`](https://codemirror.net/6/docs/ref/#commands.selectLine)\n- Ctrl-i (Cmd-i on macOS): [`selectParentSyntax`](https://codemirror.net/6/docs/ref/#commands.selectParentSyntax)\n- Ctrl-[ (Cmd-[ on macOS): [`indentLess`](https://codemirror.net/6/docs/ref/#commands.indentLess)\n- Ctrl-] (Cmd-] on macOS): [`indentMore`](https://codemirror.net/6/docs/ref/#commands.indentMore)\n- Ctrl-Alt-\\\\ (Cmd-Alt-\\\\ on macOS): [`indentSelection`](https://codemirror.net/6/docs/ref/#commands.indentSelection)\n- Shift-Ctrl-k (Shift-Cmd-k on macOS): [`deleteLine`](https://codemirror.net/6/docs/ref/#commands.deleteLine)\n- Shift-Ctrl-\\\\ (Shift-Cmd-\\\\ on macOS): [`cursorMatchingBracket`](https://codemirror.net/6/docs/ref/#commands.cursorMatchingBracket)\n- Ctrl-/ (Cmd-/ on macOS): [`toggleComment`](https://codemirror.net/6/docs/ref/#commands.toggleComment).\n- Shift-Alt-a: [`toggleBlockComment`](https://codemirror.net/6/docs/ref/#commands.toggleBlockComment).\n- Ctrl-m (Alt-Shift-m on macOS): [`toggleTabFocusMode`](https://codemirror.net/6/docs/ref/#commands.toggleTabFocusMode).\n*/\nconst defaultKeymap = /*@__PURE__*/[{\n  key: \"Alt-ArrowLeft\",\n  mac: \"Ctrl-ArrowLeft\",\n  run: cursorSyntaxLeft,\n  shift: selectSyntaxLeft\n}, {\n  key: \"Alt-ArrowRight\",\n  mac: \"Ctrl-ArrowRight\",\n  run: cursorSyntaxRight,\n  shift: selectSyntaxRight\n}, {\n  key: \"Alt-ArrowUp\",\n  run: moveLineUp\n}, {\n  key: \"Shift-Alt-ArrowUp\",\n  run: copyLineUp\n}, {\n  key: \"Alt-ArrowDown\",\n  run: moveLineDown\n}, {\n  key: \"Shift-Alt-ArrowDown\",\n  run: copyLineDown\n}, {\n  key: \"Escape\",\n  run: simplifySelection\n}, {\n  key: \"Mod-Enter\",\n  run: insertBlankLine\n}, {\n  key: \"Alt-l\",\n  mac: \"Ctrl-l\",\n  run: selectLine\n}, {\n  key: \"Mod-i\",\n  run: selectParentSyntax,\n  preventDefault: true\n}, {\n  key: \"Mod-[\",\n  run: indentLess\n}, {\n  key: \"Mod-]\",\n  run: indentMore\n}, {\n  key: \"Mod-Alt-\\\\\",\n  run: indentSelection\n}, {\n  key: \"Shift-Mod-k\",\n  run: deleteLine\n}, {\n  key: \"Shift-Mod-\\\\\",\n  run: cursorMatchingBracket\n}, {\n  key: \"Mod-/\",\n  run: toggleComment\n}, {\n  key: \"Alt-A\",\n  run: toggleBlockComment\n}, {\n  key: \"Ctrl-m\",\n  mac: \"Shift-Alt-m\",\n  run: toggleTabFocusMode\n}].concat(standardKeymap);\n/**\nA binding that binds Tab to [`indentMore`](https://codemirror.net/6/docs/ref/#commands.indentMore) and\nShift-Tab to [`indentLess`](https://codemirror.net/6/docs/ref/#commands.indentLess).\nPlease see the [Tab example](../../examples/tab/) before using\nthis.\n*/\nconst indentWithTab = {\n  key: \"Tab\",\n  run: indentMore,\n  shift: indentLess\n};\nexport { blockComment, blockUncomment, copyLineDown, copyLineUp, cursorCharBackward, cursorCharBackwardLogical, cursorCharForward, cursorCharForwardLogical, cursorCharLeft, cursorCharRight, cursorDocEnd, cursorDocStart, cursorGroupBackward, cursorGroupForward, cursorGroupForwardWin, cursorGroupLeft, cursorGroupRight, cursorLineBoundaryBackward, cursorLineBoundaryForward, cursorLineBoundaryLeft, cursorLineBoundaryRight, cursorLineDown, cursorLineEnd, cursorLineStart, cursorLineUp, cursorMatchingBracket, cursorPageDown, cursorPageUp, cursorSubwordBackward, cursorSubwordForward, cursorSyntaxLeft, cursorSyntaxRight, defaultKeymap, deleteCharBackward, deleteCharBackwardStrict, deleteCharForward, deleteGroupBackward, deleteGroupForward, deleteLine, deleteLineBoundaryBackward, deleteLineBoundaryForward, deleteToLineEnd, deleteToLineStart, deleteTrailingWhitespace, emacsStyleKeymap, history, historyField, historyKeymap, indentLess, indentMore, indentSelection, indentWithTab, insertBlankLine, insertNewline, insertNewlineAndIndent, insertNewlineKeepIndent, insertTab, invertedEffects, isolateHistory, lineComment, lineUncomment, moveLineDown, moveLineUp, redo, redoDepth, redoSelection, selectAll, selectCharBackward, selectCharBackwardLogical, selectCharForward, selectCharForwardLogical, selectCharLeft, selectCharRight, selectDocEnd, selectDocStart, selectGroupBackward, selectGroupForward, selectGroupForwardWin, selectGroupLeft, selectGroupRight, selectLine, selectLineBoundaryBackward, selectLineBoundaryForward, selectLineBoundaryLeft, selectLineBoundaryRight, selectLineDown, selectLineEnd, selectLineStart, selectLineUp, selectMatchingBracket, selectPageDown, selectPageUp, selectParentSyntax, selectSubwordBackward, selectSubwordForward, selectSyntaxLeft, selectSyntaxRight, simplifySelection, splitLine, standardKeymap, temporarilySetTabFocusMode, toggleBlockComment, toggleBlockCommentByLine, toggleComment, toggleLineComment, toggleTabFocusMode, transposeChars, undo, undoDepth, undoSelection };", "export default function crelt() {\n  var elt = arguments[0];\n  if (typeof elt == \"string\") elt = document.createElement(elt);\n  var i = 1,\n    next = arguments[1];\n  if (next && typeof next == \"object\" && next.nodeType == null && !Array.isArray(next)) {\n    for (var name in next) if (Object.prototype.hasOwnProperty.call(next, name)) {\n      var value = next[name];\n      if (typeof value == \"string\") elt.setAttribute(name, value);else if (value != null) elt[name] = value;\n    }\n    i++;\n  }\n  for (; i < arguments.length; i++) add(elt, arguments[i]);\n  return elt;\n}\nfunction add(elt, child) {\n  if (typeof child == \"string\") {\n    elt.appendChild(document.createTextNode(child));\n  } else if (child == null) {} else if (child.nodeType != null) {\n    elt.appendChild(child);\n  } else if (Array.isArray(child)) {\n    for (var i = 0; i < child.length; i++) add(elt, child[i]);\n  } else {\n    throw new RangeError(\"Unsupported child node: \" + child);\n  }\n}", "import { show<PERSON><PERSON>l, Editor<PERSON>iew, getPanel, Decoration, ViewPlugin, runScopeHandlers } from '@codemirror/view';\nimport { codePointAt, fromCodePoint, codePointSize, StateEffect, StateField, EditorSelection, Facet, combineConfig, CharCategory, RangeSetBuilder, Prec, EditorState, findClusterBreak } from '@codemirror/state';\nimport elt from 'crelt';\nconst basicNormalize = typeof String.prototype.normalize == \"function\" ? x => x.normalize(\"NFKD\") : x => x;\n/**\nA search cursor provides an iterator over text matches in a\ndocument.\n*/\nclass SearchCursor {\n  /**\n  Create a text cursor. The query is the search string, `from` to\n  `to` provides the region to search.\n  \n  When `normalize` is given, it will be called, on both the query\n  string and the content it is matched against, before comparing.\n  You can, for example, create a case-insensitive search by\n  passing `s => s.toLowerCase()`.\n  \n  Text is always normalized with\n  [`.normalize(\"NFKD\")`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/normalize)\n  (when supported).\n  */\n  constructor(text, query, from = 0, to = text.length, normalize, test) {\n    this.test = test;\n    /**\n    The current match (only holds a meaningful value after\n    [`next`](https://codemirror.net/6/docs/ref/#search.SearchCursor.next) has been called and when\n    `done` is false).\n    */\n    this.value = {\n      from: 0,\n      to: 0\n    };\n    /**\n    Whether the end of the iterated region has been reached.\n    */\n    this.done = false;\n    this.matches = [];\n    this.buffer = \"\";\n    this.bufferPos = 0;\n    this.iter = text.iterRange(from, to);\n    this.bufferStart = from;\n    this.normalize = normalize ? x => normalize(basicNormalize(x)) : basicNormalize;\n    this.query = this.normalize(query);\n  }\n  peek() {\n    if (this.bufferPos == this.buffer.length) {\n      this.bufferStart += this.buffer.length;\n      this.iter.next();\n      if (this.iter.done) return -1;\n      this.bufferPos = 0;\n      this.buffer = this.iter.value;\n    }\n    return codePointAt(this.buffer, this.bufferPos);\n  }\n  /**\n  Look for the next match. Updates the iterator's\n  [`value`](https://codemirror.net/6/docs/ref/#search.SearchCursor.value) and\n  [`done`](https://codemirror.net/6/docs/ref/#search.SearchCursor.done) properties. Should be called\n  at least once before using the cursor.\n  */\n  next() {\n    while (this.matches.length) this.matches.pop();\n    return this.nextOverlapping();\n  }\n  /**\n  The `next` method will ignore matches that partially overlap a\n  previous match. This method behaves like `next`, but includes\n  such matches.\n  */\n  nextOverlapping() {\n    for (;;) {\n      let next = this.peek();\n      if (next < 0) {\n        this.done = true;\n        return this;\n      }\n      let str = fromCodePoint(next),\n        start = this.bufferStart + this.bufferPos;\n      this.bufferPos += codePointSize(next);\n      let norm = this.normalize(str);\n      if (norm.length) for (let i = 0, pos = start;; i++) {\n        let code = norm.charCodeAt(i);\n        let match = this.match(code, pos, this.bufferPos + this.bufferStart);\n        if (i == norm.length - 1) {\n          if (match) {\n            this.value = match;\n            return this;\n          }\n          break;\n        }\n        if (pos == start && i < str.length && str.charCodeAt(i) == code) pos++;\n      }\n    }\n  }\n  match(code, pos, end) {\n    let match = null;\n    for (let i = 0; i < this.matches.length; i += 2) {\n      let index = this.matches[i],\n        keep = false;\n      if (this.query.charCodeAt(index) == code) {\n        if (index == this.query.length - 1) {\n          match = {\n            from: this.matches[i + 1],\n            to: end\n          };\n        } else {\n          this.matches[i]++;\n          keep = true;\n        }\n      }\n      if (!keep) {\n        this.matches.splice(i, 2);\n        i -= 2;\n      }\n    }\n    if (this.query.charCodeAt(0) == code) {\n      if (this.query.length == 1) match = {\n        from: pos,\n        to: end\n      };else this.matches.push(1, pos);\n    }\n    if (match && this.test && !this.test(match.from, match.to, this.buffer, this.bufferStart)) match = null;\n    return match;\n  }\n}\nif (typeof Symbol != \"undefined\") SearchCursor.prototype[Symbol.iterator] = function () {\n  return this;\n};\nconst empty = {\n  from: -1,\n  to: -1,\n  match: /*@__PURE__*//.*/.exec(\"\")\n};\nconst baseFlags = \"gm\" + (/x/.unicode == null ? \"\" : \"u\");\n/**\nThis class is similar to [`SearchCursor`](https://codemirror.net/6/docs/ref/#search.SearchCursor)\nbut searches for a regular expression pattern instead of a plain\nstring.\n*/\nclass RegExpCursor {\n  /**\n  Create a cursor that will search the given range in the given\n  document. `query` should be the raw pattern (as you'd pass it to\n  `new RegExp`).\n  */\n  constructor(text, query, options, from = 0, to = text.length) {\n    this.text = text;\n    this.to = to;\n    this.curLine = \"\";\n    /**\n    Set to `true` when the cursor has reached the end of the search\n    range.\n    */\n    this.done = false;\n    /**\n    Will contain an object with the extent of the match and the\n    match object when [`next`](https://codemirror.net/6/docs/ref/#search.RegExpCursor.next)\n    sucessfully finds a match.\n    */\n    this.value = empty;\n    if (/\\\\[sWDnr]|\\n|\\r|\\[\\^/.test(query)) return new MultilineRegExpCursor(text, query, options, from, to);\n    this.re = new RegExp(query, baseFlags + ((options === null || options === void 0 ? void 0 : options.ignoreCase) ? \"i\" : \"\"));\n    this.test = options === null || options === void 0 ? void 0 : options.test;\n    this.iter = text.iter();\n    let startLine = text.lineAt(from);\n    this.curLineStart = startLine.from;\n    this.matchPos = toCharEnd(text, from);\n    this.getLine(this.curLineStart);\n  }\n  getLine(skip) {\n    this.iter.next(skip);\n    if (this.iter.lineBreak) {\n      this.curLine = \"\";\n    } else {\n      this.curLine = this.iter.value;\n      if (this.curLineStart + this.curLine.length > this.to) this.curLine = this.curLine.slice(0, this.to - this.curLineStart);\n      this.iter.next();\n    }\n  }\n  nextLine() {\n    this.curLineStart = this.curLineStart + this.curLine.length + 1;\n    if (this.curLineStart > this.to) this.curLine = \"\";else this.getLine(0);\n  }\n  /**\n  Move to the next match, if there is one.\n  */\n  next() {\n    for (let off = this.matchPos - this.curLineStart;;) {\n      this.re.lastIndex = off;\n      let match = this.matchPos <= this.to && this.re.exec(this.curLine);\n      if (match) {\n        let from = this.curLineStart + match.index,\n          to = from + match[0].length;\n        this.matchPos = toCharEnd(this.text, to + (from == to ? 1 : 0));\n        if (from == this.curLineStart + this.curLine.length) this.nextLine();\n        if ((from < to || from > this.value.to) && (!this.test || this.test(from, to, match))) {\n          this.value = {\n            from,\n            to,\n            match\n          };\n          return this;\n        }\n        off = this.matchPos - this.curLineStart;\n      } else if (this.curLineStart + this.curLine.length < this.to) {\n        this.nextLine();\n        off = 0;\n      } else {\n        this.done = true;\n        return this;\n      }\n    }\n  }\n}\nconst flattened = /*@__PURE__*/new WeakMap();\n// Reusable (partially) flattened document strings\nclass FlattenedDoc {\n  constructor(from, text) {\n    this.from = from;\n    this.text = text;\n  }\n  get to() {\n    return this.from + this.text.length;\n  }\n  static get(doc, from, to) {\n    let cached = flattened.get(doc);\n    if (!cached || cached.from >= to || cached.to <= from) {\n      let flat = new FlattenedDoc(from, doc.sliceString(from, to));\n      flattened.set(doc, flat);\n      return flat;\n    }\n    if (cached.from == from && cached.to == to) return cached;\n    let {\n      text,\n      from: cachedFrom\n    } = cached;\n    if (cachedFrom > from) {\n      text = doc.sliceString(from, cachedFrom) + text;\n      cachedFrom = from;\n    }\n    if (cached.to < to) text += doc.sliceString(cached.to, to);\n    flattened.set(doc, new FlattenedDoc(cachedFrom, text));\n    return new FlattenedDoc(from, text.slice(from - cachedFrom, to - cachedFrom));\n  }\n}\nclass MultilineRegExpCursor {\n  constructor(text, query, options, from, to) {\n    this.text = text;\n    this.to = to;\n    this.done = false;\n    this.value = empty;\n    this.matchPos = toCharEnd(text, from);\n    this.re = new RegExp(query, baseFlags + ((options === null || options === void 0 ? void 0 : options.ignoreCase) ? \"i\" : \"\"));\n    this.test = options === null || options === void 0 ? void 0 : options.test;\n    this.flat = FlattenedDoc.get(text, from, this.chunkEnd(from + 5000 /* Chunk.Base */));\n  }\n  chunkEnd(pos) {\n    return pos >= this.to ? this.to : this.text.lineAt(pos).to;\n  }\n  next() {\n    for (;;) {\n      let off = this.re.lastIndex = this.matchPos - this.flat.from;\n      let match = this.re.exec(this.flat.text);\n      // Skip empty matches directly after the last match\n      if (match && !match[0] && match.index == off) {\n        this.re.lastIndex = off + 1;\n        match = this.re.exec(this.flat.text);\n      }\n      if (match) {\n        let from = this.flat.from + match.index,\n          to = from + match[0].length;\n        // If a match goes almost to the end of a noncomplete chunk, try\n        // again, since it'll likely be able to match more\n        if ((this.flat.to >= this.to || match.index + match[0].length <= this.flat.text.length - 10) && (!this.test || this.test(from, to, match))) {\n          this.value = {\n            from,\n            to,\n            match\n          };\n          this.matchPos = toCharEnd(this.text, to + (from == to ? 1 : 0));\n          return this;\n        }\n      }\n      if (this.flat.to == this.to) {\n        this.done = true;\n        return this;\n      }\n      // Grow the flattened doc\n      this.flat = FlattenedDoc.get(this.text, this.flat.from, this.chunkEnd(this.flat.from + this.flat.text.length * 2));\n    }\n  }\n}\nif (typeof Symbol != \"undefined\") {\n  RegExpCursor.prototype[Symbol.iterator] = MultilineRegExpCursor.prototype[Symbol.iterator] = function () {\n    return this;\n  };\n}\nfunction validRegExp(source) {\n  try {\n    new RegExp(source, baseFlags);\n    return true;\n  } catch (_a) {\n    return false;\n  }\n}\nfunction toCharEnd(text, pos) {\n  if (pos >= text.length) return pos;\n  let line = text.lineAt(pos),\n    next;\n  while (pos < line.to && (next = line.text.charCodeAt(pos - line.from)) >= 0xDC00 && next < 0xE000) pos++;\n  return pos;\n}\nfunction createLineDialog(view) {\n  let line = String(view.state.doc.lineAt(view.state.selection.main.head).number);\n  let input = elt(\"input\", {\n    class: \"cm-textfield\",\n    name: \"line\",\n    value: line\n  });\n  let dom = elt(\"form\", {\n    class: \"cm-gotoLine\",\n    onkeydown: event => {\n      if (event.keyCode == 27) {\n        // Escape\n        event.preventDefault();\n        view.dispatch({\n          effects: dialogEffect.of(false)\n        });\n        view.focus();\n      } else if (event.keyCode == 13) {\n        // Enter\n        event.preventDefault();\n        go();\n      }\n    },\n    onsubmit: event => {\n      event.preventDefault();\n      go();\n    }\n  }, elt(\"label\", view.state.phrase(\"Go to line\"), \": \", input), \" \", elt(\"button\", {\n    class: \"cm-button\",\n    type: \"submit\"\n  }, view.state.phrase(\"go\")));\n  function go() {\n    let match = /^([+-])?(\\d+)?(:\\d+)?(%)?$/.exec(input.value);\n    if (!match) return;\n    let {\n        state\n      } = view,\n      startLine = state.doc.lineAt(state.selection.main.head);\n    let [, sign, ln, cl, percent] = match;\n    let col = cl ? +cl.slice(1) : 0;\n    let line = ln ? +ln : startLine.number;\n    if (ln && percent) {\n      let pc = line / 100;\n      if (sign) pc = pc * (sign == \"-\" ? -1 : 1) + startLine.number / state.doc.lines;\n      line = Math.round(state.doc.lines * pc);\n    } else if (ln && sign) {\n      line = line * (sign == \"-\" ? -1 : 1) + startLine.number;\n    }\n    let docLine = state.doc.line(Math.max(1, Math.min(state.doc.lines, line)));\n    let selection = EditorSelection.cursor(docLine.from + Math.max(0, Math.min(col, docLine.length)));\n    view.dispatch({\n      effects: [dialogEffect.of(false), EditorView.scrollIntoView(selection.from, {\n        y: 'center'\n      })],\n      selection\n    });\n    view.focus();\n  }\n  return {\n    dom\n  };\n}\nconst dialogEffect = /*@__PURE__*/StateEffect.define();\nconst dialogField = /*@__PURE__*/StateField.define({\n  create() {\n    return true;\n  },\n  update(value, tr) {\n    for (let e of tr.effects) if (e.is(dialogEffect)) value = e.value;\n    return value;\n  },\n  provide: f => showPanel.from(f, val => val ? createLineDialog : null)\n});\n/**\nCommand that shows a dialog asking the user for a line number, and\nwhen a valid position is provided, moves the cursor to that line.\n\nSupports line numbers, relative line offsets prefixed with `+` or\n`-`, document percentages suffixed with `%`, and an optional\ncolumn position by adding `:` and a second number after the line\nnumber.\n*/\nconst gotoLine = view => {\n  let panel = getPanel(view, createLineDialog);\n  if (!panel) {\n    let effects = [dialogEffect.of(true)];\n    if (view.state.field(dialogField, false) == null) effects.push(StateEffect.appendConfig.of([dialogField, baseTheme$1]));\n    view.dispatch({\n      effects\n    });\n    panel = getPanel(view, createLineDialog);\n  }\n  if (panel) panel.dom.querySelector(\"input\").select();\n  return true;\n};\nconst baseTheme$1 = /*@__PURE__*/EditorView.baseTheme({\n  \".cm-panel.cm-gotoLine\": {\n    padding: \"2px 6px 4px\",\n    \"& label\": {\n      fontSize: \"80%\"\n    }\n  }\n});\nconst defaultHighlightOptions = {\n  highlightWordAroundCursor: false,\n  minSelectionLength: 1,\n  maxMatches: 100,\n  wholeWords: false\n};\nconst highlightConfig = /*@__PURE__*/Facet.define({\n  combine(options) {\n    return combineConfig(options, defaultHighlightOptions, {\n      highlightWordAroundCursor: (a, b) => a || b,\n      minSelectionLength: Math.min,\n      maxMatches: Math.min\n    });\n  }\n});\n/**\nThis extension highlights text that matches the selection. It uses\nthe `\"cm-selectionMatch\"` class for the highlighting. When\n`highlightWordAroundCursor` is enabled, the word at the cursor\nitself will be highlighted with `\"cm-selectionMatch-main\"`.\n*/\nfunction highlightSelectionMatches(options) {\n  let ext = [defaultTheme, matchHighlighter];\n  if (options) ext.push(highlightConfig.of(options));\n  return ext;\n}\nconst matchDeco = /*@__PURE__*/Decoration.mark({\n  class: \"cm-selectionMatch\"\n});\nconst mainMatchDeco = /*@__PURE__*/Decoration.mark({\n  class: \"cm-selectionMatch cm-selectionMatch-main\"\n});\n// Whether the characters directly outside the given positions are non-word characters\nfunction insideWordBoundaries(check, state, from, to) {\n  return (from == 0 || check(state.sliceDoc(from - 1, from)) != CharCategory.Word) && (to == state.doc.length || check(state.sliceDoc(to, to + 1)) != CharCategory.Word);\n}\n// Whether the characters directly at the given positions are word characters\nfunction insideWord(check, state, from, to) {\n  return check(state.sliceDoc(from, from + 1)) == CharCategory.Word && check(state.sliceDoc(to - 1, to)) == CharCategory.Word;\n}\nconst matchHighlighter = /*@__PURE__*/ViewPlugin.fromClass(class {\n  constructor(view) {\n    this.decorations = this.getDeco(view);\n  }\n  update(update) {\n    if (update.selectionSet || update.docChanged || update.viewportChanged) this.decorations = this.getDeco(update.view);\n  }\n  getDeco(view) {\n    let conf = view.state.facet(highlightConfig);\n    let {\n        state\n      } = view,\n      sel = state.selection;\n    if (sel.ranges.length > 1) return Decoration.none;\n    let range = sel.main,\n      query,\n      check = null;\n    if (range.empty) {\n      if (!conf.highlightWordAroundCursor) return Decoration.none;\n      let word = state.wordAt(range.head);\n      if (!word) return Decoration.none;\n      check = state.charCategorizer(range.head);\n      query = state.sliceDoc(word.from, word.to);\n    } else {\n      let len = range.to - range.from;\n      if (len < conf.minSelectionLength || len > 200) return Decoration.none;\n      if (conf.wholeWords) {\n        query = state.sliceDoc(range.from, range.to); // TODO: allow and include leading/trailing space?\n        check = state.charCategorizer(range.head);\n        if (!(insideWordBoundaries(check, state, range.from, range.to) && insideWord(check, state, range.from, range.to))) return Decoration.none;\n      } else {\n        query = state.sliceDoc(range.from, range.to);\n        if (!query) return Decoration.none;\n      }\n    }\n    let deco = [];\n    for (let part of view.visibleRanges) {\n      let cursor = new SearchCursor(state.doc, query, part.from, part.to);\n      while (!cursor.next().done) {\n        let {\n          from,\n          to\n        } = cursor.value;\n        if (!check || insideWordBoundaries(check, state, from, to)) {\n          if (range.empty && from <= range.from && to >= range.to) deco.push(mainMatchDeco.range(from, to));else if (from >= range.to || to <= range.from) deco.push(matchDeco.range(from, to));\n          if (deco.length > conf.maxMatches) return Decoration.none;\n        }\n      }\n    }\n    return Decoration.set(deco);\n  }\n}, {\n  decorations: v => v.decorations\n});\nconst defaultTheme = /*@__PURE__*/EditorView.baseTheme({\n  \".cm-selectionMatch\": {\n    backgroundColor: \"#99ff7780\"\n  },\n  \".cm-searchMatch .cm-selectionMatch\": {\n    backgroundColor: \"transparent\"\n  }\n});\n// Select the words around the cursors.\nconst selectWord = ({\n  state,\n  dispatch\n}) => {\n  let {\n    selection\n  } = state;\n  let newSel = EditorSelection.create(selection.ranges.map(range => state.wordAt(range.head) || EditorSelection.cursor(range.head)), selection.mainIndex);\n  if (newSel.eq(selection)) return false;\n  dispatch(state.update({\n    selection: newSel\n  }));\n  return true;\n};\n// Find next occurrence of query relative to last cursor. Wrap around\n// the document if there are no more matches.\nfunction findNextOccurrence(state, query) {\n  let {\n    main,\n    ranges\n  } = state.selection;\n  let word = state.wordAt(main.head),\n    fullWord = word && word.from == main.from && word.to == main.to;\n  for (let cycled = false, cursor = new SearchCursor(state.doc, query, ranges[ranges.length - 1].to);;) {\n    cursor.next();\n    if (cursor.done) {\n      if (cycled) return null;\n      cursor = new SearchCursor(state.doc, query, 0, Math.max(0, ranges[ranges.length - 1].from - 1));\n      cycled = true;\n    } else {\n      if (cycled && ranges.some(r => r.from == cursor.value.from)) continue;\n      if (fullWord) {\n        let word = state.wordAt(cursor.value.from);\n        if (!word || word.from != cursor.value.from || word.to != cursor.value.to) continue;\n      }\n      return cursor.value;\n    }\n  }\n}\n/**\nSelect next occurrence of the current selection. Expand selection\nto the surrounding word when the selection is empty.\n*/\nconst selectNextOccurrence = ({\n  state,\n  dispatch\n}) => {\n  let {\n    ranges\n  } = state.selection;\n  if (ranges.some(sel => sel.from === sel.to)) return selectWord({\n    state,\n    dispatch\n  });\n  let searchedText = state.sliceDoc(ranges[0].from, ranges[0].to);\n  if (state.selection.ranges.some(r => state.sliceDoc(r.from, r.to) != searchedText)) return false;\n  let range = findNextOccurrence(state, searchedText);\n  if (!range) return false;\n  dispatch(state.update({\n    selection: state.selection.addRange(EditorSelection.range(range.from, range.to), false),\n    effects: EditorView.scrollIntoView(range.to)\n  }));\n  return true;\n};\nconst searchConfigFacet = /*@__PURE__*/Facet.define({\n  combine(configs) {\n    return combineConfig(configs, {\n      top: false,\n      caseSensitive: false,\n      literal: false,\n      regexp: false,\n      wholeWord: false,\n      createPanel: view => new SearchPanel(view),\n      scrollToMatch: range => EditorView.scrollIntoView(range)\n    });\n  }\n});\n/**\nAdd search state to the editor configuration, and optionally\nconfigure the search extension.\n([`openSearchPanel`](https://codemirror.net/6/docs/ref/#search.openSearchPanel) will automatically\nenable this if it isn't already on).\n*/\nfunction search(config) {\n  return config ? [searchConfigFacet.of(config), searchExtensions] : searchExtensions;\n}\n/**\nA search query. Part of the editor's search state.\n*/\nclass SearchQuery {\n  /**\n  Create a query object.\n  */\n  constructor(config) {\n    this.search = config.search;\n    this.caseSensitive = !!config.caseSensitive;\n    this.literal = !!config.literal;\n    this.regexp = !!config.regexp;\n    this.replace = config.replace || \"\";\n    this.valid = !!this.search && (!this.regexp || validRegExp(this.search));\n    this.unquoted = this.unquote(this.search);\n    this.wholeWord = !!config.wholeWord;\n  }\n  /**\n  @internal\n  */\n  unquote(text) {\n    return this.literal ? text : text.replace(/\\\\([nrt\\\\])/g, (_, ch) => ch == \"n\" ? \"\\n\" : ch == \"r\" ? \"\\r\" : ch == \"t\" ? \"\\t\" : \"\\\\\");\n  }\n  /**\n  Compare this query to another query.\n  */\n  eq(other) {\n    return this.search == other.search && this.replace == other.replace && this.caseSensitive == other.caseSensitive && this.regexp == other.regexp && this.wholeWord == other.wholeWord;\n  }\n  /**\n  @internal\n  */\n  create() {\n    return this.regexp ? new RegExpQuery(this) : new StringQuery(this);\n  }\n  /**\n  Get a search cursor for this query, searching through the given\n  range in the given state.\n  */\n  getCursor(state, from = 0, to) {\n    let st = state.doc ? state : EditorState.create({\n      doc: state\n    });\n    if (to == null) to = st.doc.length;\n    return this.regexp ? regexpCursor(this, st, from, to) : stringCursor(this, st, from, to);\n  }\n}\nclass QueryType {\n  constructor(spec) {\n    this.spec = spec;\n  }\n}\nfunction stringCursor(spec, state, from, to) {\n  return new SearchCursor(state.doc, spec.unquoted, from, to, spec.caseSensitive ? undefined : x => x.toLowerCase(), spec.wholeWord ? stringWordTest(state.doc, state.charCategorizer(state.selection.main.head)) : undefined);\n}\nfunction stringWordTest(doc, categorizer) {\n  return (from, to, buf, bufPos) => {\n    if (bufPos > from || bufPos + buf.length < to) {\n      bufPos = Math.max(0, from - 2);\n      buf = doc.sliceString(bufPos, Math.min(doc.length, to + 2));\n    }\n    return (categorizer(charBefore(buf, from - bufPos)) != CharCategory.Word || categorizer(charAfter(buf, from - bufPos)) != CharCategory.Word) && (categorizer(charAfter(buf, to - bufPos)) != CharCategory.Word || categorizer(charBefore(buf, to - bufPos)) != CharCategory.Word);\n  };\n}\nclass StringQuery extends QueryType {\n  constructor(spec) {\n    super(spec);\n  }\n  nextMatch(state, curFrom, curTo) {\n    let cursor = stringCursor(this.spec, state, curTo, state.doc.length).nextOverlapping();\n    if (cursor.done) {\n      let end = Math.min(state.doc.length, curFrom + this.spec.unquoted.length);\n      cursor = stringCursor(this.spec, state, 0, end).nextOverlapping();\n    }\n    return cursor.done || cursor.value.from == curFrom && cursor.value.to == curTo ? null : cursor.value;\n  }\n  // Searching in reverse is, rather than implementing an inverted search\n  // cursor, done by scanning chunk after chunk forward.\n  prevMatchInRange(state, from, to) {\n    for (let pos = to;;) {\n      let start = Math.max(from, pos - 10000 /* FindPrev.ChunkSize */ - this.spec.unquoted.length);\n      let cursor = stringCursor(this.spec, state, start, pos),\n        range = null;\n      while (!cursor.nextOverlapping().done) range = cursor.value;\n      if (range) return range;\n      if (start == from) return null;\n      pos -= 10000 /* FindPrev.ChunkSize */;\n    }\n  }\n  prevMatch(state, curFrom, curTo) {\n    let found = this.prevMatchInRange(state, 0, curFrom);\n    if (!found) found = this.prevMatchInRange(state, Math.max(0, curTo - this.spec.unquoted.length), state.doc.length);\n    return found && (found.from != curFrom || found.to != curTo) ? found : null;\n  }\n  getReplacement(_result) {\n    return this.spec.unquote(this.spec.replace);\n  }\n  matchAll(state, limit) {\n    let cursor = stringCursor(this.spec, state, 0, state.doc.length),\n      ranges = [];\n    while (!cursor.next().done) {\n      if (ranges.length >= limit) return null;\n      ranges.push(cursor.value);\n    }\n    return ranges;\n  }\n  highlight(state, from, to, add) {\n    let cursor = stringCursor(this.spec, state, Math.max(0, from - this.spec.unquoted.length), Math.min(to + this.spec.unquoted.length, state.doc.length));\n    while (!cursor.next().done) add(cursor.value.from, cursor.value.to);\n  }\n}\nfunction regexpCursor(spec, state, from, to) {\n  return new RegExpCursor(state.doc, spec.search, {\n    ignoreCase: !spec.caseSensitive,\n    test: spec.wholeWord ? regexpWordTest(state.charCategorizer(state.selection.main.head)) : undefined\n  }, from, to);\n}\nfunction charBefore(str, index) {\n  return str.slice(findClusterBreak(str, index, false), index);\n}\nfunction charAfter(str, index) {\n  return str.slice(index, findClusterBreak(str, index));\n}\nfunction regexpWordTest(categorizer) {\n  return (_from, _to, match) => !match[0].length || (categorizer(charBefore(match.input, match.index)) != CharCategory.Word || categorizer(charAfter(match.input, match.index)) != CharCategory.Word) && (categorizer(charAfter(match.input, match.index + match[0].length)) != CharCategory.Word || categorizer(charBefore(match.input, match.index + match[0].length)) != CharCategory.Word);\n}\nclass RegExpQuery extends QueryType {\n  nextMatch(state, curFrom, curTo) {\n    let cursor = regexpCursor(this.spec, state, curTo, state.doc.length).next();\n    if (cursor.done) cursor = regexpCursor(this.spec, state, 0, curFrom).next();\n    return cursor.done ? null : cursor.value;\n  }\n  prevMatchInRange(state, from, to) {\n    for (let size = 1;; size++) {\n      let start = Math.max(from, to - size * 10000 /* FindPrev.ChunkSize */);\n      let cursor = regexpCursor(this.spec, state, start, to),\n        range = null;\n      while (!cursor.next().done) range = cursor.value;\n      if (range && (start == from || range.from > start + 10)) return range;\n      if (start == from) return null;\n    }\n  }\n  prevMatch(state, curFrom, curTo) {\n    return this.prevMatchInRange(state, 0, curFrom) || this.prevMatchInRange(state, curTo, state.doc.length);\n  }\n  getReplacement(result) {\n    return this.spec.unquote(this.spec.replace).replace(/\\$([$&\\d+])/g, (m, i) => i == \"$\" ? \"$\" : i == \"&\" ? result.match[0] : i != \"0\" && +i < result.match.length ? result.match[i] : m);\n  }\n  matchAll(state, limit) {\n    let cursor = regexpCursor(this.spec, state, 0, state.doc.length),\n      ranges = [];\n    while (!cursor.next().done) {\n      if (ranges.length >= limit) return null;\n      ranges.push(cursor.value);\n    }\n    return ranges;\n  }\n  highlight(state, from, to, add) {\n    let cursor = regexpCursor(this.spec, state, Math.max(0, from - 250 /* RegExp.HighlightMargin */), Math.min(to + 250 /* RegExp.HighlightMargin */, state.doc.length));\n    while (!cursor.next().done) add(cursor.value.from, cursor.value.to);\n  }\n}\n/**\nA state effect that updates the current search query. Note that\nthis only has an effect if the search state has been initialized\n(by including [`search`](https://codemirror.net/6/docs/ref/#search.search) in your configuration or\nby running [`openSearchPanel`](https://codemirror.net/6/docs/ref/#search.openSearchPanel) at least\nonce).\n*/\nconst setSearchQuery = /*@__PURE__*/StateEffect.define();\nconst togglePanel = /*@__PURE__*/StateEffect.define();\nconst searchState = /*@__PURE__*/StateField.define({\n  create(state) {\n    return new SearchState(defaultQuery(state).create(), null);\n  },\n  update(value, tr) {\n    for (let effect of tr.effects) {\n      if (effect.is(setSearchQuery)) value = new SearchState(effect.value.create(), value.panel);else if (effect.is(togglePanel)) value = new SearchState(value.query, effect.value ? createSearchPanel : null);\n    }\n    return value;\n  },\n  provide: f => showPanel.from(f, val => val.panel)\n});\n/**\nGet the current search query from an editor state.\n*/\nfunction getSearchQuery(state) {\n  let curState = state.field(searchState, false);\n  return curState ? curState.query.spec : defaultQuery(state);\n}\n/**\nQuery whether the search panel is open in the given editor state.\n*/\nfunction searchPanelOpen(state) {\n  var _a;\n  return ((_a = state.field(searchState, false)) === null || _a === void 0 ? void 0 : _a.panel) != null;\n}\nclass SearchState {\n  constructor(query, panel) {\n    this.query = query;\n    this.panel = panel;\n  }\n}\nconst matchMark = /*@__PURE__*/Decoration.mark({\n    class: \"cm-searchMatch\"\n  }),\n  selectedMatchMark = /*@__PURE__*/Decoration.mark({\n    class: \"cm-searchMatch cm-searchMatch-selected\"\n  });\nconst searchHighlighter = /*@__PURE__*/ViewPlugin.fromClass(class {\n  constructor(view) {\n    this.view = view;\n    this.decorations = this.highlight(view.state.field(searchState));\n  }\n  update(update) {\n    let state = update.state.field(searchState);\n    if (state != update.startState.field(searchState) || update.docChanged || update.selectionSet || update.viewportChanged) this.decorations = this.highlight(state);\n  }\n  highlight({\n    query,\n    panel\n  }) {\n    if (!panel || !query.spec.valid) return Decoration.none;\n    let {\n      view\n    } = this;\n    let builder = new RangeSetBuilder();\n    for (let i = 0, ranges = view.visibleRanges, l = ranges.length; i < l; i++) {\n      let {\n        from,\n        to\n      } = ranges[i];\n      while (i < l - 1 && to > ranges[i + 1].from - 2 * 250 /* RegExp.HighlightMargin */) to = ranges[++i].to;\n      query.highlight(view.state, from, to, (from, to) => {\n        let selected = view.state.selection.ranges.some(r => r.from == from && r.to == to);\n        builder.add(from, to, selected ? selectedMatchMark : matchMark);\n      });\n    }\n    return builder.finish();\n  }\n}, {\n  decorations: v => v.decorations\n});\nfunction searchCommand(f) {\n  return view => {\n    let state = view.state.field(searchState, false);\n    return state && state.query.spec.valid ? f(view, state) : openSearchPanel(view);\n  };\n}\n/**\nOpen the search panel if it isn't already open, and move the\nselection to the first match after the current main selection.\nWill wrap around to the start of the document when it reaches the\nend.\n*/\nconst findNext = /*@__PURE__*/searchCommand((view, {\n  query\n}) => {\n  let {\n    to\n  } = view.state.selection.main;\n  let next = query.nextMatch(view.state, to, to);\n  if (!next) return false;\n  let selection = EditorSelection.single(next.from, next.to);\n  let config = view.state.facet(searchConfigFacet);\n  view.dispatch({\n    selection,\n    effects: [announceMatch(view, next), config.scrollToMatch(selection.main, view)],\n    userEvent: \"select.search\"\n  });\n  selectSearchInput(view);\n  return true;\n});\n/**\nMove the selection to the previous instance of the search query,\nbefore the current main selection. Will wrap past the start\nof the document to start searching at the end again.\n*/\nconst findPrevious = /*@__PURE__*/searchCommand((view, {\n  query\n}) => {\n  let {\n      state\n    } = view,\n    {\n      from\n    } = state.selection.main;\n  let prev = query.prevMatch(state, from, from);\n  if (!prev) return false;\n  let selection = EditorSelection.single(prev.from, prev.to);\n  let config = view.state.facet(searchConfigFacet);\n  view.dispatch({\n    selection,\n    effects: [announceMatch(view, prev), config.scrollToMatch(selection.main, view)],\n    userEvent: \"select.search\"\n  });\n  selectSearchInput(view);\n  return true;\n});\n/**\nSelect all instances of the search query.\n*/\nconst selectMatches = /*@__PURE__*/searchCommand((view, {\n  query\n}) => {\n  let ranges = query.matchAll(view.state, 1000);\n  if (!ranges || !ranges.length) return false;\n  view.dispatch({\n    selection: EditorSelection.create(ranges.map(r => EditorSelection.range(r.from, r.to))),\n    userEvent: \"select.search.matches\"\n  });\n  return true;\n});\n/**\nSelect all instances of the currently selected text.\n*/\nconst selectSelectionMatches = ({\n  state,\n  dispatch\n}) => {\n  let sel = state.selection;\n  if (sel.ranges.length > 1 || sel.main.empty) return false;\n  let {\n    from,\n    to\n  } = sel.main;\n  let ranges = [],\n    main = 0;\n  for (let cur = new SearchCursor(state.doc, state.sliceDoc(from, to)); !cur.next().done;) {\n    if (ranges.length > 1000) return false;\n    if (cur.value.from == from) main = ranges.length;\n    ranges.push(EditorSelection.range(cur.value.from, cur.value.to));\n  }\n  dispatch(state.update({\n    selection: EditorSelection.create(ranges, main),\n    userEvent: \"select.search.matches\"\n  }));\n  return true;\n};\n/**\nReplace the current match of the search query.\n*/\nconst replaceNext = /*@__PURE__*/searchCommand((view, {\n  query\n}) => {\n  let {\n      state\n    } = view,\n    {\n      from,\n      to\n    } = state.selection.main;\n  if (state.readOnly) return false;\n  let match = query.nextMatch(state, from, from);\n  if (!match) return false;\n  let next = match;\n  let changes = [],\n    selection,\n    replacement;\n  let effects = [];\n  if (next.from == from && next.to == to) {\n    replacement = state.toText(query.getReplacement(next));\n    changes.push({\n      from: next.from,\n      to: next.to,\n      insert: replacement\n    });\n    next = query.nextMatch(state, next.from, next.to);\n    effects.push(EditorView.announce.of(state.phrase(\"replaced match on line $\", state.doc.lineAt(from).number) + \".\"));\n  }\n  if (next) {\n    let off = changes.length == 0 || changes[0].from >= match.to ? 0 : match.to - match.from - replacement.length;\n    selection = EditorSelection.single(next.from - off, next.to - off);\n    effects.push(announceMatch(view, next));\n    effects.push(state.facet(searchConfigFacet).scrollToMatch(selection.main, view));\n  }\n  view.dispatch({\n    changes,\n    selection,\n    effects,\n    userEvent: \"input.replace\"\n  });\n  return true;\n});\n/**\nReplace all instances of the search query with the given\nreplacement.\n*/\nconst replaceAll = /*@__PURE__*/searchCommand((view, {\n  query\n}) => {\n  if (view.state.readOnly) return false;\n  let changes = query.matchAll(view.state, 1e9).map(match => {\n    let {\n      from,\n      to\n    } = match;\n    return {\n      from,\n      to,\n      insert: query.getReplacement(match)\n    };\n  });\n  if (!changes.length) return false;\n  let announceText = view.state.phrase(\"replaced $ matches\", changes.length) + \".\";\n  view.dispatch({\n    changes,\n    effects: EditorView.announce.of(announceText),\n    userEvent: \"input.replace.all\"\n  });\n  return true;\n});\nfunction createSearchPanel(view) {\n  return view.state.facet(searchConfigFacet).createPanel(view);\n}\nfunction defaultQuery(state, fallback) {\n  var _a, _b, _c, _d, _e;\n  let sel = state.selection.main;\n  let selText = sel.empty || sel.to > sel.from + 100 ? \"\" : state.sliceDoc(sel.from, sel.to);\n  if (fallback && !selText) return fallback;\n  let config = state.facet(searchConfigFacet);\n  return new SearchQuery({\n    search: ((_a = fallback === null || fallback === void 0 ? void 0 : fallback.literal) !== null && _a !== void 0 ? _a : config.literal) ? selText : selText.replace(/\\n/g, \"\\\\n\"),\n    caseSensitive: (_b = fallback === null || fallback === void 0 ? void 0 : fallback.caseSensitive) !== null && _b !== void 0 ? _b : config.caseSensitive,\n    literal: (_c = fallback === null || fallback === void 0 ? void 0 : fallback.literal) !== null && _c !== void 0 ? _c : config.literal,\n    regexp: (_d = fallback === null || fallback === void 0 ? void 0 : fallback.regexp) !== null && _d !== void 0 ? _d : config.regexp,\n    wholeWord: (_e = fallback === null || fallback === void 0 ? void 0 : fallback.wholeWord) !== null && _e !== void 0 ? _e : config.wholeWord\n  });\n}\nfunction getSearchInput(view) {\n  let panel = getPanel(view, createSearchPanel);\n  return panel && panel.dom.querySelector(\"[main-field]\");\n}\nfunction selectSearchInput(view) {\n  let input = getSearchInput(view);\n  if (input && input == view.root.activeElement) input.select();\n}\n/**\nMake sure the search panel is open and focused.\n*/\nconst openSearchPanel = view => {\n  let state = view.state.field(searchState, false);\n  if (state && state.panel) {\n    let searchInput = getSearchInput(view);\n    if (searchInput && searchInput != view.root.activeElement) {\n      let query = defaultQuery(view.state, state.query.spec);\n      if (query.valid) view.dispatch({\n        effects: setSearchQuery.of(query)\n      });\n      searchInput.focus();\n      searchInput.select();\n    }\n  } else {\n    view.dispatch({\n      effects: [togglePanel.of(true), state ? setSearchQuery.of(defaultQuery(view.state, state.query.spec)) : StateEffect.appendConfig.of(searchExtensions)]\n    });\n  }\n  return true;\n};\n/**\nClose the search panel.\n*/\nconst closeSearchPanel = view => {\n  let state = view.state.field(searchState, false);\n  if (!state || !state.panel) return false;\n  let panel = getPanel(view, createSearchPanel);\n  if (panel && panel.dom.contains(view.root.activeElement)) view.focus();\n  view.dispatch({\n    effects: togglePanel.of(false)\n  });\n  return true;\n};\n/**\nDefault search-related key bindings.\n\n - Mod-f: [`openSearchPanel`](https://codemirror.net/6/docs/ref/#search.openSearchPanel)\n - F3, Mod-g: [`findNext`](https://codemirror.net/6/docs/ref/#search.findNext)\n - Shift-F3, Shift-Mod-g: [`findPrevious`](https://codemirror.net/6/docs/ref/#search.findPrevious)\n - Mod-Alt-g: [`gotoLine`](https://codemirror.net/6/docs/ref/#search.gotoLine)\n - Mod-d: [`selectNextOccurrence`](https://codemirror.net/6/docs/ref/#search.selectNextOccurrence)\n*/\nconst searchKeymap = [{\n  key: \"Mod-f\",\n  run: openSearchPanel,\n  scope: \"editor search-panel\"\n}, {\n  key: \"F3\",\n  run: findNext,\n  shift: findPrevious,\n  scope: \"editor search-panel\",\n  preventDefault: true\n}, {\n  key: \"Mod-g\",\n  run: findNext,\n  shift: findPrevious,\n  scope: \"editor search-panel\",\n  preventDefault: true\n}, {\n  key: \"Escape\",\n  run: closeSearchPanel,\n  scope: \"editor search-panel\"\n}, {\n  key: \"Mod-Shift-l\",\n  run: selectSelectionMatches\n}, {\n  key: \"Mod-Alt-g\",\n  run: gotoLine\n}, {\n  key: \"Mod-d\",\n  run: selectNextOccurrence,\n  preventDefault: true\n}];\nclass SearchPanel {\n  constructor(view) {\n    this.view = view;\n    let query = this.query = view.state.field(searchState).query.spec;\n    this.commit = this.commit.bind(this);\n    this.searchField = elt(\"input\", {\n      value: query.search,\n      placeholder: phrase(view, \"Find\"),\n      \"aria-label\": phrase(view, \"Find\"),\n      class: \"cm-textfield\",\n      name: \"search\",\n      form: \"\",\n      \"main-field\": \"true\",\n      onchange: this.commit,\n      onkeyup: this.commit\n    });\n    this.replaceField = elt(\"input\", {\n      value: query.replace,\n      placeholder: phrase(view, \"Replace\"),\n      \"aria-label\": phrase(view, \"Replace\"),\n      class: \"cm-textfield\",\n      name: \"replace\",\n      form: \"\",\n      onchange: this.commit,\n      onkeyup: this.commit\n    });\n    this.caseField = elt(\"input\", {\n      type: \"checkbox\",\n      name: \"case\",\n      form: \"\",\n      checked: query.caseSensitive,\n      onchange: this.commit\n    });\n    this.reField = elt(\"input\", {\n      type: \"checkbox\",\n      name: \"re\",\n      form: \"\",\n      checked: query.regexp,\n      onchange: this.commit\n    });\n    this.wordField = elt(\"input\", {\n      type: \"checkbox\",\n      name: \"word\",\n      form: \"\",\n      checked: query.wholeWord,\n      onchange: this.commit\n    });\n    function button(name, onclick, content) {\n      return elt(\"button\", {\n        class: \"cm-button\",\n        name,\n        onclick,\n        type: \"button\"\n      }, content);\n    }\n    this.dom = elt(\"div\", {\n      onkeydown: e => this.keydown(e),\n      class: \"cm-search\"\n    }, [this.searchField, button(\"next\", () => findNext(view), [phrase(view, \"next\")]), button(\"prev\", () => findPrevious(view), [phrase(view, \"previous\")]), button(\"select\", () => selectMatches(view), [phrase(view, \"all\")]), elt(\"label\", null, [this.caseField, phrase(view, \"match case\")]), elt(\"label\", null, [this.reField, phrase(view, \"regexp\")]), elt(\"label\", null, [this.wordField, phrase(view, \"by word\")]), ...(view.state.readOnly ? [] : [elt(\"br\"), this.replaceField, button(\"replace\", () => replaceNext(view), [phrase(view, \"replace\")]), button(\"replaceAll\", () => replaceAll(view), [phrase(view, \"replace all\")])]), elt(\"button\", {\n      name: \"close\",\n      onclick: () => closeSearchPanel(view),\n      \"aria-label\": phrase(view, \"close\"),\n      type: \"button\"\n    }, [\"×\"])]);\n  }\n  commit() {\n    let query = new SearchQuery({\n      search: this.searchField.value,\n      caseSensitive: this.caseField.checked,\n      regexp: this.reField.checked,\n      wholeWord: this.wordField.checked,\n      replace: this.replaceField.value\n    });\n    if (!query.eq(this.query)) {\n      this.query = query;\n      this.view.dispatch({\n        effects: setSearchQuery.of(query)\n      });\n    }\n  }\n  keydown(e) {\n    if (runScopeHandlers(this.view, e, \"search-panel\")) {\n      e.preventDefault();\n    } else if (e.keyCode == 13 && e.target == this.searchField) {\n      e.preventDefault();\n      (e.shiftKey ? findPrevious : findNext)(this.view);\n    } else if (e.keyCode == 13 && e.target == this.replaceField) {\n      e.preventDefault();\n      replaceNext(this.view);\n    }\n  }\n  update(update) {\n    for (let tr of update.transactions) for (let effect of tr.effects) {\n      if (effect.is(setSearchQuery) && !effect.value.eq(this.query)) this.setQuery(effect.value);\n    }\n  }\n  setQuery(query) {\n    this.query = query;\n    this.searchField.value = query.search;\n    this.replaceField.value = query.replace;\n    this.caseField.checked = query.caseSensitive;\n    this.reField.checked = query.regexp;\n    this.wordField.checked = query.wholeWord;\n  }\n  mount() {\n    this.searchField.select();\n  }\n  get pos() {\n    return 80;\n  }\n  get top() {\n    return this.view.state.facet(searchConfigFacet).top;\n  }\n}\nfunction phrase(view, phrase) {\n  return view.state.phrase(phrase);\n}\nconst AnnounceMargin = 30;\nconst Break = /[\\s\\.,:;?!]/;\nfunction announceMatch(view, {\n  from,\n  to\n}) {\n  let line = view.state.doc.lineAt(from),\n    lineEnd = view.state.doc.lineAt(to).to;\n  let start = Math.max(line.from, from - AnnounceMargin),\n    end = Math.min(lineEnd, to + AnnounceMargin);\n  let text = view.state.sliceDoc(start, end);\n  if (start != line.from) {\n    for (let i = 0; i < AnnounceMargin; i++) if (!Break.test(text[i + 1]) && Break.test(text[i])) {\n      text = text.slice(i);\n      break;\n    }\n  }\n  if (end != lineEnd) {\n    for (let i = text.length - 1; i > text.length - AnnounceMargin; i--) if (!Break.test(text[i - 1]) && Break.test(text[i])) {\n      text = text.slice(0, i);\n      break;\n    }\n  }\n  return EditorView.announce.of(`${view.state.phrase(\"current match\")}. ${text} ${view.state.phrase(\"on line\")} ${line.number}.`);\n}\nconst baseTheme = /*@__PURE__*/EditorView.baseTheme({\n  \".cm-panel.cm-search\": {\n    padding: \"2px 6px 4px\",\n    position: \"relative\",\n    \"& [name=close]\": {\n      position: \"absolute\",\n      top: \"0\",\n      right: \"4px\",\n      backgroundColor: \"inherit\",\n      border: \"none\",\n      font: \"inherit\",\n      padding: 0,\n      margin: 0\n    },\n    \"& input, & button, & label\": {\n      margin: \".2em .6em .2em 0\"\n    },\n    \"& input[type=checkbox]\": {\n      marginRight: \".2em\"\n    },\n    \"& label\": {\n      fontSize: \"80%\",\n      whiteSpace: \"pre\"\n    }\n  },\n  \"&light .cm-searchMatch\": {\n    backgroundColor: \"#ffff0054\"\n  },\n  \"&dark .cm-searchMatch\": {\n    backgroundColor: \"#00ffff8a\"\n  },\n  \"&light .cm-searchMatch-selected\": {\n    backgroundColor: \"#ff6a0054\"\n  },\n  \"&dark .cm-searchMatch-selected\": {\n    backgroundColor: \"#ff00ff8a\"\n  }\n});\nconst searchExtensions = [searchState, /*@__PURE__*/Prec.low(searchHighlighter), baseTheme];\nexport { RegExpCursor, SearchCursor, SearchQuery, closeSearchPanel, findNext, findPrevious, getSearchQuery, gotoLine, highlightSelectionMatches, openSearchPanel, replaceAll, replaceNext, search, searchKeymap, searchPanelOpen, selectMatches, selectNextOccurrence, selectSelectionMatches, setSearchQuery };", "import { Decoration, showPanel, Editor<PERSON>iew, ViewPlugin, gutter, showTooltip, hoverTooltip, getPanel, logException, WidgetType, GutterMarker } from '@codemirror/view';\nimport { StateEffect, StateField, Facet, combineConfig, RangeSet, RangeSetBuilder } from '@codemirror/state';\nimport elt from 'crelt';\nclass SelectedDiagnostic {\n  constructor(from, to, diagnostic) {\n    this.from = from;\n    this.to = to;\n    this.diagnostic = diagnostic;\n  }\n}\nclass LintState {\n  constructor(diagnostics, panel, selected) {\n    this.diagnostics = diagnostics;\n    this.panel = panel;\n    this.selected = selected;\n  }\n  static init(diagnostics, panel, state) {\n    // Filter the list of diagnostics for which to create markers\n    let markedDiagnostics = diagnostics;\n    let diagnosticFilter = state.facet(lintConfig).markerFilter;\n    if (diagnosticFilter) markedDiagnostics = diagnosticFilter(markedDiagnostics, state);\n    let sorted = diagnostics.slice().sort((a, b) => a.from - b.from || a.to - b.to);\n    let deco = new RangeSetBuilder(),\n      active = [],\n      pos = 0;\n    for (let i = 0;;) {\n      let next = i == sorted.length ? null : sorted[i];\n      if (!next && !active.length) break;\n      let from, to;\n      if (active.length) {\n        from = pos;\n        to = active.reduce((p, d) => Math.min(p, d.to), next && next.from > from ? next.from : 1e8);\n      } else {\n        from = next.from;\n        to = next.to;\n        active.push(next);\n        i++;\n      }\n      while (i < sorted.length) {\n        let next = sorted[i];\n        if (next.from == from && (next.to > next.from || next.to == from)) {\n          active.push(next);\n          i++;\n          to = Math.min(next.to, to);\n        } else {\n          to = Math.min(next.from, to);\n          break;\n        }\n      }\n      let sev = maxSeverity(active);\n      if (active.some(d => d.from == d.to || d.from == d.to - 1 && state.doc.lineAt(d.from).to == d.from)) {\n        deco.add(from, from, Decoration.widget({\n          widget: new DiagnosticWidget(sev),\n          diagnostics: active.slice()\n        }));\n      } else {\n        let markClass = active.reduce((c, d) => d.markClass ? c + \" \" + d.markClass : c, \"\");\n        deco.add(from, to, Decoration.mark({\n          class: \"cm-lintRange cm-lintRange-\" + sev + markClass,\n          diagnostics: active.slice(),\n          inclusiveEnd: active.some(a => a.to > to)\n        }));\n      }\n      pos = to;\n      for (let i = 0; i < active.length; i++) if (active[i].to <= pos) active.splice(i--, 1);\n    }\n    let set = deco.finish();\n    return new LintState(set, panel, findDiagnostic(set));\n  }\n}\nfunction findDiagnostic(diagnostics, diagnostic = null, after = 0) {\n  let found = null;\n  diagnostics.between(after, 1e9, (from, to, {\n    spec\n  }) => {\n    if (diagnostic && spec.diagnostics.indexOf(diagnostic) < 0) return;\n    if (!found) found = new SelectedDiagnostic(from, to, diagnostic || spec.diagnostics[0]);else if (spec.diagnostics.indexOf(found.diagnostic) < 0) return false;else found = new SelectedDiagnostic(found.from, to, found.diagnostic);\n  });\n  return found;\n}\nfunction hideTooltip(tr, tooltip) {\n  let from = tooltip.pos,\n    to = tooltip.end || from;\n  let result = tr.state.facet(lintConfig).hideOn(tr, from, to);\n  if (result != null) return result;\n  let line = tr.startState.doc.lineAt(tooltip.pos);\n  return !!(tr.effects.some(e => e.is(setDiagnosticsEffect)) || tr.changes.touchesRange(line.from, Math.max(line.to, to)));\n}\nfunction maybeEnableLint(state, effects) {\n  return state.field(lintState, false) ? effects : effects.concat(StateEffect.appendConfig.of(lintExtensions));\n}\n/**\nReturns a transaction spec which updates the current set of\ndiagnostics, and enables the lint extension if if wasn't already\nactive.\n*/\nfunction setDiagnostics(state, diagnostics) {\n  return {\n    effects: maybeEnableLint(state, [setDiagnosticsEffect.of(diagnostics)])\n  };\n}\n/**\nThe state effect that updates the set of active diagnostics. Can\nbe useful when writing an extension that needs to track these.\n*/\nconst setDiagnosticsEffect = /*@__PURE__*/StateEffect.define();\nconst togglePanel = /*@__PURE__*/StateEffect.define();\nconst movePanelSelection = /*@__PURE__*/StateEffect.define();\nconst lintState = /*@__PURE__*/StateField.define({\n  create() {\n    return new LintState(Decoration.none, null, null);\n  },\n  update(value, tr) {\n    if (tr.docChanged && value.diagnostics.size) {\n      let mapped = value.diagnostics.map(tr.changes),\n        selected = null,\n        panel = value.panel;\n      if (value.selected) {\n        let selPos = tr.changes.mapPos(value.selected.from, 1);\n        selected = findDiagnostic(mapped, value.selected.diagnostic, selPos) || findDiagnostic(mapped, null, selPos);\n      }\n      if (!mapped.size && panel && tr.state.facet(lintConfig).autoPanel) panel = null;\n      value = new LintState(mapped, panel, selected);\n    }\n    for (let effect of tr.effects) {\n      if (effect.is(setDiagnosticsEffect)) {\n        let panel = !tr.state.facet(lintConfig).autoPanel ? value.panel : effect.value.length ? LintPanel.open : null;\n        value = LintState.init(effect.value, panel, tr.state);\n      } else if (effect.is(togglePanel)) {\n        value = new LintState(value.diagnostics, effect.value ? LintPanel.open : null, value.selected);\n      } else if (effect.is(movePanelSelection)) {\n        value = new LintState(value.diagnostics, value.panel, effect.value);\n      }\n    }\n    return value;\n  },\n  provide: f => [showPanel.from(f, val => val.panel), EditorView.decorations.from(f, s => s.diagnostics)]\n});\n/**\nReturns the number of active lint diagnostics in the given state.\n*/\nfunction diagnosticCount(state) {\n  let lint = state.field(lintState, false);\n  return lint ? lint.diagnostics.size : 0;\n}\nconst activeMark = /*@__PURE__*/Decoration.mark({\n  class: \"cm-lintRange cm-lintRange-active\"\n});\nfunction lintTooltip(view, pos, side) {\n  let {\n    diagnostics\n  } = view.state.field(lintState);\n  let found,\n    start = -1,\n    end = -1;\n  diagnostics.between(pos - (side < 0 ? 1 : 0), pos + (side > 0 ? 1 : 0), (from, to, {\n    spec\n  }) => {\n    if (pos >= from && pos <= to && (from == to || (pos > from || side > 0) && (pos < to || side < 0))) {\n      found = spec.diagnostics;\n      start = from;\n      end = to;\n      return false;\n    }\n  });\n  let diagnosticFilter = view.state.facet(lintConfig).tooltipFilter;\n  if (found && diagnosticFilter) found = diagnosticFilter(found, view.state);\n  if (!found) return null;\n  return {\n    pos: start,\n    end: end,\n    above: view.state.doc.lineAt(start).to < end,\n    create() {\n      return {\n        dom: diagnosticsTooltip(view, found)\n      };\n    }\n  };\n}\nfunction diagnosticsTooltip(view, diagnostics) {\n  return elt(\"ul\", {\n    class: \"cm-tooltip-lint\"\n  }, diagnostics.map(d => renderDiagnostic(view, d, false)));\n}\n/**\nCommand to open and focus the lint panel.\n*/\nconst openLintPanel = view => {\n  let field = view.state.field(lintState, false);\n  if (!field || !field.panel) view.dispatch({\n    effects: maybeEnableLint(view.state, [togglePanel.of(true)])\n  });\n  let panel = getPanel(view, LintPanel.open);\n  if (panel) panel.dom.querySelector(\".cm-panel-lint ul\").focus();\n  return true;\n};\n/**\nCommand to close the lint panel, when open.\n*/\nconst closeLintPanel = view => {\n  let field = view.state.field(lintState, false);\n  if (!field || !field.panel) return false;\n  view.dispatch({\n    effects: togglePanel.of(false)\n  });\n  return true;\n};\n/**\nMove the selection to the next diagnostic.\n*/\nconst nextDiagnostic = view => {\n  let field = view.state.field(lintState, false);\n  if (!field) return false;\n  let sel = view.state.selection.main,\n    next = field.diagnostics.iter(sel.to + 1);\n  if (!next.value) {\n    next = field.diagnostics.iter(0);\n    if (!next.value || next.from == sel.from && next.to == sel.to) return false;\n  }\n  view.dispatch({\n    selection: {\n      anchor: next.from,\n      head: next.to\n    },\n    scrollIntoView: true\n  });\n  return true;\n};\n/**\nMove the selection to the previous diagnostic.\n*/\nconst previousDiagnostic = view => {\n  let {\n      state\n    } = view,\n    field = state.field(lintState, false);\n  if (!field) return false;\n  let sel = state.selection.main;\n  let prevFrom, prevTo, lastFrom, lastTo;\n  field.diagnostics.between(0, state.doc.length, (from, to) => {\n    if (to < sel.to && (prevFrom == null || prevFrom < from)) {\n      prevFrom = from;\n      prevTo = to;\n    }\n    if (lastFrom == null || from > lastFrom) {\n      lastFrom = from;\n      lastTo = to;\n    }\n  });\n  if (lastFrom == null || prevFrom == null && lastFrom == sel.from) return false;\n  view.dispatch({\n    selection: {\n      anchor: prevFrom !== null && prevFrom !== void 0 ? prevFrom : lastFrom,\n      head: prevTo !== null && prevTo !== void 0 ? prevTo : lastTo\n    },\n    scrollIntoView: true\n  });\n  return true;\n};\n/**\nA set of default key bindings for the lint functionality.\n\n- Ctrl-Shift-m (Cmd-Shift-m on macOS): [`openLintPanel`](https://codemirror.net/6/docs/ref/#lint.openLintPanel)\n- F8: [`nextDiagnostic`](https://codemirror.net/6/docs/ref/#lint.nextDiagnostic)\n*/\nconst lintKeymap = [{\n  key: \"Mod-Shift-m\",\n  run: openLintPanel,\n  preventDefault: true\n}, {\n  key: \"F8\",\n  run: nextDiagnostic\n}];\nconst lintPlugin = /*@__PURE__*/ViewPlugin.fromClass(class {\n  constructor(view) {\n    this.view = view;\n    this.timeout = -1;\n    this.set = true;\n    let {\n      delay\n    } = view.state.facet(lintConfig);\n    this.lintTime = Date.now() + delay;\n    this.run = this.run.bind(this);\n    this.timeout = setTimeout(this.run, delay);\n  }\n  run() {\n    clearTimeout(this.timeout);\n    let now = Date.now();\n    if (now < this.lintTime - 10) {\n      this.timeout = setTimeout(this.run, this.lintTime - now);\n    } else {\n      this.set = false;\n      let {\n          state\n        } = this.view,\n        {\n          sources\n        } = state.facet(lintConfig);\n      if (sources.length) batchResults(sources.map(s => Promise.resolve(s(this.view))), annotations => {\n        if (this.view.state.doc == state.doc) this.view.dispatch(setDiagnostics(this.view.state, annotations.reduce((a, b) => a.concat(b))));\n      }, error => {\n        logException(this.view.state, error);\n      });\n    }\n  }\n  update(update) {\n    let config = update.state.facet(lintConfig);\n    if (update.docChanged || config != update.startState.facet(lintConfig) || config.needsRefresh && config.needsRefresh(update)) {\n      this.lintTime = Date.now() + config.delay;\n      if (!this.set) {\n        this.set = true;\n        this.timeout = setTimeout(this.run, config.delay);\n      }\n    }\n  }\n  force() {\n    if (this.set) {\n      this.lintTime = Date.now();\n      this.run();\n    }\n  }\n  destroy() {\n    clearTimeout(this.timeout);\n  }\n});\nfunction batchResults(promises, sink, error) {\n  let collected = [],\n    timeout = -1;\n  for (let p of promises) p.then(value => {\n    collected.push(value);\n    clearTimeout(timeout);\n    if (collected.length == promises.length) sink(collected);else timeout = setTimeout(() => sink(collected), 200);\n  }, error);\n}\nconst lintConfig = /*@__PURE__*/Facet.define({\n  combine(input) {\n    return Object.assign({\n      sources: input.map(i => i.source).filter(x => x != null)\n    }, combineConfig(input.map(i => i.config), {\n      delay: 750,\n      markerFilter: null,\n      tooltipFilter: null,\n      needsRefresh: null,\n      hideOn: () => null\n    }, {\n      needsRefresh: (a, b) => !a ? b : !b ? a : u => a(u) || b(u)\n    }));\n  }\n});\n/**\nGiven a diagnostic source, this function returns an extension that\nenables linting with that source. It will be called whenever the\neditor is idle (after its content changed). If `null` is given as\nsource, this only configures the lint extension.\n*/\nfunction linter(source, config = {}) {\n  return [lintConfig.of({\n    source,\n    config\n  }), lintPlugin, lintExtensions];\n}\n/**\nForces any linters [configured](https://codemirror.net/6/docs/ref/#lint.linter) to run when the\neditor is idle to run right away.\n*/\nfunction forceLinting(view) {\n  let plugin = view.plugin(lintPlugin);\n  if (plugin) plugin.force();\n}\nfunction assignKeys(actions) {\n  let assigned = [];\n  if (actions) actions: for (let {\n    name\n  } of actions) {\n    for (let i = 0; i < name.length; i++) {\n      let ch = name[i];\n      if (/[a-zA-Z]/.test(ch) && !assigned.some(c => c.toLowerCase() == ch.toLowerCase())) {\n        assigned.push(ch);\n        continue actions;\n      }\n    }\n    assigned.push(\"\");\n  }\n  return assigned;\n}\nfunction renderDiagnostic(view, diagnostic, inPanel) {\n  var _a;\n  let keys = inPanel ? assignKeys(diagnostic.actions) : [];\n  return elt(\"li\", {\n    class: \"cm-diagnostic cm-diagnostic-\" + diagnostic.severity\n  }, elt(\"span\", {\n    class: \"cm-diagnosticText\"\n  }, diagnostic.renderMessage ? diagnostic.renderMessage(view) : diagnostic.message), (_a = diagnostic.actions) === null || _a === void 0 ? void 0 : _a.map((action, i) => {\n    let fired = false,\n      click = e => {\n        e.preventDefault();\n        if (fired) return;\n        fired = true;\n        let found = findDiagnostic(view.state.field(lintState).diagnostics, diagnostic);\n        if (found) action.apply(view, found.from, found.to);\n      };\n    let {\n        name\n      } = action,\n      keyIndex = keys[i] ? name.indexOf(keys[i]) : -1;\n    let nameElt = keyIndex < 0 ? name : [name.slice(0, keyIndex), elt(\"u\", name.slice(keyIndex, keyIndex + 1)), name.slice(keyIndex + 1)];\n    return elt(\"button\", {\n      type: \"button\",\n      class: \"cm-diagnosticAction\",\n      onclick: click,\n      onmousedown: click,\n      \"aria-label\": ` Action: ${name}${keyIndex < 0 ? \"\" : ` (access key \"${keys[i]})\"`}.`\n    }, nameElt);\n  }), diagnostic.source && elt(\"div\", {\n    class: \"cm-diagnosticSource\"\n  }, diagnostic.source));\n}\nclass DiagnosticWidget extends WidgetType {\n  constructor(sev) {\n    super();\n    this.sev = sev;\n  }\n  eq(other) {\n    return other.sev == this.sev;\n  }\n  toDOM() {\n    return elt(\"span\", {\n      class: \"cm-lintPoint cm-lintPoint-\" + this.sev\n    });\n  }\n}\nclass PanelItem {\n  constructor(view, diagnostic) {\n    this.diagnostic = diagnostic;\n    this.id = \"item_\" + Math.floor(Math.random() * 0xffffffff).toString(16);\n    this.dom = renderDiagnostic(view, diagnostic, true);\n    this.dom.id = this.id;\n    this.dom.setAttribute(\"role\", \"option\");\n  }\n}\nclass LintPanel {\n  constructor(view) {\n    this.view = view;\n    this.items = [];\n    let onkeydown = event => {\n      if (event.keyCode == 27) {\n        // Escape\n        closeLintPanel(this.view);\n        this.view.focus();\n      } else if (event.keyCode == 38 || event.keyCode == 33) {\n        // ArrowUp, PageUp\n        this.moveSelection((this.selectedIndex - 1 + this.items.length) % this.items.length);\n      } else if (event.keyCode == 40 || event.keyCode == 34) {\n        // ArrowDown, PageDown\n        this.moveSelection((this.selectedIndex + 1) % this.items.length);\n      } else if (event.keyCode == 36) {\n        // Home\n        this.moveSelection(0);\n      } else if (event.keyCode == 35) {\n        // End\n        this.moveSelection(this.items.length - 1);\n      } else if (event.keyCode == 13) {\n        // Enter\n        this.view.focus();\n      } else if (event.keyCode >= 65 && event.keyCode <= 90 && this.selectedIndex >= 0) {\n        // A-Z\n        let {\n            diagnostic\n          } = this.items[this.selectedIndex],\n          keys = assignKeys(diagnostic.actions);\n        for (let i = 0; i < keys.length; i++) if (keys[i].toUpperCase().charCodeAt(0) == event.keyCode) {\n          let found = findDiagnostic(this.view.state.field(lintState).diagnostics, diagnostic);\n          if (found) diagnostic.actions[i].apply(view, found.from, found.to);\n        }\n      } else {\n        return;\n      }\n      event.preventDefault();\n    };\n    let onclick = event => {\n      for (let i = 0; i < this.items.length; i++) {\n        if (this.items[i].dom.contains(event.target)) this.moveSelection(i);\n      }\n    };\n    this.list = elt(\"ul\", {\n      tabIndex: 0,\n      role: \"listbox\",\n      \"aria-label\": this.view.state.phrase(\"Diagnostics\"),\n      onkeydown,\n      onclick\n    });\n    this.dom = elt(\"div\", {\n      class: \"cm-panel-lint\"\n    }, this.list, elt(\"button\", {\n      type: \"button\",\n      name: \"close\",\n      \"aria-label\": this.view.state.phrase(\"close\"),\n      onclick: () => closeLintPanel(this.view)\n    }, \"×\"));\n    this.update();\n  }\n  get selectedIndex() {\n    let selected = this.view.state.field(lintState).selected;\n    if (!selected) return -1;\n    for (let i = 0; i < this.items.length; i++) if (this.items[i].diagnostic == selected.diagnostic) return i;\n    return -1;\n  }\n  update() {\n    let {\n      diagnostics,\n      selected\n    } = this.view.state.field(lintState);\n    let i = 0,\n      needsSync = false,\n      newSelectedItem = null;\n    let seen = new Set();\n    diagnostics.between(0, this.view.state.doc.length, (_start, _end, {\n      spec\n    }) => {\n      for (let diagnostic of spec.diagnostics) {\n        if (seen.has(diagnostic)) continue;\n        seen.add(diagnostic);\n        let found = -1,\n          item;\n        for (let j = i; j < this.items.length; j++) if (this.items[j].diagnostic == diagnostic) {\n          found = j;\n          break;\n        }\n        if (found < 0) {\n          item = new PanelItem(this.view, diagnostic);\n          this.items.splice(i, 0, item);\n          needsSync = true;\n        } else {\n          item = this.items[found];\n          if (found > i) {\n            this.items.splice(i, found - i);\n            needsSync = true;\n          }\n        }\n        if (selected && item.diagnostic == selected.diagnostic) {\n          if (!item.dom.hasAttribute(\"aria-selected\")) {\n            item.dom.setAttribute(\"aria-selected\", \"true\");\n            newSelectedItem = item;\n          }\n        } else if (item.dom.hasAttribute(\"aria-selected\")) {\n          item.dom.removeAttribute(\"aria-selected\");\n        }\n        i++;\n      }\n    });\n    while (i < this.items.length && !(this.items.length == 1 && this.items[0].diagnostic.from < 0)) {\n      needsSync = true;\n      this.items.pop();\n    }\n    if (this.items.length == 0) {\n      this.items.push(new PanelItem(this.view, {\n        from: -1,\n        to: -1,\n        severity: \"info\",\n        message: this.view.state.phrase(\"No diagnostics\")\n      }));\n      needsSync = true;\n    }\n    if (newSelectedItem) {\n      this.list.setAttribute(\"aria-activedescendant\", newSelectedItem.id);\n      this.view.requestMeasure({\n        key: this,\n        read: () => ({\n          sel: newSelectedItem.dom.getBoundingClientRect(),\n          panel: this.list.getBoundingClientRect()\n        }),\n        write: ({\n          sel,\n          panel\n        }) => {\n          let scaleY = panel.height / this.list.offsetHeight;\n          if (sel.top < panel.top) this.list.scrollTop -= (panel.top - sel.top) / scaleY;else if (sel.bottom > panel.bottom) this.list.scrollTop += (sel.bottom - panel.bottom) / scaleY;\n        }\n      });\n    } else if (this.selectedIndex < 0) {\n      this.list.removeAttribute(\"aria-activedescendant\");\n    }\n    if (needsSync) this.sync();\n  }\n  sync() {\n    let domPos = this.list.firstChild;\n    function rm() {\n      let prev = domPos;\n      domPos = prev.nextSibling;\n      prev.remove();\n    }\n    for (let item of this.items) {\n      if (item.dom.parentNode == this.list) {\n        while (domPos != item.dom) rm();\n        domPos = item.dom.nextSibling;\n      } else {\n        this.list.insertBefore(item.dom, domPos);\n      }\n    }\n    while (domPos) rm();\n  }\n  moveSelection(selectedIndex) {\n    if (this.selectedIndex < 0) return;\n    let field = this.view.state.field(lintState);\n    let selection = findDiagnostic(field.diagnostics, this.items[selectedIndex].diagnostic);\n    if (!selection) return;\n    this.view.dispatch({\n      selection: {\n        anchor: selection.from,\n        head: selection.to\n      },\n      scrollIntoView: true,\n      effects: movePanelSelection.of(selection)\n    });\n  }\n  static open(view) {\n    return new LintPanel(view);\n  }\n}\nfunction svg(content, attrs = `viewBox=\"0 0 40 40\"`) {\n  return `url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" ${attrs}>${encodeURIComponent(content)}</svg>')`;\n}\nfunction underline(color) {\n  return svg(`<path d=\"m0 2.5 l2 -1.5 l1 0 l2 1.5 l1 0\" stroke=\"${color}\" fill=\"none\" stroke-width=\".7\"/>`, `width=\"6\" height=\"3\"`);\n}\nconst baseTheme = /*@__PURE__*/EditorView.baseTheme({\n  \".cm-diagnostic\": {\n    padding: \"3px 6px 3px 8px\",\n    marginLeft: \"-1px\",\n    display: \"block\",\n    whiteSpace: \"pre-wrap\"\n  },\n  \".cm-diagnostic-error\": {\n    borderLeft: \"5px solid #d11\"\n  },\n  \".cm-diagnostic-warning\": {\n    borderLeft: \"5px solid orange\"\n  },\n  \".cm-diagnostic-info\": {\n    borderLeft: \"5px solid #999\"\n  },\n  \".cm-diagnostic-hint\": {\n    borderLeft: \"5px solid #66d\"\n  },\n  \".cm-diagnosticAction\": {\n    font: \"inherit\",\n    border: \"none\",\n    padding: \"2px 4px\",\n    backgroundColor: \"#444\",\n    color: \"white\",\n    borderRadius: \"3px\",\n    marginLeft: \"8px\",\n    cursor: \"pointer\"\n  },\n  \".cm-diagnosticSource\": {\n    fontSize: \"70%\",\n    opacity: .7\n  },\n  \".cm-lintRange\": {\n    backgroundPosition: \"left bottom\",\n    backgroundRepeat: \"repeat-x\",\n    paddingBottom: \"0.7px\"\n  },\n  \".cm-lintRange-error\": {\n    backgroundImage: /*@__PURE__*/underline(\"#d11\")\n  },\n  \".cm-lintRange-warning\": {\n    backgroundImage: /*@__PURE__*/underline(\"orange\")\n  },\n  \".cm-lintRange-info\": {\n    backgroundImage: /*@__PURE__*/underline(\"#999\")\n  },\n  \".cm-lintRange-hint\": {\n    backgroundImage: /*@__PURE__*/underline(\"#66d\")\n  },\n  \".cm-lintRange-active\": {\n    backgroundColor: \"#ffdd9980\"\n  },\n  \".cm-tooltip-lint\": {\n    padding: 0,\n    margin: 0\n  },\n  \".cm-lintPoint\": {\n    position: \"relative\",\n    \"&:after\": {\n      content: '\"\"',\n      position: \"absolute\",\n      bottom: 0,\n      left: \"-2px\",\n      borderLeft: \"3px solid transparent\",\n      borderRight: \"3px solid transparent\",\n      borderBottom: \"4px solid #d11\"\n    }\n  },\n  \".cm-lintPoint-warning\": {\n    \"&:after\": {\n      borderBottomColor: \"orange\"\n    }\n  },\n  \".cm-lintPoint-info\": {\n    \"&:after\": {\n      borderBottomColor: \"#999\"\n    }\n  },\n  \".cm-lintPoint-hint\": {\n    \"&:after\": {\n      borderBottomColor: \"#66d\"\n    }\n  },\n  \".cm-panel.cm-panel-lint\": {\n    position: \"relative\",\n    \"& ul\": {\n      maxHeight: \"100px\",\n      overflowY: \"auto\",\n      \"& [aria-selected]\": {\n        backgroundColor: \"#ddd\",\n        \"& u\": {\n          textDecoration: \"underline\"\n        }\n      },\n      \"&:focus [aria-selected]\": {\n        background_fallback: \"#bdf\",\n        backgroundColor: \"Highlight\",\n        color_fallback: \"white\",\n        color: \"HighlightText\"\n      },\n      \"& u\": {\n        textDecoration: \"none\"\n      },\n      padding: 0,\n      margin: 0\n    },\n    \"& [name=close]\": {\n      position: \"absolute\",\n      top: \"0\",\n      right: \"2px\",\n      background: \"inherit\",\n      border: \"none\",\n      font: \"inherit\",\n      padding: 0,\n      margin: 0\n    }\n  }\n});\nfunction severityWeight(sev) {\n  return sev == \"error\" ? 4 : sev == \"warning\" ? 3 : sev == \"info\" ? 2 : 1;\n}\nfunction maxSeverity(diagnostics) {\n  let sev = \"hint\",\n    weight = 1;\n  for (let d of diagnostics) {\n    let w = severityWeight(d.severity);\n    if (w > weight) {\n      weight = w;\n      sev = d.severity;\n    }\n  }\n  return sev;\n}\nclass LintGutterMarker extends GutterMarker {\n  constructor(diagnostics) {\n    super();\n    this.diagnostics = diagnostics;\n    this.severity = maxSeverity(diagnostics);\n  }\n  toDOM(view) {\n    let elt = document.createElement(\"div\");\n    elt.className = \"cm-lint-marker cm-lint-marker-\" + this.severity;\n    let diagnostics = this.diagnostics;\n    let diagnosticsFilter = view.state.facet(lintGutterConfig).tooltipFilter;\n    if (diagnosticsFilter) diagnostics = diagnosticsFilter(diagnostics, view.state);\n    if (diagnostics.length) elt.onmouseover = () => gutterMarkerMouseOver(view, elt, diagnostics);\n    return elt;\n  }\n}\nfunction trackHoverOn(view, marker) {\n  let mousemove = event => {\n    let rect = marker.getBoundingClientRect();\n    if (event.clientX > rect.left - 10 /* Hover.Margin */ && event.clientX < rect.right + 10 /* Hover.Margin */ && event.clientY > rect.top - 10 /* Hover.Margin */ && event.clientY < rect.bottom + 10 /* Hover.Margin */) return;\n    for (let target = event.target; target; target = target.parentNode) {\n      if (target.nodeType == 1 && target.classList.contains(\"cm-tooltip-lint\")) return;\n    }\n    window.removeEventListener(\"mousemove\", mousemove);\n    if (view.state.field(lintGutterTooltip)) view.dispatch({\n      effects: setLintGutterTooltip.of(null)\n    });\n  };\n  window.addEventListener(\"mousemove\", mousemove);\n}\nfunction gutterMarkerMouseOver(view, marker, diagnostics) {\n  function hovered() {\n    let line = view.elementAtHeight(marker.getBoundingClientRect().top + 5 - view.documentTop);\n    const linePos = view.coordsAtPos(line.from);\n    if (linePos) {\n      view.dispatch({\n        effects: setLintGutterTooltip.of({\n          pos: line.from,\n          above: false,\n          clip: false,\n          create() {\n            return {\n              dom: diagnosticsTooltip(view, diagnostics),\n              getCoords: () => marker.getBoundingClientRect()\n            };\n          }\n        })\n      });\n    }\n    marker.onmouseout = marker.onmousemove = null;\n    trackHoverOn(view, marker);\n  }\n  let {\n    hoverTime\n  } = view.state.facet(lintGutterConfig);\n  let hoverTimeout = setTimeout(hovered, hoverTime);\n  marker.onmouseout = () => {\n    clearTimeout(hoverTimeout);\n    marker.onmouseout = marker.onmousemove = null;\n  };\n  marker.onmousemove = () => {\n    clearTimeout(hoverTimeout);\n    hoverTimeout = setTimeout(hovered, hoverTime);\n  };\n}\nfunction markersForDiagnostics(doc, diagnostics) {\n  let byLine = Object.create(null);\n  for (let diagnostic of diagnostics) {\n    let line = doc.lineAt(diagnostic.from);\n    (byLine[line.from] || (byLine[line.from] = [])).push(diagnostic);\n  }\n  let markers = [];\n  for (let line in byLine) {\n    markers.push(new LintGutterMarker(byLine[line]).range(+line));\n  }\n  return RangeSet.of(markers, true);\n}\nconst lintGutterExtension = /*@__PURE__*/gutter({\n  class: \"cm-gutter-lint\",\n  markers: view => view.state.field(lintGutterMarkers),\n  widgetMarker: (view, widget, block) => {\n    let diagnostics = [];\n    view.state.field(lintGutterMarkers).between(block.from, block.to, (from, to, value) => {\n      if (from > block.from && from < block.to) diagnostics.push(...value.diagnostics);\n    });\n    return diagnostics.length ? new LintGutterMarker(diagnostics) : null;\n  }\n});\nconst lintGutterMarkers = /*@__PURE__*/StateField.define({\n  create() {\n    return RangeSet.empty;\n  },\n  update(markers, tr) {\n    markers = markers.map(tr.changes);\n    let diagnosticFilter = tr.state.facet(lintGutterConfig).markerFilter;\n    for (let effect of tr.effects) {\n      if (effect.is(setDiagnosticsEffect)) {\n        let diagnostics = effect.value;\n        if (diagnosticFilter) diagnostics = diagnosticFilter(diagnostics || [], tr.state);\n        markers = markersForDiagnostics(tr.state.doc, diagnostics.slice(0));\n      }\n    }\n    return markers;\n  }\n});\nconst setLintGutterTooltip = /*@__PURE__*/StateEffect.define();\nconst lintGutterTooltip = /*@__PURE__*/StateField.define({\n  create() {\n    return null;\n  },\n  update(tooltip, tr) {\n    if (tooltip && tr.docChanged) tooltip = hideTooltip(tr, tooltip) ? null : Object.assign(Object.assign({}, tooltip), {\n      pos: tr.changes.mapPos(tooltip.pos)\n    });\n    return tr.effects.reduce((t, e) => e.is(setLintGutterTooltip) ? e.value : t, tooltip);\n  },\n  provide: field => showTooltip.from(field)\n});\nconst lintGutterTheme = /*@__PURE__*/EditorView.baseTheme({\n  \".cm-gutter-lint\": {\n    width: \"1.4em\",\n    \"& .cm-gutterElement\": {\n      padding: \".2em\"\n    }\n  },\n  \".cm-lint-marker\": {\n    width: \"1em\",\n    height: \"1em\"\n  },\n  \".cm-lint-marker-info\": {\n    content: /*@__PURE__*/svg(`<path fill=\"#aaf\" stroke=\"#77e\" stroke-width=\"6\" stroke-linejoin=\"round\" d=\"M5 5L35 5L35 35L5 35Z\"/>`)\n  },\n  \".cm-lint-marker-warning\": {\n    content: /*@__PURE__*/svg(`<path fill=\"#fe8\" stroke=\"#fd7\" stroke-width=\"6\" stroke-linejoin=\"round\" d=\"M20 6L37 35L3 35Z\"/>`)\n  },\n  \".cm-lint-marker-error\": {\n    content: /*@__PURE__*/svg(`<circle cx=\"20\" cy=\"20\" r=\"15\" fill=\"#f87\" stroke=\"#f43\" stroke-width=\"6\"/>`)\n  }\n});\nconst lintExtensions = [lintState, /*@__PURE__*/EditorView.decorations.compute([lintState], state => {\n  let {\n    selected,\n    panel\n  } = state.field(lintState);\n  return !selected || !panel || selected.from == selected.to ? Decoration.none : Decoration.set([activeMark.range(selected.from, selected.to)]);\n}), /*@__PURE__*/hoverTooltip(lintTooltip, {\n  hideOn: hideTooltip\n}), baseTheme];\nconst lintGutterConfig = /*@__PURE__*/Facet.define({\n  combine(configs) {\n    return combineConfig(configs, {\n      hoverTime: 300 /* Hover.Time */,\n      markerFilter: null,\n      tooltipFilter: null\n    });\n  }\n});\n/**\nReturns an extension that installs a gutter showing markers for\neach line that has diagnostics, which can be hovered over to see\nthe diagnostics.\n*/\nfunction lintGutter(config = {}) {\n  return [lintGutterConfig.of(config), lintGutterMarkers, lintGutterExtension, lintGutterTheme, lintGutterTooltip];\n}\n/**\nIterate over the marked diagnostics for the given editor state,\ncalling `f` for each of them. Note that, if the document changed\nsince the diagnostics were created, the `Diagnostic` object will\nhold the original outdated position, whereas the `to` and `from`\narguments hold the diagnostic's current position.\n*/\nfunction forEachDiagnostic(state, f) {\n  let lState = state.field(lintState, false);\n  if (lState && lState.diagnostics.size) {\n    let pending = [],\n      pendingStart = [],\n      lastEnd = -1;\n    for (let iter = RangeSet.iter([lState.diagnostics]);; iter.next()) {\n      for (let i = 0; i < pending.length; i++) if (!iter.value || iter.value.spec.diagnostics.indexOf(pending[i]) < 0) {\n        f(pending[i], pendingStart[i], lastEnd);\n        pending.splice(i, 1);\n        pendingStart.splice(i--, 1);\n      }\n      if (!iter.value) break;\n      for (let d of iter.value.spec.diagnostics) if (pending.indexOf(d) < 0) {\n        pending.push(d);\n        pendingStart.push(iter.from);\n      }\n      lastEnd = iter.to;\n    }\n  }\n}\nexport { closeLintPanel, diagnosticCount, forEachDiagnostic, forceLinting, lintGutter, lintKeymap, linter, nextDiagnostic, openLintPanel, previousDiagnostic, setDiagnostics, setDiagnosticsEffect };", "import { lineNumbers, highlightActiveLineGutter, highlightSpecialChars, drawSelection, dropCursor, rectangularSelection, crosshairCursor, highlightActiveLine, keymap } from '@codemirror/view';\nexport { EditorView } from '@codemirror/view';\nimport { EditorState } from '@codemirror/state';\nimport { foldGutter, indentOnInput, syntaxHighlighting, defaultHighlightStyle, bracketMatching, foldKeymap } from '@codemirror/language';\nimport { history, defaultKeymap, historyKeymap } from '@codemirror/commands';\nimport { highlightSelectionMatches, searchKeymap } from '@codemirror/search';\nimport { closeBrackets, autocompletion, closeBracketsKeymap, completionKeymap } from '@codemirror/autocomplete';\nimport { lintKeymap } from '@codemirror/lint';\n\n// (The superfluous function calls around the list of extensions work\n// around current limitations in tree-shaking software.)\n/**\nThis is an extension value that just pulls together a number of\nextensions that you might want in a basic editor. It is meant as a\nconvenient helper to quickly set up CodeMirror without installing\nand importing a lot of separate packages.\n\nSpecifically, it includes...\n\n - [the default command bindings](https://codemirror.net/6/docs/ref/#commands.defaultKeymap)\n - [line numbers](https://codemirror.net/6/docs/ref/#view.lineNumbers)\n - [special character highlighting](https://codemirror.net/6/docs/ref/#view.highlightSpecialChars)\n - [the undo history](https://codemirror.net/6/docs/ref/#commands.history)\n - [a fold gutter](https://codemirror.net/6/docs/ref/#language.foldGutter)\n - [custom selection drawing](https://codemirror.net/6/docs/ref/#view.drawSelection)\n - [drop cursor](https://codemirror.net/6/docs/ref/#view.dropCursor)\n - [multiple selections](https://codemirror.net/6/docs/ref/#state.EditorState^allowMultipleSelections)\n - [reindentation on input](https://codemirror.net/6/docs/ref/#language.indentOnInput)\n - [the default highlight style](https://codemirror.net/6/docs/ref/#language.defaultHighlightStyle) (as fallback)\n - [bracket matching](https://codemirror.net/6/docs/ref/#language.bracketMatching)\n - [bracket closing](https://codemirror.net/6/docs/ref/#autocomplete.closeBrackets)\n - [autocompletion](https://codemirror.net/6/docs/ref/#autocomplete.autocompletion)\n - [rectangular selection](https://codemirror.net/6/docs/ref/#view.rectangularSelection) and [crosshair cursor](https://codemirror.net/6/docs/ref/#view.crosshairCursor)\n - [active line highlighting](https://codemirror.net/6/docs/ref/#view.highlightActiveLine)\n - [active line gutter highlighting](https://codemirror.net/6/docs/ref/#view.highlightActiveLineGutter)\n - [selection match highlighting](https://codemirror.net/6/docs/ref/#search.highlightSelectionMatches)\n - [search](https://codemirror.net/6/docs/ref/#search.searchKeymap)\n - [linting](https://codemirror.net/6/docs/ref/#lint.lintKeymap)\n\n(You'll probably want to add some language package to your setup\ntoo.)\n\nThis extension does not allow customization. The idea is that,\nonce you decide you want to configure your editor more precisely,\nyou take this package's source (which is just a bunch of imports\nand an array literal), copy it into your own code, and adjust it\nas desired.\n*/\nconst basicSetup = /*@__PURE__*/(() => [lineNumbers(), highlightActiveLineGutter(), highlightSpecialChars(), history(), foldGutter(), drawSelection(), dropCursor(), EditorState.allowMultipleSelections.of(true), indentOnInput(), syntaxHighlighting(defaultHighlightStyle, {\n  fallback: true\n}), bracketMatching(), closeBrackets(), autocompletion(), rectangularSelection(), crosshairCursor(), highlightActiveLine(), highlightSelectionMatches(), keymap.of([...closeBracketsKeymap, ...defaultKeymap, ...searchKeymap, ...historyKeymap, ...foldKeymap, ...completionKeymap, ...lintKeymap])])();\n/**\nA minimal set of extensions to create a functional editor. Only\nincludes [the default keymap](https://codemirror.net/6/docs/ref/#commands.defaultKeymap), [undo\nhistory](https://codemirror.net/6/docs/ref/#commands.history), [special character\nhighlighting](https://codemirror.net/6/docs/ref/#view.highlightSpecialChars), [custom selection\ndrawing](https://codemirror.net/6/docs/ref/#view.drawSelection), and [default highlight\nstyle](https://codemirror.net/6/docs/ref/#language.defaultHighlightStyle).\n*/\nconst minimalSetup = /*@__PURE__*/(() => [highlightSpecialChars(), history(), drawSelection(), syntaxHighlighting(defaultHighlightStyle, {\n  fallback: true\n}), keymap.of([...defaultKeymap, ...historyKeymap])])();\nexport { basicSetup, minimalSetup };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,gBAAgB,YAAU;AAC9B,MAAI;AAAA,IACA;AAAA,EACF,IAAI,QACJ,OAAO,MAAM,IAAI,OAAO,MAAM,UAAU,KAAK,IAAI,GACjD,SAAS,UAAU,OAAO,OAAO,KAAK,IAAI;AAC5C,SAAO,OAAO,OAAO,kBAAkB,MAAM,IAAI,OAAO,QAAQ,yBAAyB,MAAM,IAAI;AACrG;AACA,SAAS,QAAQ,GAAG,QAAQ;AAC1B,SAAO,CAAC;AAAA,IACN;AAAA,IACA;AAAA,EACF,MAAM;AACJ,QAAI,MAAM,SAAU,QAAO;AAC3B,QAAI,KAAK,EAAE,QAAQ,KAAK;AACxB,QAAI,CAAC,GAAI,QAAO;AAChB,aAAS,MAAM,OAAO,EAAE,CAAC;AACzB,WAAO;AAAA,EACT;AACF;AAOA,IAAM,oBAAiC;AAAA,EAAQ;AAAA,EAAmB;AAAA;AAA4B;AAI9F,IAAM,cAA2B;AAAA,EAAQ;AAAA,EAAmB;AAAA;AAA6B;AAIzF,IAAM,gBAA6B;AAAA,EAAQ;AAAA,EAAmB;AAAA;AAA+B;AAO7F,IAAM,qBAAkC;AAAA,EAAQ;AAAA,EAAoB;AAAA;AAA4B;AAIhG,IAAM,eAA4B;AAAA,EAAQ;AAAA,EAAoB;AAAA;AAA6B;AAI3F,IAAM,iBAA8B;AAAA,EAAQ;AAAA,EAAoB;AAAA;AAA+B;AAK/F,IAAM,2BAAwC;AAAA,EAAQ,CAAC,GAAG,MAAM,mBAAmB,GAAG,GAAG,mBAAmB,CAAC,CAAC;AAAA,EAAG;AAAA;AAA4B;AAC7I,SAAS,UAAU,OAAO,KAAK;AAC7B,MAAI,OAAO,MAAM,eAAe,iBAAiB,GAAG;AACpD,SAAO,KAAK,SAAS,KAAK,CAAC,IAAI,CAAC;AAClC;AACA,IAAM,eAAe;AAKrB,SAAS,iBAAiB,OAAO;AAAA,EAC/B;AAAA,EACA;AACF,GAAG,MAAM,IAAI;AACX,MAAI,aAAa,MAAM,SAAS,OAAO,cAAc,IAAI;AACzD,MAAI,YAAY,MAAM,SAAS,IAAI,KAAK,YAAY;AACpD,MAAI,cAAc,OAAO,KAAK,UAAU,EAAE,CAAC,EAAE,QAC3C,aAAa,OAAO,KAAK,SAAS,EAAE,CAAC,EAAE;AACzC,MAAI,YAAY,WAAW,SAAS;AACpC,MAAI,WAAW,MAAM,YAAY,KAAK,QAAQ,SAAS,KAAK,QAAQ,UAAU,MAAM,YAAY,aAAa,MAAM,MAAM,KAAK,OAAO;AACnI,WAAO;AAAA,MACL,MAAM;AAAA,QACJ,KAAK,OAAO;AAAA,QACZ,QAAQ,eAAe;AAAA,MACzB;AAAA,MACA,OAAO;AAAA,QACL,KAAK,KAAK;AAAA,QACV,QAAQ,cAAc;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AACA,MAAI,WAAW;AACf,MAAI,KAAK,QAAQ,IAAI,cAAc;AACjC,gBAAY,UAAU,MAAM,SAAS,MAAM,EAAE;AAAA,EAC/C,OAAO;AACL,gBAAY,MAAM,SAAS,MAAM,OAAO,YAAY;AACpD,cAAU,MAAM,SAAS,KAAK,cAAc,EAAE;AAAA,EAChD;AACA,MAAI,aAAa,OAAO,KAAK,SAAS,EAAE,CAAC,EAAE,QACzC,WAAW,OAAO,KAAK,OAAO,EAAE,CAAC,EAAE;AACrC,MAAI,SAAS,QAAQ,SAAS,WAAW,MAAM;AAC/C,MAAI,UAAU,MAAM,YAAY,aAAa,KAAK,MAAM,KAAK,QAAQ,QAAQ,MAAM,QAAQ,SAAS,MAAM,MAAM,KAAK,OAAO;AAC1H,WAAO;AAAA,MACL,MAAM;AAAA,QACJ,KAAK,OAAO,aAAa,KAAK;AAAA,QAC9B,QAAQ,KAAK,KAAK,UAAU,OAAO,aAAa,KAAK,MAAM,CAAC,IAAI,IAAI;AAAA,MACtE;AAAA,MACA,OAAO;AAAA,QACL,KAAK,KAAK,WAAW,MAAM;AAAA,QAC3B,QAAQ,KAAK,KAAK,QAAQ,OAAO,SAAS,CAAC,CAAC,IAAI,IAAI;AAAA,MACtD;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,OAAO;AACjC,MAAI,SAAS,CAAC;AACd,WAAS,KAAK,MAAM,UAAU,QAAQ;AACpC,QAAI,WAAW,MAAM,IAAI,OAAO,EAAE,IAAI;AACtC,QAAI,SAAS,EAAE,MAAM,SAAS,KAAK,WAAW,MAAM,IAAI,OAAO,EAAE,EAAE;AACnE,QAAI,OAAO,OAAO,SAAS,QAAQ,OAAO,QAAQ,EAAE,GAAI,UAAS,EAAE,MAAM,SAAS,KAAK,IAAI,WAAW,MAAM,IAAI,OAAO,EAAE,KAAK,CAAC;AAC/H,QAAI,OAAO,OAAO,SAAS;AAC3B,QAAI,QAAQ,KAAK,OAAO,IAAI,EAAE,KAAK,SAAS,KAAM,QAAO,IAAI,EAAE,KAAK,OAAO;AAAA,QAAQ,QAAO,KAAK;AAAA,MAC7F,MAAM,SAAS,OAAO,OAAO,KAAK,SAAS,IAAI,EAAE,CAAC,EAAE;AAAA,MACpD,IAAI,OAAO;AAAA,IACb,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAGA,SAAS,mBAAmB,QAAQ,OAAO,SAAS,MAAM,UAAU,QAAQ;AAC1E,MAAI,SAAS,OAAO,IAAI,OAAK,UAAU,OAAO,EAAE,IAAI,EAAE,KAAK;AAC3D,MAAI,CAAC,OAAO,MAAM,OAAK,CAAC,EAAG,QAAO;AAClC,MAAI,WAAW,OAAO,IAAI,CAAC,GAAG,MAAM,iBAAiB,OAAO,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC;AACpF,MAAI,UAAU,KAAmC,CAAC,SAAS,MAAM,OAAK,CAAC,GAAG;AACxE,WAAO;AAAA,MACL,SAAS,MAAM,QAAQ,OAAO,IAAI,CAAC,OAAO,MAAM;AAC9C,YAAI,SAAS,CAAC,EAAG,QAAO,CAAC;AACzB,eAAO,CAAC;AAAA,UACN,MAAM,MAAM;AAAA,UACZ,QAAQ,OAAO,CAAC,EAAE,OAAO;AAAA,QAC3B,GAAG;AAAA,UACD,MAAM,MAAM;AAAA,UACZ,QAAQ,MAAM,OAAO,CAAC,EAAE;AAAA,QAC1B,CAAC;AAAA,MACH,CAAC,CAAC;AAAA,IACJ;AAAA,EACF,WAAW,UAAU,KAAiC,SAAS,KAAK,OAAK,CAAC,GAAG;AAC3E,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,GAAG,SAAS,IAAI,SAAS,QAAQ,IAAK,KAAI,UAAU,SAAS,CAAC,GAAG;AAC5E,UAAI,QAAQ,OAAO,CAAC,GAClB;AAAA,QACE;AAAA,QACA;AAAA,MACF,IAAI;AACN,cAAQ,KAAK;AAAA,QACX,MAAM,KAAK,MAAM,MAAM,KAAK;AAAA,QAC5B,IAAI,KAAK,MAAM,KAAK;AAAA,MACtB,GAAG;AAAA,QACD,MAAM,MAAM,MAAM,MAAM;AAAA,QACxB,IAAI,MAAM,MAAM,MAAM,MAAM;AAAA,MAC9B,CAAC;AAAA,IACH;AACA,WAAO;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB,QAAQ,OAAO,SAAS,MAAM,UAAU,QAAQ;AACzE,MAAI,QAAQ,CAAC;AACb,MAAI,WAAW;AACf,WAAS;AAAA,IACP;AAAA,IACA;AAAA,EACF,KAAK,QAAQ;AACX,QAAI,SAAS,MAAM,QACjB,YAAY;AACd,QAAI,QAAQ,UAAU,OAAO,IAAI,EAAE;AACnC,QAAI,CAAC,MAAO;AACZ,aAAS,MAAM,MAAM,OAAO,MAAK;AAC/B,UAAI,OAAO,MAAM,IAAI,OAAO,GAAG;AAC/B,UAAI,KAAK,OAAO,aAAa,QAAQ,MAAM,KAAK,KAAK,OAAO;AAC1D,mBAAW,KAAK;AAChB,YAAI,SAAS,OAAO,KAAK,KAAK,IAAI,EAAE,CAAC,EAAE;AACvC,YAAIA,SAAQ,UAAU,KAAK;AAC3B,YAAI,UAAU,KAAK,KAAK,MAAM,QAAQ,SAAS,MAAM,MAAM,KAAK,QAAQ,SAAS;AACjF,YAAI,SAAS,KAAK,KAAK,UAAU,SAAS,UAAW,aAAY;AACjE,cAAM,KAAK;AAAA,UACT;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,OAAAA;AAAA,UACA,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AACA,YAAM,KAAK,KAAK;AAAA,IAClB;AACA,QAAI,YAAY;AAAK,eAAS,IAAI,QAAQ,IAAI,MAAM,QAAQ,IAAK,KAAI,MAAM,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,KAAK,KAAK,OAAQ,OAAM,CAAC,EAAE,SAAS;AAAA;AACpI,QAAI,MAAM,UAAU,SAAS,EAAG,OAAM,MAAM,EAAE,SAAS;AAAA,EACzD;AACA,MAAI,UAAU,KAAmC,MAAM,KAAK,OAAK,EAAE,UAAU,MAAM,CAAC,EAAE,SAAS,EAAE,OAAO,GAAG;AACzG,QAAI,UAAU,CAAC;AACf,aAAS;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAAA;AAAA,MACA;AAAA,IACF,KAAK,MAAO,KAAI,UAAU,CAACA,OAAO,SAAQ,KAAK;AAAA,MAC7C,MAAM,KAAK,OAAO;AAAA,MAClB,QAAQ,QAAQ;AAAA,IAClB,CAAC;AACD,QAAI,YAAY,MAAM,QAAQ,OAAO;AACrC,WAAO;AAAA,MACL,SAAS;AAAA,MACT,WAAW,MAAM,UAAU,IAAI,WAAW,CAAC;AAAA,IAC7C;AAAA,EACF,WAAW,UAAU,KAAiC,MAAM,KAAK,OAAK,EAAE,WAAW,CAAC,GAAG;AACrF,QAAI,UAAU,CAAC;AACf,aAAS;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,IACF,KAAK,MAAO,KAAI,WAAW,GAAG;AAC5B,UAAI,OAAO,KAAK,OAAO,SACrB,KAAK,OAAO,MAAM;AACpB,UAAI,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAK;AACtC,cAAQ,KAAK;AAAA,QACX;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,cAA2B,WAAW,OAAO;AAQnD,IAAM,iBAA8B,WAAW,OAAO;AAQtD,IAAM,kBAA+B,MAAM,OAAO;AAClD,IAAM,gBAA6B,MAAM,OAAO;AAAA,EAC9C,QAAQ,SAAS;AACf,WAAO,cAAc,SAAS;AAAA,MAC5B,UAAU;AAAA,MACV,eAAe;AAAA,MACf,aAAa,CAAC,IAAIC,gBAAeA;AAAA,IACnC,GAAG;AAAA,MACD,UAAU,KAAK;AAAA,MACf,eAAe,KAAK;AAAA,MACpB,aAAa,CAAC,GAAG,MAAM,CAAC,IAAI,QAAQ,EAAE,IAAI,GAAG,KAAK,EAAE,IAAI,GAAG;AAAA,IAC7D,CAAC;AAAA,EACH;AACF,CAAC;AACD,IAAM,gBAA6B,WAAW,OAAO;AAAA,EACnD,SAAS;AACP,WAAO,aAAa;AAAA,EACtB;AAAA,EACA,OAAO,OAAO,IAAI;AAChB,QAAI,SAAS,GAAG,MAAM,MAAM,aAAa;AACzC,QAAI,WAAW,GAAG,WAAW,WAAW;AACxC,QAAI,UAAU;AACZ,UAAI,OAAO,UAAU,gBAAgB,IAAI,SAAS,SAAS,GACzD,OAAO,SAAS;AAClB,UAAI,QAAQ,QAAQ,IAA0B,MAAM,SAAS,MAAM;AACnE,UAAI,KAAM,SAAQ,aAAa,OAAO,MAAM,QAAQ,OAAO,UAAU,IAAI;AAAA,UAAO,SAAQ,aAAa,OAAO,GAAG,WAAW,SAAS;AACnI,aAAO,IAAI,aAAa,QAAQ,IAA0B,SAAS,OAAO,OAAO,QAAQ,IAA0B,QAAQ,SAAS,IAAI;AAAA,IAC1I;AACA,QAAI,UAAU,GAAG,WAAW,cAAc;AAC1C,QAAI,WAAW,UAAU,WAAW,SAAU,SAAQ,MAAM,QAAQ;AACpE,QAAI,GAAG,WAAW,YAAY,YAAY,MAAM,MAAO,QAAO,CAAC,GAAG,QAAQ,QAAQ,MAAM,WAAW,GAAG,QAAQ,IAAI,IAAI;AACtH,QAAI,QAAQ,UAAU,gBAAgB,EAAE;AACxC,QAAI,OAAO,GAAG,WAAW,YAAY,IAAI,GACvC,YAAY,GAAG,WAAW,YAAY,SAAS;AACjD,QAAI,MAAO,SAAQ,MAAM,WAAW,OAAO,MAAM,WAAW,QAAQ,EAAE;AAAA,aAAW,GAAG,UAAW,SAAQ,MAAM,aAAa,GAAG,WAAW,WAAW,MAAM,WAAW,OAAO,aAAa;AACxL,QAAI,WAAW,UAAU,WAAW,QAAS,SAAQ,MAAM,QAAQ;AACnE,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO;AACZ,WAAO;AAAA,MACL,MAAM,MAAM,KAAK,IAAI,OAAK,EAAE,OAAO,CAAC;AAAA,MACpC,QAAQ,MAAM,OAAO,IAAI,OAAK,EAAE,OAAO,CAAC;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,SAAS,MAAM;AACb,WAAO,IAAI,aAAa,KAAK,KAAK,IAAI,UAAU,QAAQ,GAAG,KAAK,OAAO,IAAI,UAAU,QAAQ,CAAC;AAAA,EAChG;AACF,CAAC;AAID,SAAS,QAAQ,SAAS,CAAC,GAAG;AAC5B,SAAO,CAAC,eAAe,cAAc,GAAG,MAAM,GAAG,WAAW,iBAAiB;AAAA,IAC3E,YAAY,GAAG,MAAM;AACnB,UAAIC,WAAU,EAAE,aAAa,gBAAgB,OAAO,EAAE,aAAa,gBAAgB,OAAO;AAC1F,UAAI,CAACA,SAAS,QAAO;AACrB,QAAE,eAAe;AACjB,aAAOA,SAAQ,IAAI;AAAA,IACrB;AAAA,EACF,CAAC,CAAC;AACJ;AASA,SAAS,IAAI,MAAM,WAAW;AAC5B,SAAO,SAAU;AAAA,IACf;AAAA,IACA;AAAA,EACF,GAAG;AACD,QAAI,CAAC,aAAa,MAAM,SAAU,QAAO;AACzC,QAAI,eAAe,MAAM,MAAM,eAAe,KAAK;AACnD,QAAI,CAAC,aAAc,QAAO;AAC1B,QAAI,KAAK,aAAa,IAAI,MAAM,OAAO,SAAS;AAChD,QAAI,CAAC,GAAI,QAAO;AAChB,aAAS,EAAE;AACX,WAAO;AAAA,EACT;AACF;AAKA,IAAM,OAAoB,IAAI,GAAyB,KAAK;AAK5D,IAAM,OAAoB,IAAI,GAA2B,KAAK;AAI9D,IAAM,gBAA6B,IAAI,GAAyB,IAAI;AAIpE,IAAM,gBAA6B,IAAI,GAA2B,IAAI;AACtE,SAAS,MAAM,MAAM;AACnB,SAAO,SAAU,OAAO;AACtB,QAAI,YAAY,MAAM,MAAM,eAAe,KAAK;AAChD,QAAI,CAAC,UAAW,QAAO;AACvB,QAAI,SAAS,QAAQ,IAA0B,UAAU,OAAO,UAAU;AAC1E,WAAO,OAAO,UAAU,OAAO,UAAU,CAAC,OAAO,CAAC,EAAE,UAAU,IAAI;AAAA,EACpE;AACF;AAIA,IAAM,YAAyB;AAAA,EAAM;AAAA;AAAuB;AAI5D,IAAM,YAAyB;AAAA,EAAM;AAAA;AAAyB;AAG9D,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,YAMA,SAEA,SAGA,QAEA,gBAGA,iBAAiB;AACf,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,iBAAiB;AACtB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,YAAY,OAAO;AACjB,WAAO,IAAI,WAAU,KAAK,SAAS,KAAK,SAAS,KAAK,QAAQ,KAAK,gBAAgB,KAAK;AAAA,EAC1F;AAAA,EACA,SAAS;AACP,QAAI,IAAI,IAAI;AACZ,WAAO;AAAA,MACL,UAAU,KAAK,KAAK,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AAAA,MAC5E,SAAS,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AAAA,MAC1E,iBAAiB,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AAAA,MAC1F,iBAAiB,KAAK,gBAAgB,IAAI,OAAK,EAAE,OAAO,CAAC;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,OAAO,SAAS,MAAM;AACpB,WAAO,IAAI,WAAU,KAAK,WAAW,UAAU,SAAS,KAAK,OAAO,GAAG,CAAC,GAAG,KAAK,UAAU,WAAW,SAAS,KAAK,MAAM,GAAG,KAAK,kBAAkB,gBAAgB,SAAS,KAAK,cAAc,GAAG,KAAK,gBAAgB,IAAI,gBAAgB,QAAQ,CAAC;AAAA,EACtP;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,gBAAgB,IAAI,WAAW;AACpC,QAAI,UAAU;AACd,aAAS,UAAU,GAAG,WAAW,MAAM,eAAe,GAAG;AACvD,UAAI,SAAS,OAAO,EAAE;AACtB,UAAI,OAAO,OAAQ,WAAU,QAAQ,OAAO,MAAM;AAAA,IACpD;AACA,QAAI,CAAC,QAAQ,UAAU,GAAG,QAAQ,MAAO,QAAO;AAChD,WAAO,IAAI,WAAU,GAAG,QAAQ,OAAO,GAAG,WAAW,GAAG,GAAG,SAAS,QAAW,aAAa,GAAG,WAAW,WAAW,IAAI;AAAA,EAC3H;AAAA,EACA,OAAO,UAAU,YAAY;AAC3B,WAAO,IAAI,WAAU,QAAW,MAAM,QAAW,QAAW,UAAU;AAAA,EACxE;AACF;AACA,SAAS,aAAa,QAAQ,IAAI,QAAQ,UAAU;AAClD,MAAI,QAAQ,KAAK,IAAI,SAAS,KAAK,KAAK,SAAS,IAAI;AACrD,MAAI,YAAY,OAAO,MAAM,OAAO,EAAE;AACtC,YAAU,KAAK,QAAQ;AACvB,SAAO;AACT;AACA,SAAS,WAAW,GAAG,GAAG;AACxB,MAAI,SAAS,CAAC,GACZC,cAAa;AACf,IAAE,kBAAkB,CAAC,GAAG,MAAM,OAAO,KAAK,GAAG,CAAC,CAAC;AAC/C,IAAE,kBAAkB,CAAC,IAAI,IAAI,GAAG,MAAM;AACpC,aAAS,IAAI,GAAG,IAAI,OAAO,UAAS;AAClC,UAAI,OAAO,OAAO,GAAG,GACnB,KAAK,OAAO,GAAG;AACjB,UAAI,KAAK,QAAQ,KAAK,GAAI,CAAAA,cAAa;AAAA,IACzC;AAAA,EACF,CAAC;AACD,SAAOA;AACT;AACA,SAAS,iBAAiB,GAAG,GAAG;AAC9B,SAAO,EAAE,OAAO,UAAU,EAAE,OAAO,UAAU,EAAE,OAAO,OAAO,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,WAAW;AAClH;AACA,SAAS,KAAK,GAAG,GAAG;AAClB,SAAO,CAAC,EAAE,SAAS,IAAI,CAAC,EAAE,SAAS,IAAI,EAAE,OAAO,CAAC;AACnD;AACA,IAAM,OAAO,CAAC;AACd,IAAM,wBAAwB;AAC9B,SAAS,aAAa,QAAQ,WAAW;AACvC,MAAI,CAAC,OAAO,QAAQ;AAClB,WAAO,CAAC,UAAU,UAAU,CAAC,SAAS,CAAC,CAAC;AAAA,EAC1C,OAAO;AACL,QAAI,YAAY,OAAO,OAAO,SAAS,CAAC;AACxC,QAAI,OAAO,UAAU,gBAAgB,MAAM,KAAK,IAAI,GAAG,UAAU,gBAAgB,SAAS,qBAAqB,CAAC;AAChH,QAAI,KAAK,UAAU,KAAK,KAAK,SAAS,CAAC,EAAE,GAAG,SAAS,EAAG,QAAO;AAC/D,SAAK,KAAK,SAAS;AACnB,WAAO,aAAa,QAAQ,OAAO,SAAS,GAAG,KAAK,UAAU,YAAY,IAAI,CAAC;AAAA,EACjF;AACF;AAEA,SAAS,aAAa,QAAQ;AAC5B,MAAI,OAAO,OAAO,OAAO,SAAS,CAAC;AACnC,MAAI,YAAY,OAAO,MAAM;AAC7B,YAAU,OAAO,SAAS,CAAC,IAAI,KAAK,YAAY,KAAK,gBAAgB,MAAM,GAAG,KAAK,gBAAgB,SAAS,CAAC,CAAC;AAC9G,SAAO;AACT;AAIA,SAAS,mBAAmB,QAAQ,SAAS;AAC3C,MAAI,CAAC,OAAO,OAAQ,QAAO;AAC3B,MAAI,SAAS,OAAO,QAClB,aAAa;AACf,SAAO,QAAQ;AACb,QAAI,QAAQ,SAAS,OAAO,SAAS,CAAC,GAAG,SAAS,UAAU;AAC5D,QAAI,MAAM,WAAW,CAAC,MAAM,QAAQ,SAAS,MAAM,QAAQ,QAAQ;AAEjE,UAAI,SAAS,OAAO,MAAM,GAAG,MAAM;AACnC,aAAO,SAAS,CAAC,IAAI;AACrB,aAAO;AAAA,IACT,OAAO;AAEL,gBAAU,MAAM;AAChB;AACA,mBAAa,MAAM;AAAA,IACrB;AAAA,EACF;AACA,SAAO,WAAW,SAAS,CAAC,UAAU,UAAU,UAAU,CAAC,IAAI;AACjE;AACA,SAAS,SAAS,OAAO,SAAS,iBAAiB;AACjD,MAAI,aAAa,KAAK,MAAM,gBAAgB,SAAS,MAAM,gBAAgB,IAAI,OAAK,EAAE,IAAI,OAAO,CAAC,IAAI,MAAM,eAAe;AAE3H,MAAI,CAAC,MAAM,QAAS,QAAO,UAAU,UAAU,UAAU;AACzD,MAAI,gBAAgB,MAAM,QAAQ,IAAI,OAAO,GAC3C,SAAS,QAAQ,QAAQ,MAAM,SAAS,IAAI;AAC9C,MAAI,cAAc,MAAM,SAAS,MAAM,OAAO,YAAY,MAAM,IAAI;AACpE,SAAO,IAAI,UAAU,eAAe,YAAY,WAAW,MAAM,SAAS,OAAO,GAAG,aAAa,MAAM,eAAe,IAAI,MAAM,GAAG,UAAU;AAC/I;AACA,IAAM,oBAAoB;AAC1B,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,YAAY,MAAM,QAAQ,WAAW,GAAG,gBAAgB,QAAW;AACjE,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,UAAU;AACR,WAAO,KAAK,WAAW,IAAI,cAAa,KAAK,MAAM,KAAK,MAAM,IAAI;AAAA,EACpE;AAAA,EACA,WAAW,OAAO,MAAM,WAAW,QAAQ,IAAI;AAC7C,QAAI,OAAO,KAAK,MACd,YAAY,KAAK,KAAK,SAAS,CAAC;AAClC,QAAI,aAAa,UAAU,WAAW,CAAC,UAAU,QAAQ,SAAS,MAAM,YAAY,CAAC,aAAa,kBAAkB,KAAK,SAAS,OAAO,CAAC,UAAU,gBAAgB,UAAU,OAAO,KAAK,WAAW,OAAO,iBAAiB,OAAO,YAAY,IAAI,WAAW,UAAU,SAAS,MAAM,OAAO,CAAC;AAAA,IAEhS,aAAa,uBAAuB;AAClC,aAAO,aAAa,MAAM,KAAK,SAAS,GAAG,OAAO,UAAU,IAAI,UAAU,MAAM,QAAQ,QAAQ,UAAU,OAAO,GAAG,KAAK,YAAY,WAAW,MAAM,SAAS,UAAU,OAAO,GAAG,UAAU,OAAO,GAAG,UAAU,QAAQ,UAAU,gBAAgB,IAAI,CAAC;AAAA,IAC1P,OAAO;AACL,aAAO,aAAa,MAAM,KAAK,QAAQ,OAAO,UAAU,KAAK;AAAA,IAC/D;AACA,WAAO,IAAI,cAAa,MAAM,MAAM,MAAM,SAAS;AAAA,EACrD;AAAA,EACA,aAAa,WAAW,MAAM,WAAW,eAAe;AACtD,QAAI,OAAO,KAAK,KAAK,SAAS,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC,EAAE,kBAAkB;AAChF,QAAI,KAAK,SAAS,KAAK,OAAO,KAAK,WAAW,iBAAiB,aAAa,KAAK,iBAAiB,aAAa,gBAAgB,KAAK,SAAS,KAAK,iBAAiB,KAAK,KAAK,SAAS,CAAC,GAAG,SAAS,EAAG,QAAO;AAC7M,WAAO,IAAI,cAAa,aAAa,KAAK,MAAM,SAAS,GAAG,KAAK,QAAQ,MAAM,SAAS;AAAA,EAC1F;AAAA,EACA,WAAW,SAAS;AAClB,WAAO,IAAI,cAAa,mBAAmB,KAAK,MAAM,OAAO,GAAG,mBAAmB,KAAK,QAAQ,OAAO,GAAG,KAAK,UAAU,KAAK,aAAa;AAAA,EAC7I;AAAA,EACA,IAAI,MAAM,OAAO,eAAe;AAC9B,QAAI,SAAS,QAAQ,IAA0B,KAAK,OAAO,KAAK;AAChE,QAAI,OAAO,UAAU,EAAG,QAAO;AAC/B,QAAI,QAAQ,OAAO,OAAO,SAAS,CAAC,GAClC,YAAY,MAAM,gBAAgB,CAAC,KAAK,MAAM;AAChD,QAAI,iBAAiB,MAAM,gBAAgB,QAAQ;AACjD,aAAO,MAAM,OAAO;AAAA,QAClB,WAAW,MAAM,gBAAgB,MAAM,gBAAgB,SAAS,CAAC;AAAA,QACjE,aAAa,YAAY,GAAG;AAAA,UAC1B;AAAA,UACA,MAAM,aAAa,MAAM;AAAA,UACzB;AAAA,QACF,CAAC;AAAA,QACD,WAAW,QAAQ,IAA0B,gBAAgB;AAAA,QAC7D,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH,WAAW,CAAC,MAAM,SAAS;AACzB,aAAO;AAAA,IACT,OAAO;AACL,UAAI,OAAO,OAAO,UAAU,IAAI,OAAO,OAAO,MAAM,GAAG,OAAO,SAAS,CAAC;AACxE,UAAI,MAAM,OAAQ,QAAO,mBAAmB,MAAM,MAAM,MAAM;AAC9D,aAAO,MAAM,OAAO;AAAA,QAClB,SAAS,MAAM;AAAA,QACf,WAAW,MAAM;AAAA,QACjB,SAAS,MAAM;AAAA,QACf,aAAa,YAAY,GAAG;AAAA,UAC1B;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,QACD,QAAQ;AAAA,QACR,WAAW,QAAQ,IAA0B,SAAS;AAAA,QACtD,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AACF;AACA,aAAa,QAAqB,IAAI,aAAa,MAAM,IAAI;AAS7D,IAAM,gBAAgB,CAAC;AAAA,EACrB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,gBAAgB;AAClB,GAAG;AAAA,EACD,OAAO;AAAA,EACP,KAAK;AAAA,EACL,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,gBAAgB;AAClB,CAAC;AACD,SAAS,UAAU,KAAK,IAAI;AAC1B,SAAO,gBAAgB,OAAO,IAAI,OAAO,IAAI,EAAE,GAAG,IAAI,SAAS;AACjE;AACA,SAAS,OAAO,OAAO,WAAW;AAChC,SAAO,MAAM,OAAO;AAAA,IAClB;AAAA,IACA,gBAAgB;AAAA,IAChB,WAAW;AAAA,EACb,CAAC;AACH;AACA,SAAS,QAAQ;AAAA,EACf;AAAA,EACA;AACF,GAAG,KAAK;AACN,MAAI,YAAY,UAAU,MAAM,WAAW,GAAG;AAC9C,MAAI,UAAU,GAAG,MAAM,WAAW,IAAI,EAAG,QAAO;AAChD,WAAS,OAAO,OAAO,SAAS,CAAC;AACjC,SAAO;AACT;AACA,SAAS,SAAS,OAAO,SAAS;AAChC,SAAO,gBAAgB,OAAO,UAAU,MAAM,KAAK,MAAM,IAAI;AAC/D;AACA,SAAS,aAAa,MAAM,SAAS;AACnC,SAAO,QAAQ,MAAM,WAAS,MAAM,QAAQ,KAAK,WAAW,OAAO,OAAO,IAAI,SAAS,OAAO,OAAO,CAAC;AACxG;AACA,SAAS,YAAY,MAAM;AACzB,SAAO,KAAK,gBAAgB,KAAK,MAAM,UAAU,KAAK,IAAI,KAAK,UAAU;AAC3E;AAKA,IAAM,iBAAiB,UAAQ,aAAa,MAAM,CAAC,YAAY,IAAI,CAAC;AAIpE,IAAM,kBAAkB,UAAQ,aAAa,MAAM,YAAY,IAAI,CAAC;AA4BpE,SAAS,cAAc,MAAM,SAAS;AACpC,SAAO,QAAQ,MAAM,WAAS,MAAM,QAAQ,KAAK,YAAY,OAAO,OAAO,IAAI,SAAS,OAAO,OAAO,CAAC;AACzG;AAKA,IAAM,kBAAkB,UAAQ,cAAc,MAAM,CAAC,YAAY,IAAI,CAAC;AAItE,IAAM,mBAAmB,UAAQ,cAAc,MAAM,YAAY,IAAI,CAAC;AA2BtE,IAAM,YAAY,OAAO,QAAQ,eAAe,KAAK,YAAyB,IAAI,KAAK,UAAU,QAAW;AAAA,EAC1G,aAAa;AACf,CAAC,IAAI;AA6DL,SAAS,gBAAgB,OAAO,MAAM,aAAa;AACjD,MAAI,KAAK,KAAK,KAAK,WAAW,EAAG,QAAO;AACxC,MAAI,MAAM,KAAK,KAAK,KAAK;AACzB,SAAO,QAAQ,MAAM,KAAK,YAAY,KAAK,MAAM,SAAS,KAAK,MAAM,KAAK,EAAE,CAAC,MAAM,KAAK;AAC1F;AACA,SAAS,aAAa,OAAO,OAAO,SAAS;AAC3C,MAAI,MAAM,WAAW,KAAK,EAAE,aAAa,MAAM,IAAI;AACnD,MAAI,cAAc,UAAU,SAAS,WAAW,SAAS;AAGzD,WAAS,KAAK,MAAM,UAAQ;AAC1B,QAAI,OAAO,UAAU,IAAI,WAAW,EAAE,IAAI,IAAI,YAAY,EAAE;AAC5D,QAAI,CAAC,KAAM;AACX,QAAI,gBAAgB,OAAO,MAAM,WAAW,EAAG,OAAM;AAAA,QAAU,MAAK,UAAU,KAAK,KAAK,KAAK;AAAA,EAC/F;AACA,MAAI,UAAU,IAAI,KAAK,KAAK,WAAW,GACrC,OACA;AACF,MAAI,YAAY,QAAQ,UAAU,cAAc,OAAO,IAAI,MAAM,CAAC,IAAI,cAAc,OAAO,IAAI,IAAI,EAAE,MAAM,MAAM,QAAS,UAAS,UAAU,MAAM,IAAI,KAAK,MAAM,IAAI;AAAA,MAAU,UAAS,UAAU,IAAI,KAAK,IAAI;AAChN,SAAO,gBAAgB,OAAO,QAAQ,UAAU,KAAK,CAAC;AACxD;AAIA,IAAM,mBAAmB,UAAQ,QAAQ,MAAM,WAAS,aAAa,KAAK,OAAO,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC;AAI3G,IAAM,oBAAoB,UAAQ,QAAQ,MAAM,WAAS,aAAa,KAAK,OAAO,OAAO,YAAY,IAAI,CAAC,CAAC;AAC3G,SAAS,aAAa,MAAM,SAAS;AACnC,SAAO,QAAQ,MAAM,WAAS;AAC5B,QAAI,CAAC,MAAM,MAAO,QAAO,SAAS,OAAO,OAAO;AAChD,QAAI,QAAQ,KAAK,eAAe,OAAO,OAAO;AAC9C,WAAO,MAAM,QAAQ,MAAM,OAAO,QAAQ,KAAK,mBAAmB,OAAO,OAAO;AAAA,EAClF,CAAC;AACH;AAIA,IAAM,eAAe,UAAQ,aAAa,MAAM,KAAK;AAIrD,IAAM,iBAAiB,UAAQ,aAAa,MAAM,IAAI;AACtD,SAAS,SAAS,MAAM;AACtB,MAAI,aAAa,KAAK,UAAU,eAAe,KAAK,UAAU,eAAe;AAC7E,MAAI,YAAY,GACd,eAAe,GACf;AACF,MAAI,YAAY;AACd,aAAS,UAAU,KAAK,MAAM,MAAM,WAAW,aAAa,GAAG;AAC7D,UAAI,UAAU,OAAO,IAAI;AACzB,UAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,IAAK,aAAY,KAAK,IAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,KAAK,SAAS;AAChK,UAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,OAAQ,gBAAe,KAAK,IAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,QAAQ,YAAY;AAAA,IAC9K;AACA,aAAS,KAAK,UAAU,eAAe,YAAY;AAAA,EACrD,OAAO;AACL,cAAU,KAAK,IAAI,cAAc,eAAe,QAAQ;AAAA,EAC1D;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ,KAAK,IAAI,KAAK,mBAAmB,SAAS,CAAC;AAAA,EACrD;AACF;AACA,SAAS,aAAa,MAAM,SAAS;AACnC,MAAI,OAAO,SAAS,IAAI;AACxB,MAAI;AAAA,IACA;AAAA,EACF,IAAI,MACJ,YAAY,UAAU,MAAM,WAAW,WAAS;AAC9C,WAAO,MAAM,QAAQ,KAAK,eAAe,OAAO,SAAS,KAAK,MAAM,IAAI,SAAS,OAAO,OAAO;AAAA,EACjG,CAAC;AACH,MAAI,UAAU,GAAG,MAAM,SAAS,EAAG,QAAO;AAC1C,MAAI;AACJ,MAAI,KAAK,YAAY;AACnB,QAAI,WAAW,KAAK,YAAY,MAAM,UAAU,KAAK,IAAI;AACzD,QAAI,aAAa,KAAK,UAAU,sBAAsB;AACtD,QAAI,YAAY,WAAW,MAAM,KAAK,WACpC,eAAe,WAAW,SAAS,KAAK;AAC1C,QAAI,YAAY,SAAS,MAAM,aAAa,SAAS,SAAS,aAAc,UAAS,WAAW,eAAe,UAAU,KAAK,MAAM;AAAA,MAClI,GAAG;AAAA,MACH,SAAS,SAAS,MAAM;AAAA,IAC1B,CAAC;AAAA,EACH;AACA,OAAK,SAAS,OAAO,OAAO,SAAS,GAAG;AAAA,IACtC,SAAS;AAAA,EACX,CAAC;AACD,SAAO;AACT;AAIA,IAAM,eAAe,UAAQ,aAAa,MAAM,KAAK;AAIrD,IAAM,iBAAiB,UAAQ,aAAa,MAAM,IAAI;AACtD,SAAS,mBAAmB,MAAM,OAAO,SAAS;AAChD,MAAI,OAAO,KAAK,YAAY,MAAM,IAAI,GACpC,QAAQ,KAAK,mBAAmB,OAAO,OAAO;AAChD,MAAI,MAAM,QAAQ,MAAM,QAAQ,MAAM,SAAS,UAAU,KAAK,KAAK,KAAK,MAAO,SAAQ,KAAK,mBAAmB,OAAO,SAAS,KAAK;AACpI,MAAI,CAAC,WAAW,MAAM,QAAQ,KAAK,QAAQ,KAAK,QAAQ;AACtD,QAAI,QAAQ,OAAO,KAAK,KAAK,MAAM,SAAS,KAAK,MAAM,KAAK,IAAI,KAAK,OAAO,KAAK,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;AAC/F,QAAI,SAAS,MAAM,QAAQ,KAAK,OAAO,MAAO,SAAQ,gBAAgB,OAAO,KAAK,OAAO,KAAK;AAAA,EAChG;AACA,SAAO;AACT;AAKA,IAAM,4BAA4B,UAAQ,QAAQ,MAAM,WAAS,mBAAmB,MAAM,OAAO,IAAI,CAAC;AAOtG,IAAM,6BAA6B,UAAQ,QAAQ,MAAM,WAAS,mBAAmB,MAAM,OAAO,KAAK,CAAC;AAIxG,IAAM,yBAAyB,UAAQ,QAAQ,MAAM,WAAS,mBAAmB,MAAM,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC;AAIjH,IAAM,0BAA0B,UAAQ,QAAQ,MAAM,WAAS,mBAAmB,MAAM,OAAO,YAAY,IAAI,CAAC,CAAC;AAIjH,IAAM,kBAAkB,UAAQ,QAAQ,MAAM,WAAS,gBAAgB,OAAO,KAAK,YAAY,MAAM,IAAI,EAAE,MAAM,CAAC,CAAC;AAInH,IAAM,gBAAgB,UAAQ,QAAQ,MAAM,WAAS,gBAAgB,OAAO,KAAK,YAAY,MAAM,IAAI,EAAE,IAAI,EAAE,CAAC;AAChH,SAAS,kBAAkB,OAAO,UAAU,QAAQ;AAClD,MAAI,QAAQ,OACV,YAAY,UAAU,MAAM,WAAW,WAAS;AAC9C,QAAI,WAAW,cAAc,OAAO,MAAM,MAAM,EAAE,KAAK,cAAc,OAAO,MAAM,MAAM,CAAC,KAAK,MAAM,OAAO,KAAK,cAAc,OAAO,MAAM,OAAO,GAAG,CAAC,KAAK,MAAM,OAAO,MAAM,IAAI,UAAU,cAAc,OAAO,MAAM,OAAO,GAAG,EAAE;AACnO,QAAI,CAAC,YAAY,CAAC,SAAS,IAAK,QAAO;AACvC,YAAQ;AACR,QAAI,OAAO,SAAS,MAAM,QAAQ,MAAM,OAAO,SAAS,IAAI,KAAK,SAAS,IAAI;AAC9E,WAAO,SAAS,gBAAgB,MAAM,MAAM,QAAQ,IAAI,IAAI,gBAAgB,OAAO,IAAI;AAAA,EACzF,CAAC;AACH,MAAI,CAAC,MAAO,QAAO;AACnB,WAAS,OAAO,OAAO,SAAS,CAAC;AACjC,SAAO;AACT;AAKA,IAAM,wBAAwB,CAAC;AAAA,EAC7B;AAAA,EACA;AACF,MAAM,kBAAkB,OAAO,UAAU,KAAK;AAS9C,SAAS,UAAU,QAAQ,KAAK;AAC9B,MAAI,YAAY,UAAU,OAAO,MAAM,WAAW,WAAS;AACzD,QAAI,OAAO,IAAI,KAAK;AACpB,WAAO,gBAAgB,MAAM,MAAM,QAAQ,KAAK,MAAM,KAAK,YAAY,KAAK,aAAa,MAAS;AAAA,EACpG,CAAC;AACD,MAAI,UAAU,GAAG,OAAO,MAAM,SAAS,EAAG,QAAO;AACjD,SAAO,SAAS,OAAO,OAAO,OAAO,SAAS,CAAC;AAC/C,SAAO;AACT;AACA,SAAS,aAAa,MAAM,SAAS;AACnC,SAAO,UAAU,MAAM,WAAS,KAAK,WAAW,OAAO,OAAO,CAAC;AACjE;AAKA,IAAM,iBAAiB,UAAQ,aAAa,MAAM,CAAC,YAAY,IAAI,CAAC;AAIpE,IAAM,kBAAkB,UAAQ,aAAa,MAAM,YAAY,IAAI,CAAC;AAmBpE,SAAS,cAAc,MAAM,SAAS;AACpC,SAAO,UAAU,MAAM,WAAS,KAAK,YAAY,OAAO,OAAO,CAAC;AAClE;AAKA,IAAM,kBAAkB,UAAQ,cAAc,MAAM,CAAC,YAAY,IAAI,CAAC;AAItE,IAAM,mBAAmB,UAAQ,cAAc,MAAM,YAAY,IAAI,CAAC;AA8BtE,IAAM,mBAAmB,UAAQ,UAAU,MAAM,WAAS,aAAa,KAAK,OAAO,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC;AAI7G,IAAM,oBAAoB,UAAQ,UAAU,MAAM,WAAS,aAAa,KAAK,OAAO,OAAO,YAAY,IAAI,CAAC,CAAC;AAC7G,SAAS,aAAa,MAAM,SAAS;AACnC,SAAO,UAAU,MAAM,WAAS,KAAK,eAAe,OAAO,OAAO,CAAC;AACrE;AAIA,IAAM,eAAe,UAAQ,aAAa,MAAM,KAAK;AAIrD,IAAM,iBAAiB,UAAQ,aAAa,MAAM,IAAI;AACtD,SAAS,aAAa,MAAM,SAAS;AACnC,SAAO,UAAU,MAAM,WAAS,KAAK,eAAe,OAAO,SAAS,SAAS,IAAI,EAAE,MAAM,CAAC;AAC5F;AAIA,IAAM,eAAe,UAAQ,aAAa,MAAM,KAAK;AAIrD,IAAM,iBAAiB,UAAQ,aAAa,MAAM,IAAI;AAItD,IAAM,4BAA4B,UAAQ,UAAU,MAAM,WAAS,mBAAmB,MAAM,OAAO,IAAI,CAAC;AAIxG,IAAM,6BAA6B,UAAQ,UAAU,MAAM,WAAS,mBAAmB,MAAM,OAAO,KAAK,CAAC;AAI1G,IAAM,yBAAyB,UAAQ,UAAU,MAAM,WAAS,mBAAmB,MAAM,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC;AAInH,IAAM,0BAA0B,UAAQ,UAAU,MAAM,WAAS,mBAAmB,MAAM,OAAO,YAAY,IAAI,CAAC,CAAC;AAInH,IAAM,kBAAkB,UAAQ,UAAU,MAAM,WAAS,gBAAgB,OAAO,KAAK,YAAY,MAAM,IAAI,EAAE,IAAI,CAAC;AAIlH,IAAM,gBAAgB,UAAQ,UAAU,MAAM,WAAS,gBAAgB,OAAO,KAAK,YAAY,MAAM,IAAI,EAAE,EAAE,CAAC;AAI9G,IAAM,iBAAiB,CAAC;AAAA,EACtB;AAAA,EACA;AACF,MAAM;AACJ,WAAS,OAAO,OAAO;AAAA,IACrB,QAAQ;AAAA,EACV,CAAC,CAAC;AACF,SAAO;AACT;AAIA,IAAM,eAAe,CAAC;AAAA,EACpB;AAAA,EACA;AACF,MAAM;AACJ,WAAS,OAAO,OAAO;AAAA,IACrB,QAAQ,MAAM,IAAI;AAAA,EACpB,CAAC,CAAC;AACF,SAAO;AACT;AAIA,IAAM,iBAAiB,CAAC;AAAA,EACtB;AAAA,EACA;AACF,MAAM;AACJ,WAAS,OAAO,OAAO;AAAA,IACrB,QAAQ,MAAM,UAAU,KAAK;AAAA,IAC7B,MAAM;AAAA,EACR,CAAC,CAAC;AACF,SAAO;AACT;AAIA,IAAM,eAAe,CAAC;AAAA,EACpB;AAAA,EACA;AACF,MAAM;AACJ,WAAS,OAAO,OAAO;AAAA,IACrB,QAAQ,MAAM,UAAU,KAAK;AAAA,IAC7B,MAAM,MAAM,IAAI;AAAA,EAClB,CAAC,CAAC;AACF,SAAO;AACT;AAIA,IAAM,YAAY,CAAC;AAAA,EACjB;AAAA,EACA;AACF,MAAM;AACJ,WAAS,MAAM,OAAO;AAAA,IACpB,WAAW;AAAA,MACT,QAAQ;AAAA,MACR,MAAM,MAAM,IAAI;AAAA,IAClB;AAAA,IACA,WAAW;AAAA,EACb,CAAC,CAAC;AACF,SAAO;AACT;AAIA,IAAM,aAAa,CAAC;AAAA,EAClB;AAAA,EACA;AACF,MAAM;AACJ,MAAI,SAAS,mBAAmB,KAAK,EAAE,IAAI,CAAC;AAAA,IAC1C;AAAA,IACA;AAAA,EACF,MAAM,gBAAgB,MAAM,MAAM,KAAK,IAAI,KAAK,GAAG,MAAM,IAAI,MAAM,CAAC,CAAC;AACrE,WAAS,MAAM,OAAO;AAAA,IACpB,WAAW,gBAAgB,OAAO,MAAM;AAAA,IACxC,WAAW;AAAA,EACb,CAAC,CAAC;AACF,SAAO;AACT;AAOA,IAAM,qBAAqB,CAAC;AAAA,EAC1B;AAAA,EACA;AACF,MAAM;AACJ,MAAI,YAAY,UAAU,MAAM,WAAW,WAAS;AAClD,QAAI,OAAO,WAAW,KAAK,GACzB,QAAQ,KAAK,aAAa,MAAM,MAAM,CAAC;AACzC,QAAI,MAAM,OAAO;AACf,UAAI,cAAc,KAAK,aAAa,MAAM,MAAM,EAAE;AAClD,UAAI,YAAY,KAAK,QAAQ,MAAM,KAAK,QAAQ,YAAY,KAAK,MAAM,MAAM,KAAK,GAAI,SAAQ;AAAA,IAChG;AACA,aAAS,MAAM,OAAO,KAAK,MAAM,IAAI,MAAM;AACzC,UAAI;AAAA,QACF;AAAA,MACF,IAAI;AACJ,WAAK,KAAK,OAAO,MAAM,QAAQ,KAAK,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,QAAQ,MAAM,SAAS,IAAI,KAAM,QAAO,gBAAgB,MAAM,KAAK,IAAI,KAAK,IAAI;AAAA,IACnK;AACA,WAAO;AAAA,EACT,CAAC;AACD,MAAI,UAAU,GAAG,MAAM,SAAS,EAAG,QAAO;AAC1C,WAAS,OAAO,OAAO,SAAS,CAAC;AACjC,SAAO;AACT;AAMA,IAAM,oBAAoB,CAAC;AAAA,EACzB;AAAA,EACA;AACF,MAAM;AACJ,MAAI,MAAM,MAAM,WACd,YAAY;AACd,MAAI,IAAI,OAAO,SAAS,EAAG,aAAY,gBAAgB,OAAO,CAAC,IAAI,IAAI,CAAC;AAAA,WAAW,CAAC,IAAI,KAAK,MAAO,aAAY,gBAAgB,OAAO,CAAC,gBAAgB,OAAO,IAAI,KAAK,IAAI,CAAC,CAAC;AAC9K,MAAI,CAAC,UAAW,QAAO;AACvB,WAAS,OAAO,OAAO,SAAS,CAAC;AACjC,SAAO;AACT;AACA,SAAS,SAAS,QAAQ,IAAI;AAC5B,MAAI,OAAO,MAAM,SAAU,QAAO;AAClC,MAAI,QAAQ,oBACV;AAAA,IACE;AAAA,EACF,IAAI;AACN,MAAI,UAAU,MAAM,cAAc,WAAS;AACzC,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,QAAQ,IAAI;AACd,UAAI,UAAU,GAAG,KAAK;AACtB,UAAI,UAAU,MAAM;AAClB,gBAAQ;AACR,kBAAU,WAAW,QAAQ,SAAS,KAAK;AAAA,MAC7C,WAAW,UAAU,MAAM;AACzB,gBAAQ;AACR,kBAAU,WAAW,QAAQ,SAAS,IAAI;AAAA,MAC5C;AACA,aAAO,KAAK,IAAI,MAAM,OAAO;AAC7B,WAAK,KAAK,IAAI,IAAI,OAAO;AAAA,IAC3B,OAAO;AACL,aAAO,WAAW,QAAQ,MAAM,KAAK;AACrC,WAAK,WAAW,QAAQ,IAAI,IAAI;AAAA,IAClC;AACA,WAAO,QAAQ,KAAK;AAAA,MAClB;AAAA,IACF,IAAI;AAAA,MACF,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO,gBAAgB,OAAO,MAAM,OAAO,MAAM,OAAO,KAAK,CAAC;AAAA,IAChE;AAAA,EACF,CAAC;AACD,MAAI,QAAQ,QAAQ,MAAO,QAAO;AAClC,SAAO,SAAS,MAAM,OAAO,SAAS;AAAA,IACpC,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,SAAS,SAAS,qBAAqB,WAAW,SAAS,GAAG,MAAM,OAAO,mBAAmB,CAAC,IAAI;AAAA,EACrG,CAAC,CAAC;AACF,SAAO;AACT;AACA,SAAS,WAAW,QAAQ,KAAK,SAAS;AACxC,MAAI,kBAAkB,WAAY,UAAS,UAAU,OAAO,MAAM,MAAM,WAAW,YAAY,EAAE,IAAI,OAAK,EAAE,MAAM,CAAC,EAAG,QAAO,QAAQ,KAAK,KAAK,CAAC,MAAM,OAAO;AAC3J,QAAI,OAAO,OAAO,KAAK,IAAK,OAAM,UAAU,KAAK;AAAA,EACnD,CAAC;AACD,SAAO;AACT;AACA,IAAM,eAAe,CAAC,QAAQ,SAAS,iBAAiB,SAAS,QAAQ,WAAS;AAChF,MAAI,MAAM,MAAM,MACd;AAAA,IACE;AAAA,EACF,IAAI,QACJ,OAAO,MAAM,IAAI,OAAO,GAAG,GAC3B,QACA;AACF,MAAI,gBAAgB,CAAC,WAAW,MAAM,KAAK,QAAQ,MAAM,KAAK,OAAO,OAAO,CAAC,SAAS,KAAK,SAAS,KAAK,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,GAAG;AACxI,QAAI,OAAO,OAAO,SAAS,CAAC,KAAK,IAAM,QAAO,MAAM;AACpD,QAAI,MAAM,YAAY,QAAQ,MAAM,OAAO,GACzC,OAAO,MAAM,cAAc,KAAK,KAAK,cAAc,KAAK;AAC1D,aAAS,IAAI,GAAG,IAAI,QAAQ,OAAO,OAAO,SAAS,IAAI,CAAC,KAAK,KAAK,IAAK;AACvE,gBAAY;AAAA,EACd,OAAO;AACL,gBAAY,iBAAiB,KAAK,MAAM,MAAM,KAAK,MAAM,SAAS,OAAO,IAAI,KAAK;AAClF,QAAI,aAAa,OAAO,KAAK,WAAW,UAAU,MAAM,IAAI,QAAQ,GAAI,cAAa,UAAU,IAAI;AAAA,aAAY,CAAC,WAAW,kBAAkB,KAAK,KAAK,KAAK,MAAM,YAAY,KAAK,MAAM,MAAM,KAAK,IAAI,CAAC,EAAG,aAAY,iBAAiB,KAAK,MAAM,YAAY,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK;AAAA,EAClS;AACA,SAAO;AACT,CAAC;AAKD,IAAM,qBAAqB,UAAQ,aAAa,MAAM,OAAO,IAAI;AAUjE,IAAM,oBAAoB,UAAQ,aAAa,MAAM,MAAM,KAAK;AAChE,IAAM,gBAAgB,CAAC,QAAQ,YAAY,SAAS,QAAQ,WAAS;AACnE,MAAI,MAAM,MAAM,MACd;AAAA,IACE;AAAA,EACF,IAAI,QACJ,OAAO,MAAM,IAAI,OAAO,GAAG;AAC7B,MAAI,aAAa,MAAM,gBAAgB,GAAG;AAC1C,WAAS,MAAM,UAAQ;AACrB,QAAI,QAAQ,UAAU,KAAK,KAAK,KAAK,OAAO;AAC1C,UAAI,OAAO,MAAM,QAAQ,KAAK,WAAW,UAAU,MAAM,IAAI,QAAQ,GAAI,QAAO,UAAU,IAAI;AAC9F;AAAA,IACF;AACA,QAAI,OAAO,iBAAiB,KAAK,MAAM,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK;AACxE,QAAI,WAAW,KAAK,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI;AAC/F,QAAI,UAAU,WAAW,QAAQ;AACjC,QAAI,OAAO,QAAQ,WAAW,IAAK;AACnC,QAAI,YAAY,OAAO,OAAO,MAAM,KAAM,OAAM;AAChD,UAAM;AAAA,EACR;AACA,SAAO;AACT,CAAC;AAMD,IAAM,sBAAsB,YAAU,cAAc,QAAQ,KAAK;AAIjE,IAAM,qBAAqB,YAAU,cAAc,QAAQ,IAAI;AAM/D,IAAM,kBAAkB,UAAQ,SAAS,MAAM,WAAS;AACtD,MAAI,UAAU,KAAK,YAAY,MAAM,IAAI,EAAE;AAC3C,SAAO,MAAM,OAAO,UAAU,UAAU,KAAK,IAAI,KAAK,MAAM,IAAI,QAAQ,MAAM,OAAO,CAAC;AACxF,CAAC;AAcD,IAAM,6BAA6B,UAAQ,SAAS,MAAM,WAAS;AACjE,MAAI,YAAY,KAAK,mBAAmB,OAAO,KAAK,EAAE;AACtD,SAAO,MAAM,OAAO,YAAY,YAAY,KAAK,IAAI,GAAG,MAAM,OAAO,CAAC;AACxE,CAAC;AAKD,IAAM,4BAA4B,UAAQ,SAAS,MAAM,WAAS;AAChE,MAAI,YAAY,KAAK,mBAAmB,OAAO,IAAI,EAAE;AACrD,SAAO,MAAM,OAAO,YAAY,YAAY,KAAK,IAAI,KAAK,MAAM,IAAI,QAAQ,MAAM,OAAO,CAAC;AAC5F,CAAC;AAqCD,IAAM,YAAY,CAAC;AAAA,EACjB;AAAA,EACA;AACF,MAAM;AACJ,MAAI,MAAM,SAAU,QAAO;AAC3B,MAAI,UAAU,MAAM,cAAc,WAAS;AACzC,WAAO;AAAA,MACL,SAAS;AAAA,QACP,MAAM,MAAM;AAAA,QACZ,IAAI,MAAM;AAAA,QACV,QAAQ,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC;AAAA,MAC1B;AAAA,MACA,OAAO,gBAAgB,OAAO,MAAM,IAAI;AAAA,IAC1C;AAAA,EACF,CAAC;AACD,WAAS,MAAM,OAAO,SAAS;AAAA,IAC7B,gBAAgB;AAAA,IAChB,WAAW;AAAA,EACb,CAAC,CAAC;AACF,SAAO;AACT;AAIA,IAAM,iBAAiB,CAAC;AAAA,EACtB;AAAA,EACA;AACF,MAAM;AACJ,MAAI,MAAM,SAAU,QAAO;AAC3B,MAAI,UAAU,MAAM,cAAc,WAAS;AACzC,QAAI,CAAC,MAAM,SAAS,MAAM,QAAQ,KAAK,MAAM,QAAQ,MAAM,IAAI,OAAQ,QAAO;AAAA,MAC5E;AAAA,IACF;AACA,QAAI,MAAM,MAAM,MACd,OAAO,MAAM,IAAI,OAAO,GAAG;AAC7B,QAAI,OAAO,OAAO,KAAK,OAAO,MAAM,IAAI,iBAAiB,KAAK,MAAM,MAAM,KAAK,MAAM,KAAK,IAAI,KAAK;AACnG,QAAI,KAAK,OAAO,KAAK,KAAK,MAAM,IAAI,iBAAiB,KAAK,MAAM,MAAM,KAAK,MAAM,IAAI,IAAI,KAAK;AAC9F,WAAO;AAAA,MACL,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA,QAAQ,MAAM,IAAI,MAAM,KAAK,EAAE,EAAE,OAAO,MAAM,IAAI,MAAM,MAAM,GAAG,CAAC;AAAA,MACpE;AAAA,MACA,OAAO,gBAAgB,OAAO,EAAE;AAAA,IAClC;AAAA,EACF,CAAC;AACD,MAAI,QAAQ,QAAQ,MAAO,QAAO;AAClC,WAAS,MAAM,OAAO,SAAS;AAAA,IAC7B,gBAAgB;AAAA,IAChB,WAAW;AAAA,EACb,CAAC,CAAC;AACF,SAAO;AACT;AACA,SAAS,mBAAmB,OAAO;AACjC,MAAI,SAAS,CAAC,GACZ,OAAO;AACT,WAAS,SAAS,MAAM,UAAU,QAAQ;AACxC,QAAI,YAAY,MAAM,IAAI,OAAO,MAAM,IAAI,GACzC,UAAU,MAAM,IAAI,OAAO,MAAM,EAAE;AACrC,QAAI,CAAC,MAAM,SAAS,MAAM,MAAM,QAAQ,KAAM,WAAU,MAAM,IAAI,OAAO,MAAM,KAAK,CAAC;AACrF,QAAI,QAAQ,UAAU,QAAQ;AAC5B,UAAI,OAAO,OAAO,OAAO,SAAS,CAAC;AACnC,WAAK,KAAK,QAAQ;AAClB,WAAK,OAAO,KAAK,KAAK;AAAA,IACxB,OAAO;AACL,aAAO,KAAK;AAAA,QACV,MAAM,UAAU;AAAA,QAChB,IAAI,QAAQ;AAAA,QACZ,QAAQ,CAAC,KAAK;AAAA,MAChB,CAAC;AAAA,IACH;AACA,WAAO,QAAQ,SAAS;AAAA,EAC1B;AACA,SAAO;AACT;AACA,SAAS,SAAS,OAAO,UAAU,SAAS;AAC1C,MAAI,MAAM,SAAU,QAAO;AAC3B,MAAI,UAAU,CAAC,GACb,SAAS,CAAC;AACZ,WAAS,SAAS,mBAAmB,KAAK,GAAG;AAC3C,QAAI,UAAU,MAAM,MAAM,MAAM,IAAI,SAAS,MAAM,QAAQ,EAAG;AAC9D,QAAI,WAAW,MAAM,IAAI,OAAO,UAAU,MAAM,KAAK,IAAI,MAAM,OAAO,CAAC;AACvE,QAAI,OAAO,SAAS,SAAS;AAC7B,QAAI,SAAS;AACX,cAAQ,KAAK;AAAA,QACX,MAAM,MAAM;AAAA,QACZ,IAAI,SAAS;AAAA,MACf,GAAG;AAAA,QACD,MAAM,MAAM;AAAA,QACZ,QAAQ,SAAS,OAAO,MAAM;AAAA,MAChC,CAAC;AACD,eAAS,KAAK,MAAM,OAAQ,QAAO,KAAK,gBAAgB,MAAM,KAAK,IAAI,MAAM,IAAI,QAAQ,EAAE,SAAS,IAAI,GAAG,KAAK,IAAI,MAAM,IAAI,QAAQ,EAAE,OAAO,IAAI,CAAC,CAAC;AAAA,IACvJ,OAAO;AACL,cAAQ,KAAK;AAAA,QACX,MAAM,SAAS;AAAA,QACf,IAAI,MAAM;AAAA,MACZ,GAAG;AAAA,QACD,MAAM,MAAM;AAAA,QACZ,QAAQ,MAAM,YAAY,SAAS;AAAA,MACrC,CAAC;AACD,eAAS,KAAK,MAAM,OAAQ,QAAO,KAAK,gBAAgB,MAAM,EAAE,SAAS,MAAM,EAAE,OAAO,IAAI,CAAC;AAAA,IAC/F;AAAA,EACF;AACA,MAAI,CAAC,QAAQ,OAAQ,QAAO;AAC5B,WAAS,MAAM,OAAO;AAAA,IACpB;AAAA,IACA,gBAAgB;AAAA,IAChB,WAAW,gBAAgB,OAAO,QAAQ,MAAM,UAAU,SAAS;AAAA,IACnE,WAAW;AAAA,EACb,CAAC,CAAC;AACF,SAAO;AACT;AAIA,IAAM,aAAa,CAAC;AAAA,EAClB;AAAA,EACA;AACF,MAAM,SAAS,OAAO,UAAU,KAAK;AAIrC,IAAM,eAAe,CAAC;AAAA,EACpB;AAAA,EACA;AACF,MAAM,SAAS,OAAO,UAAU,IAAI;AACpC,SAAS,SAAS,OAAO,UAAU,SAAS;AAC1C,MAAI,MAAM,SAAU,QAAO;AAC3B,MAAI,UAAU,CAAC;AACf,WAAS,SAAS,mBAAmB,KAAK,GAAG;AAC3C,QAAI,QAAS,SAAQ,KAAK;AAAA,MACxB,MAAM,MAAM;AAAA,MACZ,QAAQ,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,EAAE,IAAI,MAAM;AAAA,IACxD,CAAC;AAAA,QAAO,SAAQ,KAAK;AAAA,MACnB,MAAM,MAAM;AAAA,MACZ,QAAQ,MAAM,YAAY,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,EAAE;AAAA,IAChE,CAAC;AAAA,EACH;AACA,WAAS,MAAM,OAAO;AAAA,IACpB;AAAA,IACA,gBAAgB;AAAA,IAChB,WAAW;AAAA,EACb,CAAC,CAAC;AACF,SAAO;AACT;AAIA,IAAM,aAAa,CAAC;AAAA,EAClB;AAAA,EACA;AACF,MAAM,SAAS,OAAO,UAAU,KAAK;AAIrC,IAAM,eAAe,CAAC;AAAA,EACpB;AAAA,EACA;AACF,MAAM,SAAS,OAAO,UAAU,IAAI;AAIpC,IAAM,aAAa,UAAQ;AACzB,MAAI,KAAK,MAAM,SAAU,QAAO;AAChC,MAAI;AAAA,IACA;AAAA,EACF,IAAI,MACJ,UAAU,MAAM,QAAQ,mBAAmB,KAAK,EAAE,IAAI,CAAC;AAAA,IACrD;AAAA,IACA;AAAA,EACF,MAAM;AACJ,QAAI,OAAO,EAAG;AAAA,aAAgB,KAAK,MAAM,IAAI,OAAQ;AACrD,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF,CAAC,CAAC;AACJ,MAAI,YAAY,UAAU,MAAM,WAAW,WAAS;AAClD,QAAI,OAAO;AACX,QAAI,KAAK,cAAc;AACrB,UAAI,QAAQ,KAAK,YAAY,MAAM,IAAI,GACrC,MAAM,KAAK,YAAY,MAAM,MAAM,MAAM,SAAS,CAAC;AACrD,UAAI,IAAK,QAAO,MAAM,SAAS,KAAK,cAAc,IAAI,SAAS,KAAK,oBAAoB;AAAA,IAC1F;AACA,WAAO,KAAK,eAAe,OAAO,MAAM,IAAI;AAAA,EAC9C,CAAC,EAAE,IAAI,OAAO;AACd,OAAK,SAAS;AAAA,IACZ;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,IAChB,WAAW;AAAA,EACb,CAAC;AACD,SAAO;AACT;AAsCA,SAAS,kBAAkB,OAAO,KAAK;AACrC,MAAI,iBAAiB,KAAK,MAAM,SAAS,MAAM,GAAG,MAAM,CAAC,CAAC,EAAG,QAAO;AAAA,IAClE,MAAM;AAAA,IACN,IAAI;AAAA,EACN;AACA,MAAI,UAAU,WAAW,KAAK,EAAE,aAAa,GAAG;AAChD,MAAI,SAAS,QAAQ,YAAY,GAAG,GAClC,QAAQ,QAAQ,WAAW,GAAG,GAC9B;AACF,MAAI,UAAU,SAAS,OAAO,MAAM,OAAO,MAAM,QAAQ,QAAQ,WAAW,OAAO,KAAK,KAAK,SAAS,QAAQ,MAAM,SAAS,QAAQ,MAAM,IAAI,IAAI,MAAM,MAAM,IAAI,OAAO,OAAO,EAAE,EAAE,QAAQ,MAAM,IAAI,OAAO,MAAM,IAAI,EAAE,QAAQ,CAAC,KAAK,KAAK,MAAM,SAAS,OAAO,IAAI,MAAM,IAAI,CAAC,EAAG,QAAO;AAAA,IAC1R,MAAM,OAAO;AAAA,IACb,IAAI,MAAM;AAAA,EACZ;AACA,SAAO;AACT;AAQA,IAAM,yBAAsC,iBAAiB,KAAK;AAIlE,IAAM,kBAA+B,iBAAiB,IAAI;AAC1D,SAAS,iBAAiB,OAAO;AAC/B,SAAO,CAAC;AAAA,IACN;AAAA,IACA;AAAA,EACF,MAAM;AACJ,QAAI,MAAM,SAAU,QAAO;AAC3B,QAAI,UAAU,MAAM,cAAc,WAAS;AACzC,UAAI;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,OACJ,OAAO,MAAM,IAAI,OAAO,IAAI;AAC9B,UAAI,UAAU,CAAC,SAAS,QAAQ,MAAM,kBAAkB,OAAO,IAAI;AACnE,UAAI,MAAO,QAAO,MAAM,MAAM,KAAK,KAAK,OAAO,MAAM,IAAI,OAAO,EAAE,GAAG;AACrE,UAAI,KAAK,IAAI,cAAc,OAAO;AAAA,QAChC,eAAe;AAAA,QACf,qBAAqB,CAAC,CAAC;AAAA,MACzB,CAAC;AACD,UAAI,SAAS,eAAe,IAAI,IAAI;AACpC,UAAI,UAAU,KAAM,UAAS,YAAY,OAAO,KAAK,MAAM,IAAI,OAAO,IAAI,EAAE,IAAI,EAAE,CAAC,GAAG,MAAM,OAAO;AACnG,aAAO,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,CAAC,EAAG;AAC7D,UAAI,QAAS,EAAC;AAAA,QACZ;AAAA,QACA;AAAA,MACF,IAAI;AAAA,eAAkB,OAAO,KAAK,QAAQ,OAAO,KAAK,OAAO,OAAO,CAAC,KAAK,KAAK,KAAK,KAAK,MAAM,GAAG,IAAI,CAAC,EAAG,QAAO,KAAK;AACtH,UAAI,SAAS,CAAC,IAAI,aAAa,OAAO,MAAM,CAAC;AAC7C,UAAI,QAAS,QAAO,KAAK,aAAa,OAAO,GAAG,WAAW,KAAK,MAAM,EAAE,CAAC,CAAC;AAC1E,aAAO;AAAA,QACL,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA,QAAQ,KAAK,GAAG,MAAM;AAAA,QACxB;AAAA,QACA,OAAO,gBAAgB,OAAO,OAAO,IAAI,OAAO,CAAC,EAAE,MAAM;AAAA,MAC3D;AAAA,IACF,CAAC;AACD,aAAS,MAAM,OAAO,SAAS;AAAA,MAC7B,gBAAgB;AAAA,MAChB,WAAW;AAAA,IACb,CAAC,CAAC;AACF,WAAO;AAAA,EACT;AACF;AACA,SAAS,qBAAqB,OAAO,GAAG;AACtC,MAAI,SAAS;AACb,SAAO,MAAM,cAAc,WAAS;AAClC,QAAI,UAAU,CAAC;AACf,aAAS,MAAM,MAAM,MAAM,OAAO,MAAM,MAAK;AAC3C,UAAI,OAAO,MAAM,IAAI,OAAO,GAAG;AAC/B,UAAI,KAAK,SAAS,WAAW,MAAM,SAAS,MAAM,KAAK,KAAK,OAAO;AACjE,UAAE,MAAM,SAAS,KAAK;AACtB,iBAAS,KAAK;AAAA,MAChB;AACA,YAAM,KAAK,KAAK;AAAA,IAClB;AACA,QAAI,YAAY,MAAM,QAAQ,OAAO;AACrC,WAAO;AAAA,MACL;AAAA,MACA,OAAO,gBAAgB,MAAM,UAAU,OAAO,MAAM,QAAQ,CAAC,GAAG,UAAU,OAAO,MAAM,MAAM,CAAC,CAAC;AAAA,IACjG;AAAA,EACF,CAAC;AACH;AAMA,IAAM,kBAAkB,CAAC;AAAA,EACvB;AAAA,EACA;AACF,MAAM;AACJ,MAAI,MAAM,SAAU,QAAO;AAC3B,MAAI,UAAU,uBAAO,OAAO,IAAI;AAChC,MAAI,UAAU,IAAI,cAAc,OAAO;AAAA,IACrC,qBAAqB,WAAS;AAC5B,UAAI,QAAQ,QAAQ,KAAK;AACzB,aAAO,SAAS,OAAO,KAAK;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,MAAI,UAAU,qBAAqB,OAAO,CAAC,MAAMC,UAAS,UAAU;AAClE,QAAI,SAAS,eAAe,SAAS,KAAK,IAAI;AAC9C,QAAI,UAAU,KAAM;AACpB,QAAI,CAAC,KAAK,KAAK,KAAK,IAAI,EAAG,UAAS;AACpC,QAAI,MAAM,OAAO,KAAK,KAAK,IAAI,EAAE,CAAC;AAClC,QAAI,OAAO,aAAa,OAAO,MAAM;AACrC,QAAI,OAAO,QAAQ,MAAM,OAAO,KAAK,OAAO,IAAI,QAAQ;AACtD,cAAQ,KAAK,IAAI,IAAI;AACrB,MAAAA,SAAQ,KAAK;AAAA,QACX,MAAM,KAAK;AAAA,QACX,IAAI,KAAK,OAAO,IAAI;AAAA,QACpB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,MAAI,CAAC,QAAQ,QAAQ,MAAO,UAAS,MAAM,OAAO,SAAS;AAAA,IACzD,WAAW;AAAA,EACb,CAAC,CAAC;AACF,SAAO;AACT;AAKA,IAAM,aAAa,CAAC;AAAA,EAClB;AAAA,EACA;AACF,MAAM;AACJ,MAAI,MAAM,SAAU,QAAO;AAC3B,WAAS,MAAM,OAAO,qBAAqB,OAAO,CAAC,MAAM,YAAY;AACnE,YAAQ,KAAK;AAAA,MACX,MAAM,KAAK;AAAA,MACX,QAAQ,MAAM,MAAM,UAAU;AAAA,IAChC,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,WAAW;AAAA,EACb,CAAC,CAAC;AACF,SAAO;AACT;AAKA,IAAM,aAAa,CAAC;AAAA,EAClB;AAAA,EACA;AACF,MAAM;AACJ,MAAI,MAAM,SAAU,QAAO;AAC3B,WAAS,MAAM,OAAO,qBAAqB,OAAO,CAAC,MAAM,YAAY;AACnE,QAAI,QAAQ,OAAO,KAAK,KAAK,IAAI,EAAE,CAAC;AACpC,QAAI,CAAC,MAAO;AACZ,QAAI,MAAM,YAAY,OAAO,MAAM,OAAO,GACxC,OAAO;AACT,QAAI,SAAS,aAAa,OAAO,KAAK,IAAI,GAAG,MAAM,cAAc,KAAK,CAAC,CAAC;AACxE,WAAO,OAAO,MAAM,UAAU,OAAO,OAAO,UAAU,MAAM,WAAW,IAAI,KAAK,OAAO,WAAW,IAAI,EAAG;AACzG,YAAQ,KAAK;AAAA,MACX,MAAM,KAAK,OAAO;AAAA,MAClB,IAAI,KAAK,OAAO,MAAM;AAAA,MACtB,QAAQ,OAAO,MAAM,IAAI;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,WAAW;AAAA,EACb,CAAC,CAAC;AACF,SAAO;AACT;AAQA,IAAM,qBAAqB,UAAQ;AACjC,OAAK,gBAAgB;AACrB,SAAO;AACT;AAgDA,IAAM,mBAAmB,CAAC;AAAA,EACxB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,CAAC;AAkCD,IAAM,iBAA8B,CAAC;AAAA,EACnC,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,CAAC,EAAE,OAAoB,iBAAiB,IAAI,QAAM;AAAA,EAChD,KAAK,EAAE;AAAA,EACP,KAAK,EAAE;AAAA,EACP,OAAO,EAAE;AACX,EAAE,CAAC;AAwBH,IAAM,gBAA6B,CAAC;AAAA,EAClC,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP,CAAC,EAAE,OAAO,cAAc;;;ACjlET,SAAR,QAAyB;AAC9B,MAAI,MAAM,UAAU,CAAC;AACrB,MAAI,OAAO,OAAO,SAAU,OAAM,SAAS,cAAc,GAAG;AAC5D,MAAI,IAAI,GACN,OAAO,UAAU,CAAC;AACpB,MAAI,QAAQ,OAAO,QAAQ,YAAY,KAAK,YAAY,QAAQ,CAAC,MAAM,QAAQ,IAAI,GAAG;AACpF,aAAS,QAAQ,KAAM,KAAI,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,GAAG;AAC3E,UAAI,QAAQ,KAAK,IAAI;AACrB,UAAI,OAAO,SAAS,SAAU,KAAI,aAAa,MAAM,KAAK;AAAA,eAAW,SAAS,KAAM,KAAI,IAAI,IAAI;AAAA,IAClG;AACA;AAAA,EACF;AACA,SAAO,IAAI,UAAU,QAAQ,IAAK,KAAI,KAAK,UAAU,CAAC,CAAC;AACvD,SAAO;AACT;AACA,SAAS,IAAI,KAAK,OAAO;AACvB,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,YAAY,SAAS,eAAe,KAAK,CAAC;AAAA,EAChD,WAAW,SAAS,MAAM;AAAA,EAAC,WAAW,MAAM,YAAY,MAAM;AAC5D,QAAI,YAAY,KAAK;AAAA,EACvB,WAAW,MAAM,QAAQ,KAAK,GAAG;AAC/B,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAK,KAAI,KAAK,MAAM,CAAC,CAAC;AAAA,EAC1D,OAAO;AACL,UAAM,IAAI,WAAW,6BAA6B,KAAK;AAAA,EACzD;AACF;;;ACtBA,IAAM,iBAAiB,OAAO,OAAO,UAAU,aAAa,aAAa,OAAK,EAAE,UAAU,MAAM,IAAI,OAAK;AAKzG,IAAM,eAAN,MAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcjB,YAAY,MAAM,OAAO,OAAO,GAAG,KAAK,KAAK,QAAQ,WAAW,MAAM;AACpE,SAAK,OAAO;AAMZ,SAAK,QAAQ;AAAA,MACX,MAAM;AAAA,MACN,IAAI;AAAA,IACN;AAIA,SAAK,OAAO;AACZ,SAAK,UAAU,CAAC;AAChB,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,OAAO,KAAK,UAAU,MAAM,EAAE;AACnC,SAAK,cAAc;AACnB,SAAK,YAAY,YAAY,OAAK,UAAU,eAAe,CAAC,CAAC,IAAI;AACjE,SAAK,QAAQ,KAAK,UAAU,KAAK;AAAA,EACnC;AAAA,EACA,OAAO;AACL,QAAI,KAAK,aAAa,KAAK,OAAO,QAAQ;AACxC,WAAK,eAAe,KAAK,OAAO;AAChC,WAAK,KAAK,KAAK;AACf,UAAI,KAAK,KAAK,KAAM,QAAO;AAC3B,WAAK,YAAY;AACjB,WAAK,SAAS,KAAK,KAAK;AAAA,IAC1B;AACA,WAAO,YAAY,KAAK,QAAQ,KAAK,SAAS;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO;AACL,WAAO,KAAK,QAAQ,OAAQ,MAAK,QAAQ,IAAI;AAC7C,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,eAAS;AACP,UAAI,OAAO,KAAK,KAAK;AACrB,UAAI,OAAO,GAAG;AACZ,aAAK,OAAO;AACZ,eAAO;AAAA,MACT;AACA,UAAI,MAAM,cAAc,IAAI,GAC1B,QAAQ,KAAK,cAAc,KAAK;AAClC,WAAK,aAAa,cAAc,IAAI;AACpC,UAAI,OAAO,KAAK,UAAU,GAAG;AAC7B,UAAI,KAAK,OAAQ,UAAS,IAAI,GAAG,MAAM,SAAQ,KAAK;AAClD,YAAI,OAAO,KAAK,WAAW,CAAC;AAC5B,YAAI,QAAQ,KAAK,MAAM,MAAM,KAAK,KAAK,YAAY,KAAK,WAAW;AACnE,YAAI,KAAK,KAAK,SAAS,GAAG;AACxB,cAAI,OAAO;AACT,iBAAK,QAAQ;AACb,mBAAO;AAAA,UACT;AACA;AAAA,QACF;AACA,YAAI,OAAO,SAAS,IAAI,IAAI,UAAU,IAAI,WAAW,CAAC,KAAK,KAAM;AAAA,MACnE;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM,MAAM,KAAK,KAAK;AACpB,QAAI,QAAQ;AACZ,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK,GAAG;AAC/C,UAAI,QAAQ,KAAK,QAAQ,CAAC,GACxB,OAAO;AACT,UAAI,KAAK,MAAM,WAAW,KAAK,KAAK,MAAM;AACxC,YAAI,SAAS,KAAK,MAAM,SAAS,GAAG;AAClC,kBAAQ;AAAA,YACN,MAAM,KAAK,QAAQ,IAAI,CAAC;AAAA,YACxB,IAAI;AAAA,UACN;AAAA,QACF,OAAO;AACL,eAAK,QAAQ,CAAC;AACd,iBAAO;AAAA,QACT;AAAA,MACF;AACA,UAAI,CAAC,MAAM;AACT,aAAK,QAAQ,OAAO,GAAG,CAAC;AACxB,aAAK;AAAA,MACP;AAAA,IACF;AACA,QAAI,KAAK,MAAM,WAAW,CAAC,KAAK,MAAM;AACpC,UAAI,KAAK,MAAM,UAAU,EAAG,SAAQ;AAAA,QAClC,MAAM;AAAA,QACN,IAAI;AAAA,MACN;AAAA,UAAO,MAAK,QAAQ,KAAK,GAAG,GAAG;AAAA,IACjC;AACA,QAAI,SAAS,KAAK,QAAQ,CAAC,KAAK,KAAK,MAAM,MAAM,MAAM,IAAI,KAAK,QAAQ,KAAK,WAAW,EAAG,SAAQ;AACnG,WAAO;AAAA,EACT;AACF;AACA,IAAI,OAAO,UAAU,YAAa,cAAa,UAAU,OAAO,QAAQ,IAAI,WAAY;AACtF,SAAO;AACT;AACA,IAAM,QAAQ;AAAA,EACZ,MAAM;AAAA,EACN,IAAI;AAAA,EACJ,OAAoB,KAAK,KAAK,EAAE;AAClC;AACA,IAAM,YAAY,QAAQ,IAAI,WAAW,OAAO,KAAK;AAMrD,IAAM,eAAN,MAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjB,YAAY,MAAM,OAAO,SAAS,OAAO,GAAG,KAAK,KAAK,QAAQ;AAC5D,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,UAAU;AAKf,SAAK,OAAO;AAMZ,SAAK,QAAQ;AACb,QAAI,uBAAuB,KAAK,KAAK,EAAG,QAAO,IAAI,sBAAsB,MAAM,OAAO,SAAS,MAAM,EAAE;AACvG,SAAK,KAAK,IAAI,OAAO,OAAO,cAAc,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,cAAc,MAAM,GAAG;AAC3H,SAAK,OAAO,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AACtE,SAAK,OAAO,KAAK,KAAK;AACtB,QAAI,YAAY,KAAK,OAAO,IAAI;AAChC,SAAK,eAAe,UAAU;AAC9B,SAAK,WAAW,UAAU,MAAM,IAAI;AACpC,SAAK,QAAQ,KAAK,YAAY;AAAA,EAChC;AAAA,EACA,QAAQ,MAAM;AACZ,SAAK,KAAK,KAAK,IAAI;AACnB,QAAI,KAAK,KAAK,WAAW;AACvB,WAAK,UAAU;AAAA,IACjB,OAAO;AACL,WAAK,UAAU,KAAK,KAAK;AACzB,UAAI,KAAK,eAAe,KAAK,QAAQ,SAAS,KAAK,GAAI,MAAK,UAAU,KAAK,QAAQ,MAAM,GAAG,KAAK,KAAK,KAAK,YAAY;AACvH,WAAK,KAAK,KAAK;AAAA,IACjB;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,eAAe,KAAK,eAAe,KAAK,QAAQ,SAAS;AAC9D,QAAI,KAAK,eAAe,KAAK,GAAI,MAAK,UAAU;AAAA,QAAQ,MAAK,QAAQ,CAAC;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO;AACL,aAAS,MAAM,KAAK,WAAW,KAAK,kBAAgB;AAClD,WAAK,GAAG,YAAY;AACpB,UAAI,QAAQ,KAAK,YAAY,KAAK,MAAM,KAAK,GAAG,KAAK,KAAK,OAAO;AACjE,UAAI,OAAO;AACT,YAAI,OAAO,KAAK,eAAe,MAAM,OACnC,KAAK,OAAO,MAAM,CAAC,EAAE;AACvB,aAAK,WAAW,UAAU,KAAK,MAAM,MAAM,QAAQ,KAAK,IAAI,EAAE;AAC9D,YAAI,QAAQ,KAAK,eAAe,KAAK,QAAQ,OAAQ,MAAK,SAAS;AACnE,aAAK,OAAO,MAAM,OAAO,KAAK,MAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI;AACrF,eAAK,QAAQ;AAAA,YACX;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,cAAM,KAAK,WAAW,KAAK;AAAA,MAC7B,WAAW,KAAK,eAAe,KAAK,QAAQ,SAAS,KAAK,IAAI;AAC5D,aAAK,SAAS;AACd,cAAM;AAAA,MACR,OAAO;AACL,aAAK,OAAO;AACZ,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,YAAyB,oBAAI,QAAQ;AAE3C,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,YAAY,MAAM,MAAM;AACtB,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,EACd;AAAA,EACA,IAAI,KAAK;AACP,WAAO,KAAK,OAAO,KAAK,KAAK;AAAA,EAC/B;AAAA,EACA,OAAO,IAAI,KAAK,MAAM,IAAI;AACxB,QAAI,SAAS,UAAU,IAAI,GAAG;AAC9B,QAAI,CAAC,UAAU,OAAO,QAAQ,MAAM,OAAO,MAAM,MAAM;AACrD,UAAI,OAAO,IAAI,cAAa,MAAM,IAAI,YAAY,MAAM,EAAE,CAAC;AAC3D,gBAAU,IAAI,KAAK,IAAI;AACvB,aAAO;AAAA,IACT;AACA,QAAI,OAAO,QAAQ,QAAQ,OAAO,MAAM,GAAI,QAAO;AACnD,QAAI;AAAA,MACF;AAAA,MACA,MAAM;AAAA,IACR,IAAI;AACJ,QAAI,aAAa,MAAM;AACrB,aAAO,IAAI,YAAY,MAAM,UAAU,IAAI;AAC3C,mBAAa;AAAA,IACf;AACA,QAAI,OAAO,KAAK,GAAI,SAAQ,IAAI,YAAY,OAAO,IAAI,EAAE;AACzD,cAAU,IAAI,KAAK,IAAI,cAAa,YAAY,IAAI,CAAC;AACrD,WAAO,IAAI,cAAa,MAAM,KAAK,MAAM,OAAO,YAAY,KAAK,UAAU,CAAC;AAAA,EAC9E;AACF;AACA,IAAM,wBAAN,MAA4B;AAAA,EAC1B,YAAY,MAAM,OAAO,SAAS,MAAM,IAAI;AAC1C,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,WAAW,UAAU,MAAM,IAAI;AACpC,SAAK,KAAK,IAAI,OAAO,OAAO,cAAc,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,cAAc,MAAM,GAAG;AAC3H,SAAK,OAAO,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AACtE,SAAK,OAAO,aAAa,IAAI,MAAM,MAAM,KAAK;AAAA,MAAS,OAAO;AAAA;AAAA,IAAqB,CAAC;AAAA,EACtF;AAAA,EACA,SAAS,KAAK;AACZ,WAAO,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,EAAE;AAAA,EAC1D;AAAA,EACA,OAAO;AACL,eAAS;AACP,UAAI,MAAM,KAAK,GAAG,YAAY,KAAK,WAAW,KAAK,KAAK;AACxD,UAAI,QAAQ,KAAK,GAAG,KAAK,KAAK,KAAK,IAAI;AAEvC,UAAI,SAAS,CAAC,MAAM,CAAC,KAAK,MAAM,SAAS,KAAK;AAC5C,aAAK,GAAG,YAAY,MAAM;AAC1B,gBAAQ,KAAK,GAAG,KAAK,KAAK,KAAK,IAAI;AAAA,MACrC;AACA,UAAI,OAAO;AACT,YAAI,OAAO,KAAK,KAAK,OAAO,MAAM,OAChC,KAAK,OAAO,MAAM,CAAC,EAAE;AAGvB,aAAK,KAAK,KAAK,MAAM,KAAK,MAAM,MAAM,QAAQ,MAAM,CAAC,EAAE,UAAU,KAAK,KAAK,KAAK,SAAS,QAAQ,CAAC,KAAK,QAAQ,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI;AAC1I,eAAK,QAAQ;AAAA,YACX;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,eAAK,WAAW,UAAU,KAAK,MAAM,MAAM,QAAQ,KAAK,IAAI,EAAE;AAC9D,iBAAO;AAAA,QACT;AAAA,MACF;AACA,UAAI,KAAK,KAAK,MAAM,KAAK,IAAI;AAC3B,aAAK,OAAO;AACZ,eAAO;AAAA,MACT;AAEA,WAAK,OAAO,aAAa,IAAI,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,SAAS,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,SAAS,CAAC,CAAC;AAAA,IACnH;AAAA,EACF;AACF;AACA,IAAI,OAAO,UAAU,aAAa;AAChC,eAAa,UAAU,OAAO,QAAQ,IAAI,sBAAsB,UAAU,OAAO,QAAQ,IAAI,WAAY;AACvG,WAAO;AAAA,EACT;AACF;AACA,SAAS,YAAY,QAAQ;AAC3B,MAAI;AACF,QAAI,OAAO,QAAQ,SAAS;AAC5B,WAAO;AAAA,EACT,SAAS,IAAI;AACX,WAAO;AAAA,EACT;AACF;AACA,SAAS,UAAU,MAAM,KAAK;AAC5B,MAAI,OAAO,KAAK,OAAQ,QAAO;AAC/B,MAAI,OAAO,KAAK,OAAO,GAAG,GACxB;AACF,SAAO,MAAM,KAAK,OAAO,OAAO,KAAK,KAAK,WAAW,MAAM,KAAK,IAAI,MAAM,SAAU,OAAO,MAAQ;AACnG,SAAO;AACT;AACA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,OAAO,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,MAAM,UAAU,KAAK,IAAI,EAAE,MAAM;AAC9E,MAAI,QAAQ,MAAI,SAAS;AAAA,IACvB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,EACT,CAAC;AACD,MAAI,MAAM,MAAI,QAAQ;AAAA,IACpB,OAAO;AAAA,IACP,WAAW,WAAS;AAClB,UAAI,MAAM,WAAW,IAAI;AAEvB,cAAM,eAAe;AACrB,aAAK,SAAS;AAAA,UACZ,SAAS,aAAa,GAAG,KAAK;AAAA,QAChC,CAAC;AACD,aAAK,MAAM;AAAA,MACb,WAAW,MAAM,WAAW,IAAI;AAE9B,cAAM,eAAe;AACrB,WAAG;AAAA,MACL;AAAA,IACF;AAAA,IACA,UAAU,WAAS;AACjB,YAAM,eAAe;AACrB,SAAG;AAAA,IACL;AAAA,EACF,GAAG,MAAI,SAAS,KAAK,MAAM,OAAO,YAAY,GAAG,MAAM,KAAK,GAAG,KAAK,MAAI,UAAU;AAAA,IAChF,OAAO;AAAA,IACP,MAAM;AAAA,EACR,GAAG,KAAK,MAAM,OAAO,IAAI,CAAC,CAAC;AAC3B,WAAS,KAAK;AACZ,QAAI,QAAQ,6BAA6B,KAAK,MAAM,KAAK;AACzD,QAAI,CAAC,MAAO;AACZ,QAAI;AAAA,MACA;AAAA,IACF,IAAI,MACJ,YAAY,MAAM,IAAI,OAAO,MAAM,UAAU,KAAK,IAAI;AACxD,QAAI,CAAC,EAAE,MAAM,IAAI,IAAI,OAAO,IAAI;AAChC,QAAI,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC,IAAI;AAC9B,QAAIC,QAAO,KAAK,CAAC,KAAK,UAAU;AAChC,QAAI,MAAM,SAAS;AACjB,UAAI,KAAKA,QAAO;AAChB,UAAI,KAAM,MAAK,MAAM,QAAQ,MAAM,KAAK,KAAK,UAAU,SAAS,MAAM,IAAI;AAC1E,MAAAA,QAAO,KAAK,MAAM,MAAM,IAAI,QAAQ,EAAE;AAAA,IACxC,WAAW,MAAM,MAAM;AACrB,MAAAA,QAAOA,SAAQ,QAAQ,MAAM,KAAK,KAAK,UAAU;AAAA,IACnD;AACA,QAAI,UAAU,MAAM,IAAI,KAAK,KAAK,IAAI,GAAG,KAAK,IAAI,MAAM,IAAI,OAAOA,KAAI,CAAC,CAAC;AACzE,QAAI,YAAY,gBAAgB,OAAO,QAAQ,OAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,QAAQ,MAAM,CAAC,CAAC;AAChG,SAAK,SAAS;AAAA,MACZ,SAAS,CAAC,aAAa,GAAG,KAAK,GAAG,WAAW,eAAe,UAAU,MAAM;AAAA,QAC1E,GAAG;AAAA,MACL,CAAC,CAAC;AAAA,MACF;AAAA,IACF,CAAC;AACD,SAAK,MAAM;AAAA,EACb;AACA,SAAO;AAAA,IACL;AAAA,EACF;AACF;AACA,IAAM,eAA4B,YAAY,OAAO;AACrD,IAAM,cAA2B,WAAW,OAAO;AAAA,EACjD,SAAS;AACP,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,IAAI;AAChB,aAAS,KAAK,GAAG,QAAS,KAAI,EAAE,GAAG,YAAY,EAAG,SAAQ,EAAE;AAC5D,WAAO;AAAA,EACT;AAAA,EACA,SAAS,OAAK,UAAU,KAAK,GAAG,SAAO,MAAM,mBAAmB,IAAI;AACtE,CAAC;AAUD,IAAM,WAAW,UAAQ;AACvB,MAAI,QAAQ,SAAS,MAAM,gBAAgB;AAC3C,MAAI,CAAC,OAAO;AACV,QAAI,UAAU,CAAC,aAAa,GAAG,IAAI,CAAC;AACpC,QAAI,KAAK,MAAM,MAAM,aAAa,KAAK,KAAK,KAAM,SAAQ,KAAK,YAAY,aAAa,GAAG,CAAC,aAAa,WAAW,CAAC,CAAC;AACtH,SAAK,SAAS;AAAA,MACZ;AAAA,IACF,CAAC;AACD,YAAQ,SAAS,MAAM,gBAAgB;AAAA,EACzC;AACA,MAAI,MAAO,OAAM,IAAI,cAAc,OAAO,EAAE,OAAO;AACnD,SAAO;AACT;AACA,IAAM,cAA2B,WAAW,UAAU;AAAA,EACpD,yBAAyB;AAAA,IACvB,SAAS;AAAA,IACT,WAAW;AAAA,MACT,UAAU;AAAA,IACZ;AAAA,EACF;AACF,CAAC;AACD,IAAM,0BAA0B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,YAAY;AAAA,EACZ,YAAY;AACd;AACA,IAAM,kBAA+B,MAAM,OAAO;AAAA,EAChD,QAAQ,SAAS;AACf,WAAO,cAAc,SAAS,yBAAyB;AAAA,MACrD,2BAA2B,CAAC,GAAG,MAAM,KAAK;AAAA,MAC1C,oBAAoB,KAAK;AAAA,MACzB,YAAY,KAAK;AAAA,IACnB,CAAC;AAAA,EACH;AACF,CAAC;AAOD,SAAS,0BAA0B,SAAS;AAC1C,MAAI,MAAM,CAAC,cAAc,gBAAgB;AACzC,MAAI,QAAS,KAAI,KAAK,gBAAgB,GAAG,OAAO,CAAC;AACjD,SAAO;AACT;AACA,IAAM,YAAyB,WAAW,KAAK;AAAA,EAC7C,OAAO;AACT,CAAC;AACD,IAAM,gBAA6B,WAAW,KAAK;AAAA,EACjD,OAAO;AACT,CAAC;AAED,SAAS,qBAAqB,OAAO,OAAO,MAAM,IAAI;AACpD,UAAQ,QAAQ,KAAK,MAAM,MAAM,SAAS,OAAO,GAAG,IAAI,CAAC,KAAK,aAAa,UAAU,MAAM,MAAM,IAAI,UAAU,MAAM,MAAM,SAAS,IAAI,KAAK,CAAC,CAAC,KAAK,aAAa;AACnK;AAEA,SAAS,WAAW,OAAO,OAAO,MAAM,IAAI;AAC1C,SAAO,MAAM,MAAM,SAAS,MAAM,OAAO,CAAC,CAAC,KAAK,aAAa,QAAQ,MAAM,MAAM,SAAS,KAAK,GAAG,EAAE,CAAC,KAAK,aAAa;AACzH;AACA,IAAM,mBAAgC,WAAW,UAAU,MAAM;AAAA,EAC/D,YAAY,MAAM;AAChB,SAAK,cAAc,KAAK,QAAQ,IAAI;AAAA,EACtC;AAAA,EACA,OAAO,QAAQ;AACb,QAAI,OAAO,gBAAgB,OAAO,cAAc,OAAO,gBAAiB,MAAK,cAAc,KAAK,QAAQ,OAAO,IAAI;AAAA,EACrH;AAAA,EACA,QAAQ,MAAM;AACZ,QAAI,OAAO,KAAK,MAAM,MAAM,eAAe;AAC3C,QAAI;AAAA,MACA;AAAA,IACF,IAAI,MACJ,MAAM,MAAM;AACd,QAAI,IAAI,OAAO,SAAS,EAAG,QAAO,WAAW;AAC7C,QAAI,QAAQ,IAAI,MACd,OACA,QAAQ;AACV,QAAI,MAAM,OAAO;AACf,UAAI,CAAC,KAAK,0BAA2B,QAAO,WAAW;AACvD,UAAI,OAAO,MAAM,OAAO,MAAM,IAAI;AAClC,UAAI,CAAC,KAAM,QAAO,WAAW;AAC7B,cAAQ,MAAM,gBAAgB,MAAM,IAAI;AACxC,cAAQ,MAAM,SAAS,KAAK,MAAM,KAAK,EAAE;AAAA,IAC3C,OAAO;AACL,UAAI,MAAM,MAAM,KAAK,MAAM;AAC3B,UAAI,MAAM,KAAK,sBAAsB,MAAM,IAAK,QAAO,WAAW;AAClE,UAAI,KAAK,YAAY;AACnB,gBAAQ,MAAM,SAAS,MAAM,MAAM,MAAM,EAAE;AAC3C,gBAAQ,MAAM,gBAAgB,MAAM,IAAI;AACxC,YAAI,EAAE,qBAAqB,OAAO,OAAO,MAAM,MAAM,MAAM,EAAE,KAAK,WAAW,OAAO,OAAO,MAAM,MAAM,MAAM,EAAE,GAAI,QAAO,WAAW;AAAA,MACvI,OAAO;AACL,gBAAQ,MAAM,SAAS,MAAM,MAAM,MAAM,EAAE;AAC3C,YAAI,CAAC,MAAO,QAAO,WAAW;AAAA,MAChC;AAAA,IACF;AACA,QAAI,OAAO,CAAC;AACZ,aAAS,QAAQ,KAAK,eAAe;AACnC,UAAI,SAAS,IAAI,aAAa,MAAM,KAAK,OAAO,KAAK,MAAM,KAAK,EAAE;AAClE,aAAO,CAAC,OAAO,KAAK,EAAE,MAAM;AAC1B,YAAI;AAAA,UACF;AAAA,UACA;AAAA,QACF,IAAI,OAAO;AACX,YAAI,CAAC,SAAS,qBAAqB,OAAO,OAAO,MAAM,EAAE,GAAG;AAC1D,cAAI,MAAM,SAAS,QAAQ,MAAM,QAAQ,MAAM,MAAM,GAAI,MAAK,KAAK,cAAc,MAAM,MAAM,EAAE,CAAC;AAAA,mBAAW,QAAQ,MAAM,MAAM,MAAM,MAAM,KAAM,MAAK,KAAK,UAAU,MAAM,MAAM,EAAE,CAAC;AACpL,cAAI,KAAK,SAAS,KAAK,WAAY,QAAO,WAAW;AAAA,QACvD;AAAA,MACF;AAAA,IACF;AACA,WAAO,WAAW,IAAI,IAAI;AAAA,EAC5B;AACF,GAAG;AAAA,EACD,aAAa,OAAK,EAAE;AACtB,CAAC;AACD,IAAM,eAA4B,WAAW,UAAU;AAAA,EACrD,sBAAsB;AAAA,IACpB,iBAAiB;AAAA,EACnB;AAAA,EACA,sCAAsC;AAAA,IACpC,iBAAiB;AAAA,EACnB;AACF,CAAC;AAED,IAAM,aAAa,CAAC;AAAA,EAClB;AAAA,EACA;AACF,MAAM;AACJ,MAAI;AAAA,IACF;AAAA,EACF,IAAI;AACJ,MAAI,SAAS,gBAAgB,OAAO,UAAU,OAAO,IAAI,WAAS,MAAM,OAAO,MAAM,IAAI,KAAK,gBAAgB,OAAO,MAAM,IAAI,CAAC,GAAG,UAAU,SAAS;AACtJ,MAAI,OAAO,GAAG,SAAS,EAAG,QAAO;AACjC,WAAS,MAAM,OAAO;AAAA,IACpB,WAAW;AAAA,EACb,CAAC,CAAC;AACF,SAAO;AACT;AAGA,SAAS,mBAAmB,OAAO,OAAO;AACxC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,MAAM;AACV,MAAI,OAAO,MAAM,OAAO,KAAK,IAAI,GAC/B,WAAW,QAAQ,KAAK,QAAQ,KAAK,QAAQ,KAAK,MAAM,KAAK;AAC/D,WAAS,SAAS,OAAO,SAAS,IAAI,aAAa,MAAM,KAAK,OAAO,OAAO,OAAO,SAAS,CAAC,EAAE,EAAE,OAAK;AACpG,WAAO,KAAK;AACZ,QAAI,OAAO,MAAM;AACf,UAAI,OAAQ,QAAO;AACnB,eAAS,IAAI,aAAa,MAAM,KAAK,OAAO,GAAG,KAAK,IAAI,GAAG,OAAO,OAAO,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;AAC9F,eAAS;AAAA,IACX,OAAO;AACL,UAAI,UAAU,OAAO,KAAK,OAAK,EAAE,QAAQ,OAAO,MAAM,IAAI,EAAG;AAC7D,UAAI,UAAU;AACZ,YAAIC,QAAO,MAAM,OAAO,OAAO,MAAM,IAAI;AACzC,YAAI,CAACA,SAAQA,MAAK,QAAQ,OAAO,MAAM,QAAQA,MAAK,MAAM,OAAO,MAAM,GAAI;AAAA,MAC7E;AACA,aAAO,OAAO;AAAA,IAChB;AAAA,EACF;AACF;AAKA,IAAM,uBAAuB,CAAC;AAAA,EAC5B;AAAA,EACA;AACF,MAAM;AACJ,MAAI;AAAA,IACF;AAAA,EACF,IAAI,MAAM;AACV,MAAI,OAAO,KAAK,SAAO,IAAI,SAAS,IAAI,EAAE,EAAG,QAAO,WAAW;AAAA,IAC7D;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,eAAe,MAAM,SAAS,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC,EAAE,EAAE;AAC9D,MAAI,MAAM,UAAU,OAAO,KAAK,OAAK,MAAM,SAAS,EAAE,MAAM,EAAE,EAAE,KAAK,YAAY,EAAG,QAAO;AAC3F,MAAI,QAAQ,mBAAmB,OAAO,YAAY;AAClD,MAAI,CAAC,MAAO,QAAO;AACnB,WAAS,MAAM,OAAO;AAAA,IACpB,WAAW,MAAM,UAAU,SAAS,gBAAgB,MAAM,MAAM,MAAM,MAAM,EAAE,GAAG,KAAK;AAAA,IACtF,SAAS,WAAW,eAAe,MAAM,EAAE;AAAA,EAC7C,CAAC,CAAC;AACF,SAAO;AACT;AACA,IAAM,oBAAiC,MAAM,OAAO;AAAA,EAClD,QAAQ,SAAS;AACf,WAAO,cAAc,SAAS;AAAA,MAC5B,KAAK;AAAA,MACL,eAAe;AAAA,MACf,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa,UAAQ,IAAI,YAAY,IAAI;AAAA,MACzC,eAAe,WAAS,WAAW,eAAe,KAAK;AAAA,IACzD,CAAC;AAAA,EACH;AACF,CAAC;AAaD,IAAM,cAAN,MAAkB;AAAA;AAAA;AAAA;AAAA,EAIhB,YAAY,QAAQ;AAClB,SAAK,SAAS,OAAO;AACrB,SAAK,gBAAgB,CAAC,CAAC,OAAO;AAC9B,SAAK,UAAU,CAAC,CAAC,OAAO;AACxB,SAAK,SAAS,CAAC,CAAC,OAAO;AACvB,SAAK,UAAU,OAAO,WAAW;AACjC,SAAK,QAAQ,CAAC,CAAC,KAAK,WAAW,CAAC,KAAK,UAAU,YAAY,KAAK,MAAM;AACtE,SAAK,WAAW,KAAK,QAAQ,KAAK,MAAM;AACxC,SAAK,YAAY,CAAC,CAAC,OAAO;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,MAAM;AACZ,WAAO,KAAK,UAAU,OAAO,KAAK,QAAQ,gBAAgB,CAAC,GAAG,OAAO,MAAM,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM,MAAM,MAAO,IAAI;AAAA,EACpI;AAAA;AAAA;AAAA;AAAA,EAIA,GAAG,OAAO;AACR,WAAO,KAAK,UAAU,MAAM,UAAU,KAAK,WAAW,MAAM,WAAW,KAAK,iBAAiB,MAAM,iBAAiB,KAAK,UAAU,MAAM,UAAU,KAAK,aAAa,MAAM;AAAA,EAC7K;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACP,WAAO,KAAK,SAAS,IAAI,YAAY,IAAI,IAAI,IAAI,YAAY,IAAI;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,OAAO,OAAO,GAAG,IAAI;AAC7B,QAAI,KAAK,MAAM,MAAM,QAAQ,YAAY,OAAO;AAAA,MAC9C,KAAK;AAAA,IACP,CAAC;AACD,QAAI,MAAM,KAAM,MAAK,GAAG,IAAI;AAC5B,WAAO,KAAK,SAAS,aAAa,MAAM,IAAI,MAAM,EAAE,IAAI,aAAa,MAAM,IAAI,MAAM,EAAE;AAAA,EACzF;AACF;AACA,IAAM,YAAN,MAAgB;AAAA,EACd,YAAY,MAAM;AAChB,SAAK,OAAO;AAAA,EACd;AACF;AACA,SAAS,aAAa,MAAM,OAAO,MAAM,IAAI;AAC3C,SAAO,IAAI,aAAa,MAAM,KAAK,KAAK,UAAU,MAAM,IAAI,KAAK,gBAAgB,SAAY,OAAK,EAAE,YAAY,GAAG,KAAK,YAAY,eAAe,MAAM,KAAK,MAAM,gBAAgB,MAAM,UAAU,KAAK,IAAI,CAAC,IAAI,MAAS;AAC7N;AACA,SAAS,eAAe,KAAK,aAAa;AACxC,SAAO,CAAC,MAAM,IAAI,KAAK,WAAW;AAChC,QAAI,SAAS,QAAQ,SAAS,IAAI,SAAS,IAAI;AAC7C,eAAS,KAAK,IAAI,GAAG,OAAO,CAAC;AAC7B,YAAM,IAAI,YAAY,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,CAAC,CAAC;AAAA,IAC5D;AACA,YAAQ,YAAY,WAAW,KAAK,OAAO,MAAM,CAAC,KAAK,aAAa,QAAQ,YAAY,UAAU,KAAK,OAAO,MAAM,CAAC,KAAK,aAAa,UAAU,YAAY,UAAU,KAAK,KAAK,MAAM,CAAC,KAAK,aAAa,QAAQ,YAAY,WAAW,KAAK,KAAK,MAAM,CAAC,KAAK,aAAa;AAAA,EAC9Q;AACF;AACA,IAAM,cAAN,cAA0B,UAAU;AAAA,EAClC,YAAY,MAAM;AAChB,UAAM,IAAI;AAAA,EACZ;AAAA,EACA,UAAU,OAAO,SAAS,OAAO;AAC/B,QAAI,SAAS,aAAa,KAAK,MAAM,OAAO,OAAO,MAAM,IAAI,MAAM,EAAE,gBAAgB;AACrF,QAAI,OAAO,MAAM;AACf,UAAI,MAAM,KAAK,IAAI,MAAM,IAAI,QAAQ,UAAU,KAAK,KAAK,SAAS,MAAM;AACxE,eAAS,aAAa,KAAK,MAAM,OAAO,GAAG,GAAG,EAAE,gBAAgB;AAAA,IAClE;AACA,WAAO,OAAO,QAAQ,OAAO,MAAM,QAAQ,WAAW,OAAO,MAAM,MAAM,QAAQ,OAAO,OAAO;AAAA,EACjG;AAAA;AAAA;AAAA,EAGA,iBAAiB,OAAO,MAAM,IAAI;AAChC,aAAS,MAAM,QAAM;AACnB,UAAI,QAAQ,KAAK,IAAI,MAAM,MAAM,MAAiC,KAAK,KAAK,SAAS,MAAM;AAC3F,UAAI,SAAS,aAAa,KAAK,MAAM,OAAO,OAAO,GAAG,GACpD,QAAQ;AACV,aAAO,CAAC,OAAO,gBAAgB,EAAE,KAAM,SAAQ,OAAO;AACtD,UAAI,MAAO,QAAO;AAClB,UAAI,SAAS,KAAM,QAAO;AAC1B,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,UAAU,OAAO,SAAS,OAAO;AAC/B,QAAI,QAAQ,KAAK,iBAAiB,OAAO,GAAG,OAAO;AACnD,QAAI,CAAC,MAAO,SAAQ,KAAK,iBAAiB,OAAO,KAAK,IAAI,GAAG,QAAQ,KAAK,KAAK,SAAS,MAAM,GAAG,MAAM,IAAI,MAAM;AACjH,WAAO,UAAU,MAAM,QAAQ,WAAW,MAAM,MAAM,SAAS,QAAQ;AAAA,EACzE;AAAA,EACA,eAAe,SAAS;AACtB,WAAO,KAAK,KAAK,QAAQ,KAAK,KAAK,OAAO;AAAA,EAC5C;AAAA,EACA,SAAS,OAAO,OAAO;AACrB,QAAI,SAAS,aAAa,KAAK,MAAM,OAAO,GAAG,MAAM,IAAI,MAAM,GAC7D,SAAS,CAAC;AACZ,WAAO,CAAC,OAAO,KAAK,EAAE,MAAM;AAC1B,UAAI,OAAO,UAAU,MAAO,QAAO;AACnC,aAAO,KAAK,OAAO,KAAK;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU,OAAO,MAAM,IAAIC,MAAK;AAC9B,QAAI,SAAS,aAAa,KAAK,MAAM,OAAO,KAAK,IAAI,GAAG,OAAO,KAAK,KAAK,SAAS,MAAM,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,SAAS,QAAQ,MAAM,IAAI,MAAM,CAAC;AACrJ,WAAO,CAAC,OAAO,KAAK,EAAE,KAAM,CAAAA,KAAI,OAAO,MAAM,MAAM,OAAO,MAAM,EAAE;AAAA,EACpE;AACF;AACA,SAAS,aAAa,MAAM,OAAO,MAAM,IAAI;AAC3C,SAAO,IAAI,aAAa,MAAM,KAAK,KAAK,QAAQ;AAAA,IAC9C,YAAY,CAAC,KAAK;AAAA,IAClB,MAAM,KAAK,YAAY,eAAe,MAAM,gBAAgB,MAAM,UAAU,KAAK,IAAI,CAAC,IAAI;AAAA,EAC5F,GAAG,MAAM,EAAE;AACb;AACA,SAAS,WAAW,KAAK,OAAO;AAC9B,SAAO,IAAI,MAAM,iBAAiB,KAAK,OAAO,KAAK,GAAG,KAAK;AAC7D;AACA,SAAS,UAAU,KAAK,OAAO;AAC7B,SAAO,IAAI,MAAM,OAAO,iBAAiB,KAAK,KAAK,CAAC;AACtD;AACA,SAAS,eAAe,aAAa;AACnC,SAAO,CAAC,OAAO,KAAK,UAAU,CAAC,MAAM,CAAC,EAAE,WAAW,YAAY,WAAW,MAAM,OAAO,MAAM,KAAK,CAAC,KAAK,aAAa,QAAQ,YAAY,UAAU,MAAM,OAAO,MAAM,KAAK,CAAC,KAAK,aAAa,UAAU,YAAY,UAAU,MAAM,OAAO,MAAM,QAAQ,MAAM,CAAC,EAAE,MAAM,CAAC,KAAK,aAAa,QAAQ,YAAY,WAAW,MAAM,OAAO,MAAM,QAAQ,MAAM,CAAC,EAAE,MAAM,CAAC,KAAK,aAAa;AACzX;AACA,IAAM,cAAN,cAA0B,UAAU;AAAA,EAClC,UAAU,OAAO,SAAS,OAAO;AAC/B,QAAI,SAAS,aAAa,KAAK,MAAM,OAAO,OAAO,MAAM,IAAI,MAAM,EAAE,KAAK;AAC1E,QAAI,OAAO,KAAM,UAAS,aAAa,KAAK,MAAM,OAAO,GAAG,OAAO,EAAE,KAAK;AAC1E,WAAO,OAAO,OAAO,OAAO,OAAO;AAAA,EACrC;AAAA,EACA,iBAAiB,OAAO,MAAM,IAAI;AAChC,aAAS,OAAO,KAAI,QAAQ;AAC1B,UAAI,QAAQ,KAAK;AAAA,QAAI;AAAA,QAAM,KAAK,OAAO;AAAA;AAAA,MAA8B;AACrE,UAAI,SAAS,aAAa,KAAK,MAAM,OAAO,OAAO,EAAE,GACnD,QAAQ;AACV,aAAO,CAAC,OAAO,KAAK,EAAE,KAAM,SAAQ,OAAO;AAC3C,UAAI,UAAU,SAAS,QAAQ,MAAM,OAAO,QAAQ,IAAK,QAAO;AAChE,UAAI,SAAS,KAAM,QAAO;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,UAAU,OAAO,SAAS,OAAO;AAC/B,WAAO,KAAK,iBAAiB,OAAO,GAAG,OAAO,KAAK,KAAK,iBAAiB,OAAO,OAAO,MAAM,IAAI,MAAM;AAAA,EACzG;AAAA,EACA,eAAe,QAAQ;AACrB,WAAO,KAAK,KAAK,QAAQ,KAAK,KAAK,OAAO,EAAE,QAAQ,gBAAgB,CAAC,GAAG,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,OAAO,MAAM,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,OAAO,MAAM,SAAS,OAAO,MAAM,CAAC,IAAI,CAAC;AAAA,EACxL;AAAA,EACA,SAAS,OAAO,OAAO;AACrB,QAAI,SAAS,aAAa,KAAK,MAAM,OAAO,GAAG,MAAM,IAAI,MAAM,GAC7D,SAAS,CAAC;AACZ,WAAO,CAAC,OAAO,KAAK,EAAE,MAAM;AAC1B,UAAI,OAAO,UAAU,MAAO,QAAO;AACnC,aAAO,KAAK,OAAO,KAAK;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU,OAAO,MAAM,IAAIA,MAAK;AAC9B,QAAI,SAAS,aAAa,KAAK,MAAM,OAAO,KAAK;AAAA,MAAI;AAAA,MAAG,OAAO;AAAA;AAAA,IAAgC,GAAG,KAAK,IAAI,KAAK,KAAkC,MAAM,IAAI,MAAM,CAAC;AACnK,WAAO,CAAC,OAAO,KAAK,EAAE,KAAM,CAAAA,KAAI,OAAO,MAAM,MAAM,OAAO,MAAM,EAAE;AAAA,EACpE;AACF;AAQA,IAAM,iBAA8B,YAAY,OAAO;AACvD,IAAM,cAA2B,YAAY,OAAO;AACpD,IAAM,cAA2B,WAAW,OAAO;AAAA,EACjD,OAAO,OAAO;AACZ,WAAO,IAAI,YAAY,aAAa,KAAK,EAAE,OAAO,GAAG,IAAI;AAAA,EAC3D;AAAA,EACA,OAAO,OAAO,IAAI;AAChB,aAAS,UAAU,GAAG,SAAS;AAC7B,UAAI,OAAO,GAAG,cAAc,EAAG,SAAQ,IAAI,YAAY,OAAO,MAAM,OAAO,GAAG,MAAM,KAAK;AAAA,eAAW,OAAO,GAAG,WAAW,EAAG,SAAQ,IAAI,YAAY,MAAM,OAAO,OAAO,QAAQ,oBAAoB,IAAI;AAAA,IAC1M;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS,OAAK,UAAU,KAAK,GAAG,SAAO,IAAI,KAAK;AAClD,CAAC;AAeD,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,OAAO,OAAO;AACxB,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACf;AACF;AACA,IAAM,YAAyB,WAAW,KAAK;AAAA,EAC3C,OAAO;AACT,CAAC;AAFH,IAGE,oBAAiC,WAAW,KAAK;AAAA,EAC/C,OAAO;AACT,CAAC;AACH,IAAM,oBAAiC,WAAW,UAAU,MAAM;AAAA,EAChE,YAAY,MAAM;AAChB,SAAK,OAAO;AACZ,SAAK,cAAc,KAAK,UAAU,KAAK,MAAM,MAAM,WAAW,CAAC;AAAA,EACjE;AAAA,EACA,OAAO,QAAQ;AACb,QAAI,QAAQ,OAAO,MAAM,MAAM,WAAW;AAC1C,QAAI,SAAS,OAAO,WAAW,MAAM,WAAW,KAAK,OAAO,cAAc,OAAO,gBAAgB,OAAO,gBAAiB,MAAK,cAAc,KAAK,UAAU,KAAK;AAAA,EAClK;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,EACF,GAAG;AACD,QAAI,CAAC,SAAS,CAAC,MAAM,KAAK,MAAO,QAAO,WAAW;AACnD,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,UAAU,IAAI,gBAAgB;AAClC,aAAS,IAAI,GAAG,SAAS,KAAK,eAAe,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC1E,UAAI;AAAA,QACF;AAAA,QACA;AAAA,MACF,IAAI,OAAO,CAAC;AACZ,aAAO,IAAI,IAAI,KAAK,KAAK,OAAO,IAAI,CAAC,EAAE,OAAO,IAAI,IAAkC,MAAK,OAAO,EAAE,CAAC,EAAE;AACrG,YAAM,UAAU,KAAK,OAAO,MAAM,IAAI,CAACC,OAAMC,QAAO;AAClD,YAAI,WAAW,KAAK,MAAM,UAAU,OAAO,KAAK,OAAK,EAAE,QAAQD,SAAQ,EAAE,MAAMC,GAAE;AACjF,gBAAQ,IAAID,OAAMC,KAAI,WAAW,oBAAoB,SAAS;AAAA,MAChE,CAAC;AAAA,IACH;AACA,WAAO,QAAQ,OAAO;AAAA,EACxB;AACF,GAAG;AAAA,EACD,aAAa,OAAK,EAAE;AACtB,CAAC;AACD,SAAS,cAAc,GAAG;AACxB,SAAO,UAAQ;AACb,QAAI,QAAQ,KAAK,MAAM,MAAM,aAAa,KAAK;AAC/C,WAAO,SAAS,MAAM,MAAM,KAAK,QAAQ,EAAE,MAAM,KAAK,IAAI,gBAAgB,IAAI;AAAA,EAChF;AACF;AAOA,IAAM,WAAwB,cAAc,CAAC,MAAM;AAAA,EACjD;AACF,MAAM;AACJ,MAAI;AAAA,IACF;AAAA,EACF,IAAI,KAAK,MAAM,UAAU;AACzB,MAAI,OAAO,MAAM,UAAU,KAAK,OAAO,IAAI,EAAE;AAC7C,MAAI,CAAC,KAAM,QAAO;AAClB,MAAI,YAAY,gBAAgB,OAAO,KAAK,MAAM,KAAK,EAAE;AACzD,MAAI,SAAS,KAAK,MAAM,MAAM,iBAAiB;AAC/C,OAAK,SAAS;AAAA,IACZ;AAAA,IACA,SAAS,CAAC,cAAc,MAAM,IAAI,GAAG,OAAO,cAAc,UAAU,MAAM,IAAI,CAAC;AAAA,IAC/E,WAAW;AAAA,EACb,CAAC;AACD,oBAAkB,IAAI;AACtB,SAAO;AACT,CAAC;AAMD,IAAM,eAA4B,cAAc,CAAC,MAAM;AAAA,EACrD;AACF,MAAM;AACJ,MAAI;AAAA,IACA;AAAA,EACF,IAAI,MACJ;AAAA,IACE;AAAA,EACF,IAAI,MAAM,UAAU;AACtB,MAAI,OAAO,MAAM,UAAU,OAAO,MAAM,IAAI;AAC5C,MAAI,CAAC,KAAM,QAAO;AAClB,MAAI,YAAY,gBAAgB,OAAO,KAAK,MAAM,KAAK,EAAE;AACzD,MAAI,SAAS,KAAK,MAAM,MAAM,iBAAiB;AAC/C,OAAK,SAAS;AAAA,IACZ;AAAA,IACA,SAAS,CAAC,cAAc,MAAM,IAAI,GAAG,OAAO,cAAc,UAAU,MAAM,IAAI,CAAC;AAAA,IAC/E,WAAW;AAAA,EACb,CAAC;AACD,oBAAkB,IAAI;AACtB,SAAO;AACT,CAAC;AAID,IAAM,gBAA6B,cAAc,CAAC,MAAM;AAAA,EACtD;AACF,MAAM;AACJ,MAAI,SAAS,MAAM,SAAS,KAAK,OAAO,GAAI;AAC5C,MAAI,CAAC,UAAU,CAAC,OAAO,OAAQ,QAAO;AACtC,OAAK,SAAS;AAAA,IACZ,WAAW,gBAAgB,OAAO,OAAO,IAAI,OAAK,gBAAgB,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;AAAA,IACtF,WAAW;AAAA,EACb,CAAC;AACD,SAAO;AACT,CAAC;AAID,IAAM,yBAAyB,CAAC;AAAA,EAC9B;AAAA,EACA;AACF,MAAM;AACJ,MAAI,MAAM,MAAM;AAChB,MAAI,IAAI,OAAO,SAAS,KAAK,IAAI,KAAK,MAAO,QAAO;AACpD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,IAAI;AACR,MAAI,SAAS,CAAC,GACZ,OAAO;AACT,WAAS,MAAM,IAAI,aAAa,MAAM,KAAK,MAAM,SAAS,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,EAAE,QAAO;AACvF,QAAI,OAAO,SAAS,IAAM,QAAO;AACjC,QAAI,IAAI,MAAM,QAAQ,KAAM,QAAO,OAAO;AAC1C,WAAO,KAAK,gBAAgB,MAAM,IAAI,MAAM,MAAM,IAAI,MAAM,EAAE,CAAC;AAAA,EACjE;AACA,WAAS,MAAM,OAAO;AAAA,IACpB,WAAW,gBAAgB,OAAO,QAAQ,IAAI;AAAA,IAC9C,WAAW;AAAA,EACb,CAAC,CAAC;AACF,SAAO;AACT;AAIA,IAAM,cAA2B,cAAc,CAAC,MAAM;AAAA,EACpD;AACF,MAAM;AACJ,MAAI;AAAA,IACA;AAAA,EACF,IAAI,MACJ;AAAA,IACE;AAAA,IACA;AAAA,EACF,IAAI,MAAM,UAAU;AACtB,MAAI,MAAM,SAAU,QAAO;AAC3B,MAAI,QAAQ,MAAM,UAAU,OAAO,MAAM,IAAI;AAC7C,MAAI,CAAC,MAAO,QAAO;AACnB,MAAI,OAAO;AACX,MAAI,UAAU,CAAC,GACb,WACA;AACF,MAAI,UAAU,CAAC;AACf,MAAI,KAAK,QAAQ,QAAQ,KAAK,MAAM,IAAI;AACtC,kBAAc,MAAM,OAAO,MAAM,eAAe,IAAI,CAAC;AACrD,YAAQ,KAAK;AAAA,MACX,MAAM,KAAK;AAAA,MACX,IAAI,KAAK;AAAA,MACT,QAAQ;AAAA,IACV,CAAC;AACD,WAAO,MAAM,UAAU,OAAO,KAAK,MAAM,KAAK,EAAE;AAChD,YAAQ,KAAK,WAAW,SAAS,GAAG,MAAM,OAAO,4BAA4B,MAAM,IAAI,OAAO,IAAI,EAAE,MAAM,IAAI,GAAG,CAAC;AAAA,EACpH;AACA,MAAI,MAAM;AACR,QAAI,MAAM,QAAQ,UAAU,KAAK,QAAQ,CAAC,EAAE,QAAQ,MAAM,KAAK,IAAI,MAAM,KAAK,MAAM,OAAO,YAAY;AACvG,gBAAY,gBAAgB,OAAO,KAAK,OAAO,KAAK,KAAK,KAAK,GAAG;AACjE,YAAQ,KAAK,cAAc,MAAM,IAAI,CAAC;AACtC,YAAQ,KAAK,MAAM,MAAM,iBAAiB,EAAE,cAAc,UAAU,MAAM,IAAI,CAAC;AAAA,EACjF;AACA,OAAK,SAAS;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW;AAAA,EACb,CAAC;AACD,SAAO;AACT,CAAC;AAKD,IAAM,aAA0B,cAAc,CAAC,MAAM;AAAA,EACnD;AACF,MAAM;AACJ,MAAI,KAAK,MAAM,SAAU,QAAO;AAChC,MAAI,UAAU,MAAM,SAAS,KAAK,OAAO,GAAG,EAAE,IAAI,WAAS;AACzD,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,QAAQ,MAAM,eAAe,KAAK;AAAA,IACpC;AAAA,EACF,CAAC;AACD,MAAI,CAAC,QAAQ,OAAQ,QAAO;AAC5B,MAAI,eAAe,KAAK,MAAM,OAAO,sBAAsB,QAAQ,MAAM,IAAI;AAC7E,OAAK,SAAS;AAAA,IACZ;AAAA,IACA,SAAS,WAAW,SAAS,GAAG,YAAY;AAAA,IAC5C,WAAW;AAAA,EACb,CAAC;AACD,SAAO;AACT,CAAC;AACD,SAAS,kBAAkB,MAAM;AAC/B,SAAO,KAAK,MAAM,MAAM,iBAAiB,EAAE,YAAY,IAAI;AAC7D;AACA,SAAS,aAAa,OAAO,UAAU;AACrC,MAAI,IAAI,IAAI,IAAI,IAAI;AACpB,MAAI,MAAM,MAAM,UAAU;AAC1B,MAAI,UAAU,IAAI,SAAS,IAAI,KAAK,IAAI,OAAO,MAAM,KAAK,MAAM,SAAS,IAAI,MAAM,IAAI,EAAE;AACzF,MAAI,YAAY,CAAC,QAAS,QAAO;AACjC,MAAI,SAAS,MAAM,MAAM,iBAAiB;AAC1C,SAAO,IAAI,YAAY;AAAA,IACrB,UAAU,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,aAAa,QAAQ,OAAO,SAAS,KAAK,OAAO,WAAW,UAAU,QAAQ,QAAQ,OAAO,KAAK;AAAA,IAC9K,gBAAgB,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,mBAAmB,QAAQ,OAAO,SAAS,KAAK,OAAO;AAAA,IACzI,UAAU,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,aAAa,QAAQ,OAAO,SAAS,KAAK,OAAO;AAAA,IAC7H,SAAS,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,YAAY,QAAQ,OAAO,SAAS,KAAK,OAAO;AAAA,IAC3H,YAAY,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,eAAe,QAAQ,OAAO,SAAS,KAAK,OAAO;AAAA,EACnI,CAAC;AACH;AACA,SAAS,eAAe,MAAM;AAC5B,MAAI,QAAQ,SAAS,MAAM,iBAAiB;AAC5C,SAAO,SAAS,MAAM,IAAI,cAAc,cAAc;AACxD;AACA,SAAS,kBAAkB,MAAM;AAC/B,MAAI,QAAQ,eAAe,IAAI;AAC/B,MAAI,SAAS,SAAS,KAAK,KAAK,cAAe,OAAM,OAAO;AAC9D;AAIA,IAAM,kBAAkB,UAAQ;AAC9B,MAAI,QAAQ,KAAK,MAAM,MAAM,aAAa,KAAK;AAC/C,MAAI,SAAS,MAAM,OAAO;AACxB,QAAI,cAAc,eAAe,IAAI;AACrC,QAAI,eAAe,eAAe,KAAK,KAAK,eAAe;AACzD,UAAI,QAAQ,aAAa,KAAK,OAAO,MAAM,MAAM,IAAI;AACrD,UAAI,MAAM,MAAO,MAAK,SAAS;AAAA,QAC7B,SAAS,eAAe,GAAG,KAAK;AAAA,MAClC,CAAC;AACD,kBAAY,MAAM;AAClB,kBAAY,OAAO;AAAA,IACrB;AAAA,EACF,OAAO;AACL,SAAK,SAAS;AAAA,MACZ,SAAS,CAAC,YAAY,GAAG,IAAI,GAAG,QAAQ,eAAe,GAAG,aAAa,KAAK,OAAO,MAAM,MAAM,IAAI,CAAC,IAAI,YAAY,aAAa,GAAG,gBAAgB,CAAC;AAAA,IACvJ,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAIA,IAAM,mBAAmB,UAAQ;AAC/B,MAAI,QAAQ,KAAK,MAAM,MAAM,aAAa,KAAK;AAC/C,MAAI,CAAC,SAAS,CAAC,MAAM,MAAO,QAAO;AACnC,MAAI,QAAQ,SAAS,MAAM,iBAAiB;AAC5C,MAAI,SAAS,MAAM,IAAI,SAAS,KAAK,KAAK,aAAa,EAAG,MAAK,MAAM;AACrE,OAAK,SAAS;AAAA,IACZ,SAAS,YAAY,GAAG,KAAK;AAAA,EAC/B,CAAC;AACD,SAAO;AACT;AAUA,IAAM,eAAe,CAAC;AAAA,EACpB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,OAAO;AAAA,EACP,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,OAAO;AAAA,EACP,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AAAA,EACL,gBAAgB;AAClB,CAAC;AACD,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,MAAM;AAChB,SAAK,OAAO;AACZ,QAAI,QAAQ,KAAK,QAAQ,KAAK,MAAM,MAAM,WAAW,EAAE,MAAM;AAC7D,SAAK,SAAS,KAAK,OAAO,KAAK,IAAI;AACnC,SAAK,cAAc,MAAI,SAAS;AAAA,MAC9B,OAAO,MAAM;AAAA,MACb,aAAa,OAAO,MAAM,MAAM;AAAA,MAChC,cAAc,OAAO,MAAM,MAAM;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,cAAc;AAAA,MACd,UAAU,KAAK;AAAA,MACf,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,eAAe,MAAI,SAAS;AAAA,MAC/B,OAAO,MAAM;AAAA,MACb,aAAa,OAAO,MAAM,SAAS;AAAA,MACnC,cAAc,OAAO,MAAM,SAAS;AAAA,MACpC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU,KAAK;AAAA,MACf,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,YAAY,MAAI,SAAS;AAAA,MAC5B,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,MACf,UAAU,KAAK;AAAA,IACjB,CAAC;AACD,SAAK,UAAU,MAAI,SAAS;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,MACf,UAAU,KAAK;AAAA,IACjB,CAAC;AACD,SAAK,YAAY,MAAI,SAAS;AAAA,MAC5B,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,MACf,UAAU,KAAK;AAAA,IACjB,CAAC;AACD,aAAS,OAAO,MAAM,SAAS,SAAS;AACtC,aAAO,MAAI,UAAU;AAAA,QACnB,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR,GAAG,OAAO;AAAA,IACZ;AACA,SAAK,MAAM,MAAI,OAAO;AAAA,MACpB,WAAW,OAAK,KAAK,QAAQ,CAAC;AAAA,MAC9B,OAAO;AAAA,IACT,GAAG,CAAC,KAAK,aAAa,OAAO,QAAQ,MAAM,SAAS,IAAI,GAAG,CAAC,OAAO,MAAM,MAAM,CAAC,CAAC,GAAG,OAAO,QAAQ,MAAM,aAAa,IAAI,GAAG,CAAC,OAAO,MAAM,UAAU,CAAC,CAAC,GAAG,OAAO,UAAU,MAAM,cAAc,IAAI,GAAG,CAAC,OAAO,MAAM,KAAK,CAAC,CAAC,GAAG,MAAI,SAAS,MAAM,CAAC,KAAK,WAAW,OAAO,MAAM,YAAY,CAAC,CAAC,GAAG,MAAI,SAAS,MAAM,CAAC,KAAK,SAAS,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,MAAI,SAAS,MAAM,CAAC,KAAK,WAAW,OAAO,MAAM,SAAS,CAAC,CAAC,GAAG,GAAI,KAAK,MAAM,WAAW,CAAC,IAAI,CAAC,MAAI,IAAI,GAAG,KAAK,cAAc,OAAO,WAAW,MAAM,YAAY,IAAI,GAAG,CAAC,OAAO,MAAM,SAAS,CAAC,CAAC,GAAG,OAAO,cAAc,MAAM,WAAW,IAAI,GAAG,CAAC,OAAO,MAAM,aAAa,CAAC,CAAC,CAAC,GAAI,MAAI,UAAU;AAAA,MAC3nB,MAAM;AAAA,MACN,SAAS,MAAM,iBAAiB,IAAI;AAAA,MACpC,cAAc,OAAO,MAAM,OAAO;AAAA,MAClC,MAAM;AAAA,IACR,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAAA,EACZ;AAAA,EACA,SAAS;AACP,QAAI,QAAQ,IAAI,YAAY;AAAA,MAC1B,QAAQ,KAAK,YAAY;AAAA,MACzB,eAAe,KAAK,UAAU;AAAA,MAC9B,QAAQ,KAAK,QAAQ;AAAA,MACrB,WAAW,KAAK,UAAU;AAAA,MAC1B,SAAS,KAAK,aAAa;AAAA,IAC7B,CAAC;AACD,QAAI,CAAC,MAAM,GAAG,KAAK,KAAK,GAAG;AACzB,WAAK,QAAQ;AACb,WAAK,KAAK,SAAS;AAAA,QACjB,SAAS,eAAe,GAAG,KAAK;AAAA,MAClC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,QAAQ,GAAG;AACT,QAAI,iBAAiB,KAAK,MAAM,GAAG,cAAc,GAAG;AAClD,QAAE,eAAe;AAAA,IACnB,WAAW,EAAE,WAAW,MAAM,EAAE,UAAU,KAAK,aAAa;AAC1D,QAAE,eAAe;AACjB,OAAC,EAAE,WAAW,eAAe,UAAU,KAAK,IAAI;AAAA,IAClD,WAAW,EAAE,WAAW,MAAM,EAAE,UAAU,KAAK,cAAc;AAC3D,QAAE,eAAe;AACjB,kBAAY,KAAK,IAAI;AAAA,IACvB;AAAA,EACF;AAAA,EACA,OAAO,QAAQ;AACb,aAAS,MAAM,OAAO,aAAc,UAAS,UAAU,GAAG,SAAS;AACjE,UAAI,OAAO,GAAG,cAAc,KAAK,CAAC,OAAO,MAAM,GAAG,KAAK,KAAK,EAAG,MAAK,SAAS,OAAO,KAAK;AAAA,IAC3F;AAAA,EACF;AAAA,EACA,SAAS,OAAO;AACd,SAAK,QAAQ;AACb,SAAK,YAAY,QAAQ,MAAM;AAC/B,SAAK,aAAa,QAAQ,MAAM;AAChC,SAAK,UAAU,UAAU,MAAM;AAC/B,SAAK,QAAQ,UAAU,MAAM;AAC7B,SAAK,UAAU,UAAU,MAAM;AAAA,EACjC;AAAA,EACA,QAAQ;AACN,SAAK,YAAY,OAAO;AAAA,EAC1B;AAAA,EACA,IAAI,MAAM;AACR,WAAO;AAAA,EACT;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK,KAAK,MAAM,MAAM,iBAAiB,EAAE;AAAA,EAClD;AACF;AACA,SAAS,OAAO,MAAMC,SAAQ;AAC5B,SAAO,KAAK,MAAM,OAAOA,OAAM;AACjC;AACA,IAAM,iBAAiB;AACvB,IAAM,QAAQ;AACd,SAAS,cAAc,MAAM;AAAA,EAC3B;AAAA,EACA;AACF,GAAG;AACD,MAAI,OAAO,KAAK,MAAM,IAAI,OAAO,IAAI,GACnC,UAAU,KAAK,MAAM,IAAI,OAAO,EAAE,EAAE;AACtC,MAAI,QAAQ,KAAK,IAAI,KAAK,MAAM,OAAO,cAAc,GACnD,MAAM,KAAK,IAAI,SAAS,KAAK,cAAc;AAC7C,MAAI,OAAO,KAAK,MAAM,SAAS,OAAO,GAAG;AACzC,MAAI,SAAS,KAAK,MAAM;AACtB,aAAS,IAAI,GAAG,IAAI,gBAAgB,IAAK,KAAI,CAAC,MAAM,KAAK,KAAK,IAAI,CAAC,CAAC,KAAK,MAAM,KAAK,KAAK,CAAC,CAAC,GAAG;AAC5F,aAAO,KAAK,MAAM,CAAC;AACnB;AAAA,IACF;AAAA,EACF;AACA,MAAI,OAAO,SAAS;AAClB,aAAS,IAAI,KAAK,SAAS,GAAG,IAAI,KAAK,SAAS,gBAAgB,IAAK,KAAI,CAAC,MAAM,KAAK,KAAK,IAAI,CAAC,CAAC,KAAK,MAAM,KAAK,KAAK,CAAC,CAAC,GAAG;AACxH,aAAO,KAAK,MAAM,GAAG,CAAC;AACtB;AAAA,IACF;AAAA,EACF;AACA,SAAO,WAAW,SAAS,GAAG,GAAG,KAAK,MAAM,OAAO,eAAe,CAAC,KAAK,IAAI,IAAI,KAAK,MAAM,OAAO,SAAS,CAAC,IAAI,KAAK,MAAM,GAAG;AAChI;AACA,IAAM,YAAyB,WAAW,UAAU;AAAA,EAClD,uBAAuB;AAAA,IACrB,SAAS;AAAA,IACT,UAAU;AAAA,IACV,kBAAkB;AAAA,MAChB,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,IACA,8BAA8B;AAAA,MAC5B,QAAQ;AAAA,IACV;AAAA,IACA,0BAA0B;AAAA,MACxB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,UAAU;AAAA,MACV,YAAY;AAAA,IACd;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,IACxB,iBAAiB;AAAA,EACnB;AAAA,EACA,yBAAyB;AAAA,IACvB,iBAAiB;AAAA,EACnB;AAAA,EACA,mCAAmC;AAAA,IACjC,iBAAiB;AAAA,EACnB;AAAA,EACA,kCAAkC;AAAA,IAChC,iBAAiB;AAAA,EACnB;AACF,CAAC;AACD,IAAM,mBAAmB,CAAC,aAA0B,KAAK,IAAI,iBAAiB,GAAG,SAAS;;;AC9wC1F,IAAM,qBAAN,MAAyB;AAAA,EACvB,YAAY,MAAM,IAAI,YAAY;AAChC,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,aAAa;AAAA,EACpB;AACF;AACA,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,YAAY,aAAa,OAAO,UAAU;AACxC,SAAK,cAAc;AACnB,SAAK,QAAQ;AACb,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO,KAAK,aAAa,OAAO,OAAO;AAErC,QAAI,oBAAoB;AACxB,QAAI,mBAAmB,MAAM,MAAM,UAAU,EAAE;AAC/C,QAAI,iBAAkB,qBAAoB,iBAAiB,mBAAmB,KAAK;AACnF,QAAI,SAAS,YAAY,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE;AAC9E,QAAI,OAAO,IAAI,gBAAgB,GAC7B,SAAS,CAAC,GACV,MAAM;AACR,aAAS,IAAI,OAAK;AAChB,UAAI,OAAO,KAAK,OAAO,SAAS,OAAO,OAAO,CAAC;AAC/C,UAAI,CAAC,QAAQ,CAAC,OAAO,OAAQ;AAC7B,UAAI,MAAM;AACV,UAAI,OAAO,QAAQ;AACjB,eAAO;AACP,aAAK,OAAO,OAAO,CAAC,GAAG,MAAM,KAAK,IAAI,GAAG,EAAE,EAAE,GAAG,QAAQ,KAAK,OAAO,OAAO,KAAK,OAAO,GAAG;AAAA,MAC5F,OAAO;AACL,eAAO,KAAK;AACZ,aAAK,KAAK;AACV,eAAO,KAAK,IAAI;AAChB;AAAA,MACF;AACA,aAAO,IAAI,OAAO,QAAQ;AACxB,YAAIC,QAAO,OAAO,CAAC;AACnB,YAAIA,MAAK,QAAQ,SAASA,MAAK,KAAKA,MAAK,QAAQA,MAAK,MAAM,OAAO;AACjE,iBAAO,KAAKA,KAAI;AAChB;AACA,eAAK,KAAK,IAAIA,MAAK,IAAI,EAAE;AAAA,QAC3B,OAAO;AACL,eAAK,KAAK,IAAIA,MAAK,MAAM,EAAE;AAC3B;AAAA,QACF;AAAA,MACF;AACA,UAAI,MAAM,YAAY,MAAM;AAC5B,UAAI,OAAO,KAAK,OAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,KAAK,MAAM,IAAI,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,GAAG;AACnG,aAAK,IAAI,MAAM,MAAM,WAAW,OAAO;AAAA,UACrC,QAAQ,IAAI,iBAAiB,GAAG;AAAA,UAChC,aAAa,OAAO,MAAM;AAAA,QAC5B,CAAC,CAAC;AAAA,MACJ,OAAO;AACL,YAAI,YAAY,OAAO,OAAO,CAAC,GAAG,MAAM,EAAE,YAAY,IAAI,MAAM,EAAE,YAAY,GAAG,EAAE;AACnF,aAAK,IAAI,MAAM,IAAI,WAAW,KAAK;AAAA,UACjC,OAAO,+BAA+B,MAAM;AAAA,UAC5C,aAAa,OAAO,MAAM;AAAA,UAC1B,cAAc,OAAO,KAAK,OAAK,EAAE,KAAK,EAAE;AAAA,QAC1C,CAAC,CAAC;AAAA,MACJ;AACA,YAAM;AACN,eAASC,KAAI,GAAGA,KAAI,OAAO,QAAQA,KAAK,KAAI,OAAOA,EAAC,EAAE,MAAM,IAAK,QAAO,OAAOA,MAAK,CAAC;AAAA,IACvF;AACA,QAAI,MAAM,KAAK,OAAO;AACtB,WAAO,IAAI,WAAU,KAAK,OAAO,eAAe,GAAG,CAAC;AAAA,EACtD;AACF;AACA,SAAS,eAAe,aAAa,aAAa,MAAM,QAAQ,GAAG;AACjE,MAAI,QAAQ;AACZ,cAAY,QAAQ,OAAO,KAAK,CAAC,MAAM,IAAI;AAAA,IACzC;AAAA,EACF,MAAM;AACJ,QAAI,cAAc,KAAK,YAAY,QAAQ,UAAU,IAAI,EAAG;AAC5D,QAAI,CAAC,MAAO,SAAQ,IAAI,mBAAmB,MAAM,IAAI,cAAc,KAAK,YAAY,CAAC,CAAC;AAAA,aAAW,KAAK,YAAY,QAAQ,MAAM,UAAU,IAAI,EAAG,QAAO;AAAA,QAAW,SAAQ,IAAI,mBAAmB,MAAM,MAAM,IAAI,MAAM,UAAU;AAAA,EACpO,CAAC;AACD,SAAO;AACT;AACA,SAAS,YAAY,IAAI,SAAS;AAChC,MAAI,OAAO,QAAQ,KACjB,KAAK,QAAQ,OAAO;AACtB,MAAI,SAAS,GAAG,MAAM,MAAM,UAAU,EAAE,OAAO,IAAI,MAAM,EAAE;AAC3D,MAAI,UAAU,KAAM,QAAO;AAC3B,MAAI,OAAO,GAAG,WAAW,IAAI,OAAO,QAAQ,GAAG;AAC/C,SAAO,CAAC,EAAE,GAAG,QAAQ,KAAK,OAAK,EAAE,GAAG,oBAAoB,CAAC,KAAK,GAAG,QAAQ,aAAa,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC;AACxH;AACA,SAAS,gBAAgB,OAAO,SAAS;AACvC,SAAO,MAAM,MAAM,WAAW,KAAK,IAAI,UAAU,QAAQ,OAAO,YAAY,aAAa,GAAG,cAAc,CAAC;AAC7G;AAMA,SAAS,eAAe,OAAO,aAAa;AAC1C,SAAO;AAAA,IACL,SAAS,gBAAgB,OAAO,CAAC,qBAAqB,GAAG,WAAW,CAAC,CAAC;AAAA,EACxE;AACF;AAKA,IAAM,uBAAoC,YAAY,OAAO;AAC7D,IAAMC,eAA2B,YAAY,OAAO;AACpD,IAAM,qBAAkC,YAAY,OAAO;AAC3D,IAAM,YAAyB,WAAW,OAAO;AAAA,EAC/C,SAAS;AACP,WAAO,IAAI,UAAU,WAAW,MAAM,MAAM,IAAI;AAAA,EAClD;AAAA,EACA,OAAO,OAAO,IAAI;AAChB,QAAI,GAAG,cAAc,MAAM,YAAY,MAAM;AAC3C,UAAI,SAAS,MAAM,YAAY,IAAI,GAAG,OAAO,GAC3C,WAAW,MACX,QAAQ,MAAM;AAChB,UAAI,MAAM,UAAU;AAClB,YAAI,SAAS,GAAG,QAAQ,OAAO,MAAM,SAAS,MAAM,CAAC;AACrD,mBAAW,eAAe,QAAQ,MAAM,SAAS,YAAY,MAAM,KAAK,eAAe,QAAQ,MAAM,MAAM;AAAA,MAC7G;AACA,UAAI,CAAC,OAAO,QAAQ,SAAS,GAAG,MAAM,MAAM,UAAU,EAAE,UAAW,SAAQ;AAC3E,cAAQ,IAAI,UAAU,QAAQ,OAAO,QAAQ;AAAA,IAC/C;AACA,aAAS,UAAU,GAAG,SAAS;AAC7B,UAAI,OAAO,GAAG,oBAAoB,GAAG;AACnC,YAAI,QAAQ,CAAC,GAAG,MAAM,MAAM,UAAU,EAAE,YAAY,MAAM,QAAQ,OAAO,MAAM,SAAS,UAAU,OAAO;AACzG,gBAAQ,UAAU,KAAK,OAAO,OAAO,OAAO,GAAG,KAAK;AAAA,MACtD,WAAW,OAAO,GAAGA,YAAW,GAAG;AACjC,gBAAQ,IAAI,UAAU,MAAM,aAAa,OAAO,QAAQ,UAAU,OAAO,MAAM,MAAM,QAAQ;AAAA,MAC/F,WAAW,OAAO,GAAG,kBAAkB,GAAG;AACxC,gBAAQ,IAAI,UAAU,MAAM,aAAa,MAAM,OAAO,OAAO,KAAK;AAAA,MACpE;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS,OAAK,CAAC,UAAU,KAAK,GAAG,SAAO,IAAI,KAAK,GAAG,WAAW,YAAY,KAAK,GAAG,OAAK,EAAE,WAAW,CAAC;AACxG,CAAC;AAQD,IAAM,aAA0B,WAAW,KAAK;AAAA,EAC9C,OAAO;AACT,CAAC;AACD,SAAS,YAAY,MAAM,KAAK,MAAM;AACpC,MAAI;AAAA,IACF;AAAA,EACF,IAAI,KAAK,MAAM,MAAM,SAAS;AAC9B,MAAI,OACF,QAAQ,IACR,MAAM;AACR,cAAY,QAAQ,OAAO,OAAO,IAAI,IAAI,IAAI,OAAO,OAAO,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI;AAAA,IACjF;AAAA,EACF,MAAM;AACJ,QAAI,OAAO,QAAQ,OAAO,OAAO,QAAQ,OAAO,MAAM,QAAQ,OAAO,OAAO,MAAM,MAAM,OAAO,KAAK;AAClG,cAAQ,KAAK;AACb,cAAQ;AACR,YAAM;AACN,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,MAAI,mBAAmB,KAAK,MAAM,MAAM,UAAU,EAAE;AACpD,MAAI,SAAS,iBAAkB,SAAQ,iBAAiB,OAAO,KAAK,KAAK;AACzE,MAAI,CAAC,MAAO,QAAO;AACnB,SAAO;AAAA,IACL,KAAK;AAAA,IACL;AAAA,IACA,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,EAAE,KAAK;AAAA,IACzC,SAAS;AACP,aAAO;AAAA,QACL,KAAK,mBAAmB,MAAM,KAAK;AAAA,MACrC;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,mBAAmB,MAAM,aAAa;AAC7C,SAAO,MAAI,MAAM;AAAA,IACf,OAAO;AAAA,EACT,GAAG,YAAY,IAAI,OAAK,iBAAiB,MAAM,GAAG,KAAK,CAAC,CAAC;AAC3D;AAIA,IAAM,gBAAgB,UAAQ;AAC5B,MAAI,QAAQ,KAAK,MAAM,MAAM,WAAW,KAAK;AAC7C,MAAI,CAAC,SAAS,CAAC,MAAM,MAAO,MAAK,SAAS;AAAA,IACxC,SAAS,gBAAgB,KAAK,OAAO,CAACC,aAAY,GAAG,IAAI,CAAC,CAAC;AAAA,EAC7D,CAAC;AACD,MAAI,QAAQ,SAAS,MAAM,UAAU,IAAI;AACzC,MAAI,MAAO,OAAM,IAAI,cAAc,mBAAmB,EAAE,MAAM;AAC9D,SAAO;AACT;AAIA,IAAM,iBAAiB,UAAQ;AAC7B,MAAI,QAAQ,KAAK,MAAM,MAAM,WAAW,KAAK;AAC7C,MAAI,CAAC,SAAS,CAAC,MAAM,MAAO,QAAO;AACnC,OAAK,SAAS;AAAA,IACZ,SAASA,aAAY,GAAG,KAAK;AAAA,EAC/B,CAAC;AACD,SAAO;AACT;AAIA,IAAM,iBAAiB,UAAQ;AAC7B,MAAI,QAAQ,KAAK,MAAM,MAAM,WAAW,KAAK;AAC7C,MAAI,CAAC,MAAO,QAAO;AACnB,MAAI,MAAM,KAAK,MAAM,UAAU,MAC7B,OAAO,MAAM,YAAY,KAAK,IAAI,KAAK,CAAC;AAC1C,MAAI,CAAC,KAAK,OAAO;AACf,WAAO,MAAM,YAAY,KAAK,CAAC;AAC/B,QAAI,CAAC,KAAK,SAAS,KAAK,QAAQ,IAAI,QAAQ,KAAK,MAAM,IAAI,GAAI,QAAO;AAAA,EACxE;AACA,OAAK,SAAS;AAAA,IACZ,WAAW;AAAA,MACT,QAAQ,KAAK;AAAA,MACb,MAAM,KAAK;AAAA,IACb;AAAA,IACA,gBAAgB;AAAA,EAClB,CAAC;AACD,SAAO;AACT;AAsCA,IAAM,aAAa,CAAC;AAAA,EAClB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,gBAAgB;AAClB,GAAG;AAAA,EACD,KAAK;AAAA,EACL,KAAK;AACP,CAAC;AACD,IAAM,aAA0B,WAAW,UAAU,MAAM;AAAA,EACzD,YAAY,MAAM;AAChB,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,MAAM;AACX,QAAI;AAAA,MACF;AAAA,IACF,IAAI,KAAK,MAAM,MAAM,UAAU;AAC/B,SAAK,WAAW,KAAK,IAAI,IAAI;AAC7B,SAAK,MAAM,KAAK,IAAI,KAAK,IAAI;AAC7B,SAAK,UAAU,WAAW,KAAK,KAAK,KAAK;AAAA,EAC3C;AAAA,EACA,MAAM;AACJ,iBAAa,KAAK,OAAO;AACzB,QAAI,MAAM,KAAK,IAAI;AACnB,QAAI,MAAM,KAAK,WAAW,IAAI;AAC5B,WAAK,UAAU,WAAW,KAAK,KAAK,KAAK,WAAW,GAAG;AAAA,IACzD,OAAO;AACL,WAAK,MAAM;AACX,UAAI;AAAA,QACA;AAAA,MACF,IAAI,KAAK,MACT;AAAA,QACE;AAAA,MACF,IAAI,MAAM,MAAM,UAAU;AAC5B,UAAI,QAAQ,OAAQ,cAAa,QAAQ,IAAI,OAAK,QAAQ,QAAQ,EAAE,KAAK,IAAI,CAAC,CAAC,GAAG,iBAAe;AAC/F,YAAI,KAAK,KAAK,MAAM,OAAO,MAAM,IAAK,MAAK,KAAK,SAAS,eAAe,KAAK,KAAK,OAAO,YAAY,OAAO,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;AAAA,MACrI,GAAG,WAAS;AACV,qBAAa,KAAK,KAAK,OAAO,KAAK;AAAA,MACrC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,QAAQ;AACb,QAAI,SAAS,OAAO,MAAM,MAAM,UAAU;AAC1C,QAAI,OAAO,cAAc,UAAU,OAAO,WAAW,MAAM,UAAU,KAAK,OAAO,gBAAgB,OAAO,aAAa,MAAM,GAAG;AAC5H,WAAK,WAAW,KAAK,IAAI,IAAI,OAAO;AACpC,UAAI,CAAC,KAAK,KAAK;AACb,aAAK,MAAM;AACX,aAAK,UAAU,WAAW,KAAK,KAAK,OAAO,KAAK;AAAA,MAClD;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AACN,QAAI,KAAK,KAAK;AACZ,WAAK,WAAW,KAAK,IAAI;AACzB,WAAK,IAAI;AAAA,IACX;AAAA,EACF;AAAA,EACA,UAAU;AACR,iBAAa,KAAK,OAAO;AAAA,EAC3B;AACF,CAAC;AACD,SAAS,aAAa,UAAU,MAAM,OAAO;AAC3C,MAAI,YAAY,CAAC,GACf,UAAU;AACZ,WAAS,KAAK,SAAU,GAAE,KAAK,WAAS;AACtC,cAAU,KAAK,KAAK;AACpB,iBAAa,OAAO;AACpB,QAAI,UAAU,UAAU,SAAS,OAAQ,MAAK,SAAS;AAAA,QAAO,WAAU,WAAW,MAAM,KAAK,SAAS,GAAG,GAAG;AAAA,EAC/G,GAAG,KAAK;AACV;AACA,IAAM,aAA0B,MAAM,OAAO;AAAA,EAC3C,QAAQ,OAAO;AACb,WAAO,OAAO,OAAO;AAAA,MACnB,SAAS,MAAM,IAAI,OAAK,EAAE,MAAM,EAAE,OAAO,OAAK,KAAK,IAAI;AAAA,IACzD,GAAG,cAAc,MAAM,IAAI,OAAK,EAAE,MAAM,GAAG;AAAA,MACzC,OAAO;AAAA,MACP,cAAc;AAAA,MACd,eAAe;AAAA,MACf,cAAc;AAAA,MACd,QAAQ,MAAM;AAAA,IAChB,GAAG;AAAA,MACD,cAAc,CAAC,GAAG,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,OAAK,EAAE,CAAC,KAAK,EAAE,CAAC;AAAA,IAC5D,CAAC,CAAC;AAAA,EACJ;AACF,CAAC;AAqBD,SAAS,WAAW,SAAS;AAC3B,MAAI,WAAW,CAAC;AAChB,MAAI,QAAS,SAAS,UAAS;AAAA,IAC7B;AAAA,EACF,KAAK,SAAS;AACZ,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAI,KAAK,KAAK,CAAC;AACf,UAAI,WAAW,KAAK,EAAE,KAAK,CAAC,SAAS,KAAK,OAAK,EAAE,YAAY,KAAK,GAAG,YAAY,CAAC,GAAG;AACnF,iBAAS,KAAK,EAAE;AAChB,iBAAS;AAAA,MACX;AAAA,IACF;AACA,aAAS,KAAK,EAAE;AAAA,EAClB;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,MAAM,YAAY,SAAS;AACnD,MAAI;AACJ,MAAI,OAAO,UAAU,WAAW,WAAW,OAAO,IAAI,CAAC;AACvD,SAAO,MAAI,MAAM;AAAA,IACf,OAAO,iCAAiC,WAAW;AAAA,EACrD,GAAG,MAAI,QAAQ;AAAA,IACb,OAAO;AAAA,EACT,GAAG,WAAW,gBAAgB,WAAW,cAAc,IAAI,IAAI,WAAW,OAAO,IAAI,KAAK,WAAW,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,CAAC,QAAQ,MAAM;AACvK,QAAI,QAAQ,OACV,QAAQ,OAAK;AACX,QAAE,eAAe;AACjB,UAAI,MAAO;AACX,cAAQ;AACR,UAAI,QAAQ,eAAe,KAAK,MAAM,MAAM,SAAS,EAAE,aAAa,UAAU;AAC9E,UAAI,MAAO,QAAO,MAAM,MAAM,MAAM,MAAM,MAAM,EAAE;AAAA,IACpD;AACF,QAAI;AAAA,MACA;AAAA,IACF,IAAI,QACJ,WAAW,KAAK,CAAC,IAAI,KAAK,QAAQ,KAAK,CAAC,CAAC,IAAI;AAC/C,QAAI,UAAU,WAAW,IAAI,OAAO,CAAC,KAAK,MAAM,GAAG,QAAQ,GAAG,MAAI,KAAK,KAAK,MAAM,UAAU,WAAW,CAAC,CAAC,GAAG,KAAK,MAAM,WAAW,CAAC,CAAC;AACpI,WAAO,MAAI,UAAU;AAAA,MACnB,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,aAAa;AAAA,MACb,cAAc,YAAY,IAAI,GAAG,WAAW,IAAI,KAAK,iBAAiB,KAAK,CAAC,CAAC,IAAI;AAAA,IACnF,GAAG,OAAO;AAAA,EACZ,CAAC,GAAG,WAAW,UAAU,MAAI,OAAO;AAAA,IAClC,OAAO;AAAA,EACT,GAAG,WAAW,MAAM,CAAC;AACvB;AACA,IAAM,mBAAN,cAA+B,WAAW;AAAA,EACxC,YAAY,KAAK;AACf,UAAM;AACN,SAAK,MAAM;AAAA,EACb;AAAA,EACA,GAAG,OAAO;AACR,WAAO,MAAM,OAAO,KAAK;AAAA,EAC3B;AAAA,EACA,QAAQ;AACN,WAAO,MAAI,QAAQ;AAAA,MACjB,OAAO,+BAA+B,KAAK;AAAA,IAC7C,CAAC;AAAA,EACH;AACF;AACA,IAAM,YAAN,MAAgB;AAAA,EACd,YAAY,MAAM,YAAY;AAC5B,SAAK,aAAa;AAClB,SAAK,KAAK,UAAU,KAAK,MAAM,KAAK,OAAO,IAAI,UAAU,EAAE,SAAS,EAAE;AACtE,SAAK,MAAM,iBAAiB,MAAM,YAAY,IAAI;AAClD,SAAK,IAAI,KAAK,KAAK;AACnB,SAAK,IAAI,aAAa,QAAQ,QAAQ;AAAA,EACxC;AACF;AACA,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,YAAY,MAAM;AAChB,SAAK,OAAO;AACZ,SAAK,QAAQ,CAAC;AACd,QAAI,YAAY,WAAS;AACvB,UAAI,MAAM,WAAW,IAAI;AAEvB,uBAAe,KAAK,IAAI;AACxB,aAAK,KAAK,MAAM;AAAA,MAClB,WAAW,MAAM,WAAW,MAAM,MAAM,WAAW,IAAI;AAErD,aAAK,eAAe,KAAK,gBAAgB,IAAI,KAAK,MAAM,UAAU,KAAK,MAAM,MAAM;AAAA,MACrF,WAAW,MAAM,WAAW,MAAM,MAAM,WAAW,IAAI;AAErD,aAAK,eAAe,KAAK,gBAAgB,KAAK,KAAK,MAAM,MAAM;AAAA,MACjE,WAAW,MAAM,WAAW,IAAI;AAE9B,aAAK,cAAc,CAAC;AAAA,MACtB,WAAW,MAAM,WAAW,IAAI;AAE9B,aAAK,cAAc,KAAK,MAAM,SAAS,CAAC;AAAA,MAC1C,WAAW,MAAM,WAAW,IAAI;AAE9B,aAAK,KAAK,MAAM;AAAA,MAClB,WAAW,MAAM,WAAW,MAAM,MAAM,WAAW,MAAM,KAAK,iBAAiB,GAAG;AAEhF,YAAI;AAAA,UACA;AAAA,QACF,IAAI,KAAK,MAAM,KAAK,aAAa,GACjC,OAAO,WAAW,WAAW,OAAO;AACtC,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAK,KAAI,KAAK,CAAC,EAAE,YAAY,EAAE,WAAW,CAAC,KAAK,MAAM,SAAS;AAC9F,cAAI,QAAQ,eAAe,KAAK,KAAK,MAAM,MAAM,SAAS,EAAE,aAAa,UAAU;AACnF,cAAI,MAAO,YAAW,QAAQ,CAAC,EAAE,MAAM,MAAM,MAAM,MAAM,MAAM,EAAE;AAAA,QACnE;AAAA,MACF,OAAO;AACL;AAAA,MACF;AACA,YAAM,eAAe;AAAA,IACvB;AACA,QAAI,UAAU,WAAS;AACrB,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,YAAI,KAAK,MAAM,CAAC,EAAE,IAAI,SAAS,MAAM,MAAM,EAAG,MAAK,cAAc,CAAC;AAAA,MACpE;AAAA,IACF;AACA,SAAK,OAAO,MAAI,MAAM;AAAA,MACpB,UAAU;AAAA,MACV,MAAM;AAAA,MACN,cAAc,KAAK,KAAK,MAAM,OAAO,aAAa;AAAA,MAClD;AAAA,MACA;AAAA,IACF,CAAC;AACD,SAAK,MAAM,MAAI,OAAO;AAAA,MACpB,OAAO;AAAA,IACT,GAAG,KAAK,MAAM,MAAI,UAAU;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM;AAAA,MACN,cAAc,KAAK,KAAK,MAAM,OAAO,OAAO;AAAA,MAC5C,SAAS,MAAM,eAAe,KAAK,IAAI;AAAA,IACzC,GAAG,GAAG,CAAC;AACP,SAAK,OAAO;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB;AAClB,QAAI,WAAW,KAAK,KAAK,MAAM,MAAM,SAAS,EAAE;AAChD,QAAI,CAAC,SAAU,QAAO;AACtB,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,IAAK,KAAI,KAAK,MAAM,CAAC,EAAE,cAAc,SAAS,WAAY,QAAO;AACxG,WAAO;AAAA,EACT;AAAA,EACA,SAAS;AACP,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI,KAAK,KAAK,MAAM,MAAM,SAAS;AACnC,QAAI,IAAI,GACN,YAAY,OACZ,kBAAkB;AACpB,QAAI,OAAO,oBAAI,IAAI;AACnB,gBAAY,QAAQ,GAAG,KAAK,KAAK,MAAM,IAAI,QAAQ,CAAC,QAAQ,MAAM;AAAA,MAChE;AAAA,IACF,MAAM;AACJ,eAAS,cAAc,KAAK,aAAa;AACvC,YAAI,KAAK,IAAI,UAAU,EAAG;AAC1B,aAAK,IAAI,UAAU;AACnB,YAAI,QAAQ,IACV;AACF,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,IAAK,KAAI,KAAK,MAAM,CAAC,EAAE,cAAc,YAAY;AACtF,kBAAQ;AACR;AAAA,QACF;AACA,YAAI,QAAQ,GAAG;AACb,iBAAO,IAAI,UAAU,KAAK,MAAM,UAAU;AAC1C,eAAK,MAAM,OAAO,GAAG,GAAG,IAAI;AAC5B,sBAAY;AAAA,QACd,OAAO;AACL,iBAAO,KAAK,MAAM,KAAK;AACvB,cAAI,QAAQ,GAAG;AACb,iBAAK,MAAM,OAAO,GAAG,QAAQ,CAAC;AAC9B,wBAAY;AAAA,UACd;AAAA,QACF;AACA,YAAI,YAAY,KAAK,cAAc,SAAS,YAAY;AACtD,cAAI,CAAC,KAAK,IAAI,aAAa,eAAe,GAAG;AAC3C,iBAAK,IAAI,aAAa,iBAAiB,MAAM;AAC7C,8BAAkB;AAAA,UACpB;AAAA,QACF,WAAW,KAAK,IAAI,aAAa,eAAe,GAAG;AACjD,eAAK,IAAI,gBAAgB,eAAe;AAAA,QAC1C;AACA;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO,IAAI,KAAK,MAAM,UAAU,EAAE,KAAK,MAAM,UAAU,KAAK,KAAK,MAAM,CAAC,EAAE,WAAW,OAAO,IAAI;AAC9F,kBAAY;AACZ,WAAK,MAAM,IAAI;AAAA,IACjB;AACA,QAAI,KAAK,MAAM,UAAU,GAAG;AAC1B,WAAK,MAAM,KAAK,IAAI,UAAU,KAAK,MAAM;AAAA,QACvC,MAAM;AAAA,QACN,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,SAAS,KAAK,KAAK,MAAM,OAAO,gBAAgB;AAAA,MAClD,CAAC,CAAC;AACF,kBAAY;AAAA,IACd;AACA,QAAI,iBAAiB;AACnB,WAAK,KAAK,aAAa,yBAAyB,gBAAgB,EAAE;AAClE,WAAK,KAAK,eAAe;AAAA,QACvB,KAAK;AAAA,QACL,MAAM,OAAO;AAAA,UACX,KAAK,gBAAgB,IAAI,sBAAsB;AAAA,UAC/C,OAAO,KAAK,KAAK,sBAAsB;AAAA,QACzC;AAAA,QACA,OAAO,CAAC;AAAA,UACN;AAAA,UACA;AAAA,QACF,MAAM;AACJ,cAAI,SAAS,MAAM,SAAS,KAAK,KAAK;AACtC,cAAI,IAAI,MAAM,MAAM,IAAK,MAAK,KAAK,cAAc,MAAM,MAAM,IAAI,OAAO;AAAA,mBAAgB,IAAI,SAAS,MAAM,OAAQ,MAAK,KAAK,cAAc,IAAI,SAAS,MAAM,UAAU;AAAA,QAC1K;AAAA,MACF,CAAC;AAAA,IACH,WAAW,KAAK,gBAAgB,GAAG;AACjC,WAAK,KAAK,gBAAgB,uBAAuB;AAAA,IACnD;AACA,QAAI,UAAW,MAAK,KAAK;AAAA,EAC3B;AAAA,EACA,OAAO;AACL,QAAI,SAAS,KAAK,KAAK;AACvB,aAAS,KAAK;AACZ,UAAI,OAAO;AACX,eAAS,KAAK;AACd,WAAK,OAAO;AAAA,IACd;AACA,aAAS,QAAQ,KAAK,OAAO;AAC3B,UAAI,KAAK,IAAI,cAAc,KAAK,MAAM;AACpC,eAAO,UAAU,KAAK,IAAK,IAAG;AAC9B,iBAAS,KAAK,IAAI;AAAA,MACpB,OAAO;AACL,aAAK,KAAK,aAAa,KAAK,KAAK,MAAM;AAAA,MACzC;AAAA,IACF;AACA,WAAO,OAAQ,IAAG;AAAA,EACpB;AAAA,EACA,cAAc,eAAe;AAC3B,QAAI,KAAK,gBAAgB,EAAG;AAC5B,QAAI,QAAQ,KAAK,KAAK,MAAM,MAAM,SAAS;AAC3C,QAAI,YAAY,eAAe,MAAM,aAAa,KAAK,MAAM,aAAa,EAAE,UAAU;AACtF,QAAI,CAAC,UAAW;AAChB,SAAK,KAAK,SAAS;AAAA,MACjB,WAAW;AAAA,QACT,QAAQ,UAAU;AAAA,QAClB,MAAM,UAAU;AAAA,MAClB;AAAA,MACA,gBAAgB;AAAA,MAChB,SAAS,mBAAmB,GAAG,SAAS;AAAA,IAC1C,CAAC;AAAA,EACH;AAAA,EACA,OAAO,KAAK,MAAM;AAChB,WAAO,IAAI,WAAU,IAAI;AAAA,EAC3B;AACF;AACA,SAAS,IAAI,SAAS,QAAQ,uBAAuB;AACnD,SAAO,mEAAmE,KAAK,IAAI,mBAAmB,OAAO,CAAC;AAChH;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,IAAI,qDAAqD,KAAK,qCAAqC,sBAAsB;AAClI;AACA,IAAMC,aAAyB,WAAW,UAAU;AAAA,EAClD,kBAAkB;AAAA,IAChB,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,YAAY;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACtB,YAAY;AAAA,EACd;AAAA,EACA,0BAA0B;AAAA,IACxB,YAAY;AAAA,EACd;AAAA,EACA,uBAAuB;AAAA,IACrB,YAAY;AAAA,EACd;AAAA,EACA,uBAAuB;AAAA,IACrB,YAAY;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACtB,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,OAAO;AAAA,IACP,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,QAAQ;AAAA,EACV;AAAA,EACA,wBAAwB;AAAA,IACtB,UAAU;AAAA,IACV,SAAS;AAAA,EACX;AAAA,EACA,iBAAiB;AAAA,IACf,oBAAoB;AAAA,IACpB,kBAAkB;AAAA,IAClB,eAAe;AAAA,EACjB;AAAA,EACA,uBAAuB;AAAA,IACrB,iBAA8B,UAAU,MAAM;AAAA,EAChD;AAAA,EACA,yBAAyB;AAAA,IACvB,iBAA8B,UAAU,QAAQ;AAAA,EAClD;AAAA,EACA,sBAAsB;AAAA,IACpB,iBAA8B,UAAU,MAAM;AAAA,EAChD;AAAA,EACA,sBAAsB;AAAA,IACpB,iBAA8B,UAAU,MAAM;AAAA,EAChD;AAAA,EACA,wBAAwB;AAAA,IACtB,iBAAiB;AAAA,EACnB;AAAA,EACA,oBAAoB;AAAA,IAClB,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,iBAAiB;AAAA,IACf,UAAU;AAAA,IACV,WAAW;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,cAAc;AAAA,IAChB;AAAA,EACF;AAAA,EACA,yBAAyB;AAAA,IACvB,WAAW;AAAA,MACT,mBAAmB;AAAA,IACrB;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,WAAW;AAAA,MACT,mBAAmB;AAAA,IACrB;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,WAAW;AAAA,MACT,mBAAmB;AAAA,IACrB;AAAA,EACF;AAAA,EACA,2BAA2B;AAAA,IACzB,UAAU;AAAA,IACV,QAAQ;AAAA,MACN,WAAW;AAAA,MACX,WAAW;AAAA,MACX,qBAAqB;AAAA,QACnB,iBAAiB;AAAA,QACjB,OAAO;AAAA,UACL,gBAAgB;AAAA,QAClB;AAAA,MACF;AAAA,MACA,2BAA2B;AAAA,QACzB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,gBAAgB;AAAA,MAClB;AAAA,MACA,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,IACA,kBAAkB;AAAA,MAChB,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,EACF;AACF,CAAC;AACD,SAAS,eAAe,KAAK;AAC3B,SAAO,OAAO,UAAU,IAAI,OAAO,YAAY,IAAI,OAAO,SAAS,IAAI;AACzE;AACA,SAAS,YAAY,aAAa;AAChC,MAAI,MAAM,QACR,SAAS;AACX,WAAS,KAAK,aAAa;AACzB,QAAI,IAAI,eAAe,EAAE,QAAQ;AACjC,QAAI,IAAI,QAAQ;AACd,eAAS;AACT,YAAM,EAAE;AAAA,IACV;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,mBAAN,cAA+B,aAAa;AAAA,EAC1C,YAAY,aAAa;AACvB,UAAM;AACN,SAAK,cAAc;AACnB,SAAK,WAAW,YAAY,WAAW;AAAA,EACzC;AAAA,EACA,MAAM,MAAM;AACV,QAAI,MAAM,SAAS,cAAc,KAAK;AACtC,QAAI,YAAY,mCAAmC,KAAK;AACxD,QAAI,cAAc,KAAK;AACvB,QAAI,oBAAoB,KAAK,MAAM,MAAM,gBAAgB,EAAE;AAC3D,QAAI,kBAAmB,eAAc,kBAAkB,aAAa,KAAK,KAAK;AAC9E,QAAI,YAAY,OAAQ,KAAI,cAAc,MAAM,sBAAsB,MAAM,KAAK,WAAW;AAC5F,WAAO;AAAA,EACT;AACF;AACA,SAAS,aAAa,MAAM,QAAQ;AAClC,MAAI,YAAY,WAAS;AACvB,QAAI,OAAO,OAAO,sBAAsB;AACxC,QAAI,MAAM,UAAU,KAAK,OAAO,MAAyB,MAAM,UAAU,KAAK,QAAQ,MAAyB,MAAM,UAAU,KAAK,MAAM,MAAyB,MAAM,UAAU,KAAK,SAAS,GAAuB;AACxN,aAAS,SAAS,MAAM,QAAQ,QAAQ,SAAS,OAAO,YAAY;AAClE,UAAI,OAAO,YAAY,KAAK,OAAO,UAAU,SAAS,iBAAiB,EAAG;AAAA,IAC5E;AACA,WAAO,oBAAoB,aAAa,SAAS;AACjD,QAAI,KAAK,MAAM,MAAM,iBAAiB,EAAG,MAAK,SAAS;AAAA,MACrD,SAAS,qBAAqB,GAAG,IAAI;AAAA,IACvC,CAAC;AAAA,EACH;AACA,SAAO,iBAAiB,aAAa,SAAS;AAChD;AACA,SAAS,sBAAsB,MAAM,QAAQ,aAAa;AACxD,WAAS,UAAU;AACjB,QAAI,OAAO,KAAK,gBAAgB,OAAO,sBAAsB,EAAE,MAAM,IAAI,KAAK,WAAW;AACzF,UAAM,UAAU,KAAK,YAAY,KAAK,IAAI;AAC1C,QAAI,SAAS;AACX,WAAK,SAAS;AAAA,QACZ,SAAS,qBAAqB,GAAG;AAAA,UAC/B,KAAK,KAAK;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,SAAS;AACP,mBAAO;AAAA,cACL,KAAK,mBAAmB,MAAM,WAAW;AAAA,cACzC,WAAW,MAAM,OAAO,sBAAsB;AAAA,YAChD;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,WAAO,aAAa,OAAO,cAAc;AACzC,iBAAa,MAAM,MAAM;AAAA,EAC3B;AACA,MAAI;AAAA,IACF;AAAA,EACF,IAAI,KAAK,MAAM,MAAM,gBAAgB;AACrC,MAAI,eAAe,WAAW,SAAS,SAAS;AAChD,SAAO,aAAa,MAAM;AACxB,iBAAa,YAAY;AACzB,WAAO,aAAa,OAAO,cAAc;AAAA,EAC3C;AACA,SAAO,cAAc,MAAM;AACzB,iBAAa,YAAY;AACzB,mBAAe,WAAW,SAAS,SAAS;AAAA,EAC9C;AACF;AACA,SAAS,sBAAsB,KAAK,aAAa;AAC/C,MAAI,SAAS,uBAAO,OAAO,IAAI;AAC/B,WAAS,cAAc,aAAa;AAClC,QAAI,OAAO,IAAI,OAAO,WAAW,IAAI;AACrC,KAAC,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU;AAAA,EACjE;AACA,MAAI,UAAU,CAAC;AACf,WAAS,QAAQ,QAAQ;AACvB,YAAQ,KAAK,IAAI,iBAAiB,OAAO,IAAI,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC;AAAA,EAC9D;AACA,SAAO,SAAS,GAAG,SAAS,IAAI;AAClC;AACA,IAAM,sBAAmC,OAAO;AAAA,EAC9C,OAAO;AAAA,EACP,SAAS,UAAQ,KAAK,MAAM,MAAM,iBAAiB;AAAA,EACnD,cAAc,CAAC,MAAM,QAAQ,UAAU;AACrC,QAAI,cAAc,CAAC;AACnB,SAAK,MAAM,MAAM,iBAAiB,EAAE,QAAQ,MAAM,MAAM,MAAM,IAAI,CAAC,MAAM,IAAI,UAAU;AACrF,UAAI,OAAO,MAAM,QAAQ,OAAO,MAAM,GAAI,aAAY,KAAK,GAAG,MAAM,WAAW;AAAA,IACjF,CAAC;AACD,WAAO,YAAY,SAAS,IAAI,iBAAiB,WAAW,IAAI;AAAA,EAClE;AACF,CAAC;AACD,IAAM,oBAAiC,WAAW,OAAO;AAAA,EACvD,SAAS;AACP,WAAO,SAAS;AAAA,EAClB;AAAA,EACA,OAAO,SAAS,IAAI;AAClB,cAAU,QAAQ,IAAI,GAAG,OAAO;AAChC,QAAI,mBAAmB,GAAG,MAAM,MAAM,gBAAgB,EAAE;AACxD,aAAS,UAAU,GAAG,SAAS;AAC7B,UAAI,OAAO,GAAG,oBAAoB,GAAG;AACnC,YAAI,cAAc,OAAO;AACzB,YAAI,iBAAkB,eAAc,iBAAiB,eAAe,CAAC,GAAG,GAAG,KAAK;AAChF,kBAAU,sBAAsB,GAAG,MAAM,KAAK,YAAY,MAAM,CAAC,CAAC;AAAA,MACpE;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF,CAAC;AACD,IAAM,uBAAoC,YAAY,OAAO;AAC7D,IAAM,oBAAiC,WAAW,OAAO;AAAA,EACvD,SAAS;AACP,WAAO;AAAA,EACT;AAAA,EACA,OAAO,SAAS,IAAI;AAClB,QAAI,WAAW,GAAG,WAAY,WAAU,YAAY,IAAI,OAAO,IAAI,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG;AAAA,MAClH,KAAK,GAAG,QAAQ,OAAO,QAAQ,GAAG;AAAA,IACpC,CAAC;AACD,WAAO,GAAG,QAAQ,OAAO,CAAC,GAAG,MAAM,EAAE,GAAG,oBAAoB,IAAI,EAAE,QAAQ,GAAG,OAAO;AAAA,EACtF;AAAA,EACA,SAAS,WAAS,YAAY,KAAK,KAAK;AAC1C,CAAC;AACD,IAAM,kBAA+B,WAAW,UAAU;AAAA,EACxD,mBAAmB;AAAA,IACjB,OAAO;AAAA,IACP,uBAAuB;AAAA,MACrB,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,mBAAmB;AAAA,IACjB,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AAAA,EACA,wBAAwB;AAAA,IACtB,SAAsB,IAAI,sGAAsG;AAAA,EAClI;AAAA,EACA,2BAA2B;AAAA,IACzB,SAAsB,IAAI,kGAAkG;AAAA,EAC9H;AAAA,EACA,yBAAyB;AAAA,IACvB,SAAsB,IAAI,6EAA6E;AAAA,EACzG;AACF,CAAC;AACD,IAAM,iBAAiB,CAAC,WAAwB,WAAW,YAAY,QAAQ,CAAC,SAAS,GAAG,WAAS;AACnG,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,MAAM,MAAM,SAAS;AACzB,SAAO,CAAC,YAAY,CAAC,SAAS,SAAS,QAAQ,SAAS,KAAK,WAAW,OAAO,WAAW,IAAI,CAAC,WAAW,MAAM,SAAS,MAAM,SAAS,EAAE,CAAC,CAAC;AAC9I,CAAC,GAAgB,aAAa,aAAa;AAAA,EACzC,QAAQ;AACV,CAAC,GAAGA,UAAS;AACb,IAAM,mBAAgC,MAAM,OAAO;AAAA,EACjD,QAAQ,SAAS;AACf,WAAO,cAAc,SAAS;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF,CAAC;;;ACn2BD,IAAM,cAA2B,MAAM,CAAC,YAAY,GAAG,0BAA0B,GAAG,sBAAsB,GAAG,QAAQ,GAAG,WAAW,GAAG,cAAc,GAAG,WAAW,GAAG,YAAY,wBAAwB,GAAG,IAAI,GAAG,cAAc,GAAG,mBAAmB,uBAAuB;AAAA,EAC5Q,UAAU;AACZ,CAAC,GAAG,gBAAgB,GAAG,cAAc,GAAG,eAAe,GAAG,qBAAqB,GAAG,gBAAgB,GAAG,oBAAoB,GAAG,0BAA0B,GAAG,OAAO,GAAG,CAAC,GAAG,qBAAqB,GAAG,eAAe,GAAG,cAAc,GAAG,eAAe,GAAG,YAAY,GAAG,kBAAkB,GAAG,UAAU,CAAC,CAAC,GAAG;AASvS,IAAM,gBAA6B,MAAM,CAAC,sBAAsB,GAAG,QAAQ,GAAG,cAAc,GAAG,mBAAmB,uBAAuB;AAAA,EACvI,UAAU;AACZ,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,eAAe,GAAG,aAAa,CAAC,CAAC,GAAG;", "names": ["empty", "isAd<PERSON><PERSON>", "command", "isAd<PERSON><PERSON>", "changes", "line", "word", "add", "from", "to", "phrase", "next", "i", "togglePanel", "togglePanel", "baseTheme"]}