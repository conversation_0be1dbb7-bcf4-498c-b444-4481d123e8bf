import { ChangeDetectionStrategy, Component, DestroyRef, inject, input, OnInit, signal } from '@angular/core';
import { FormControl, ReactiveFormsModule, UntypedFormBuilder, Validators } from '@angular/forms';
import { ExtraFieldValueService } from '@proxy/extra-field-values';
import { EntityType, FieldType } from '@proxy/extra-field-definitions';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { dateToISO, LanguagePipe, provideNativeDatetimeAdapter } from '@ttwr-framework/ngx-main-visuals';
import { DynamicFormFieldComponent } from './dynamic-form-field/dynamic-form-field.component';
import { provideNativeDateAdapter } from '@angular/material/core';
import { ExtraFieldDto } from '@proxy/extra-field-values/dto';
import { clearDate } from '@shared/functions';

@Component({
  selector: 'app-extra-fields-form',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    DynamicFormFieldComponent,
    LanguagePipe
  ],
  templateUrl: './extra-fields-form.component.html',
  styleUrl: './extra-fields-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    provideNativeDateAdapter(),
    provideNativeDatetimeAdapter(),
  ],
})
export class ExtraFieldsFormComponent implements OnInit {
  private fb = inject(UntypedFormBuilder);
  private extraField = inject(ExtraFieldValueService);
  private destroyRef = inject(DestroyRef);

  public entityType = input.required<EntityType>();
  public entityId = input<string | undefined>(undefined);

  public form = this.fb.group({});

  public fieldNameToDefinitionId = new Map<string, string>();
  public fieldNameToExtraValueId = new Map<string, string | null>();

  protected fields = signal<Field[]>([]);

  ngOnInit() {
    this.extraField.getList(
      this.entityId() as any,
      this.entityType(),
    ).pipe(
      takeUntilDestroyed(this.destroyRef),
    ).subscribe(fields => {
      const _fields: Field[] = [];

      fields.forEach(field => {
        this.fieldNameToDefinitionId.set(field.fieldName!, field.extraFieldDefinitionId!);
        this.fieldNameToExtraValueId.set(field.fieldName!, field.extraFieldDefinitionId ?? null);

        let value: any;

        if (field.fieldType === FieldType.DateOnly) {
          value = new Date(field.value!);
        } else if (field.fieldType === FieldType.Bool) {
          value = field.value === "True";
        } else if (field.fieldType === FieldType.Decimal) {
          value = parseFloat(field.value!);
        } else if (field.fieldType === FieldType.Int) {
          value = parseInt(field.value!);
        } else {
          value = field.value!;
        }

        const control = this.fb.control(value, Validators.required);

        _fields.push({
          name: field.fieldName!,
          control,
          type: field.fieldType,
          options: field.selectList,
        });

        this.form.addControl(field.fieldName!, control);
      })

      this.fields.set(_fields);
    });
  }

  get extraFieldsValue(): ExtraFieldDto[] {
    return this.fields().map(field => {
      const initialValue = field.control.value;

      let value: any;

      if (field.type === FieldType.DateOnly) {
        value = clearDate(
          dateToISO(
            new Date(initialValue)
          )
        );
      } else if (field.type === FieldType.DateTime) {
        value = dateToISO(
          new Date(initialValue)
        );
      } else if (field.type === FieldType.Selection) {
        value = initialValue;
      } else if (field.type === FieldType.Int) {
        value = parseInt(initialValue);
      } else if (field.type === FieldType.Decimal) {
        value = parseFloat(initialValue);
      } else {
        value = initialValue;
      }

      return {
        extraFieldValueId: this.fieldNameToExtraValueId.get(field.name),
        extraFieldDefinitionId: this.fieldNameToDefinitionId.get(field.name),
        value: JSON.stringify(value),
        fieldName: '',
        fieldType: FieldType.String,
      } as any;
    })
  }
}

interface Field {
  name: string;
  control: FormControl;
  type: FieldType;
  options: string[];
}
