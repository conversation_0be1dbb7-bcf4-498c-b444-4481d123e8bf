import { arrayMap, fields, model } from '@ttwr-framework/ngx-main-visuals';
import { inject } from '@angular/core';
import { LeaveTypeService } from '@proxy/leave-types';
import { EmployeeService } from '@proxy/hr/employees';
import { requireAllOperator } from '@shared';
import { map, tap } from 'rxjs';
import { RequestUnit } from '@proxy/hr/leave-types';

export const leaveAllocations = (optionalLeaveTypeObserver?: LeaveTypeObserver) => {
  const leaveType = inject(LeaveTypeService);
  const employee = inject(EmployeeService);

  return model({
    id: fields.text(),
    leaveTypeId: fields.selectFetch('single', () => leaveType.getAllocatableLeaveTypes({
      maxResultCount: 999
    }).pipe(
      requireAllOperator(),
      map(res => res.items),
      tap(items => optionalLeaveTypeObserver?.(items)),
      arrayMap(type => ({
        label: type.name,
        value: type.id,
      }))
    )),
    leaveTypeName: fields.text(),
    employeeId: fields.selectFetch('single', () => employee.getList(
      { maxResultCount: 999 }, undefined as any, undefined as any,
    ).pipe(
      requireAllOperator(),
      map(res => res.items),
      arrayMap(employee => ({
        label: `${employee.name} ${employee.surname}`,
        value: employee.id,
      }))
    )),
    employeeName: fields.text(),

    // custom input implementation
    validityDateRange: fields.text(),

    numberOfDays: fields.number(),
    numberOfHours: fields.number(),
  });
}

type LeaveTypeObserver = (type: LeaveType[]) => void

type LeaveType = {
  id: string;
  name: string;
  requestUnit: RequestUnit;
}
