import { Component, DestroyRef, inject } from '@angular/core';
import { AlertService, LanguagePipe, LOADING, TtwrFormComponent } from '@ttwr-framework/ngx-main-visuals';
import { finalize } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { leaveTypes } from '../../leave-types.model';
import { MatCard, MatCardContent } from '@angular/material/card';
import { ActivatedRoute, Router } from '@angular/router';
import { LeaveTypeService } from '@proxy/leave-types';

@Component({
  selector: 'app-leave-types-update',
  standalone: true,
  templateUrl: './leave-types-update.component.html',
  styleUrl: './leave-types-update.component.scss',
  imports: [
    MatCard,
    MatCardContent,
    TtwrFormComponent,
    LanguagePipe
  ],
})
export class LeaveTypesUpdateComponent {
  private leaveType = inject(LeaveTypeService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private loading = inject(LOADING);

  protected config = leaveTypes.form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        this.leaveType.update(this.route.snapshot.params['id'], body)
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::UpdatedSuccessfully');
            this.router.navigate(['.'], { relativeTo: this.route.parent });

          })
      },
    },
    viewFunc: () => this.leaveType.get(this.route.snapshot.params['id']),
    fields: {
      code: {
        label: '::GoldenOwl:Code',
      },
      kindOfTimeOff: {
        label: '::KindOfTimeOff',
      },
      requestUnit: {
        label: '::RequestUnit',
      },
      requiresAllocation: {
        label: '::RequiresAllocation',
      }
    },
  })
}
