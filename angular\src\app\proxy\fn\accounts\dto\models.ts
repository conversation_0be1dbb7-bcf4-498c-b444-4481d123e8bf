import type { FullAuditedEntityDto } from '@abp/ng.core';
import type { CashFlowType } from '../cash-flow-type.enum';

export interface AccountDto extends FullAuditedEntityDto<string> {
  code?: string;
  name?: string;
  currencyCode?: string;
  typeName?: string;
  isDeprecated: boolean;
  cashFlowType: CashFlowType;
}

export interface CreateAccountDto {
  code?: string;
  name?: string;
  currencyCode?: string;
  typeName?: string;
  cashFlowType: CashFlowType;
}

export interface UpdateAccountDto {
  code?: string;
  name?: string;
  typeName?: string;
  cashFlowType: CashFlowType;
}
