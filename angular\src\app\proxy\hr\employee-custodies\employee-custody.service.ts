import type { CreateEmployeeCustodyDto, EmployeeCustodyDto, GetEmployeeCustodiesRequestDto, ReleaseEmployeeCustodyDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class EmployeeCustodyService {
  apiName = 'Default';
  

  getGetAllEmployeeCustodiesByDto = (dto: GetEmployeeCustodiesRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<EmployeeCustodyDto>>({
      method: 'GET',
      url: '/api/app/employee-custody/get-all-employee-custodies',
      params: { employeeId: dto.employeeId, skipCount: dto.skipCount, maxResultCount: dto.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getGetEmployeeCustodyByIdById = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, EmployeeCustodyDto>({
      method: 'GET',
      url: `/api/app/employee-custody/${id}/get-employee-custody-by-id`,
    },
    { apiName: this.apiName,...config });
  

  postCreateEmployeeCustodyByDto = (dto: CreateEmployeeCustodyDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/employee-custody/create-employee-custody',
      body: dto,
    },
    { apiName: this.apiName,...config });
  

  putReleaseEmployeeCustodyByIdAndDto = (id: string, dto: ReleaseEmployeeCustodyDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: `/api/app/employee-custody/${id}/release-employee-custody`,
      body: dto,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
