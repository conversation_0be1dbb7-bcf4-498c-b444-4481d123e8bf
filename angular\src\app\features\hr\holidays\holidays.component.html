<h1>
  {{ '::Holiday' | i18n }}
</h1>
<mat-card>
  <mat-card-content>
    <div class="top-card-row">
      <mat-button-toggle-group>
        <mat-button-toggle
          (click)="activeSection.set('table')"
          [checked]="activeSection() === 'table'"
        >
          {{ 'Table' | i18n }}
        </mat-button-toggle>
        <mat-button-toggle
          (click)="activeSection.set('calendar')"
          [checked]="activeSection() === 'calendar'"
        >
          {{ 'Calendar' | i18n }}
        </mat-button-toggle>
      </mat-button-toggle-group>
      <button (click)="openCreateDialog()" mat-flat-button>
        {{ 'Add' | i18n }}
      </button>
    </div>
    <ttwr-grid
      [style.display]="activeSection() === 'calendar' ? 'none' : 'block'"
      [config]="config"
    />
    @if (activeSection() === 'calendar' && holidays(); as holidays) {
      @defer (on viewport) {
        <app-calendar
          [holidays]="$any(holidays)"
        />
      } @placeholder {
        <div style="height: 640px; width: 100%;"></div>
      }
    }
  </mat-card-content>
</mat-card>
