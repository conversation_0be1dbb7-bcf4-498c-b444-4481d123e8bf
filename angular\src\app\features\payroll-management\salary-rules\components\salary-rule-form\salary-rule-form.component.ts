import { Component, computed, DestroyRef, inject, input, output, signal } from '@angular/core';
import { AlertService, LOADING, TtwrFormComponent } from '@ttwr-framework/ngx-main-visuals';
import { RuleConditionType, RuleValueType, SalaryRuleService } from '@proxy/payroll/salary-rules';
import { salaryRules } from '../../salary-rules.model';
import { finalize, Observable } from 'rxjs';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { JsEditorCustomInputComponent } from '@shared';

@Component({
  selector: 'app-salary-rule-form',
  standalone: true,
  imports: [TtwrFormComponent],
  template: `<ttwr-form [config]="config" />`,
})
export class SalaryRuleFormComponent {
  private salaryRule = inject(SalaryRuleService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);

  public initialValue = input<SalaryFormValue>();
  public finish = output<string>();

  private isUpdate = computed(() => !!this.initialValue());

  private conditionCodeHiddenSignal = signal(false);
  private valueCodeHiddenSignal = signal(false);
  private fixedValueHiddenSignal = computed(() => !this.valueCodeHiddenSignal());

  private initialValue$ = toObservable(this.initialValue);

  protected config = salaryRules().exclude({
    categoryName: true,
  }).form({
    initialRequired: true,
    submitAction: {
      onSubmit: body => {
        this.loading.set(true);

        const observable: Observable<string | void> = this.isUpdate()
          ? this.salaryRule.update(this.initialValue()!.id, body)
          : this.salaryRule.create(body);


        observable.pipe(
          finalize(() => this.loading.set(false)),
          takeUntilDestroyed(this.destroyRef),
        ).subscribe(res => {
          this.alert.success(this.isUpdate() ? '::UpdatedSuccessfully' : '::CreatedSuccessfully');
          this.finish.emit(res ? res.replace(/"/g, '') : '');
        })
      },
    },
    fields: {
      conditionBasedOn: {
        defaultValue: RuleConditionType.AlwaysTrue,
        onChange: value => {
          if (value === null || value === undefined) return;

          this.conditionCodeHiddenSignal.set(value === RuleConditionType.AlwaysTrue);
        },
      },
      valueBasedOn: {
        defaultValue: RuleValueType.FixedAmount,
        onChange: value => {
          if (value === null || value === undefined) return;

          this.valueCodeHiddenSignal.set(value === RuleValueType.FixedAmount);
        },
      },

      conditionCode: {
        label: '::GoldenOwl:ConditionCode',
        hiddenSignal: this.conditionCodeHiddenSignal,
        customInputComponent: JsEditorCustomInputComponent,
        inputSize: 'span 2',
      },
      valueCode: {
        label: '::GoldenOwl:ValueCode',
        hiddenSignal: this.valueCodeHiddenSignal,
        customInputComponent: JsEditorCustomInputComponent,
        inputSize: 'span 2',
      },
      valueFixedAmount: {
        label: '::GoldenOwl:ValueFixedAmount',
        hiddenSignal: this.fixedValueHiddenSignal,
      },
      code: { label: '::GoldenOwl:Code' },
      isActive: { label: '::GoldenOwl:IsActive' },
      appearForEmployee: { label: '::GoldenOwl:AppearForEmployee' },
      categoryId: {
        label: '::GoldenOwl:Category',
        search: true,
      },
    },
    viewFunc: () => this.initialValue$,
  })
}

interface SalaryFormValue {
  id: string;
  name: string;
  code: string;
  conditionBasedOn: RuleConditionType;
  conditionCode: string;
  valueBasedOn: RuleValueType;
  valueFixedAmount: number;
  valueCode: string;
  categoryId: string;
  isActive: boolean;
  appearForEmployee: boolean;
}
