import {arrayMap, fields, model, Option, takeOptions} from '@ttwr-framework/ngx-main-visuals';
import { of } from 'rxjs';
import {AttendanceRequestState} from '@proxy/self-service/attendance-requests';
import {LeaveRequestState, LeaveUnit} from '@proxy/hr/leave-requests';
import {inject} from '@angular/core';
import {LeaveTypeService} from '@proxy/leave-types';
import {PagedResultRequestDto} from '@abp/ng.core';

export const leaveRequests = model({
  id: fields.text(),
  leaveTypeName: fields.text(),
  duration: fields.number(),
  from: fields.date(),
  to: fields.date(),
  state: fields.select('single', takeOptions(LeaveRequestState)),
  unit: fields.number(),
  dateAndTimeRange: fields.text(),
  durationUnit: fields.text()
});

export const leaveRequestCreate = () => {
  const type = inject(LeaveTypeService);

  const leaveTypeOptionsFunc = () => {

    return type.getListIdAndName().pipe(
      arrayMap(a => ({
        label: a.name!,
        value: a.id!,
      }))
    )
  }

  return model({
    leaveTypeId: fields.selectFetch('single', leaveTypeOptionsFunc),
    leaveUnit: fields.select('single', takeOptions(LeaveUnit)),
    fromTime: fields.datetime(),
    toTime: fields.datetime(),
    fromDate: fields.date(),
    toDate: fields.date(),
    from: fields.date(),
    to: fields.date(),
    duration: fields.text()
  });
}
