import type { CreateFinancialClientDto, FinancialClientDto, UpdateFinancialClientDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class FinancialClientService {
  apiName = 'Default';
  

  create = (financialClientDto: CreateFinancialClientDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/financial-client',
      body: financialClientDto,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/financial-client/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, FinancialClientDto>({
      method: 'GET',
      url: `/api/app/financial-client/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, name?: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<FinancialClientDto>>({
      method: 'GET',
      url: '/api/app/financial-client',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount, name },
    },
    { apiName: this.apiName,...config });
  

  update = (id: string, financialClientDto: UpdateFinancialClientDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: `/api/app/financial-client/${id}`,
      body: financialClientDto,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
