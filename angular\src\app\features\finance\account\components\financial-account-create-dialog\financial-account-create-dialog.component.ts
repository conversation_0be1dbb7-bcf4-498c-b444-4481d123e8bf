import { Component, DestroyRef, inject, signal } from '@angular/core';
import {
  AlertService,
  LanguagePipe,
  LOADING,
  TtwrFormComponent,
} from '@ttwr-framework/ngx-main-visuals';
import {
  MatDialogContent,
  MatDialogRef,
  MatDialogTitle,
} from '@angular/material/dialog';
import { finalize } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { accountTypeOptions, financialAccount } from '../../financial-account.model';
import { FinancialAccountService } from '@proxy/fn/accounts';
import { CustomAccountCodeComponent } from '@shared/components/custom-account-code.component';

@Component({
  selector: 'app-financial-account-create-dialog',
  standalone: true,
  imports: [
    LanguagePipe,
    MatDialogContent,
    MatDialogTitle,
    TtwrFormComponent,
  ],
  template: `
    <h2 mat-dialog-title>{{ '::CreateAccount' | i18n }}</h2>
    <mat-dialog-content>
      <ttwr-form [config]="config" />
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class FinancialAccountCreateDialogComponent {
  private financialAccount = inject(FinancialAccountService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);
  private dialogRef = inject(MatDialogRef);
  protected prefixSignal = signal<string>('');

  protected config = financialAccount('name')
    .exclude({ isDeprecated: true })
    .form({
      initialRequired: true,
      submitAction: {
        onSubmit: (body) => {
          this.loading.set(true);
          body.code = this.prefixSignal() + body.code;
          this.financialAccount
            .create(body)
            .pipe(
              finalize(() => this.loading.set(false)),
              takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(() => {
              this.alert.success('::CreatedSuccessfully');
              this.dialogRef.close(true);
            });
        },
      },
      fields: {
        code: {
          customInputComponent: CustomAccountCodeComponent,
          customInputComponentExtraInputs: {
            prefix: this.prefixSignal
          },
          label: '::Account:Code',
        },

        currencyCode: {
          search: true,
          label: '::Account:Currency',
        },
        typeName: {
          search: true,
          label: '::Account:TypeName',
          onChange: (selectedName, _, form) => {
            const matched = accountTypeOptions().find(o => o.value === selectedName);
            if (matched) {
              form.get('codePrefix')?.setValue(matched.prefix);
              this.prefixSignal.set(matched.prefix);
            }
          }
        },
        cashFlowType: {
          label: '::Account:CashFlowType',
        },
        codePrefix: {
          hiddenSignal: signal(true),
        }
      },
    });
}
