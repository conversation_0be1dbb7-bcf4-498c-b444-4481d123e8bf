using Elsa.Common.Models;
using Elsa.Workflows.Models;
using Elsa.Workflows.Runtime;
using Elsa.Workflows.Runtime.Filters;
using ElsaWorkflows.GoldenOwl.Common;
using Volo.Abp.DependencyInjection;

namespace ElsaWorkflows.Domain;

public class GoldenOwlElsaWorkflowsHelper : IGoldenOwlElsaWorkflowsHelper, ITransientDependency
{
    private readonly IWorkflowStarter _workflowStarter;
    private readonly IBookmarkStore _bookmarkStore;
    private readonly IBookmarkResumer _bookmarkResumer;
    private readonly IWorkflowCancellationService _workflowCancellationService;

    public GoldenOwlElsaWorkflowsHelper(IWorkflowStarter workflowStarter, IBookmarkStore bookmarkStore,
        IBookmarkResumer bookmarkResumer, IWorkflowCancellationService workflowCancellationService)
    {
        _workflowStarter = workflowStarter;
        _bookmarkStore = bookmarkStore;
        _bookmarkResumer = bookmarkResumer;
        _workflowCancellationService = workflowCancellationService;
    }

    public async Task<string> StartWorkflowAsync(string definitionId, IDictionary<string, object>? input)
    {
        var startWorkflowRequest = new StartWorkflowRequest()
        {
            WorkflowDefinitionHandle = WorkflowDefinitionHandle.ByDefinitionId(definitionId, VersionOptions.Published),
            Input = input,
        };
        var result = await _workflowStarter.StartWorkflowAsync(startWorkflowRequest);

        return result.WorkflowInstanceId ??
               throw new InvalidOperationException("Elsa Workflow Couldn't Start Correctly");
    }

    public async Task ResumeWorkflowAsync(string bookmarkId, Dictionary<string, object> payload)
    {
        var bookmark = await _bookmarkStore.FindAsync(new BookmarkFilter() { BookmarkId = bookmarkId });
        if (bookmark is null)
        {
            return;
        }

        var resumeBookmarkRequest = new ResumeBookmarkRequest()
        {
            BookmarkId = bookmark.Id,
            WorkflowInstanceId = bookmark.WorkflowInstanceId,
            Input = payload,
        };

        await _bookmarkResumer.ResumeAsync(resumeBookmarkRequest);
    }

    public async Task CancelWorkflowAsync(string instanceId)
    {
        await _workflowCancellationService.CancelWorkflowAsync(instanceId);
    }
}