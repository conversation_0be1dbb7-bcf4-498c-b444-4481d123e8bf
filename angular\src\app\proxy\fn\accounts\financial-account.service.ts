import type { AccountDto, CreateAccountDto, UpdateAccountDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class FinancialAccountService {
  apiName = 'Default';
  

  changeDeprecation = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/financial-account/${id}/change-deprecation`,
    },
    { apiName: this.apiName,...config });
  

  create = (accountDto: CreateAccountDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/financial-account',
      body: accountDto,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AccountDto>({
      method: 'GET',
      url: `/api/app/financial-account/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, codePrefix?: string, name?: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<AccountDto>>({
      method: 'GET',
      url: '/api/app/financial-account',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount, codePrefix, name },
    },
    { apiName: this.apiName,...config });
  

  update = (accountDto: UpdateAccountDto, id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: `/api/app/financial-account/${id}`,
      body: accountDto,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
