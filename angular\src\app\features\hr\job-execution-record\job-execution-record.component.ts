import { Component, DestroyRef, inject } from '@angular/core';
import { LanguageService, LOADING, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { jobExecutionRecord } from './job-execution-record.model';
import { JobExecutionRecordService, JobExecutionStatus } from '@proxy/job-execution-records';
import { requireAllOperator } from '@shared';
import { Subject } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatDialog } from '@angular/material/dialog';
import { JobExecutionLogsDialogComponent } from './job-execution-logs-dialog.component';

@Component({
  selector: 'app-job-execution-record',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `<ttwr-grid class="actions-end" [config]="config" />`,
})
export class JobExecutionRecordComponent {
  private jobExecutionRecord = inject(JobExecutionRecordService);
  private destroyRef = inject(DestroyRef);
  private language = inject(LanguageService);
  private dialog = inject(MatDialog);
  private loading = inject(LOADING);

  private refreshSubject = new Subject<void>();

  protected config = jobExecutionRecord.grid({
    title: '::JobExecutionRecord',
    refreshSubject: this.refreshSubject,
    dataFunc: pagination => this.jobExecutionRecord.getList({
      skipCount: pagination.pageSize * pagination.pageIndex,
      maxResultCount: pagination.pageSize,
    }).pipe(requireAllOperator()),
    actions: [
      {
        label: '::AttendanceSyncManagerJob',
        delegateFunc: () => {
          this.loading.set(true);

          this.jobExecutionRecord.syncAttendance().pipe(
            takeUntilDestroyed(this.destroyRef)
          ).subscribe(() => {
            this.loading.set(false);
            this.refreshSubject.next();
          })
        },
      }
    ],
    fieldActions: [
      {
        label: '::JobExecutionLogs',
        color: 'info',
        delegateFunc: ({ id }) => this.dialog.open(JobExecutionLogsDialogComponent, {
          data: { id },
          maxWidth: '700px',
          width: '100%',
        }),
      }
    ],
    fields: {
      name: {
        displayCell: cell => this.language.translate(cell),
      },
      groupName: {
        columnName: '::GoldenOwl:GroupName',
        displayCell: cell => this.language.translate(cell),
      },
      status: {
        columnName: '::GoldenOwl:Status',
        displayCell: cell => this.language.translate(`::GoldenOwl:JobExecutionStatus.${JobExecutionStatus[cell]}`),
      },
      details: {
        columnName: '::GoldenOwl:Details',
      },
      creationTime: {
        columnName: '::GoldenOwl:CreationTime',
      },
      lastModificationTime: {
        columnName: '::GoldenOwl:LastModificationTime',
      }
    },
  })
}
