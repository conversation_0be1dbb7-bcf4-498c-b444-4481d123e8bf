using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoldenOwl.FN.Currencies.DTO;
using GoldenOwl.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace GoldenOwl.FN.Currencies;

public class CurrencyAppService : GoldenOwlAppService, ICurrencyAppService
{
    private readonly IRepository<Currency, string> _currencyRepository;

    public CurrencyAppService(IRepository<Currency, string> currencyRepository)
    {
        _currencyRepository = currencyRepository;
    }

    [Authorize(GoldenOwlPermissions.CurrencyAndExchangeRateManagement)]
    public async Task ChangeActivationAsync(string code)
    {
        Currency currency = await _currencyRepository.GetAsync(code);
        currency.ChangeActivation();
    }

    [Authorize(GoldenOwlPermissions.CurrencyAndExchangeRateIndex)]
    public async Task<PagedResultDto<CurrencyDto>> GetListAsync(PagedResultRequestDto dto, string? name, bool? isActive)
    {
        IQueryable<Currency> query = await _currencyRepository.GetQueryableAsync();
        query = query.OrderBy(c => c.Id);

        if (!string.IsNullOrWhiteSpace(name))
        {
            query = query.Where(c => c.FullName.ToLower().Contains(name.ToLower()));
        }

        if (isActive != null)
        {
            query = query.Where(c => c.IsActive == isActive);
        }

        int totalCount = await AsyncExecuter.CountAsync(query);
        List<Currency> currencies = await AsyncExecuter.ToListAsync(query.PageBy(dto));
        List<CurrencyDto> currenciesDto =
            ObjectMapper.Map<List<Currency>, List<CurrencyDto>>(currencies);

        return new PagedResultDto<CurrencyDto>(totalCount, currenciesDto);
    }

    [Authorize(GoldenOwlPermissions.CurrencyAndExchangeRateIndex)]
    public async Task<CurrencyDto> GetViewAsync(string code)
    {
        Currency currency = await _currencyRepository.GetAsync(code);
        return ObjectMapper.Map<Currency, CurrencyDto>(currency);
    }
}