import { Component, DestroyRef, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  MAT_DIALOG_DATA,
  MatDialogContent,
  MatDialogRef,
  MatDialogTitle,
} from '@angular/material/dialog';
import {
  AlertService,
  LanguagePipe,
  LOADING,
  TtwrFormComponent,
} from '@ttwr-framework/ngx-main-visuals';
import { finalize, of } from 'rxjs';
import { clearDate } from '@shared';
import { FinancialPeriodService } from '@proxy/fn/financial-periods';
import { fiscalPeriod } from '../../fiscal-period.model';

@Component({
  selector: 'app-fiscal-period-update-dialog',
  standalone: true,
  imports: [TtwrFormComponent, MatDialogTitle, MatDialogContent, LanguagePipe],
  template: `
    <h1 mat-dialog-title>{{ '::Update' | i18n }}</h1>
    <mat-dialog-content>
      <ttwr-form [config]="config" />
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-gap: 1rem;
      --ttwr-form-grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
  `,
})
export class FiscalPeriodUpdateDialogComponent {
  private financialPeriodService = inject(FinancialPeriodService);
  private alert = inject(AlertService);
  private dialogRef = inject(MatDialogRef);
  private destroyRef = inject(DestroyRef);
  private data = inject<DialogData>(MAT_DIALOG_DATA);
  private loading = inject(LOADING);

  protected config = fiscalPeriod
    .exclude({
      startDate: true,
      financialPeriodState: true,
      lastAccountingAuditing: true,
    })
    .form({
      initialRequired: true,
      submitAction: {
        onSubmit: (body) => {
          this.loading.set(true);
          this.financialPeriodService
            .putUpdate( {
              id: this.data.id,
              code: body.code,
              endDate: clearDate(body.endDate),
            })
            .pipe(
              finalize(() => this.loading.set(false)),
              takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(() => {
              this.alert.success('::UpdatedSuccessfully');
              this.dialogRef.close(true);
            });
        },
      },
      viewFunc: () => of(this.data),
    });
}

interface DialogData {
  id: string;
  startDate: string;
  endDate: string;
  code: string;
}
