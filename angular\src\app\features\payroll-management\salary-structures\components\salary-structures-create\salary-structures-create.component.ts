import { Component, computed, DestroyRef, inject, signal } from '@angular/core';
import { AlertService, LanguagePipe, LOADING, TtwrFormComponent } from '@ttwr-framework/ngx-main-visuals';
import { finalize } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SalaryStructureService } from '@proxy/payroll/salary-structures';
import { salaryStructures } from '../../salary-structures.model';
import { SalaryRulesListComponent } from '../salary-rules-list/salary-rules-list.component';
import { ActivatedRoute, Router } from '@angular/router';
import { MatCard, MatCardContent } from '@angular/material/card';

@Component({
  selector: 'app-salary-structures-create',
  standalone: true,
  imports: [
    LanguagePipe,
    TtwrFormComponent,
    SalaryRulesListComponent,
    MatCard,
    MatCardContent,
  ],
  templateUrl: './salary-structures-create.component.html',
  styles: `
    ttwr-form {
      display: block;
      max-width: 800px;
    }
  `,
})
export class SalaryStructuresCreateComponent {
  private salaryStructure = inject(SalaryStructureService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);
  private router = inject(Router);
  private route = inject(ActivatedRoute);

  protected selectedRules = signal<(string | null)[]>([null]);
  private notNullRules = computed<string[]>(() => this.selectedRules().filter(Boolean) as string[]);

  protected config = salaryStructures.form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        const rules = this.notNullRules();

        if (rules.length <= 0) {
          this.alert.error('::YouMustAddOneNonEmptyRuleAtLeast');
          return;
        }

        this.loading.set(true);

        this.salaryStructure.create({
          ...body,
          structureRules: this.notNullRules().map((id, index) => ({
            ruleId: id,
            sequence: index + 1,
          })),
        })
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::CreatedSuccessfully');
            this.router.navigate(['.'], { relativeTo: this.route.parent });
          })
      },
    },
    fields: {
      name: {
        inputSize: 'span 2',
      },
    },
  });
}
