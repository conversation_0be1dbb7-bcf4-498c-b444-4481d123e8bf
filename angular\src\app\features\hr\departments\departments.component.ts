import { Component, DestroyRef, inject, signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { MatDialog } from '@angular/material/dialog';
import { BehaviorSubject, map, shareReplay, switchMap } from 'rxjs';
import { LanguagePipe, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { departments } from './departments.model';
import { DepartmentsCreateDialogComponent } from './departments-create-dialog/departments-create-dialog.component';
import { DepartmentsUpdateDialogComponent } from './departments-update-dialog/departments-update-dialog.component';
import { DepartmentService } from '@proxy/hr/departments';
import { OrgTreeComponent, OrgTreeData, requireAllOperator } from '@shared';
import { MatButtonToggle, MatButtonToggleGroup } from '@angular/material/button-toggle';
import { MatCard, MatCardContent } from '@angular/material/card';
import type { DepartmentDto } from '@proxy/hr/departments/dto';
import { MatButton } from '@angular/material/button';

@Component({
  selector: 'app-departments-index',
  standalone: true,
  imports: [TtwrGridComponent, MatButtonToggleGroup, MatButtonToggle, LanguagePipe, MatCard, MatCardContent, OrgTreeComponent, MatButton],
  templateUrl: './departments.component.html',
  styleUrl: './departments.component.scss',
})
export class DepartmentsComponent {
  private departments = inject(DepartmentService);
  private dialog = inject(MatDialog);
  private destroyRef = inject(DestroyRef);

  private refreshSubject = new BehaviorSubject(null);
  protected activeSection = signal<'table' | 'tree'>('table');

  private allDepartments$ = this.refreshSubject.pipe(
    switchMap(() => this.departments.getGetAllDepartmentsByInput({
      maxResultCount: 1000,
    })),
    requireAllOperator(),
    shareReplay({
      bufferSize: 1,
      refCount: true,
    })
  );

  protected treeData = toSignal(this.allDepartments$.pipe(
    map(({ items }) => {
      return this.arrayToTree(items) as OrgTreeData;
    })
  ))

  protected config = departments().exclude({ parentDepartmentId: true }).grid({
    hiddenPagination: true,
    elevationClass: 'mat-elevation-z0',
    dataFunc: () => this.allDepartments$,
    fieldActions: [
      {
        matIcon: 'edit',
        tooltip: 'Edit',
        color: 'primary',
        delegateFunc: (obj) => {
          const ref = this.dialog.open(DepartmentsUpdateDialogComponent, {
            width: '500px',
            data: {
              id: obj.id
            },
          });

          ref.afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
              if (res) {
                this.refreshSubject.next(null);
              }
            });
        }
      },
    ],
    fields: {
      parentDepartmentName: {
        columnName: '::ParentDepartment',
      },
    }
  });

  protected openCreateDialog() {
    const ref = this.dialog.open(DepartmentsCreateDialogComponent, {
      width: '500px'
    });

    ref.afterClosed()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(res => {
        if (res) {
          this.refreshSubject.next(null);
        }
      });
  }

  private arrayToTree(nodes: Required<DepartmentDto>[]): OrgTreeData {
    const nodeMap = new Map<string, TreeNode>();
    const rootNodes: TreeNode[] = [];

    // Initialize the map with all nodes
    nodes.forEach((node) => {
      nodeMap.set(node.id, {
        id: node.id,
        name: node.name,
        children: [],
      });
    });

    // Build the tree structure
    nodes.forEach((node) => {
      const treeNode = nodeMap.get(node.id);
      if (!treeNode) return;

      if (node.parentDepartmentId) {
        const parentTreeNode = nodeMap.get(node.parentDepartmentId);
        if (parentTreeNode) {
          parentTreeNode.children.push(treeNode);
        }
      } else {
        rootNodes.push(treeNode); // No parentId means it's a root node
      }
    });

    return rootNodes[0];
  }
}

type TreeNode = {
  id: string;
  name: string;
  children: TreeNode[];
};
