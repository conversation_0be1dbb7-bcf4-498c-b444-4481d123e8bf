import { Component, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { AlertService, fields, LanguagePipe, LOADING, TtwrFormComponent } from '@ttwr-framework/ngx-main-visuals';
import { finalize, of } from 'rxjs';
import { AttachmentTypeService } from '@proxy/attachment-types';
import { attachmentTypes } from '../attachment-types.model';
import { bytesToFormattedSize } from '@shared';

@Component({
  selector: 'app-attachment-types-update-dialog',
  standalone: true,
  imports: [TtwrFormComponent, MatDialogTitle, MatDialogContent, LanguagePipe],
  template: `
    <h1 mat-dialog-title>{{ '::EditAttachmentType' | i18n }}</h1>
    <mat-dialog-content>
      <ttwr-form [config]="config"/>
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-grid-template-columns: repeat(3, 1fr);
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class AttachmentTypesUpdateDialogComponent {
  private attachmentType = inject(AttachmentTypeService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private data = inject<DialogData>(MAT_DIALOG_DATA);
  private dialogRef = inject(MatDialogRef);
  private loading = inject(LOADING);

  protected config = attachmentTypes().extend({
    sizeType: fields.select('single', [
      { value: 'KB', label: 'KB' },
      { value: 'MB', label: 'MB' },
      { value: 'GB', label: 'GB' },
    ])
  }).select({
    // reorder
    name: true,
    entityType: true,
    allowedSize: true,
    sizeType: true,
    allowedExtensions: true
  }).form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        const allowedSize = body.sizeType === 'KB'
          ? body.allowedSize * 1024
          : body.sizeType === 'MB'
            ? body.allowedSize * 1024 * 1024
            : body.allowedSize * 1024 * 1024 * 1024

        this.attachmentType
          .update({
            ...body,
            id: this.data.id,
            allowedSize,
            allowedExtensions: body.allowedExtensions.map(ext => ({ value: ext })),
          })
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::CreatedSuccessfully');
            this.dialogRef.close(true);
          })
      },
    },
    fields: {
      name: { inputSize: 'span 3' },
      entityType: {
        inputSize: 'span 3',
        label: '::EntityType',
      },
      allowedSize: {
        inputSize: 'span 2',
        label: '::AllowedSize',
      },
      allowedExtensions: {
        inputSize: 'span 3',
        label: '::AllowedExtensions',
      },
    },
    viewFunc: () => {
      const { size, unit } = bytesToFormattedSize(this.data.allowedSize);

      return of({
        ...this.data,
        sizeType: unit,
        allowedSize: parseInt(size.toFixed(2)),
      })
    }
  });
}

interface DialogData {
  id: string;
  name: string;
  allowedExtensions: string[];
  allowedSize: number;
}
