﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoldenOwl.Attachments.DTO;
using GoldenOwl.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.BlobStoring;
using Volo.Abp.Content;
using Volo.Abp.Domain.Repositories;

namespace GoldenOwl.Attachments;

[Authorize(GoldenOwlPermissions.AttachmentManagement)]
public class AttachmentAppService : GoldenOwlAppService, IAttachmentAppService
{
    private readonly IAttachmentManager _attachmentManager;
    private readonly IRepository<Attachment, Guid> _attachmentRepo;
    private readonly IBlobContainer<AttachmentContainer> _blobContainer;
    
    public AttachmentAppService(IAttachmentManager attachmentManager,
         IBlobContainer<AttachmentContainer> blobContainer, IRepository<Attachment, Guid> attachmentRepo)
    {
        _attachmentManager = attachmentManager;
        _blobContainer = blobContainer;
        _attachmentRepo = attachmentRepo;
    }

    public async Task<AttachmentResponseDto> CreateAsync(AttachmentRequestDto dto)
    {
        var attachment = await _attachmentManager.CreateTempAsync
        (
            dto.AttachmentTypeId,
            dto.FileName,
            dto.File.Length,
            dto.File,
            dto.ExpiryDate
        );

        var attachmentResponseDto = ObjectMapper.Map<Attachment, AttachmentResponseDto>(attachment);

        return attachmentResponseDto;
    }

    public async Task<List<AttachmentResponseDto>> GetListAsync(Guid bindEntityId)
    {
        var queryable = await _attachmentRepo.GetQueryableAsync();

        var attachments = await AsyncExecuter.ToListAsync(
            queryable
                .Where(x => x.BindEntityId == bindEntityId)
        );

        var response = ObjectMapper.Map<List<Attachment>, List<AttachmentResponseDto>>
            (attachments);
        return response;
    }


    public async Task SaveTempAsync(List<SaveTempDto> tempDtos)
    {
        foreach (var tempDto in tempDtos)
        {
           var attachment = await _attachmentRepo.GetAsync(tempDto.AttachmentId);
            await _attachmentManager.SaveTempAsync(attachment, tempDto.BindEntityId);
        }
    }
    
    public async Task<IRemoteStreamContent> GetAttachmentFileAsync(Guid id)
    {
        return await _attachmentManager.GetAttachmentFileAsync(id);
    }
    
    public async Task DeleteAsync(Guid id)
    {
        var attachment = await _attachmentRepo.SingleOrDefaultAsync(x => x.Id == id);

        if (attachment is null)
        {
            throw new BusinessException(L["NotFound"]);
        }

        if (attachment.BindEntityId is null)
        {
            await _blobContainer.DeleteAsync(attachment.Id.ToString());
        }

        await _attachmentRepo.DeleteAsync(attachment);
    }
}