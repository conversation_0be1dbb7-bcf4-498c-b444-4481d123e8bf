import { Component, DestroyRef, inject, signal } from '@angular/core';
import { penaltiesTypes } from './penalties-types.model';
import { PenaltyTypeService, SalaryEffectValueType } from '@proxy/hr/penalty-types';
import { requireAllOperator } from '@shared';
import { pagedMap, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { Subject } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  PenaltiesTypesCreateDialogComponent
} from './penalties-types-create-dialog/penalties-types-create-dialog.component';
import {
  PenaltiesTypesUpdateDialogComponent
} from './penalties-types-update-dialog/penalties-types-update-dialog.component';

@Component({
  selector: 'app-penalties-types',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `<ttwr-grid class="actions-end" [config]="config" />`,
})
export class PenaltiesTypesComponent {
  private penaltyType = inject(PenaltyTypeService);
  private dialog = inject(MatDialog);
  private destroyRef = inject(DestroyRef);

  private refreshSubject = new Subject<void>();

  protected config = penaltiesTypes.grid({
    refreshSubject: this.refreshSubject,
    title: '::PenaltiesTypes',
    dataFunc: pagination => this.penaltyType.getGetAllPenaltyTypesByInput({
      skipCount: pagination.pageSize * pagination.pageIndex,
      maxResultCount: pagination.pageSize
    }).pipe(
      requireAllOperator(),
      pagedMap(type => ({
        ...type,
        salaryEffectValueType: type.penaltySalaryEffect?.salaryEffectValueType,
        salaryEffectValue: type.penaltySalaryEffect?.salaryEffectValue,
      })),
    ),
    fields: {
      salaryEffectValueType: {
        hiddenSignal: signal(true),
      },
      salaryEffectValue: {
        columnName: '::PenaltyType:SalaryEffectValue',
        displayCell: (cell, field) => !cell
          ? '-'
          : field.salaryEffectValueType === SalaryEffectValueType.FixedAmount
          ? cell.toString()
          : `${cell}%`,
      },
      code: {
        columnName: '::PenaltyType:Code',
      },
      isTerminateContract: {
        columnName: '::PenaltyType:IsTerminateContract',
      },
    },
    actions: [
      {
        matIcon: 'add',
        tooltip: 'Add',
        delegateFunc: () => {
          const ref = this.dialog.open(PenaltiesTypesCreateDialogComponent, {
            width: '100%',
            maxWidth: '700px',
          });

          ref.afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        },
      }
    ],
    fieldActions: [
      {
        matIcon: 'edit',
        tooltip: 'Edit',
        delegateFunc: (obj) => {
          const ref = this.dialog.open(PenaltiesTypesUpdateDialogComponent, {
            width: '100%',
            maxWidth: '700px',
            data: obj,
          });

          ref.afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        },
      }
    ],
  });
}
