import { Component, DestroyRef, inject } from '@angular/core';
import { AlertService, LanguagePipe, LOADING, TtwrFormComponent } from '@ttwr-framework/ngx-main-visuals';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { finalize, of } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { salaryRulesCategories } from '../salary-rules-categories.model';
import { SalaryRuleCategoryService } from '@proxy/payroll/salary-rule-categories';

@Component({
  selector: 'app-salary-rules-categories-update-dialog',
  standalone: true,
  imports: [
    LanguagePipe,
    MatDialogContent,
    MatDialogTitle,
    TtwrFormComponent
  ],
  template: `
    <h2 mat-dialog-title>{{ '::UpdateSalaryRuleCategory' | i18n }}</h2>
    <mat-dialog-content>
      <ttwr-form [config]="config"/>
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class SalaryRulesCategoriesUpdateDialogComponent {
  private salaryRuleCategory = inject(SalaryRuleCategoryService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);
  private data = inject<DialogData>(MAT_DIALOG_DATA);
  private dialogRef = inject(MatDialogRef);

  protected config = salaryRulesCategories.form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        this.salaryRuleCategory.update(this.data.id, body)
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::UpdatedSuccessfully');
            this.dialogRef.close(true);
          })
      },
    },
    viewFunc: () => of(this.data),
    fields: {
      code: {
        label: '::GoldenOwl:Code',
      }
    },
  })
}

interface DialogData {
  id: string;
  name: string;
  code: string;
}
