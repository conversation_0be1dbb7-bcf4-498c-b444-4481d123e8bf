import { Component, input } from '@angular/core';
import { FieldType } from '@proxy/extra-field-definitions';
import { <PERSON><PERSON><PERSON>r, FormControl, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { <PERSON><PERSON><PERSON><PERSON>, Mat<PERSON>orm<PERSON>ield, <PERSON><PERSON><PERSON><PERSON>, MatSuffix } from '@angular/material/form-field';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatInput } from '@angular/material/input';
import {
  LanguagePipe,
  TtwrDatetimepicker,
  TtwrDatetimepickerInput,
  TtwrDatetimepickerToggle
} from '@ttwr-framework/ngx-main-visuals';
import { MatDatepicker, MatDatepickerInput, MatDatepickerToggle } from '@angular/material/datepicker';
import { MatOption, MatSelect } from '@angular/material/select';

@Component({
  selector: 'app-dynamic-form-field',
  standalone: true,
  imports: [
    MatFormField,
    <PERSON><PERSON>abe<PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    MatSuffix,
    ReactiveFormsModule,
    MatCheckbox,
    MatInput,
    LanguagePipe,
    MatDatepickerInput,
    Mat<PERSON>atepickerToggle,
    MatDatepicker,
    TtwrDatetimepickerInput,
    TtwrDatetimepickerToggle,
    TtwrDatetimepicker,
    MatSelect,
    MatOption,
  ],
  templateUrl: './dynamic-form-field.component.html',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective },
  ],
  styles: `
    mat-form-field {
      width: 100%;
    }
  `,
})
export class DynamicFormFieldComponent {
  public type = input.required<FieldType>();
  public control = input.required<FormControl>();
  public name = input.required<string>();
  public options = input<string[]>([]);

  protected fieldType = FieldType;
}
