import { FormFieldValidator } from '@ttwr-framework/ngx-main-visuals';

export const phoneNumberValidator: FormFieldValidator = {
  name: 'phoneNumber',
  validator: (control) => {
    if (!control.value) return null;

    const regex1 = /^[+][0-9]{9,12}$/;
    const regex2 = /^[0-9]{10,14}$/;

    const value = control.value as string;

    if (value.match(regex1) || value.match(regex2)) return null;

    return {
      'phoneNumber': true,
    };
  },
  message: '::GoldenOwl:PhoneNumberInvalidFormat',
}
