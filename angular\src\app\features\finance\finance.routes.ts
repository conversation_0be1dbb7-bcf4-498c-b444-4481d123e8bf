import { Routes } from '@angular/router';
import type { ABP } from '@abp/ng.core';
import { routes as currencyRoutes } from './currency/currency.routes';
import { FinancialAccountComponent } from './account/financial-account.component';
import { FiscalPeriodComponent } from './fiscal-period/fiscal-period.component';

export const routes: Routes = [
  {
    path: 'currency',
    children: currencyRoutes,
  },
  {
    path: 'financial-account',
    component: FinancialAccountComponent,
  },
  {
    path: 'fiscal-period',
    component: FiscalPeriodComponent,
  }
];

const financeRoute = '::FN';

export const abpRoutes: ABP.Route[] = [
  {
    group: 'FN',
    name: financeRoute,
    iconClass: 'local_atm',
    order: 7,
  },
  {
    path: '/finance/currency',
    name: '::GoldenOwl:CurrenciesAndExchangeRate',
    iconClass: 'attach_money',
    parentName: financeRoute,
    requiredPolicy: 'FN.CurrencyAndExchangeRate.Index',
  },
  {
    path: '/finance/financial-account',
    name: '::GoldenOwl:Accounts',
    iconClass: 'groups',
    parentName: financeRoute,
    requiredPolicy: 'FN.Account.Index',
  },
  {
    path: '/finance/fiscal-period',
    name: '::GoldenOwl:FiscalPeriod',
    iconClass: 'calendar_month',
    parentName: financeRoute,
    requiredPolicy: 'FN.FinancialPeriod.Index',
  },
];
