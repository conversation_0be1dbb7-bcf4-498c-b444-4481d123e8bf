{"version": 3, "sources": ["../../../../../../node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.js"], "sourcesContent": ["/*!\n * \n *               jsPDF AutoTable plugin v3.8.4\n *\n *               Copyright (c) 2024 <PERSON>, https://github.com/simonben<PERSON><PERSON>/jsPDF-AutoTable\n *               Licensed under the MIT License.\n *               http://opensource.org/licenses/mit-license\n *\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n  if (typeof exports === 'object' && typeof module === 'object') module.exports = factory(function webpackLoadOptionalExternalModule() {\n    try {\n      return require(\"jspdf\");\n    } catch (e) {}\n  }());else if (typeof define === 'function' && define.amd) define([\"jspdf\"], factory);else {\n    var a = typeof exports === 'object' ? factory(function webpackLoadOptionalExternalModule() {\n      try {\n        return require(\"jspdf\");\n      } catch (e) {}\n    }()) : factory(root[\"jspdf\"]);\n    for (var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n  }\n})(typeof globalThis !== 'undefined' ? globalThis : typeof this !== 'undefined' ? this : typeof window !== 'undefined' ? window : typeof self !== 'undefined' ? self : global, function (__WEBPACK_EXTERNAL_MODULE__964__) {\n  return /******/function () {\n    // webpackBootstrap\n    /******/\n    \"use strict\";\n\n    /******/\n    var __webpack_modules__ = {\n      /***/172: (/***/function (__unused_webpack_module, exports) {\n        var __extends = this && this.__extends || function () {\n          var extendStatics = function (d, b) {\n            extendStatics = Object.setPrototypeOf || {\n              __proto__: []\n            } instanceof Array && function (d, b) {\n              d.__proto__ = b;\n            } || function (d, b) {\n              for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n            };\n            return extendStatics(d, b);\n          };\n          return function (d, b) {\n            if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n            extendStatics(d, b);\n            function __() {\n              this.constructor = d;\n            }\n            d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n          };\n        }();\n        Object.defineProperty(exports, \"__esModule\", {\n          value: true\n        });\n        exports.CellHookData = exports.HookData = void 0;\n        var HookData = /** @class */function () {\n          function HookData(doc, table, cursor) {\n            this.table = table;\n            this.pageNumber = table.pageNumber;\n            this.pageCount = this.pageNumber;\n            this.settings = table.settings;\n            this.cursor = cursor;\n            this.doc = doc.getDocument();\n          }\n          return HookData;\n        }();\n        exports.HookData = HookData;\n        var CellHookData = /** @class */function (_super) {\n          __extends(CellHookData, _super);\n          function CellHookData(doc, table, cell, row, column, cursor) {\n            var _this = _super.call(this, doc, table, cursor) || this;\n            _this.cell = cell;\n            _this.row = row;\n            _this.column = column;\n            _this.section = row.section;\n            return _this;\n          }\n          return CellHookData;\n        }(HookData);\n        exports.CellHookData = CellHookData;\n\n        /***/\n      }),\n      /***/340: (/***/function (__unused_webpack_module, exports, __webpack_require__) {\n        Object.defineProperty(exports, \"__esModule\", {\n          value: true\n        });\n        var htmlParser_1 = __webpack_require__(4);\n        var autoTableText_1 = __webpack_require__(136);\n        var documentHandler_1 = __webpack_require__(744);\n        var inputParser_1 = __webpack_require__(776);\n        var tableDrawer_1 = __webpack_require__(664);\n        var tableCalculator_1 = __webpack_require__(972);\n        function default_1(jsPDF) {\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          jsPDF.API.autoTable = function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n              args[_i] = arguments[_i];\n            }\n            var options;\n            if (args.length === 1) {\n              options = args[0];\n            } else {\n              console.error('Use of deprecated autoTable initiation');\n              options = args[2] || {};\n              options.columns = args[0];\n              options.body = args[1];\n            }\n            var input = (0, inputParser_1.parseInput)(this, options);\n            var table = (0, tableCalculator_1.createTable)(this, input);\n            (0, tableDrawer_1.drawTable)(this, table);\n            return this;\n          };\n          // Assign false to enable `doc.lastAutoTable.finalY || 40` sugar\n          jsPDF.API.lastAutoTable = false;\n          jsPDF.API.previousAutoTable = false; // deprecated in v3\n          jsPDF.API.autoTable.previous = false; // deprecated in v3\n          jsPDF.API.autoTableText = function (text, x, y, styles) {\n            (0, autoTableText_1.default)(text, x, y, styles, this);\n          };\n          jsPDF.API.autoTableSetDefaults = function (defaults) {\n            documentHandler_1.DocHandler.setDefaults(defaults, this);\n            return this;\n          };\n          jsPDF.autoTableSetDefaults = function (defaults, doc) {\n            documentHandler_1.DocHandler.setDefaults(defaults, doc);\n          };\n          jsPDF.API.autoTableHtmlToJson = function (tableElem, includeHiddenElements) {\n            var _a;\n            if (includeHiddenElements === void 0) {\n              includeHiddenElements = false;\n            }\n            if (typeof window === 'undefined') {\n              console.error('Cannot run autoTableHtmlToJson in non browser environment');\n              return null;\n            }\n            var doc = new documentHandler_1.DocHandler(this);\n            var _b = (0, htmlParser_1.parseHtml)(doc, tableElem, window, includeHiddenElements, false),\n              head = _b.head,\n              body = _b.body;\n            var columns = ((_a = head[0]) === null || _a === void 0 ? void 0 : _a.map(function (c) {\n              return c.content;\n            })) || [];\n            return {\n              columns: columns,\n              rows: body,\n              data: body\n            };\n          };\n          /**\n           * @deprecated\n           */\n          jsPDF.API.autoTableEndPosY = function () {\n            console.error('Use of deprecated function: autoTableEndPosY. Use doc.lastAutoTable.finalY instead.');\n            var prev = this.lastAutoTable;\n            if (prev && prev.finalY) {\n              return prev.finalY;\n            } else {\n              return 0;\n            }\n          };\n          /**\n           * @deprecated\n           */\n          jsPDF.API.autoTableAddPageContent = function (hook) {\n            console.error('Use of deprecated function: autoTableAddPageContent. Use jsPDF.autoTableSetDefaults({didDrawPage: () => {}}) instead.');\n            if (!jsPDF.API.autoTable.globalDefaults) {\n              jsPDF.API.autoTable.globalDefaults = {};\n            }\n            jsPDF.API.autoTable.globalDefaults.addPageContent = hook;\n            return this;\n          };\n          /**\n           * @deprecated\n           */\n          jsPDF.API.autoTableAddPage = function () {\n            console.error('Use of deprecated function: autoTableAddPage. Use doc.addPage()');\n            this.addPage();\n            return this;\n          };\n        }\n        exports[\"default\"] = default_1;\n\n        /***/\n      }),\n      /***/136: (/***/function (__unused_webpack_module, exports) {\n        Object.defineProperty(exports, \"__esModule\", {\n          value: true\n        });\n        /**\n         * Improved text function with halign and valign support\n         * Inspiration from: http://stackoverflow.com/questions/28327510/align-text-right-using-jspdf/28433113#28433113\n         */\n        function default_1(text, x, y, styles, doc) {\n          styles = styles || {};\n          var PHYSICAL_LINE_HEIGHT = 1.15;\n          var k = doc.internal.scaleFactor;\n          var fontSize = doc.internal.getFontSize() / k;\n          var lineHeightFactor = doc.getLineHeightFactor ? doc.getLineHeightFactor() : PHYSICAL_LINE_HEIGHT;\n          var lineHeight = fontSize * lineHeightFactor;\n          var splitRegex = /\\r\\n|\\r|\\n/g;\n          var splitText = '';\n          var lineCount = 1;\n          if (styles.valign === 'middle' || styles.valign === 'bottom' || styles.halign === 'center' || styles.halign === 'right') {\n            splitText = typeof text === 'string' ? text.split(splitRegex) : text;\n            lineCount = splitText.length || 1;\n          }\n          // Align the top\n          y += fontSize * (2 - PHYSICAL_LINE_HEIGHT);\n          if (styles.valign === 'middle') y -= lineCount / 2 * lineHeight;else if (styles.valign === 'bottom') y -= lineCount * lineHeight;\n          if (styles.halign === 'center' || styles.halign === 'right') {\n            var alignSize = fontSize;\n            if (styles.halign === 'center') alignSize *= 0.5;\n            if (splitText && lineCount >= 1) {\n              for (var iLine = 0; iLine < splitText.length; iLine++) {\n                doc.text(splitText[iLine], x - doc.getStringUnitWidth(splitText[iLine]) * alignSize, y);\n                y += lineHeight;\n              }\n              return doc;\n            }\n            x -= doc.getStringUnitWidth(text) * alignSize;\n          }\n          if (styles.halign === 'justify') {\n            doc.text(text, x, y, {\n              maxWidth: styles.maxWidth || 100,\n              align: 'justify'\n            });\n          } else {\n            doc.text(text, x, y);\n          }\n          return doc;\n        }\n        exports[\"default\"] = default_1;\n\n        /***/\n      }),\n      /***/420: (/***/function (__unused_webpack_module, exports) {\n        Object.defineProperty(exports, \"__esModule\", {\n          value: true\n        });\n        exports.getPageAvailableWidth = exports.parseSpacing = exports.getFillStyle = exports.addTableBorder = exports.getStringWidth = void 0;\n        function getStringWidth(text, styles, doc) {\n          doc.applyStyles(styles, true);\n          var textArr = Array.isArray(text) ? text : [text];\n          var widestLineWidth = textArr.map(function (text) {\n            return doc.getTextWidth(text);\n          }).reduce(function (a, b) {\n            return Math.max(a, b);\n          }, 0);\n          return widestLineWidth;\n        }\n        exports.getStringWidth = getStringWidth;\n        function addTableBorder(doc, table, startPos, cursor) {\n          var lineWidth = table.settings.tableLineWidth;\n          var lineColor = table.settings.tableLineColor;\n          doc.applyStyles({\n            lineWidth: lineWidth,\n            lineColor: lineColor\n          });\n          var fillStyle = getFillStyle(lineWidth, false);\n          if (fillStyle) {\n            doc.rect(startPos.x, startPos.y, table.getWidth(doc.pageSize().width), cursor.y - startPos.y, fillStyle);\n          }\n        }\n        exports.addTableBorder = addTableBorder;\n        function getFillStyle(lineWidth, fillColor) {\n          var drawLine = lineWidth > 0;\n          var drawBackground = fillColor || fillColor === 0;\n          if (drawLine && drawBackground) {\n            return 'DF'; // Fill then stroke\n          } else if (drawLine) {\n            return 'S'; // Only stroke (transparent background)\n          } else if (drawBackground) {\n            return 'F'; // Only fill, no stroke\n          } else {\n            return null;\n          }\n        }\n        exports.getFillStyle = getFillStyle;\n        function parseSpacing(value, defaultValue) {\n          var _a, _b, _c, _d;\n          value = value || defaultValue;\n          if (Array.isArray(value)) {\n            if (value.length >= 4) {\n              return {\n                top: value[0],\n                right: value[1],\n                bottom: value[2],\n                left: value[3]\n              };\n            } else if (value.length === 3) {\n              return {\n                top: value[0],\n                right: value[1],\n                bottom: value[2],\n                left: value[1]\n              };\n            } else if (value.length === 2) {\n              return {\n                top: value[0],\n                right: value[1],\n                bottom: value[0],\n                left: value[1]\n              };\n            } else if (value.length === 1) {\n              value = value[0];\n            } else {\n              value = defaultValue;\n            }\n          }\n          if (typeof value === 'object') {\n            if (typeof value.vertical === 'number') {\n              value.top = value.vertical;\n              value.bottom = value.vertical;\n            }\n            if (typeof value.horizontal === 'number') {\n              value.right = value.horizontal;\n              value.left = value.horizontal;\n            }\n            return {\n              left: (_a = value.left) !== null && _a !== void 0 ? _a : defaultValue,\n              top: (_b = value.top) !== null && _b !== void 0 ? _b : defaultValue,\n              right: (_c = value.right) !== null && _c !== void 0 ? _c : defaultValue,\n              bottom: (_d = value.bottom) !== null && _d !== void 0 ? _d : defaultValue\n            };\n          }\n          if (typeof value !== 'number') {\n            value = defaultValue;\n          }\n          return {\n            top: value,\n            right: value,\n            bottom: value,\n            left: value\n          };\n        }\n        exports.parseSpacing = parseSpacing;\n        function getPageAvailableWidth(doc, table) {\n          var margins = parseSpacing(table.settings.margin, 0);\n          return doc.pageSize().width - (margins.left + margins.right);\n        }\n        exports.getPageAvailableWidth = getPageAvailableWidth;\n\n        /***/\n      }),\n      /***/796: (/***/function (__unused_webpack_module, exports) {\n        var __extends = this && this.__extends || function () {\n          var extendStatics = function (d, b) {\n            extendStatics = Object.setPrototypeOf || {\n              __proto__: []\n            } instanceof Array && function (d, b) {\n              d.__proto__ = b;\n            } || function (d, b) {\n              for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n            };\n            return extendStatics(d, b);\n          };\n          return function (d, b) {\n            if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n            extendStatics(d, b);\n            function __() {\n              this.constructor = d;\n            }\n            d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n          };\n        }();\n        Object.defineProperty(exports, \"__esModule\", {\n          value: true\n        });\n        exports.getTheme = exports.defaultStyles = exports.HtmlRowInput = void 0;\n        var HtmlRowInput = /** @class */function (_super) {\n          __extends(HtmlRowInput, _super);\n          function HtmlRowInput(element) {\n            var _this = _super.call(this) || this;\n            _this._element = element;\n            return _this;\n          }\n          return HtmlRowInput;\n        }(Array);\n        exports.HtmlRowInput = HtmlRowInput;\n        // Base style for all themes\n        function defaultStyles(scaleFactor) {\n          return {\n            font: 'helvetica',\n            // helvetica, times, courier\n            fontStyle: 'normal',\n            // normal, bold, italic, bolditalic\n            overflow: 'linebreak',\n            // linebreak, ellipsize, visible or hidden\n            fillColor: false,\n            // Either false for transparent, rbg array e.g. [255, 255, 255] or gray level e.g 200\n            textColor: 20,\n            halign: 'left',\n            // left, center, right, justify\n            valign: 'top',\n            // top, middle, bottom\n            fontSize: 10,\n            cellPadding: 5 / scaleFactor,\n            // number or {top,left,right,left,vertical,horizontal}\n            lineColor: 200,\n            lineWidth: 0,\n            cellWidth: 'auto',\n            // 'auto'|'wrap'|number\n            minCellHeight: 0,\n            minCellWidth: 0\n          };\n        }\n        exports.defaultStyles = defaultStyles;\n        function getTheme(name) {\n          var themes = {\n            striped: {\n              table: {\n                fillColor: 255,\n                textColor: 80,\n                fontStyle: 'normal'\n              },\n              head: {\n                textColor: 255,\n                fillColor: [41, 128, 185],\n                fontStyle: 'bold'\n              },\n              body: {},\n              foot: {\n                textColor: 255,\n                fillColor: [41, 128, 185],\n                fontStyle: 'bold'\n              },\n              alternateRow: {\n                fillColor: 245\n              }\n            },\n            grid: {\n              table: {\n                fillColor: 255,\n                textColor: 80,\n                fontStyle: 'normal',\n                lineWidth: 0.1\n              },\n              head: {\n                textColor: 255,\n                fillColor: [26, 188, 156],\n                fontStyle: 'bold',\n                lineWidth: 0\n              },\n              body: {},\n              foot: {\n                textColor: 255,\n                fillColor: [26, 188, 156],\n                fontStyle: 'bold',\n                lineWidth: 0\n              },\n              alternateRow: {}\n            },\n            plain: {\n              head: {\n                fontStyle: 'bold'\n              },\n              foot: {\n                fontStyle: 'bold'\n              }\n            }\n          };\n          return themes[name];\n        }\n        exports.getTheme = getTheme;\n\n        /***/\n      }),\n      /***/903: (/***/function (__unused_webpack_module, exports, __webpack_require__) {\n        Object.defineProperty(exports, \"__esModule\", {\n          value: true\n        });\n        exports.parseCss = void 0;\n        // Limitations\n        // - No support for border spacing\n        // - No support for transparency\n        var common_1 = __webpack_require__(420);\n        function parseCss(supportedFonts, element, scaleFactor, style, window) {\n          var result = {};\n          var pxScaleFactor = 96 / 72;\n          var backgroundColor = parseColor(element, function (elem) {\n            return window.getComputedStyle(elem)['backgroundColor'];\n          });\n          if (backgroundColor != null) result.fillColor = backgroundColor;\n          var textColor = parseColor(element, function (elem) {\n            return window.getComputedStyle(elem)['color'];\n          });\n          if (textColor != null) result.textColor = textColor;\n          var padding = parsePadding(style, scaleFactor);\n          if (padding) result.cellPadding = padding;\n          var borderColorSide = 'borderTopColor';\n          var finalScaleFactor = pxScaleFactor * scaleFactor;\n          var btw = style.borderTopWidth;\n          if (style.borderBottomWidth === btw && style.borderRightWidth === btw && style.borderLeftWidth === btw) {\n            var borderWidth = (parseFloat(btw) || 0) / finalScaleFactor;\n            if (borderWidth) result.lineWidth = borderWidth;\n          } else {\n            result.lineWidth = {\n              top: (parseFloat(style.borderTopWidth) || 0) / finalScaleFactor,\n              right: (parseFloat(style.borderRightWidth) || 0) / finalScaleFactor,\n              bottom: (parseFloat(style.borderBottomWidth) || 0) / finalScaleFactor,\n              left: (parseFloat(style.borderLeftWidth) || 0) / finalScaleFactor\n            };\n            // Choose border color of first available side\n            // could be improved by supporting object as lineColor\n            if (!result.lineWidth.top) {\n              if (result.lineWidth.right) {\n                borderColorSide = 'borderRightColor';\n              } else if (result.lineWidth.bottom) {\n                borderColorSide = 'borderBottomColor';\n              } else if (result.lineWidth.left) {\n                borderColorSide = 'borderLeftColor';\n              }\n            }\n          }\n          var borderColor = parseColor(element, function (elem) {\n            return window.getComputedStyle(elem)[borderColorSide];\n          });\n          if (borderColor != null) result.lineColor = borderColor;\n          var accepted = ['left', 'right', 'center', 'justify'];\n          if (accepted.indexOf(style.textAlign) !== -1) {\n            result.halign = style.textAlign;\n          }\n          accepted = ['middle', 'bottom', 'top'];\n          if (accepted.indexOf(style.verticalAlign) !== -1) {\n            result.valign = style.verticalAlign;\n          }\n          var res = parseInt(style.fontSize || '');\n          if (!isNaN(res)) result.fontSize = res / pxScaleFactor;\n          var fontStyle = parseFontStyle(style);\n          if (fontStyle) result.fontStyle = fontStyle;\n          var font = (style.fontFamily || '').toLowerCase();\n          if (supportedFonts.indexOf(font) !== -1) {\n            result.font = font;\n          }\n          return result;\n        }\n        exports.parseCss = parseCss;\n        function parseFontStyle(style) {\n          var res = '';\n          if (style.fontWeight === 'bold' || style.fontWeight === 'bolder' || parseInt(style.fontWeight) >= 700) {\n            res = 'bold';\n          }\n          if (style.fontStyle === 'italic' || style.fontStyle === 'oblique') {\n            res += 'italic';\n          }\n          return res;\n        }\n        function parseColor(element, styleGetter) {\n          var cssColor = realColor(element, styleGetter);\n          if (!cssColor) return null;\n          var rgba = cssColor.match(/^rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)(?:,\\s*(\\d*\\.?\\d*))?\\)$/);\n          if (!rgba || !Array.isArray(rgba)) {\n            return null;\n          }\n          var color = [parseInt(rgba[1]), parseInt(rgba[2]), parseInt(rgba[3])];\n          var alpha = parseInt(rgba[4]);\n          if (alpha === 0 || isNaN(color[0]) || isNaN(color[1]) || isNaN(color[2])) {\n            return null;\n          }\n          return color;\n        }\n        function realColor(elem, styleGetter) {\n          var bg = styleGetter(elem);\n          if (bg === 'rgba(0, 0, 0, 0)' || bg === 'transparent' || bg === 'initial' || bg === 'inherit') {\n            if (elem.parentElement == null) {\n              return null;\n            }\n            return realColor(elem.parentElement, styleGetter);\n          } else {\n            return bg;\n          }\n        }\n        function parsePadding(style, scaleFactor) {\n          var val = [style.paddingTop, style.paddingRight, style.paddingBottom, style.paddingLeft];\n          var pxScaleFactor = 96 / (72 / scaleFactor);\n          var linePadding = (parseInt(style.lineHeight) - parseInt(style.fontSize)) / scaleFactor / 2;\n          var inputPadding = val.map(function (n) {\n            return parseInt(n || '0') / pxScaleFactor;\n          });\n          var padding = (0, common_1.parseSpacing)(inputPadding, 0);\n          if (linePadding > padding.top) {\n            padding.top = linePadding;\n          }\n          if (linePadding > padding.bottom) {\n            padding.bottom = linePadding;\n          }\n          return padding;\n        }\n\n        /***/\n      }),\n      /***/744: (/***/function (__unused_webpack_module, exports) {\n        Object.defineProperty(exports, \"__esModule\", {\n          value: true\n        });\n        exports.DocHandler = void 0;\n        var globalDefaults = {};\n        var DocHandler = /** @class */function () {\n          function DocHandler(jsPDFDocument) {\n            this.jsPDFDocument = jsPDFDocument;\n            this.userStyles = {\n              // Black for versions of jspdf without getTextColor\n              textColor: jsPDFDocument.getTextColor ? this.jsPDFDocument.getTextColor() : 0,\n              fontSize: jsPDFDocument.internal.getFontSize(),\n              fontStyle: jsPDFDocument.internal.getFont().fontStyle,\n              font: jsPDFDocument.internal.getFont().fontName,\n              // 0 for versions of jspdf without getLineWidth\n              lineWidth: jsPDFDocument.getLineWidth ? this.jsPDFDocument.getLineWidth() : 0,\n              // Black for versions of jspdf without getDrawColor\n              lineColor: jsPDFDocument.getDrawColor ? this.jsPDFDocument.getDrawColor() : 0\n            };\n          }\n          DocHandler.setDefaults = function (defaults, doc) {\n            if (doc === void 0) {\n              doc = null;\n            }\n            if (doc) {\n              doc.__autoTableDocumentDefaults = defaults;\n            } else {\n              globalDefaults = defaults;\n            }\n          };\n          DocHandler.unifyColor = function (c) {\n            if (Array.isArray(c)) {\n              return c;\n            } else if (typeof c === 'number') {\n              return [c, c, c];\n            } else if (typeof c === 'string') {\n              return [c];\n            } else {\n              return null;\n            }\n          };\n          DocHandler.prototype.applyStyles = function (styles, fontOnly) {\n            // Font style needs to be applied before font\n            // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/632\n            var _a, _b, _c;\n            if (fontOnly === void 0) {\n              fontOnly = false;\n            }\n            if (styles.fontStyle) this.jsPDFDocument.setFontStyle && this.jsPDFDocument.setFontStyle(styles.fontStyle);\n            var _d = this.jsPDFDocument.internal.getFont(),\n              fontStyle = _d.fontStyle,\n              fontName = _d.fontName;\n            if (styles.font) fontName = styles.font;\n            if (styles.fontStyle) {\n              fontStyle = styles.fontStyle;\n              var availableFontStyles = this.getFontList()[fontName];\n              if (availableFontStyles && availableFontStyles.indexOf(fontStyle) === -1) {\n                // Common issue was that the default bold in headers\n                // made custom fonts not work. For example:\n                // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/653\n                this.jsPDFDocument.setFontStyle && this.jsPDFDocument.setFontStyle(availableFontStyles[0]);\n                fontStyle = availableFontStyles[0];\n              }\n            }\n            this.jsPDFDocument.setFont(fontName, fontStyle);\n            if (styles.fontSize) this.jsPDFDocument.setFontSize(styles.fontSize);\n            if (fontOnly) {\n              return; // Performance improvement\n            }\n            var color = DocHandler.unifyColor(styles.fillColor);\n            if (color) (_a = this.jsPDFDocument).setFillColor.apply(_a, color);\n            color = DocHandler.unifyColor(styles.textColor);\n            if (color) (_b = this.jsPDFDocument).setTextColor.apply(_b, color);\n            color = DocHandler.unifyColor(styles.lineColor);\n            if (color) (_c = this.jsPDFDocument).setDrawColor.apply(_c, color);\n            if (typeof styles.lineWidth === 'number') {\n              this.jsPDFDocument.setLineWidth(styles.lineWidth);\n            }\n          };\n          DocHandler.prototype.splitTextToSize = function (text, size, opts) {\n            return this.jsPDFDocument.splitTextToSize(text, size, opts);\n          };\n          /**\n           * Adds a rectangle to the PDF\n           * @param x Coordinate (in units declared at inception of PDF document) against left edge of the page\n           * @param y Coordinate (in units declared at inception of PDF document) against upper edge of the page\n           * @param width Width (in units declared at inception of PDF document)\n           * @param height Height (in units declared at inception of PDF document)\n           * @param fillStyle A string specifying the painting style or null. Valid styles include: 'S' [default] - stroke, 'F' - fill, and 'DF' (or 'FD') - fill then stroke.\n           */\n          DocHandler.prototype.rect = function (x, y, width, height, fillStyle) {\n            // null is excluded from fillStyle possible values because it isn't needed\n            // and is prone to bugs as it's used to postpone setting the style\n            // https://rawgit.com/MrRio/jsPDF/master/docs/jsPDF.html#rect\n            return this.jsPDFDocument.rect(x, y, width, height, fillStyle);\n          };\n          DocHandler.prototype.getLastAutoTable = function () {\n            return this.jsPDFDocument.lastAutoTable || null;\n          };\n          DocHandler.prototype.getTextWidth = function (text) {\n            return this.jsPDFDocument.getTextWidth(text);\n          };\n          DocHandler.prototype.getDocument = function () {\n            return this.jsPDFDocument;\n          };\n          DocHandler.prototype.setPage = function (page) {\n            this.jsPDFDocument.setPage(page);\n          };\n          DocHandler.prototype.addPage = function () {\n            return this.jsPDFDocument.addPage();\n          };\n          DocHandler.prototype.getFontList = function () {\n            return this.jsPDFDocument.getFontList();\n          };\n          DocHandler.prototype.getGlobalOptions = function () {\n            return globalDefaults || {};\n          };\n          DocHandler.prototype.getDocumentOptions = function () {\n            return this.jsPDFDocument.__autoTableDocumentDefaults || {};\n          };\n          DocHandler.prototype.pageSize = function () {\n            var pageSize = this.jsPDFDocument.internal.pageSize;\n            // JSPDF 1.4 uses get functions instead of properties on pageSize\n            if (pageSize.width == null) {\n              pageSize = {\n                width: pageSize.getWidth(),\n                height: pageSize.getHeight()\n              };\n            }\n            return pageSize;\n          };\n          DocHandler.prototype.scaleFactor = function () {\n            return this.jsPDFDocument.internal.scaleFactor;\n          };\n          DocHandler.prototype.getLineHeightFactor = function () {\n            var doc = this.jsPDFDocument;\n            return doc.getLineHeightFactor ? doc.getLineHeightFactor() : 1.15;\n          };\n          DocHandler.prototype.getLineHeight = function (fontSize) {\n            return fontSize / this.scaleFactor() * this.getLineHeightFactor();\n          };\n          DocHandler.prototype.pageNumber = function () {\n            var pageInfo = this.jsPDFDocument.internal.getCurrentPageInfo();\n            if (!pageInfo) {\n              // Only recent versions of jspdf has pageInfo\n              return this.jsPDFDocument.internal.getNumberOfPages();\n            }\n            return pageInfo.pageNumber;\n          };\n          return DocHandler;\n        }();\n        exports.DocHandler = DocHandler;\n\n        /***/\n      }),\n      /***/4: (/***/function (__unused_webpack_module, exports, __webpack_require__) {\n        Object.defineProperty(exports, \"__esModule\", {\n          value: true\n        });\n        exports.parseHtml = void 0;\n        var cssParser_1 = __webpack_require__(903);\n        var config_1 = __webpack_require__(796);\n        function parseHtml(doc, input, window, includeHiddenHtml, useCss) {\n          var _a, _b;\n          if (includeHiddenHtml === void 0) {\n            includeHiddenHtml = false;\n          }\n          if (useCss === void 0) {\n            useCss = false;\n          }\n          var tableElement;\n          if (typeof input === 'string') {\n            tableElement = window.document.querySelector(input);\n          } else {\n            tableElement = input;\n          }\n          var supportedFonts = Object.keys(doc.getFontList());\n          var scaleFactor = doc.scaleFactor();\n          var head = [],\n            body = [],\n            foot = [];\n          if (!tableElement) {\n            console.error('Html table could not be found with input: ', input);\n            return {\n              head: head,\n              body: body,\n              foot: foot\n            };\n          }\n          for (var i = 0; i < tableElement.rows.length; i++) {\n            var element = tableElement.rows[i];\n            var tagName = (_b = (_a = element === null || element === void 0 ? void 0 : element.parentElement) === null || _a === void 0 ? void 0 : _a.tagName) === null || _b === void 0 ? void 0 : _b.toLowerCase();\n            var row = parseRowContent(supportedFonts, scaleFactor, window, element, includeHiddenHtml, useCss);\n            if (!row) continue;\n            if (tagName === 'thead') {\n              head.push(row);\n            } else if (tagName === 'tfoot') {\n              foot.push(row);\n            } else {\n              // Add to body both if parent is tbody or table\n              body.push(row);\n            }\n          }\n          return {\n            head: head,\n            body: body,\n            foot: foot\n          };\n        }\n        exports.parseHtml = parseHtml;\n        function parseRowContent(supportedFonts, scaleFactor, window, row, includeHidden, useCss) {\n          var resultRow = new config_1.HtmlRowInput(row);\n          for (var i = 0; i < row.cells.length; i++) {\n            var cell = row.cells[i];\n            var style_1 = window.getComputedStyle(cell);\n            if (includeHidden || style_1.display !== 'none') {\n              var cellStyles = void 0;\n              if (useCss) {\n                cellStyles = (0, cssParser_1.parseCss)(supportedFonts, cell, scaleFactor, style_1, window);\n              }\n              resultRow.push({\n                rowSpan: cell.rowSpan,\n                colSpan: cell.colSpan,\n                styles: cellStyles,\n                _element: cell,\n                content: parseCellContent(cell)\n              });\n            }\n          }\n          var style = window.getComputedStyle(row);\n          if (resultRow.length > 0 && (includeHidden || style.display !== 'none')) {\n            return resultRow;\n          }\n        }\n        function parseCellContent(orgCell) {\n          // Work on cloned node to make sure no changes are applied to html table\n          var cell = orgCell.cloneNode(true);\n          // Remove extra space and line breaks in markup to make it more similar to\n          // what would be shown in html\n          cell.innerHTML = cell.innerHTML.replace(/\\n/g, '').replace(/ +/g, ' ');\n          // Preserve <br> tags as line breaks in the pdf\n          cell.innerHTML = cell.innerHTML.split(/<br.*?>/) //start with '<br' and ends with '>'.\n          .map(function (part) {\n            return part.trim();\n          }).join('\\n');\n          // innerText for ie\n          return cell.innerText || cell.textContent || '';\n        }\n\n        /***/\n      }),\n      /***/776: (/***/function (__unused_webpack_module, exports, __webpack_require__) {\n        Object.defineProperty(exports, \"__esModule\", {\n          value: true\n        });\n        exports.parseInput = void 0;\n        var htmlParser_1 = __webpack_require__(4);\n        var polyfills_1 = __webpack_require__(356);\n        var common_1 = __webpack_require__(420);\n        var documentHandler_1 = __webpack_require__(744);\n        var inputValidator_1 = __webpack_require__(792);\n        function parseInput(d, current) {\n          var doc = new documentHandler_1.DocHandler(d);\n          var document = doc.getDocumentOptions();\n          var global = doc.getGlobalOptions();\n          (0, inputValidator_1.default)(doc, global, document, current);\n          var options = (0, polyfills_1.assign)({}, global, document, current);\n          var win;\n          if (typeof window !== 'undefined') {\n            win = window;\n          }\n          var styles = parseStyles(global, document, current);\n          var hooks = parseHooks(global, document, current);\n          var settings = parseSettings(doc, options);\n          var content = parseContent(doc, options, win);\n          return {\n            id: current.tableId,\n            content: content,\n            hooks: hooks,\n            styles: styles,\n            settings: settings\n          };\n        }\n        exports.parseInput = parseInput;\n        function parseStyles(gInput, dInput, cInput) {\n          var styleOptions = {\n            styles: {},\n            headStyles: {},\n            bodyStyles: {},\n            footStyles: {},\n            alternateRowStyles: {},\n            columnStyles: {}\n          };\n          var _loop_1 = function (prop) {\n            if (prop === 'columnStyles') {\n              var global_1 = gInput[prop];\n              var document_1 = dInput[prop];\n              var current = cInput[prop];\n              styleOptions.columnStyles = (0, polyfills_1.assign)({}, global_1, document_1, current);\n            } else {\n              var allOptions = [gInput, dInput, cInput];\n              var styles = allOptions.map(function (opts) {\n                return opts[prop] || {};\n              });\n              styleOptions[prop] = (0, polyfills_1.assign)({}, styles[0], styles[1], styles[2]);\n            }\n          };\n          for (var _i = 0, _a = Object.keys(styleOptions); _i < _a.length; _i++) {\n            var prop = _a[_i];\n            _loop_1(prop);\n          }\n          return styleOptions;\n        }\n        function parseHooks(global, document, current) {\n          var allOptions = [global, document, current];\n          var result = {\n            didParseCell: [],\n            willDrawCell: [],\n            didDrawCell: [],\n            willDrawPage: [],\n            didDrawPage: []\n          };\n          for (var _i = 0, allOptions_1 = allOptions; _i < allOptions_1.length; _i++) {\n            var options = allOptions_1[_i];\n            if (options.didParseCell) result.didParseCell.push(options.didParseCell);\n            if (options.willDrawCell) result.willDrawCell.push(options.willDrawCell);\n            if (options.didDrawCell) result.didDrawCell.push(options.didDrawCell);\n            if (options.willDrawPage) result.willDrawPage.push(options.willDrawPage);\n            if (options.didDrawPage) result.didDrawPage.push(options.didDrawPage);\n          }\n          return result;\n        }\n        function parseSettings(doc, options) {\n          var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;\n          var margin = (0, common_1.parseSpacing)(options.margin, 40 / doc.scaleFactor());\n          var startY = (_a = getStartY(doc, options.startY)) !== null && _a !== void 0 ? _a : margin.top;\n          var showFoot;\n          if (options.showFoot === true) {\n            showFoot = 'everyPage';\n          } else if (options.showFoot === false) {\n            showFoot = 'never';\n          } else {\n            showFoot = (_b = options.showFoot) !== null && _b !== void 0 ? _b : 'everyPage';\n          }\n          var showHead;\n          if (options.showHead === true) {\n            showHead = 'everyPage';\n          } else if (options.showHead === false) {\n            showHead = 'never';\n          } else {\n            showHead = (_c = options.showHead) !== null && _c !== void 0 ? _c : 'everyPage';\n          }\n          var useCss = (_d = options.useCss) !== null && _d !== void 0 ? _d : false;\n          var theme = options.theme || (useCss ? 'plain' : 'striped');\n          var horizontalPageBreak = !!options.horizontalPageBreak;\n          var horizontalPageBreakRepeat = (_e = options.horizontalPageBreakRepeat) !== null && _e !== void 0 ? _e : null;\n          return {\n            includeHiddenHtml: (_f = options.includeHiddenHtml) !== null && _f !== void 0 ? _f : false,\n            useCss: useCss,\n            theme: theme,\n            startY: startY,\n            margin: margin,\n            pageBreak: (_g = options.pageBreak) !== null && _g !== void 0 ? _g : 'auto',\n            rowPageBreak: (_h = options.rowPageBreak) !== null && _h !== void 0 ? _h : 'auto',\n            tableWidth: (_j = options.tableWidth) !== null && _j !== void 0 ? _j : 'auto',\n            showHead: showHead,\n            showFoot: showFoot,\n            tableLineWidth: (_k = options.tableLineWidth) !== null && _k !== void 0 ? _k : 0,\n            tableLineColor: (_l = options.tableLineColor) !== null && _l !== void 0 ? _l : 200,\n            horizontalPageBreak: horizontalPageBreak,\n            horizontalPageBreakRepeat: horizontalPageBreakRepeat,\n            horizontalPageBreakBehaviour: (_m = options.horizontalPageBreakBehaviour) !== null && _m !== void 0 ? _m : 'afterAllRows'\n          };\n        }\n        function getStartY(doc, userStartY) {\n          var previous = doc.getLastAutoTable();\n          var sf = doc.scaleFactor();\n          var currentPage = doc.pageNumber();\n          var isSamePageAsPreviousTable = false;\n          if (previous && previous.startPageNumber) {\n            var endingPage = previous.startPageNumber + previous.pageNumber - 1;\n            isSamePageAsPreviousTable = endingPage === currentPage;\n          }\n          if (typeof userStartY === 'number') {\n            return userStartY;\n          } else if (userStartY == null || userStartY === false) {\n            if (isSamePageAsPreviousTable && (previous === null || previous === void 0 ? void 0 : previous.finalY) != null) {\n              // Some users had issues with overlapping tables when they used multiple\n              // tables without setting startY so setting it here to a sensible default.\n              return previous.finalY + 20 / sf;\n            }\n          }\n          return null;\n        }\n        function parseContent(doc, options, window) {\n          var head = options.head || [];\n          var body = options.body || [];\n          var foot = options.foot || [];\n          if (options.html) {\n            var hidden = options.includeHiddenHtml;\n            if (window) {\n              var htmlContent = (0, htmlParser_1.parseHtml)(doc, options.html, window, hidden, options.useCss) || {};\n              head = htmlContent.head || head;\n              body = htmlContent.body || head;\n              foot = htmlContent.foot || head;\n            } else {\n              console.error('Cannot parse html in non browser environment');\n            }\n          }\n          var columns = options.columns || parseColumns(head, body, foot);\n          return {\n            columns: columns,\n            head: head,\n            body: body,\n            foot: foot\n          };\n        }\n        function parseColumns(head, body, foot) {\n          var firstRow = head[0] || body[0] || foot[0] || [];\n          var result = [];\n          Object.keys(firstRow).filter(function (key) {\n            return key !== '_element';\n          }).forEach(function (key) {\n            var colSpan = 1;\n            var input;\n            if (Array.isArray(firstRow)) {\n              input = firstRow[parseInt(key)];\n            } else {\n              input = firstRow[key];\n            }\n            if (typeof input === 'object' && !Array.isArray(input)) {\n              colSpan = (input === null || input === void 0 ? void 0 : input.colSpan) || 1;\n            }\n            for (var i = 0; i < colSpan; i++) {\n              var id = void 0;\n              if (Array.isArray(firstRow)) {\n                id = result.length;\n              } else {\n                id = key + (i > 0 ? \"_\".concat(i) : '');\n              }\n              var rowResult = {\n                dataKey: id\n              };\n              result.push(rowResult);\n            }\n          });\n          return result;\n        }\n\n        /***/\n      }),\n      /***/792: (/***/function (__unused_webpack_module, exports) {\n        Object.defineProperty(exports, \"__esModule\", {\n          value: true\n        });\n        function default_1(doc, global, document, current) {\n          var _loop_1 = function (options) {\n            if (options && typeof options !== 'object') {\n              console.error('The options parameter should be of type object, is: ' + typeof options);\n            }\n            if (typeof options.extendWidth !== 'undefined') {\n              options.tableWidth = options.extendWidth ? 'auto' : 'wrap';\n              console.error('Use of deprecated option: extendWidth, use tableWidth instead.');\n            }\n            if (typeof options.margins !== 'undefined') {\n              if (typeof options.margin === 'undefined') options.margin = options.margins;\n              console.error('Use of deprecated option: margins, use margin instead.');\n            }\n            if (options.startY && typeof options.startY !== 'number') {\n              console.error('Invalid value for startY option', options.startY);\n              delete options.startY;\n            }\n            if (!options.didDrawPage && (options.afterPageContent || options.beforePageContent || options.afterPageAdd)) {\n              console.error('The afterPageContent, beforePageContent and afterPageAdd hooks are deprecated. Use didDrawPage instead');\n              options.didDrawPage = function (data) {\n                doc.applyStyles(doc.userStyles);\n                if (options.beforePageContent) options.beforePageContent(data);\n                doc.applyStyles(doc.userStyles);\n                if (options.afterPageContent) options.afterPageContent(data);\n                doc.applyStyles(doc.userStyles);\n                if (options.afterPageAdd && data.pageNumber > 1) {\n                  ;\n                  data.afterPageAdd(data);\n                }\n                doc.applyStyles(doc.userStyles);\n              };\n            }\n            ;\n            ['createdHeaderCell', 'drawHeaderRow', 'drawRow', 'drawHeaderCell'].forEach(function (name) {\n              if (options[name]) {\n                console.error(\"The \\\"\".concat(name, \"\\\" hook has changed in version 3.0, check the changelog for how to migrate.\"));\n              }\n            });\n            [['showFoot', 'showFooter'], ['showHead', 'showHeader'], ['didDrawPage', 'addPageContent'], ['didParseCell', 'createdCell'], ['headStyles', 'headerStyles']].forEach(function (_a) {\n              var current = _a[0],\n                deprecated = _a[1];\n              if (options[deprecated]) {\n                console.error(\"Use of deprecated option \".concat(deprecated, \". Use \").concat(current, \" instead\"));\n                options[current] = options[deprecated];\n              }\n            });\n            [['padding', 'cellPadding'], ['lineHeight', 'rowHeight'], 'fontSize', 'overflow'].forEach(function (o) {\n              var deprecatedOption = typeof o === 'string' ? o : o[0];\n              var style = typeof o === 'string' ? o : o[1];\n              if (typeof options[deprecatedOption] !== 'undefined') {\n                if (typeof options.styles[style] === 'undefined') {\n                  options.styles[style] = options[deprecatedOption];\n                }\n                console.error('Use of deprecated option: ' + deprecatedOption + ', use the style ' + style + ' instead.');\n              }\n            });\n            for (var _b = 0, _c = ['styles', 'bodyStyles', 'headStyles', 'footStyles']; _b < _c.length; _b++) {\n              var styleProp = _c[_b];\n              checkStyles(options[styleProp] || {});\n            }\n            var columnStyles = options['columnStyles'] || {};\n            for (var _d = 0, _e = Object.keys(columnStyles); _d < _e.length; _d++) {\n              var key = _e[_d];\n              checkStyles(columnStyles[key] || {});\n            }\n          };\n          for (var _i = 0, _a = [global, document, current]; _i < _a.length; _i++) {\n            var options = _a[_i];\n            _loop_1(options);\n          }\n        }\n        exports[\"default\"] = default_1;\n        function checkStyles(styles) {\n          if (styles.rowHeight) {\n            console.error('Use of deprecated style rowHeight. It is renamed to minCellHeight.');\n            if (!styles.minCellHeight) {\n              styles.minCellHeight = styles.rowHeight;\n            }\n          } else if (styles.columnWidth) {\n            console.error('Use of deprecated style columnWidth. It is renamed to cellWidth.');\n            if (!styles.cellWidth) {\n              styles.cellWidth = styles.columnWidth;\n            }\n          }\n        }\n\n        /***/\n      }),\n      /***/260: (/***/function (__unused_webpack_module, exports, __webpack_require__) {\n        Object.defineProperty(exports, \"__esModule\", {\n          value: true\n        });\n        exports.Column = exports.Cell = exports.Row = exports.Table = void 0;\n        var config_1 = __webpack_require__(796);\n        var HookData_1 = __webpack_require__(172);\n        var common_1 = __webpack_require__(420);\n        var Table = /** @class */function () {\n          function Table(input, content) {\n            this.pageNumber = 1;\n            // Deprecated, use pageNumber instead\n            // Not using getter since:\n            // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/596\n            this.pageCount = 1;\n            this.id = input.id;\n            this.settings = input.settings;\n            this.styles = input.styles;\n            this.hooks = input.hooks;\n            this.columns = content.columns;\n            this.head = content.head;\n            this.body = content.body;\n            this.foot = content.foot;\n          }\n          Table.prototype.getHeadHeight = function (columns) {\n            return this.head.reduce(function (acc, row) {\n              return acc + row.getMaxCellHeight(columns);\n            }, 0);\n          };\n          Table.prototype.getFootHeight = function (columns) {\n            return this.foot.reduce(function (acc, row) {\n              return acc + row.getMaxCellHeight(columns);\n            }, 0);\n          };\n          Table.prototype.allRows = function () {\n            return this.head.concat(this.body).concat(this.foot);\n          };\n          Table.prototype.callCellHooks = function (doc, handlers, cell, row, column, cursor) {\n            for (var _i = 0, handlers_1 = handlers; _i < handlers_1.length; _i++) {\n              var handler = handlers_1[_i];\n              var data = new HookData_1.CellHookData(doc, this, cell, row, column, cursor);\n              var result = handler(data) === false;\n              // Make sure text is always string[] since user can assign string\n              cell.text = Array.isArray(cell.text) ? cell.text : [cell.text];\n              if (result) {\n                return false;\n              }\n            }\n            return true;\n          };\n          Table.prototype.callEndPageHooks = function (doc, cursor) {\n            doc.applyStyles(doc.userStyles);\n            for (var _i = 0, _a = this.hooks.didDrawPage; _i < _a.length; _i++) {\n              var handler = _a[_i];\n              handler(new HookData_1.HookData(doc, this, cursor));\n            }\n          };\n          Table.prototype.callWillDrawPageHooks = function (doc, cursor) {\n            for (var _i = 0, _a = this.hooks.willDrawPage; _i < _a.length; _i++) {\n              var handler = _a[_i];\n              handler(new HookData_1.HookData(doc, this, cursor));\n            }\n          };\n          Table.prototype.getWidth = function (pageWidth) {\n            if (typeof this.settings.tableWidth === 'number') {\n              return this.settings.tableWidth;\n            } else if (this.settings.tableWidth === 'wrap') {\n              var wrappedWidth = this.columns.reduce(function (total, col) {\n                return total + col.wrappedWidth;\n              }, 0);\n              return wrappedWidth;\n            } else {\n              var margin = this.settings.margin;\n              return pageWidth - margin.left - margin.right;\n            }\n          };\n          return Table;\n        }();\n        exports.Table = Table;\n        var Row = /** @class */function () {\n          function Row(raw, index, section, cells, spansMultiplePages) {\n            if (spansMultiplePages === void 0) {\n              spansMultiplePages = false;\n            }\n            this.height = 0;\n            this.raw = raw;\n            if (raw instanceof config_1.HtmlRowInput) {\n              this.raw = raw._element;\n              this.element = raw._element;\n            }\n            this.index = index;\n            this.section = section;\n            this.cells = cells;\n            this.spansMultiplePages = spansMultiplePages;\n          }\n          Row.prototype.getMaxCellHeight = function (columns) {\n            var _this = this;\n            return columns.reduce(function (acc, column) {\n              var _a;\n              return Math.max(acc, ((_a = _this.cells[column.index]) === null || _a === void 0 ? void 0 : _a.height) || 0);\n            }, 0);\n          };\n          Row.prototype.hasRowSpan = function (columns) {\n            var _this = this;\n            return columns.filter(function (column) {\n              var cell = _this.cells[column.index];\n              if (!cell) return false;\n              return cell.rowSpan > 1;\n            }).length > 0;\n          };\n          Row.prototype.canEntireRowFit = function (height, columns) {\n            return this.getMaxCellHeight(columns) <= height;\n          };\n          Row.prototype.getMinimumRowHeight = function (columns, doc) {\n            var _this = this;\n            return columns.reduce(function (acc, column) {\n              var cell = _this.cells[column.index];\n              if (!cell) return 0;\n              var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n              var vPadding = cell.padding('vertical');\n              var oneRowHeight = vPadding + lineHeight;\n              return oneRowHeight > acc ? oneRowHeight : acc;\n            }, 0);\n          };\n          return Row;\n        }();\n        exports.Row = Row;\n        var Cell = /** @class */function () {\n          function Cell(raw, styles, section) {\n            var _a, _b;\n            this.contentHeight = 0;\n            this.contentWidth = 0;\n            this.wrappedWidth = 0;\n            this.minReadableWidth = 0;\n            this.minWidth = 0;\n            this.width = 0;\n            this.height = 0;\n            this.x = 0;\n            this.y = 0;\n            this.styles = styles;\n            this.section = section;\n            this.raw = raw;\n            var content = raw;\n            if (raw != null && typeof raw === 'object' && !Array.isArray(raw)) {\n              this.rowSpan = raw.rowSpan || 1;\n              this.colSpan = raw.colSpan || 1;\n              content = (_b = (_a = raw.content) !== null && _a !== void 0 ? _a : raw.title) !== null && _b !== void 0 ? _b : raw;\n              if (raw._element) {\n                this.raw = raw._element;\n              }\n            } else {\n              this.rowSpan = 1;\n              this.colSpan = 1;\n            }\n            // Stringify 0 and false, but not undefined or null\n            var text = content != null ? '' + content : '';\n            var splitRegex = /\\r\\n|\\r|\\n/g;\n            this.text = text.split(splitRegex);\n          }\n          Cell.prototype.getTextPos = function () {\n            var y;\n            if (this.styles.valign === 'top') {\n              y = this.y + this.padding('top');\n            } else if (this.styles.valign === 'bottom') {\n              y = this.y + this.height - this.padding('bottom');\n            } else {\n              var netHeight = this.height - this.padding('vertical');\n              y = this.y + netHeight / 2 + this.padding('top');\n            }\n            var x;\n            if (this.styles.halign === 'right') {\n              x = this.x + this.width - this.padding('right');\n            } else if (this.styles.halign === 'center') {\n              var netWidth = this.width - this.padding('horizontal');\n              x = this.x + netWidth / 2 + this.padding('left');\n            } else {\n              x = this.x + this.padding('left');\n            }\n            return {\n              x: x,\n              y: y\n            };\n          };\n          // TODO (v4): replace parameters with only (lineHeight)\n          Cell.prototype.getContentHeight = function (scaleFactor, lineHeightFactor) {\n            if (lineHeightFactor === void 0) {\n              lineHeightFactor = 1.15;\n            }\n            var lineCount = Array.isArray(this.text) ? this.text.length : 1;\n            var lineHeight = this.styles.fontSize / scaleFactor * lineHeightFactor;\n            var height = lineCount * lineHeight + this.padding('vertical');\n            return Math.max(height, this.styles.minCellHeight);\n          };\n          Cell.prototype.padding = function (name) {\n            var padding = (0, common_1.parseSpacing)(this.styles.cellPadding, 0);\n            if (name === 'vertical') {\n              return padding.top + padding.bottom;\n            } else if (name === 'horizontal') {\n              return padding.left + padding.right;\n            } else {\n              return padding[name];\n            }\n          };\n          return Cell;\n        }();\n        exports.Cell = Cell;\n        var Column = /** @class */function () {\n          function Column(dataKey, raw, index) {\n            this.wrappedWidth = 0;\n            this.minReadableWidth = 0;\n            this.minWidth = 0;\n            this.width = 0;\n            this.dataKey = dataKey;\n            this.raw = raw;\n            this.index = index;\n          }\n          Column.prototype.getMaxCustomCellWidth = function (table) {\n            var max = 0;\n            for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n              var row = _a[_i];\n              var cell = row.cells[this.index];\n              if (cell && typeof cell.styles.cellWidth === 'number') {\n                max = Math.max(max, cell.styles.cellWidth);\n              }\n            }\n            return max;\n          };\n          return Column;\n        }();\n        exports.Column = Column;\n\n        /***/\n      }),\n      /***/356: (/***/function (__unused_webpack_module, exports) {\n        /* eslint-disable @typescript-eslint/no-unused-vars */\n        Object.defineProperty(exports, \"__esModule\", {\n          value: true\n        });\n        exports.assign = void 0;\n        // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\n        function assign(target, s, s1, s2, s3) {\n          if (target == null) {\n            throw new TypeError('Cannot convert undefined or null to object');\n          }\n          var to = Object(target);\n          for (var index = 1; index < arguments.length; index++) {\n            // eslint-disable-next-line prefer-rest-params\n            var nextSource = arguments[index];\n            if (nextSource != null) {\n              // Skip over if undefined or null\n              for (var nextKey in nextSource) {\n                // Avoid bugs when hasOwnProperty is shadowed\n                if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {\n                  to[nextKey] = nextSource[nextKey];\n                }\n              }\n            }\n          }\n          return to;\n        }\n        exports.assign = assign;\n\n        /***/\n      }),\n      /***/972: (/***/function (__unused_webpack_module, exports, __webpack_require__) {\n        Object.defineProperty(exports, \"__esModule\", {\n          value: true\n        });\n        exports.createTable = void 0;\n        var documentHandler_1 = __webpack_require__(744);\n        var models_1 = __webpack_require__(260);\n        var widthCalculator_1 = __webpack_require__(324);\n        var config_1 = __webpack_require__(796);\n        var polyfills_1 = __webpack_require__(356);\n        function createTable(jsPDFDoc, input) {\n          var doc = new documentHandler_1.DocHandler(jsPDFDoc);\n          var content = parseContent(input, doc.scaleFactor());\n          var table = new models_1.Table(input, content);\n          (0, widthCalculator_1.calculateWidths)(doc, table);\n          doc.applyStyles(doc.userStyles);\n          return table;\n        }\n        exports.createTable = createTable;\n        function parseContent(input, sf) {\n          var content = input.content;\n          var columns = createColumns(content.columns);\n          // If no head or foot is set, try generating it with content from columns\n          if (content.head.length === 0) {\n            var sectionRow = generateSectionRow(columns, 'head');\n            if (sectionRow) content.head.push(sectionRow);\n          }\n          if (content.foot.length === 0) {\n            var sectionRow = generateSectionRow(columns, 'foot');\n            if (sectionRow) content.foot.push(sectionRow);\n          }\n          var theme = input.settings.theme;\n          var styles = input.styles;\n          return {\n            columns: columns,\n            head: parseSection('head', content.head, columns, styles, theme, sf),\n            body: parseSection('body', content.body, columns, styles, theme, sf),\n            foot: parseSection('foot', content.foot, columns, styles, theme, sf)\n          };\n        }\n        function parseSection(sectionName, sectionRows, columns, styleProps, theme, scaleFactor) {\n          var rowSpansLeftForColumn = {};\n          var result = sectionRows.map(function (rawRow, rowIndex) {\n            var skippedRowForRowSpans = 0;\n            var cells = {};\n            var colSpansAdded = 0;\n            var columnSpansLeft = 0;\n            for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n              var column = columns_1[_i];\n              if (rowSpansLeftForColumn[column.index] == null || rowSpansLeftForColumn[column.index].left === 0) {\n                if (columnSpansLeft === 0) {\n                  var rawCell = void 0;\n                  if (Array.isArray(rawRow)) {\n                    rawCell = rawRow[column.index - colSpansAdded - skippedRowForRowSpans];\n                  } else {\n                    rawCell = rawRow[column.dataKey];\n                  }\n                  var cellInputStyles = {};\n                  if (typeof rawCell === 'object' && !Array.isArray(rawCell)) {\n                    cellInputStyles = (rawCell === null || rawCell === void 0 ? void 0 : rawCell.styles) || {};\n                  }\n                  var styles = cellStyles(sectionName, column, rowIndex, theme, styleProps, scaleFactor, cellInputStyles);\n                  var cell = new models_1.Cell(rawCell, styles, sectionName);\n                  // dataKey is not used internally no more but keep for\n                  // backwards compat in hooks\n                  cells[column.dataKey] = cell;\n                  cells[column.index] = cell;\n                  columnSpansLeft = cell.colSpan - 1;\n                  rowSpansLeftForColumn[column.index] = {\n                    left: cell.rowSpan - 1,\n                    times: columnSpansLeft\n                  };\n                } else {\n                  columnSpansLeft--;\n                  colSpansAdded++;\n                }\n              } else {\n                rowSpansLeftForColumn[column.index].left--;\n                columnSpansLeft = rowSpansLeftForColumn[column.index].times;\n                skippedRowForRowSpans++;\n              }\n            }\n            return new models_1.Row(rawRow, rowIndex, sectionName, cells);\n          });\n          return result;\n        }\n        function generateSectionRow(columns, section) {\n          var sectionRow = {};\n          columns.forEach(function (col) {\n            if (col.raw != null) {\n              var title = getSectionTitle(section, col.raw);\n              if (title != null) sectionRow[col.dataKey] = title;\n            }\n          });\n          return Object.keys(sectionRow).length > 0 ? sectionRow : null;\n        }\n        function getSectionTitle(section, column) {\n          if (section === 'head') {\n            if (typeof column === 'object') {\n              return column.header || column.title || null;\n            } else if (typeof column === 'string' || typeof column === 'number') {\n              return column;\n            }\n          } else if (section === 'foot' && typeof column === 'object') {\n            return column.footer;\n          }\n          return null;\n        }\n        function createColumns(columns) {\n          return columns.map(function (input, index) {\n            var _a, _b;\n            var key;\n            if (typeof input === 'object') {\n              key = (_b = (_a = input.dataKey) !== null && _a !== void 0 ? _a : input.key) !== null && _b !== void 0 ? _b : index;\n            } else {\n              key = index;\n            }\n            return new models_1.Column(key, input, index);\n          });\n        }\n        function cellStyles(sectionName, column, rowIndex, themeName, styles, scaleFactor, cellInputStyles) {\n          var theme = (0, config_1.getTheme)(themeName);\n          var sectionStyles;\n          if (sectionName === 'head') {\n            sectionStyles = styles.headStyles;\n          } else if (sectionName === 'body') {\n            sectionStyles = styles.bodyStyles;\n          } else if (sectionName === 'foot') {\n            sectionStyles = styles.footStyles;\n          }\n          var otherStyles = (0, polyfills_1.assign)({}, theme.table, theme[sectionName], styles.styles, sectionStyles);\n          var columnStyles = styles.columnStyles[column.dataKey] || styles.columnStyles[column.index] || {};\n          var colStyles = sectionName === 'body' ? columnStyles : {};\n          var rowStyles = sectionName === 'body' && rowIndex % 2 === 0 ? (0, polyfills_1.assign)({}, theme.alternateRow, styles.alternateRowStyles) : {};\n          var defaultStyle = (0, config_1.defaultStyles)(scaleFactor);\n          var themeStyles = (0, polyfills_1.assign)({}, defaultStyle, otherStyles, rowStyles, colStyles);\n          return (0, polyfills_1.assign)(themeStyles, cellInputStyles);\n        }\n\n        /***/\n      }),\n      /***/664: (/***/function (__unused_webpack_module, exports, __webpack_require__) {\n        Object.defineProperty(exports, \"__esModule\", {\n          value: true\n        });\n        exports.addPage = exports.drawTable = void 0;\n        var common_1 = __webpack_require__(420);\n        var models_1 = __webpack_require__(260);\n        var documentHandler_1 = __webpack_require__(744);\n        var polyfills_1 = __webpack_require__(356);\n        var autoTableText_1 = __webpack_require__(136);\n        var tablePrinter_1 = __webpack_require__(224);\n        function drawTable(jsPDFDoc, table) {\n          var settings = table.settings;\n          var startY = settings.startY;\n          var margin = settings.margin;\n          var cursor = {\n            x: margin.left,\n            y: startY\n          };\n          var sectionsHeight = table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n          var minTableBottomPos = startY + margin.bottom + sectionsHeight;\n          if (settings.pageBreak === 'avoid') {\n            var rows = table.body;\n            var tableHeight = rows.reduce(function (acc, row) {\n              return acc + row.height;\n            }, 0);\n            minTableBottomPos += tableHeight;\n          }\n          var doc = new documentHandler_1.DocHandler(jsPDFDoc);\n          if (settings.pageBreak === 'always' || settings.startY != null && minTableBottomPos > doc.pageSize().height) {\n            nextPage(doc);\n            cursor.y = margin.top;\n          }\n          table.callWillDrawPageHooks(doc, cursor);\n          var startPos = (0, polyfills_1.assign)({}, cursor);\n          table.startPageNumber = doc.pageNumber();\n          if (settings.horizontalPageBreak) {\n            // managed flow for split columns\n            printTableWithHorizontalPageBreak(doc, table, startPos, cursor);\n          } else {\n            // normal flow\n            doc.applyStyles(doc.userStyles);\n            if (settings.showHead === 'firstPage' || settings.showHead === 'everyPage') {\n              table.head.forEach(function (row) {\n                return printRow(doc, table, row, cursor, table.columns);\n              });\n            }\n            doc.applyStyles(doc.userStyles);\n            table.body.forEach(function (row, index) {\n              var isLastRow = index === table.body.length - 1;\n              printFullRow(doc, table, row, isLastRow, startPos, cursor, table.columns);\n            });\n            doc.applyStyles(doc.userStyles);\n            if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n              table.foot.forEach(function (row) {\n                return printRow(doc, table, row, cursor, table.columns);\n              });\n            }\n          }\n          (0, common_1.addTableBorder)(doc, table, startPos, cursor);\n          table.callEndPageHooks(doc, cursor);\n          table.finalY = cursor.y;\n          jsPDFDoc.lastAutoTable = table;\n          jsPDFDoc.previousAutoTable = table; // Deprecated\n          if (jsPDFDoc.autoTable) jsPDFDoc.autoTable.previous = table; // Deprecated\n          doc.applyStyles(doc.userStyles);\n        }\n        exports.drawTable = drawTable;\n        function printTableWithHorizontalPageBreak(doc, table, startPos, cursor) {\n          // calculate width of columns and render only those which can fit into page\n          var allColumnsCanFitResult = (0, tablePrinter_1.calculateAllColumnsCanFitInPage)(doc, table);\n          var settings = table.settings;\n          if (settings.horizontalPageBreakBehaviour === 'afterAllRows') {\n            allColumnsCanFitResult.forEach(function (colsAndIndexes, index) {\n              doc.applyStyles(doc.userStyles);\n              // add page to print next columns in new page\n              if (index > 0) {\n                // When adding a page here, make sure not to print the footers\n                // because they were already printed before on this same loop\n                addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n              } else {\n                // print head for selected columns\n                printHead(doc, table, cursor, colsAndIndexes.columns);\n              }\n              // print body & footer for selected columns\n              printBody(doc, table, startPos, cursor, colsAndIndexes.columns);\n              printFoot(doc, table, cursor, colsAndIndexes.columns);\n            });\n          } else {\n            var lastRowIndexOfLastPage_1 = -1;\n            var firstColumnsToFitResult = allColumnsCanFitResult[0];\n            var _loop_1 = function () {\n              // Print the first columns, taking note of the last row printed\n              var lastPrintedRowIndex = lastRowIndexOfLastPage_1;\n              if (firstColumnsToFitResult) {\n                doc.applyStyles(doc.userStyles);\n                var firstColumnsToFit = firstColumnsToFitResult.columns;\n                if (lastRowIndexOfLastPage_1 >= 0) {\n                  // When adding a page here, make sure not to print the footers\n                  // because they were already printed before on this same loop\n                  addPage(doc, table, startPos, cursor, firstColumnsToFit, true);\n                } else {\n                  printHead(doc, table, cursor, firstColumnsToFit);\n                }\n                lastPrintedRowIndex = printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, firstColumnsToFit);\n                printFoot(doc, table, cursor, firstColumnsToFit);\n              }\n              // Check how many rows were printed, so that the next columns would not print more rows than that\n              var maxNumberOfRows = lastPrintedRowIndex - lastRowIndexOfLastPage_1;\n              // Print the next columns, never exceding maxNumberOfRows\n              allColumnsCanFitResult.slice(1).forEach(function (colsAndIndexes) {\n                doc.applyStyles(doc.userStyles);\n                // When adding a page here, make sure not to print the footers\n                // because they were already printed before on this same loop\n                addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n                printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, colsAndIndexes.columns, maxNumberOfRows);\n                printFoot(doc, table, cursor, colsAndIndexes.columns);\n              });\n              lastRowIndexOfLastPage_1 = lastPrintedRowIndex;\n            };\n            while (lastRowIndexOfLastPage_1 < table.body.length - 1) {\n              _loop_1();\n            }\n          }\n        }\n        function printHead(doc, table, cursor, columns) {\n          var settings = table.settings;\n          doc.applyStyles(doc.userStyles);\n          if (settings.showHead === 'firstPage' || settings.showHead === 'everyPage') {\n            table.head.forEach(function (row) {\n              return printRow(doc, table, row, cursor, columns);\n            });\n          }\n        }\n        function printBody(doc, table, startPos, cursor, columns) {\n          doc.applyStyles(doc.userStyles);\n          table.body.forEach(function (row, index) {\n            var isLastRow = index === table.body.length - 1;\n            printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n          });\n        }\n        function printBodyWithoutPageBreaks(doc, table, startRowIndex, cursor, columns, maxNumberOfRows) {\n          doc.applyStyles(doc.userStyles);\n          maxNumberOfRows = maxNumberOfRows !== null && maxNumberOfRows !== void 0 ? maxNumberOfRows : table.body.length;\n          var endRowIndex = Math.min(startRowIndex + maxNumberOfRows, table.body.length);\n          var lastPrintedRowIndex = -1;\n          table.body.slice(startRowIndex, endRowIndex).forEach(function (row, index) {\n            var isLastRow = startRowIndex + index === table.body.length - 1;\n            var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n            if (row.canEntireRowFit(remainingSpace, columns)) {\n              printRow(doc, table, row, cursor, columns);\n              lastPrintedRowIndex = startRowIndex + index;\n            }\n          });\n          return lastPrintedRowIndex;\n        }\n        function printFoot(doc, table, cursor, columns) {\n          var settings = table.settings;\n          doc.applyStyles(doc.userStyles);\n          if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n            table.foot.forEach(function (row) {\n              return printRow(doc, table, row, cursor, columns);\n            });\n          }\n        }\n        function getRemainingLineCount(cell, remainingPageSpace, doc) {\n          var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n          var vPadding = cell.padding('vertical');\n          var remainingLines = Math.floor((remainingPageSpace - vPadding) / lineHeight);\n          return Math.max(0, remainingLines);\n        }\n        function modifyRowToFit(row, remainingPageSpace, table, doc) {\n          var cells = {};\n          row.spansMultiplePages = true;\n          row.height = 0;\n          var rowHeight = 0;\n          for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var cell = row.cells[column.index];\n            if (!cell) continue;\n            if (!Array.isArray(cell.text)) {\n              cell.text = [cell.text];\n            }\n            var remainderCell = new models_1.Cell(cell.raw, cell.styles, cell.section);\n            remainderCell = (0, polyfills_1.assign)(remainderCell, cell);\n            remainderCell.text = [];\n            var remainingLineCount = getRemainingLineCount(cell, remainingPageSpace, doc);\n            if (cell.text.length > remainingLineCount) {\n              remainderCell.text = cell.text.splice(remainingLineCount, cell.text.length);\n            }\n            var scaleFactor = doc.scaleFactor();\n            var lineHeightFactor = doc.getLineHeightFactor();\n            cell.contentHeight = cell.getContentHeight(scaleFactor, lineHeightFactor);\n            if (cell.contentHeight >= remainingPageSpace) {\n              cell.contentHeight = remainingPageSpace;\n              remainderCell.styles.minCellHeight -= remainingPageSpace;\n            }\n            if (cell.contentHeight > row.height) {\n              row.height = cell.contentHeight;\n            }\n            remainderCell.contentHeight = remainderCell.getContentHeight(scaleFactor, lineHeightFactor);\n            if (remainderCell.contentHeight > rowHeight) {\n              rowHeight = remainderCell.contentHeight;\n            }\n            cells[column.index] = remainderCell;\n          }\n          var remainderRow = new models_1.Row(row.raw, -1, row.section, cells, true);\n          remainderRow.height = rowHeight;\n          for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n            var column = _c[_b];\n            var remainderCell = remainderRow.cells[column.index];\n            if (remainderCell) {\n              remainderCell.height = remainderRow.height;\n            }\n            var cell = row.cells[column.index];\n            if (cell) {\n              cell.height = row.height;\n            }\n          }\n          return remainderRow;\n        }\n        function shouldPrintOnCurrentPage(doc, row, remainingPageSpace, table) {\n          var pageHeight = doc.pageSize().height;\n          var margin = table.settings.margin;\n          var marginHeight = margin.top + margin.bottom;\n          var maxRowHeight = pageHeight - marginHeight;\n          if (row.section === 'body') {\n            // Should also take into account that head and foot is not\n            // on every page with some settings\n            maxRowHeight -= table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n          }\n          var minRowHeight = row.getMinimumRowHeight(table.columns, doc);\n          var minRowFits = minRowHeight < remainingPageSpace;\n          if (minRowHeight > maxRowHeight) {\n            console.error(\"Will not be able to print row \".concat(row.index, \" correctly since it's minimum height is larger than page height\"));\n            return true;\n          }\n          if (!minRowFits) {\n            return false;\n          }\n          var rowHasRowSpanCell = row.hasRowSpan(table.columns);\n          var rowHigherThanPage = row.getMaxCellHeight(table.columns) > maxRowHeight;\n          if (rowHigherThanPage) {\n            if (rowHasRowSpanCell) {\n              console.error(\"The content of row \".concat(row.index, \" will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.\"));\n            }\n            return true;\n          }\n          if (rowHasRowSpanCell) {\n            // Currently a new page is required whenever a rowspan row don't fit a page.\n            return false;\n          }\n          if (table.settings.rowPageBreak === 'avoid') {\n            return false;\n          }\n          // In all other cases print the row on current page\n          return true;\n        }\n        function printFullRow(doc, table, row, isLastRow, startPos, cursor, columns) {\n          var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n          if (row.canEntireRowFit(remainingSpace, columns)) {\n            // The row fits in the current page\n            printRow(doc, table, row, cursor, columns);\n          } else if (shouldPrintOnCurrentPage(doc, row, remainingSpace, table)) {\n            // The row gets split in two here, each piece in one page\n            var remainderRow = modifyRowToFit(row, remainingSpace, table, doc);\n            printRow(doc, table, row, cursor, columns);\n            addPage(doc, table, startPos, cursor, columns);\n            printFullRow(doc, table, remainderRow, isLastRow, startPos, cursor, columns);\n          } else {\n            // The row get printed entirelly on the next page\n            addPage(doc, table, startPos, cursor, columns);\n            printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n          }\n        }\n        function printRow(doc, table, row, cursor, columns) {\n          cursor.x = table.settings.margin.left;\n          for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n            var column = columns_1[_i];\n            var cell = row.cells[column.index];\n            if (!cell) {\n              cursor.x += column.width;\n              continue;\n            }\n            doc.applyStyles(cell.styles);\n            cell.x = cursor.x;\n            cell.y = cursor.y;\n            var result = table.callCellHooks(doc, table.hooks.willDrawCell, cell, row, column, cursor);\n            if (result === false) {\n              cursor.x += column.width;\n              continue;\n            }\n            drawCellRect(doc, cell, cursor);\n            var textPos = cell.getTextPos();\n            (0, autoTableText_1.default)(cell.text, textPos.x, textPos.y, {\n              halign: cell.styles.halign,\n              valign: cell.styles.valign,\n              maxWidth: Math.ceil(cell.width - cell.padding('left') - cell.padding('right'))\n            }, doc.getDocument());\n            table.callCellHooks(doc, table.hooks.didDrawCell, cell, row, column, cursor);\n            cursor.x += column.width;\n          }\n          cursor.y += row.height;\n        }\n        function drawCellRect(doc, cell, cursor) {\n          var cellStyles = cell.styles;\n          // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/774\n          // TODO (v4): better solution?\n          doc.getDocument().setFillColor(doc.getDocument().getFillColor());\n          if (typeof cellStyles.lineWidth === 'number') {\n            // Draw cell background with normal borders\n            var fillStyle = (0, common_1.getFillStyle)(cellStyles.lineWidth, cellStyles.fillColor);\n            if (fillStyle) {\n              doc.rect(cell.x, cursor.y, cell.width, cell.height, fillStyle);\n            }\n          } else if (typeof cellStyles.lineWidth === 'object') {\n            // Draw cell background\n            if (cellStyles.fillColor) {\n              doc.rect(cell.x, cursor.y, cell.width, cell.height, 'F');\n            }\n            // Draw cell individual borders\n            drawCellBorders(doc, cell, cursor, cellStyles.lineWidth);\n          }\n        }\n        /**\n         * Draw all specified borders. Borders are centered on cell's edge and lengthened\n         * to overlap with neighbours to create sharp corners.\n         * @param doc\n         * @param cell\n         * @param cursor\n         * @param fillColor\n         * @param lineWidth\n         */\n        function drawCellBorders(doc, cell, cursor, lineWidth) {\n          var x1, y1, x2, y2;\n          if (lineWidth.top) {\n            x1 = cursor.x;\n            y1 = cursor.y;\n            x2 = cursor.x + cell.width;\n            y2 = cursor.y;\n            if (lineWidth.right) {\n              x2 += 0.5 * lineWidth.right;\n            }\n            if (lineWidth.left) {\n              x1 -= 0.5 * lineWidth.left;\n            }\n            drawLine(lineWidth.top, x1, y1, x2, y2);\n          }\n          if (lineWidth.bottom) {\n            x1 = cursor.x;\n            y1 = cursor.y + cell.height;\n            x2 = cursor.x + cell.width;\n            y2 = cursor.y + cell.height;\n            if (lineWidth.right) {\n              x2 += 0.5 * lineWidth.right;\n            }\n            if (lineWidth.left) {\n              x1 -= 0.5 * lineWidth.left;\n            }\n            drawLine(lineWidth.bottom, x1, y1, x2, y2);\n          }\n          if (lineWidth.left) {\n            x1 = cursor.x;\n            y1 = cursor.y;\n            x2 = cursor.x;\n            y2 = cursor.y + cell.height;\n            if (lineWidth.top) {\n              y1 -= 0.5 * lineWidth.top;\n            }\n            if (lineWidth.bottom) {\n              y2 += 0.5 * lineWidth.bottom;\n            }\n            drawLine(lineWidth.left, x1, y1, x2, y2);\n          }\n          if (lineWidth.right) {\n            x1 = cursor.x + cell.width;\n            y1 = cursor.y;\n            x2 = cursor.x + cell.width;\n            y2 = cursor.y + cell.height;\n            if (lineWidth.top) {\n              y1 -= 0.5 * lineWidth.top;\n            }\n            if (lineWidth.bottom) {\n              y2 += 0.5 * lineWidth.bottom;\n            }\n            drawLine(lineWidth.right, x1, y1, x2, y2);\n          }\n          function drawLine(width, x1, y1, x2, y2) {\n            doc.getDocument().setLineWidth(width);\n            doc.getDocument().line(x1, y1, x2, y2, 'S');\n          }\n        }\n        function getRemainingPageSpace(doc, table, isLastRow, cursor) {\n          var bottomContentHeight = table.settings.margin.bottom;\n          var showFoot = table.settings.showFoot;\n          if (showFoot === 'everyPage' || showFoot === 'lastPage' && isLastRow) {\n            bottomContentHeight += table.getFootHeight(table.columns);\n          }\n          return doc.pageSize().height - cursor.y - bottomContentHeight;\n        }\n        function addPage(doc, table, startPos, cursor, columns, suppressFooter) {\n          if (columns === void 0) {\n            columns = [];\n          }\n          if (suppressFooter === void 0) {\n            suppressFooter = false;\n          }\n          doc.applyStyles(doc.userStyles);\n          if (table.settings.showFoot === 'everyPage' && !suppressFooter) {\n            table.foot.forEach(function (row) {\n              return printRow(doc, table, row, cursor, columns);\n            });\n          }\n          // Add user content just before adding new page ensure it will\n          // be drawn above other things on the page\n          table.callEndPageHooks(doc, cursor);\n          var margin = table.settings.margin;\n          (0, common_1.addTableBorder)(doc, table, startPos, cursor);\n          nextPage(doc);\n          table.pageNumber++;\n          table.pageCount++;\n          cursor.x = margin.left;\n          cursor.y = margin.top;\n          startPos.y = margin.top;\n          // call didAddPage hooks before any content is added to the page\n          table.callWillDrawPageHooks(doc, cursor);\n          if (table.settings.showHead === 'everyPage') {\n            table.head.forEach(function (row) {\n              return printRow(doc, table, row, cursor, columns);\n            });\n            doc.applyStyles(doc.userStyles);\n          }\n        }\n        exports.addPage = addPage;\n        function nextPage(doc) {\n          var current = doc.pageNumber();\n          doc.setPage(current + 1);\n          var newCurrent = doc.pageNumber();\n          if (newCurrent === current) {\n            doc.addPage();\n            return true;\n          }\n          return false;\n        }\n\n        /***/\n      }),\n      /***/224: (/***/function (__unused_webpack_module, exports, __webpack_require__) {\n        Object.defineProperty(exports, \"__esModule\", {\n          value: true\n        });\n        exports.calculateAllColumnsCanFitInPage = void 0;\n        var common_1 = __webpack_require__(420);\n        // get columns can be fit into page\n        function getColumnsCanFitInPage(doc, table, config) {\n          var _a;\n          if (config === void 0) {\n            config = {};\n          }\n          // Get page width\n          var remainingWidth = (0, common_1.getPageAvailableWidth)(doc, table);\n          // Get column data key to repeat\n          var repeatColumnsMap = new Map();\n          var colIndexes = [];\n          var columns = [];\n          var horizontalPageBreakRepeat = [];\n          table.settings.horizontalPageBreakRepeat;\n          if (Array.isArray(table.settings.horizontalPageBreakRepeat)) {\n            horizontalPageBreakRepeat = table.settings.horizontalPageBreakRepeat;\n            // It can be a single value of type string or number (even number: 0)\n          } else if (typeof table.settings.horizontalPageBreakRepeat === 'string' || typeof table.settings.horizontalPageBreakRepeat === 'number') {\n            horizontalPageBreakRepeat = [table.settings.horizontalPageBreakRepeat];\n          }\n          // Code to repeat the given column in split pages\n          horizontalPageBreakRepeat.forEach(function (field) {\n            var col = table.columns.find(function (item) {\n              return item.dataKey === field || item.index === field;\n            });\n            if (col && !repeatColumnsMap.has(col.index)) {\n              repeatColumnsMap.set(col.index, true);\n              colIndexes.push(col.index);\n              columns.push(table.columns[col.index]);\n              remainingWidth -= col.wrappedWidth;\n            }\n          });\n          var first = true;\n          var i = (_a = config === null || config === void 0 ? void 0 : config.start) !== null && _a !== void 0 ? _a : 0; // make sure couter is initiated outside the loop\n          while (i < table.columns.length) {\n            // Prevent duplicates\n            if (repeatColumnsMap.has(i)) {\n              i++;\n              continue;\n            }\n            var colWidth = table.columns[i].wrappedWidth;\n            // Take at least one column even if it doesn't fit\n            if (first || remainingWidth >= colWidth) {\n              first = false;\n              colIndexes.push(i);\n              columns.push(table.columns[i]);\n              remainingWidth -= colWidth;\n            } else {\n              break;\n            }\n            i++;\n          }\n          return {\n            colIndexes: colIndexes,\n            columns: columns,\n            lastIndex: i - 1\n          };\n        }\n        function calculateAllColumnsCanFitInPage(doc, table) {\n          var allResults = [];\n          for (var i = 0; i < table.columns.length; i++) {\n            var result = getColumnsCanFitInPage(doc, table, {\n              start: i\n            });\n            if (result.columns.length) {\n              allResults.push(result);\n              i = result.lastIndex;\n            }\n          }\n          return allResults;\n        }\n        exports.calculateAllColumnsCanFitInPage = calculateAllColumnsCanFitInPage;\n\n        /***/\n      }),\n      /***/324: (/***/function (__unused_webpack_module, exports, __webpack_require__) {\n        Object.defineProperty(exports, \"__esModule\", {\n          value: true\n        });\n        exports.ellipsize = exports.resizeColumns = exports.calculateWidths = void 0;\n        var common_1 = __webpack_require__(420);\n        /**\n         * Calculate the column widths\n         */\n        function calculateWidths(doc, table) {\n          calculate(doc, table);\n          var resizableColumns = [];\n          var initialTableWidth = 0;\n          table.columns.forEach(function (column) {\n            var customWidth = column.getMaxCustomCellWidth(table);\n            if (customWidth) {\n              // final column width\n              column.width = customWidth;\n            } else {\n              // initial column width (will be resized)\n              column.width = column.wrappedWidth;\n              resizableColumns.push(column);\n            }\n            initialTableWidth += column.width;\n          });\n          // width difference that needs to be distributed\n          var resizeWidth = table.getWidth(doc.pageSize().width) - initialTableWidth;\n          // first resize attempt: with respect to minReadableWidth and minWidth\n          if (resizeWidth) {\n            resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) {\n              return Math.max(column.minReadableWidth, column.minWidth);\n            });\n          }\n          // second resize attempt: ignore minReadableWidth but respect minWidth\n          if (resizeWidth) {\n            resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) {\n              return column.minWidth;\n            });\n          }\n          resizeWidth = Math.abs(resizeWidth);\n          if (!table.settings.horizontalPageBreak && resizeWidth > 0.1 / doc.scaleFactor()) {\n            // Table can't get smaller due to custom-width or minWidth restrictions\n            // We can't really do much here. Up to user to for example\n            // reduce font size, increase page size or remove custom cell widths\n            // to allow more columns to be reduced in size\n            resizeWidth = resizeWidth < 1 ? resizeWidth : Math.round(resizeWidth);\n            console.warn(\"Of the table content, \".concat(resizeWidth, \" units width could not fit page\"));\n          }\n          applyColSpans(table);\n          fitContent(table, doc);\n          applyRowSpans(table);\n        }\n        exports.calculateWidths = calculateWidths;\n        function calculate(doc, table) {\n          var sf = doc.scaleFactor();\n          var horizontalPageBreak = table.settings.horizontalPageBreak;\n          var availablePageWidth = (0, common_1.getPageAvailableWidth)(doc, table);\n          table.allRows().forEach(function (row) {\n            for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n              var column = _a[_i];\n              var cell = row.cells[column.index];\n              if (!cell) continue;\n              var hooks = table.hooks.didParseCell;\n              table.callCellHooks(doc, hooks, cell, row, column, null);\n              var padding = cell.padding('horizontal');\n              cell.contentWidth = (0, common_1.getStringWidth)(cell.text, cell.styles, doc) + padding;\n              // Using [^\\S\\u00A0] instead of \\s ensures that we split the text on all\n              // whitespace except non-breaking spaces (\\u00A0). We need to preserve\n              // them in the split process to ensure correct word separation and width\n              // calculation.\n              var longestWordWidth = (0, common_1.getStringWidth)(cell.text.join(' ').split(/[^\\S\\u00A0]+/), cell.styles, doc);\n              cell.minReadableWidth = longestWordWidth + cell.padding('horizontal');\n              if (typeof cell.styles.cellWidth === 'number') {\n                cell.minWidth = cell.styles.cellWidth;\n                cell.wrappedWidth = cell.styles.cellWidth;\n              } else if (cell.styles.cellWidth === 'wrap' || horizontalPageBreak === true) {\n                // cell width should not be more than available page width\n                if (cell.contentWidth > availablePageWidth) {\n                  cell.minWidth = availablePageWidth;\n                  cell.wrappedWidth = availablePageWidth;\n                } else {\n                  cell.minWidth = cell.contentWidth;\n                  cell.wrappedWidth = cell.contentWidth;\n                }\n              } else {\n                // auto\n                var defaultMinWidth = 10 / sf;\n                cell.minWidth = cell.styles.minCellWidth || defaultMinWidth;\n                cell.wrappedWidth = cell.contentWidth;\n                if (cell.minWidth > cell.wrappedWidth) {\n                  cell.wrappedWidth = cell.minWidth;\n                }\n              }\n            }\n          });\n          table.allRows().forEach(function (row) {\n            for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n              var column = _a[_i];\n              var cell = row.cells[column.index];\n              // For now we ignore the minWidth and wrappedWidth of colspan cells when calculating colspan widths.\n              // Could probably be improved upon however.\n              if (cell && cell.colSpan === 1) {\n                column.wrappedWidth = Math.max(column.wrappedWidth, cell.wrappedWidth);\n                column.minWidth = Math.max(column.minWidth, cell.minWidth);\n                column.minReadableWidth = Math.max(column.minReadableWidth, cell.minReadableWidth);\n              } else {\n                // Respect cellWidth set in columnStyles even if there is no cells for this column\n                // or if the column only have colspan cells. Since the width of colspan cells\n                // does not affect the width of columns, setting columnStyles cellWidth enables the\n                // user to at least do it manually.\n                // Note that this is not perfect for now since for example row and table styles are\n                // not accounted for\n                var columnStyles = table.styles.columnStyles[column.dataKey] || table.styles.columnStyles[column.index] || {};\n                var cellWidth = columnStyles.cellWidth || columnStyles.minCellWidth;\n                if (cellWidth && typeof cellWidth === 'number') {\n                  column.minWidth = cellWidth;\n                  column.wrappedWidth = cellWidth;\n                }\n              }\n              if (cell) {\n                // Make sure all columns get at least min width even though width calculations are not based on them\n                if (cell.colSpan > 1 && !column.minWidth) {\n                  column.minWidth = cell.minWidth;\n                }\n                if (cell.colSpan > 1 && !column.wrappedWidth) {\n                  column.wrappedWidth = cell.minWidth;\n                }\n              }\n            }\n          });\n        }\n        /**\n         * Distribute resizeWidth on passed resizable columns\n         */\n        function resizeColumns(columns, resizeWidth, getMinWidth) {\n          var initialResizeWidth = resizeWidth;\n          var sumWrappedWidth = columns.reduce(function (acc, column) {\n            return acc + column.wrappedWidth;\n          }, 0);\n          for (var i = 0; i < columns.length; i++) {\n            var column = columns[i];\n            var ratio = column.wrappedWidth / sumWrappedWidth;\n            var suggestedChange = initialResizeWidth * ratio;\n            var suggestedWidth = column.width + suggestedChange;\n            var minWidth = getMinWidth(column);\n            var newWidth = suggestedWidth < minWidth ? minWidth : suggestedWidth;\n            resizeWidth -= newWidth - column.width;\n            column.width = newWidth;\n          }\n          resizeWidth = Math.round(resizeWidth * 1e10) / 1e10;\n          // Run the resizer again if there's remaining width needs\n          // to be distributed and there're columns that can be resized\n          if (resizeWidth) {\n            var resizableColumns = columns.filter(function (column) {\n              return resizeWidth < 0 ? column.width > getMinWidth(column) // check if column can shrink\n              : true; // check if column can grow\n            });\n            if (resizableColumns.length) {\n              resizeWidth = resizeColumns(resizableColumns, resizeWidth, getMinWidth);\n            }\n          }\n          return resizeWidth;\n        }\n        exports.resizeColumns = resizeColumns;\n        function applyRowSpans(table) {\n          var rowSpanCells = {};\n          var colRowSpansLeft = 1;\n          var all = table.allRows();\n          for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n            var row = all[rowIndex];\n            for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n              var column = _a[_i];\n              var data = rowSpanCells[column.index];\n              if (colRowSpansLeft > 1) {\n                colRowSpansLeft--;\n                delete row.cells[column.index];\n              } else if (data) {\n                data.cell.height += row.height;\n                colRowSpansLeft = data.cell.colSpan;\n                delete row.cells[column.index];\n                data.left--;\n                if (data.left <= 1) {\n                  delete rowSpanCells[column.index];\n                }\n              } else {\n                var cell = row.cells[column.index];\n                if (!cell) {\n                  continue;\n                }\n                cell.height = row.height;\n                if (cell.rowSpan > 1) {\n                  var remaining = all.length - rowIndex;\n                  var left = cell.rowSpan > remaining ? remaining : cell.rowSpan;\n                  rowSpanCells[column.index] = {\n                    cell: cell,\n                    left: left,\n                    row: row\n                  };\n                }\n              }\n            }\n          }\n        }\n        function applyColSpans(table) {\n          var all = table.allRows();\n          for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n            var row = all[rowIndex];\n            var colSpanCell = null;\n            var combinedColSpanWidth = 0;\n            var colSpansLeft = 0;\n            for (var columnIndex = 0; columnIndex < table.columns.length; columnIndex++) {\n              var column = table.columns[columnIndex];\n              // Width and colspan\n              colSpansLeft -= 1;\n              if (colSpansLeft > 1 && table.columns[columnIndex + 1]) {\n                combinedColSpanWidth += column.width;\n                delete row.cells[column.index];\n              } else if (colSpanCell) {\n                var cell = colSpanCell;\n                delete row.cells[column.index];\n                colSpanCell = null;\n                cell.width = column.width + combinedColSpanWidth;\n              } else {\n                var cell = row.cells[column.index];\n                if (!cell) continue;\n                colSpansLeft = cell.colSpan;\n                combinedColSpanWidth = 0;\n                if (cell.colSpan > 1) {\n                  colSpanCell = cell;\n                  combinedColSpanWidth += column.width;\n                  continue;\n                }\n                cell.width = column.width + combinedColSpanWidth;\n              }\n            }\n          }\n        }\n        function fitContent(table, doc) {\n          var rowSpanHeight = {\n            count: 0,\n            height: 0\n          };\n          for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n            var row = _a[_i];\n            for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n              var column = _c[_b];\n              var cell = row.cells[column.index];\n              if (!cell) continue;\n              doc.applyStyles(cell.styles, true);\n              var textSpace = cell.width - cell.padding('horizontal');\n              if (cell.styles.overflow === 'linebreak') {\n                // Add one pt to textSpace to fix rounding error\n                cell.text = doc.splitTextToSize(cell.text, textSpace + 1 / doc.scaleFactor(), {\n                  fontSize: cell.styles.fontSize\n                });\n              } else if (cell.styles.overflow === 'ellipsize') {\n                cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '...');\n              } else if (cell.styles.overflow === 'hidden') {\n                cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '');\n              } else if (typeof cell.styles.overflow === 'function') {\n                var result = cell.styles.overflow(cell.text, textSpace);\n                if (typeof result === 'string') {\n                  cell.text = [result];\n                } else {\n                  cell.text = result;\n                }\n              }\n              cell.contentHeight = cell.getContentHeight(doc.scaleFactor(), doc.getLineHeightFactor());\n              var realContentHeight = cell.contentHeight / cell.rowSpan;\n              if (cell.rowSpan > 1 && rowSpanHeight.count * rowSpanHeight.height < realContentHeight * cell.rowSpan) {\n                rowSpanHeight = {\n                  height: realContentHeight,\n                  count: cell.rowSpan\n                };\n              } else if (rowSpanHeight && rowSpanHeight.count > 0) {\n                if (rowSpanHeight.height > realContentHeight) {\n                  realContentHeight = rowSpanHeight.height;\n                }\n              }\n              if (realContentHeight > row.height) {\n                row.height = realContentHeight;\n              }\n            }\n            rowSpanHeight.count--;\n          }\n        }\n        function ellipsize(text, width, styles, doc, overflow) {\n          return text.map(function (str) {\n            return ellipsizeStr(str, width, styles, doc, overflow);\n          });\n        }\n        exports.ellipsize = ellipsize;\n        function ellipsizeStr(text, width, styles, doc, overflow) {\n          var precision = 10000 * doc.scaleFactor();\n          width = Math.ceil(width * precision) / precision;\n          if (width >= (0, common_1.getStringWidth)(text, styles, doc)) {\n            return text;\n          }\n          while (width < (0, common_1.getStringWidth)(text + overflow, styles, doc)) {\n            if (text.length <= 1) {\n              break;\n            }\n            text = text.substring(0, text.length - 1);\n          }\n          return text.trim() + overflow;\n        }\n\n        /***/\n      }),\n      /***/964: (/***/function (module) {\n        if (typeof __WEBPACK_EXTERNAL_MODULE__964__ === 'undefined') {\n          var e = new Error(\"Cannot find module 'undefined'\");\n          e.code = 'MODULE_NOT_FOUND';\n          throw e;\n        }\n        module.exports = __WEBPACK_EXTERNAL_MODULE__964__;\n\n        /***/\n      })\n\n      /******/\n    };\n    /************************************************************************/\n    /******/ // The module cache\n    /******/\n    var __webpack_module_cache__ = {};\n    /******/\n    /******/ // The require function\n    /******/\n    function __webpack_require__(moduleId) {\n      /******/ // Check if module is in cache\n      /******/var cachedModule = __webpack_module_cache__[moduleId];\n      /******/\n      if (cachedModule !== undefined) {\n        /******/return cachedModule.exports;\n        /******/\n      }\n      /******/ // Create a new module (and put it into the cache)\n      /******/\n      var module = __webpack_module_cache__[moduleId] = {\n        /******/ // no module.id needed\n        /******/ // no module.loaded needed\n        /******/exports: {}\n        /******/\n      };\n      /******/\n      /******/ // Execute the module function\n      /******/\n      __webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n      /******/\n      /******/ // Return the exports of the module\n      /******/\n      return module.exports;\n      /******/\n    }\n    /******/\n    /************************************************************************/\n    var __webpack_exports__ = {};\n    // This entry need to be wrapped in an IIFE because it need to be isolated against other modules in the chunk.\n    !function () {\n      var exports = __webpack_exports__;\n      Object.defineProperty(exports, \"__esModule\", {\n        value: true\n      });\n      exports.Cell = exports.Column = exports.Row = exports.Table = exports.CellHookData = exports.__drawTable = exports.__createTable = exports.applyPlugin = void 0;\n      var applyPlugin_1 = __webpack_require__(340);\n      var inputParser_1 = __webpack_require__(776);\n      var tableDrawer_1 = __webpack_require__(664);\n      var tableCalculator_1 = __webpack_require__(972);\n      var models_1 = __webpack_require__(260);\n      Object.defineProperty(exports, \"Table\", {\n        enumerable: true,\n        get: function () {\n          return models_1.Table;\n        }\n      });\n      var HookData_1 = __webpack_require__(172);\n      Object.defineProperty(exports, \"CellHookData\", {\n        enumerable: true,\n        get: function () {\n          return HookData_1.CellHookData;\n        }\n      });\n      var models_2 = __webpack_require__(260);\n      Object.defineProperty(exports, \"Cell\", {\n        enumerable: true,\n        get: function () {\n          return models_2.Cell;\n        }\n      });\n      Object.defineProperty(exports, \"Column\", {\n        enumerable: true,\n        get: function () {\n          return models_2.Column;\n        }\n      });\n      Object.defineProperty(exports, \"Row\", {\n        enumerable: true,\n        get: function () {\n          return models_2.Row;\n        }\n      });\n      // export { applyPlugin } didn't export applyPlugin\n      // to index.d.ts for some reason\n      function applyPlugin(jsPDF) {\n        (0, applyPlugin_1.default)(jsPDF);\n      }\n      exports.applyPlugin = applyPlugin;\n      function autoTable(d, options) {\n        var input = (0, inputParser_1.parseInput)(d, options);\n        var table = (0, tableCalculator_1.createTable)(d, input);\n        (0, tableDrawer_1.drawTable)(d, table);\n      }\n      // Experimental export\n      function __createTable(d, options) {\n        var input = (0, inputParser_1.parseInput)(d, options);\n        return (0, tableCalculator_1.createTable)(d, input);\n      }\n      exports.__createTable = __createTable;\n      function __drawTable(d, table) {\n        (0, tableDrawer_1.drawTable)(d, table);\n      }\n      exports.__drawTable = __drawTable;\n      try {\n        // eslint-disable-next-line @typescript-eslint/no-var-requires\n        var jsPDF = __webpack_require__(964);\n        // Webpack imported jspdf instead of jsPDF for some reason\n        // while it seemed to work everywhere else.\n        if (jsPDF.jsPDF) jsPDF = jsPDF.jsPDF;\n        applyPlugin(jsPDF);\n      } catch (error) {\n        // Importing jspdf in nodejs environments does not work as of jspdf\n        // 1.5.3 so we need to silence potential errors to support using for example\n        // the nodejs jspdf dist files with the exported applyPlugin\n      }\n      exports[\"default\"] = autoTable;\n    }();\n    /******/\n    return __webpack_exports__;\n    /******/\n  }();\n});"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AASA,KAAC,SAAS,iCAAiC,MAAM,SAAS;AACxD,UAAI,OAAO,YAAY,YAAY,OAAO,WAAW,SAAU,QAAO,UAAU,QAAQ,SAAS,oCAAoC;AACnI,YAAI;AACF,iBAAO;AAAA,QACT,SAAS,GAAG;AAAA,QAAC;AAAA,MACf,EAAE,CAAC;AAAA,eAAW,OAAO,WAAW,cAAc,OAAO,IAAK,QAAO,CAAC,OAAO,GAAG,OAAO;AAAA,WAAO;AACxF,YAAI,IAAI,OAAO,YAAY,WAAW,QAAQ,SAAS,oCAAoC;AACzF,cAAI;AACF,mBAAO;AAAA,UACT,SAAS,GAAG;AAAA,UAAC;AAAA,QACf,EAAE,CAAC,IAAI,QAAQ,KAAK,OAAO,CAAC;AAC5B,iBAAS,KAAK,EAAG,EAAC,OAAO,YAAY,WAAW,UAAU,MAAM,CAAC,IAAI,EAAE,CAAC;AAAA,MAC1E;AAAA,IACF,GAAG,OAAO,eAAe,cAAc,aAAa,OAAO,YAAS,cAAc,UAAO,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,QAAQ,SAAU,kCAAkC;AACzN;AAAA;AAAA,QAAe,WAAY;AAGzB;AAGA,cAAI,sBAAsB;AAAA;AAAA,YACnB;AAAA;AAAA,cAAW,SAAU,yBAAyBA,UAAS;AAC1D,oBAAI,YAAY,QAAQ,KAAK,aAAa,2BAAY;AACpD,sBAAI,gBAAgB,SAAU,GAAG,GAAG;AAClC,oCAAgB,OAAO,kBAAkB;AAAA,sBACvC,WAAW,CAAC;AAAA,oBACd,aAAa,SAAS,SAAUC,IAAGC,IAAG;AACpC,sBAAAD,GAAE,YAAYC;AAAA,oBAChB,KAAK,SAAUD,IAAGC,IAAG;AACnB,+BAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,oBAC7E;AACA,2BAAO,cAAc,GAAG,CAAC;AAAA,kBAC3B;AACA,yBAAO,SAAU,GAAG,GAAG;AACrB,wBAAI,OAAO,MAAM,cAAc,MAAM,KAAM,OAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AACnI,kCAAc,GAAG,CAAC;AAClB,6BAAS,KAAK;AACZ,2BAAK,cAAc;AAAA,oBACrB;AACA,sBAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AAAA,kBACpF;AAAA,gBACF,EAAE;AACF,uBAAO,eAAeF,UAAS,cAAc;AAAA,kBAC3C,OAAO;AAAA,gBACT,CAAC;AACD,gBAAAA,SAAQ,eAAeA,SAAQ,WAAW;AAC1C,oBAAI;AAAA;AAAA,kBAAwB,2BAAY;AACtC,6BAASG,UAAS,KAAK,OAAO,QAAQ;AACpC,2BAAK,QAAQ;AACb,2BAAK,aAAa,MAAM;AACxB,2BAAK,YAAY,KAAK;AACtB,2BAAK,WAAW,MAAM;AACtB,2BAAK,SAAS;AACd,2BAAK,MAAM,IAAI,YAAY;AAAA,oBAC7B;AACA,2BAAOA;AAAA,kBACT,EAAE;AAAA;AACF,gBAAAH,SAAQ,WAAW;AACnB,oBAAI;AAAA;AAAA,kBAA4B,SAAU,QAAQ;AAChD,8BAAUI,eAAc,MAAM;AAC9B,6BAASA,cAAa,KAAK,OAAO,MAAM,KAAK,QAAQ,QAAQ;AAC3D,0BAAI,QAAQ,OAAO,KAAK,MAAM,KAAK,OAAO,MAAM,KAAK;AACrD,4BAAM,OAAO;AACb,4BAAM,MAAM;AACZ,4BAAM,SAAS;AACf,4BAAM,UAAU,IAAI;AACpB,6BAAO;AAAA,oBACT;AACA,2BAAOA;AAAA,kBACT,EAAE,QAAQ;AAAA;AACV,gBAAAJ,SAAQ,eAAe;AAAA,cAGzB;AAAA;AAAA;AAAA,YACK;AAAA;AAAA,cAAW,SAAU,yBAAyBA,UAASK,sBAAqB;AAC/E,uBAAO,eAAeL,UAAS,cAAc;AAAA,kBAC3C,OAAO;AAAA,gBACT,CAAC;AACD,oBAAI,eAAeK,qBAAoB,CAAC;AACxC,oBAAI,kBAAkBA,qBAAoB,GAAG;AAC7C,oBAAI,oBAAoBA,qBAAoB,GAAG;AAC/C,oBAAI,gBAAgBA,qBAAoB,GAAG;AAC3C,oBAAI,gBAAgBA,qBAAoB,GAAG;AAC3C,oBAAI,oBAAoBA,qBAAoB,GAAG;AAC/C,yBAAS,UAAU,OAAO;AAExB,wBAAM,IAAI,YAAY,WAAY;AAChC,wBAAI,OAAO,CAAC;AACZ,6BAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,2BAAK,EAAE,IAAI,UAAU,EAAE;AAAA,oBACzB;AACA,wBAAI;AACJ,wBAAI,KAAK,WAAW,GAAG;AACrB,gCAAU,KAAK,CAAC;AAAA,oBAClB,OAAO;AACL,8BAAQ,MAAM,wCAAwC;AACtD,gCAAU,KAAK,CAAC,KAAK,CAAC;AACtB,8BAAQ,UAAU,KAAK,CAAC;AACxB,8BAAQ,OAAO,KAAK,CAAC;AAAA,oBACvB;AACA,wBAAI,SAAS,GAAG,cAAc,YAAY,MAAM,OAAO;AACvD,wBAAI,SAAS,GAAG,kBAAkB,aAAa,MAAM,KAAK;AAC1D,qBAAC,GAAG,cAAc,WAAW,MAAM,KAAK;AACxC,2BAAO;AAAA,kBACT;AAEA,wBAAM,IAAI,gBAAgB;AAC1B,wBAAM,IAAI,oBAAoB;AAC9B,wBAAM,IAAI,UAAU,WAAW;AAC/B,wBAAM,IAAI,gBAAgB,SAAU,MAAM,GAAG,GAAG,QAAQ;AACtD,qBAAC,GAAG,gBAAgB,SAAS,MAAM,GAAG,GAAG,QAAQ,IAAI;AAAA,kBACvD;AACA,wBAAM,IAAI,uBAAuB,SAAU,UAAU;AACnD,sCAAkB,WAAW,YAAY,UAAU,IAAI;AACvD,2BAAO;AAAA,kBACT;AACA,wBAAM,uBAAuB,SAAU,UAAU,KAAK;AACpD,sCAAkB,WAAW,YAAY,UAAU,GAAG;AAAA,kBACxD;AACA,wBAAM,IAAI,sBAAsB,SAAU,WAAW,uBAAuB;AAC1E,wBAAI;AACJ,wBAAI,0BAA0B,QAAQ;AACpC,8CAAwB;AAAA,oBAC1B;AACA,wBAAI,OAAO,WAAW,aAAa;AACjC,8BAAQ,MAAM,2DAA2D;AACzE,6BAAO;AAAA,oBACT;AACA,wBAAI,MAAM,IAAI,kBAAkB,WAAW,IAAI;AAC/C,wBAAI,MAAM,GAAG,aAAa,WAAW,KAAK,WAAW,QAAQ,uBAAuB,KAAK,GACvF,OAAO,GAAG,MACV,OAAO,GAAG;AACZ,wBAAI,YAAY,KAAK,KAAK,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,SAAU,GAAG;AACrF,6BAAO,EAAE;AAAA,oBACX,CAAC,MAAM,CAAC;AACR,2BAAO;AAAA,sBACL;AAAA,sBACA,MAAM;AAAA,sBACN,MAAM;AAAA,oBACR;AAAA,kBACF;AAIA,wBAAM,IAAI,mBAAmB,WAAY;AACvC,4BAAQ,MAAM,qFAAqF;AACnG,wBAAI,OAAO,KAAK;AAChB,wBAAI,QAAQ,KAAK,QAAQ;AACvB,6BAAO,KAAK;AAAA,oBACd,OAAO;AACL,6BAAO;AAAA,oBACT;AAAA,kBACF;AAIA,wBAAM,IAAI,0BAA0B,SAAU,MAAM;AAClD,4BAAQ,MAAM,uHAAuH;AACrI,wBAAI,CAAC,MAAM,IAAI,UAAU,gBAAgB;AACvC,4BAAM,IAAI,UAAU,iBAAiB,CAAC;AAAA,oBACxC;AACA,0BAAM,IAAI,UAAU,eAAe,iBAAiB;AACpD,2BAAO;AAAA,kBACT;AAIA,wBAAM,IAAI,mBAAmB,WAAY;AACvC,4BAAQ,MAAM,iEAAiE;AAC/E,yBAAK,QAAQ;AACb,2BAAO;AAAA,kBACT;AAAA,gBACF;AACA,gBAAAL,SAAQ,SAAS,IAAI;AAAA,cAGvB;AAAA;AAAA;AAAA,YACK;AAAA;AAAA,cAAW,SAAU,yBAAyBA,UAAS;AAC1D,uBAAO,eAAeA,UAAS,cAAc;AAAA,kBAC3C,OAAO;AAAA,gBACT,CAAC;AAKD,yBAAS,UAAU,MAAM,GAAG,GAAG,QAAQ,KAAK;AAC1C,2BAAS,UAAU,CAAC;AACpB,sBAAI,uBAAuB;AAC3B,sBAAI,IAAI,IAAI,SAAS;AACrB,sBAAI,WAAW,IAAI,SAAS,YAAY,IAAI;AAC5C,sBAAI,mBAAmB,IAAI,sBAAsB,IAAI,oBAAoB,IAAI;AAC7E,sBAAI,aAAa,WAAW;AAC5B,sBAAI,aAAa;AACjB,sBAAI,YAAY;AAChB,sBAAI,YAAY;AAChB,sBAAI,OAAO,WAAW,YAAY,OAAO,WAAW,YAAY,OAAO,WAAW,YAAY,OAAO,WAAW,SAAS;AACvH,gCAAY,OAAO,SAAS,WAAW,KAAK,MAAM,UAAU,IAAI;AAChE,gCAAY,UAAU,UAAU;AAAA,kBAClC;AAEA,uBAAK,YAAY,IAAI;AACrB,sBAAI,OAAO,WAAW,SAAU,MAAK,YAAY,IAAI;AAAA,2BAAoB,OAAO,WAAW,SAAU,MAAK,YAAY;AACtH,sBAAI,OAAO,WAAW,YAAY,OAAO,WAAW,SAAS;AAC3D,wBAAI,YAAY;AAChB,wBAAI,OAAO,WAAW,SAAU,cAAa;AAC7C,wBAAI,aAAa,aAAa,GAAG;AAC/B,+BAAS,QAAQ,GAAG,QAAQ,UAAU,QAAQ,SAAS;AACrD,4BAAI,KAAK,UAAU,KAAK,GAAG,IAAI,IAAI,mBAAmB,UAAU,KAAK,CAAC,IAAI,WAAW,CAAC;AACtF,6BAAK;AAAA,sBACP;AACA,6BAAO;AAAA,oBACT;AACA,yBAAK,IAAI,mBAAmB,IAAI,IAAI;AAAA,kBACtC;AACA,sBAAI,OAAO,WAAW,WAAW;AAC/B,wBAAI,KAAK,MAAM,GAAG,GAAG;AAAA,sBACnB,UAAU,OAAO,YAAY;AAAA,sBAC7B,OAAO;AAAA,oBACT,CAAC;AAAA,kBACH,OAAO;AACL,wBAAI,KAAK,MAAM,GAAG,CAAC;AAAA,kBACrB;AACA,yBAAO;AAAA,gBACT;AACA,gBAAAA,SAAQ,SAAS,IAAI;AAAA,cAGvB;AAAA;AAAA;AAAA,YACK;AAAA;AAAA,cAAW,SAAU,yBAAyBA,UAAS;AAC1D,uBAAO,eAAeA,UAAS,cAAc;AAAA,kBAC3C,OAAO;AAAA,gBACT,CAAC;AACD,gBAAAA,SAAQ,wBAAwBA,SAAQ,eAAeA,SAAQ,eAAeA,SAAQ,iBAAiBA,SAAQ,iBAAiB;AAChI,yBAAS,eAAe,MAAM,QAAQ,KAAK;AACzC,sBAAI,YAAY,QAAQ,IAAI;AAC5B,sBAAI,UAAU,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AAChD,sBAAI,kBAAkB,QAAQ,IAAI,SAAUM,OAAM;AAChD,2BAAO,IAAI,aAAaA,KAAI;AAAA,kBAC9B,CAAC,EAAE,OAAO,SAAU,GAAG,GAAG;AACxB,2BAAO,KAAK,IAAI,GAAG,CAAC;AAAA,kBACtB,GAAG,CAAC;AACJ,yBAAO;AAAA,gBACT;AACA,gBAAAN,SAAQ,iBAAiB;AACzB,yBAAS,eAAe,KAAK,OAAO,UAAU,QAAQ;AACpD,sBAAI,YAAY,MAAM,SAAS;AAC/B,sBAAI,YAAY,MAAM,SAAS;AAC/B,sBAAI,YAAY;AAAA,oBACd;AAAA,oBACA;AAAA,kBACF,CAAC;AACD,sBAAI,YAAY,aAAa,WAAW,KAAK;AAC7C,sBAAI,WAAW;AACb,wBAAI,KAAK,SAAS,GAAG,SAAS,GAAG,MAAM,SAAS,IAAI,SAAS,EAAE,KAAK,GAAG,OAAO,IAAI,SAAS,GAAG,SAAS;AAAA,kBACzG;AAAA,gBACF;AACA,gBAAAA,SAAQ,iBAAiB;AACzB,yBAAS,aAAa,WAAW,WAAW;AAC1C,sBAAI,WAAW,YAAY;AAC3B,sBAAI,iBAAiB,aAAa,cAAc;AAChD,sBAAI,YAAY,gBAAgB;AAC9B,2BAAO;AAAA,kBACT,WAAW,UAAU;AACnB,2BAAO;AAAA,kBACT,WAAW,gBAAgB;AACzB,2BAAO;AAAA,kBACT,OAAO;AACL,2BAAO;AAAA,kBACT;AAAA,gBACF;AACA,gBAAAA,SAAQ,eAAe;AACvB,yBAAS,aAAa,OAAO,cAAc;AACzC,sBAAI,IAAI,IAAI,IAAI;AAChB,0BAAQ,SAAS;AACjB,sBAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,wBAAI,MAAM,UAAU,GAAG;AACrB,6BAAO;AAAA,wBACL,KAAK,MAAM,CAAC;AAAA,wBACZ,OAAO,MAAM,CAAC;AAAA,wBACd,QAAQ,MAAM,CAAC;AAAA,wBACf,MAAM,MAAM,CAAC;AAAA,sBACf;AAAA,oBACF,WAAW,MAAM,WAAW,GAAG;AAC7B,6BAAO;AAAA,wBACL,KAAK,MAAM,CAAC;AAAA,wBACZ,OAAO,MAAM,CAAC;AAAA,wBACd,QAAQ,MAAM,CAAC;AAAA,wBACf,MAAM,MAAM,CAAC;AAAA,sBACf;AAAA,oBACF,WAAW,MAAM,WAAW,GAAG;AAC7B,6BAAO;AAAA,wBACL,KAAK,MAAM,CAAC;AAAA,wBACZ,OAAO,MAAM,CAAC;AAAA,wBACd,QAAQ,MAAM,CAAC;AAAA,wBACf,MAAM,MAAM,CAAC;AAAA,sBACf;AAAA,oBACF,WAAW,MAAM,WAAW,GAAG;AAC7B,8BAAQ,MAAM,CAAC;AAAA,oBACjB,OAAO;AACL,8BAAQ;AAAA,oBACV;AAAA,kBACF;AACA,sBAAI,OAAO,UAAU,UAAU;AAC7B,wBAAI,OAAO,MAAM,aAAa,UAAU;AACtC,4BAAM,MAAM,MAAM;AAClB,4BAAM,SAAS,MAAM;AAAA,oBACvB;AACA,wBAAI,OAAO,MAAM,eAAe,UAAU;AACxC,4BAAM,QAAQ,MAAM;AACpB,4BAAM,OAAO,MAAM;AAAA,oBACrB;AACA,2BAAO;AAAA,sBACL,OAAO,KAAK,MAAM,UAAU,QAAQ,OAAO,SAAS,KAAK;AAAA,sBACzD,MAAM,KAAK,MAAM,SAAS,QAAQ,OAAO,SAAS,KAAK;AAAA,sBACvD,QAAQ,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,KAAK;AAAA,sBAC3D,SAAS,KAAK,MAAM,YAAY,QAAQ,OAAO,SAAS,KAAK;AAAA,oBAC/D;AAAA,kBACF;AACA,sBAAI,OAAO,UAAU,UAAU;AAC7B,4BAAQ;AAAA,kBACV;AACA,yBAAO;AAAA,oBACL,KAAK;AAAA,oBACL,OAAO;AAAA,oBACP,QAAQ;AAAA,oBACR,MAAM;AAAA,kBACR;AAAA,gBACF;AACA,gBAAAA,SAAQ,eAAe;AACvB,yBAAS,sBAAsB,KAAK,OAAO;AACzC,sBAAI,UAAU,aAAa,MAAM,SAAS,QAAQ,CAAC;AACnD,yBAAO,IAAI,SAAS,EAAE,SAAS,QAAQ,OAAO,QAAQ;AAAA,gBACxD;AACA,gBAAAA,SAAQ,wBAAwB;AAAA,cAGlC;AAAA;AAAA;AAAA,YACK;AAAA;AAAA,cAAW,SAAU,yBAAyBA,UAAS;AAC1D,oBAAI,YAAY,QAAQ,KAAK,aAAa,2BAAY;AACpD,sBAAI,gBAAgB,SAAU,GAAG,GAAG;AAClC,oCAAgB,OAAO,kBAAkB;AAAA,sBACvC,WAAW,CAAC;AAAA,oBACd,aAAa,SAAS,SAAUC,IAAGC,IAAG;AACpC,sBAAAD,GAAE,YAAYC;AAAA,oBAChB,KAAK,SAAUD,IAAGC,IAAG;AACnB,+BAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,oBAC7E;AACA,2BAAO,cAAc,GAAG,CAAC;AAAA,kBAC3B;AACA,yBAAO,SAAU,GAAG,GAAG;AACrB,wBAAI,OAAO,MAAM,cAAc,MAAM,KAAM,OAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AACnI,kCAAc,GAAG,CAAC;AAClB,6BAAS,KAAK;AACZ,2BAAK,cAAc;AAAA,oBACrB;AACA,sBAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AAAA,kBACpF;AAAA,gBACF,EAAE;AACF,uBAAO,eAAeF,UAAS,cAAc;AAAA,kBAC3C,OAAO;AAAA,gBACT,CAAC;AACD,gBAAAA,SAAQ,WAAWA,SAAQ,gBAAgBA,SAAQ,eAAe;AAClE,oBAAI;AAAA;AAAA,kBAA4B,SAAU,QAAQ;AAChD,8BAAUO,eAAc,MAAM;AAC9B,6BAASA,cAAa,SAAS;AAC7B,0BAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,4BAAM,WAAW;AACjB,6BAAO;AAAA,oBACT;AACA,2BAAOA;AAAA,kBACT,EAAE,KAAK;AAAA;AACP,gBAAAP,SAAQ,eAAe;AAEvB,yBAAS,cAAc,aAAa;AAClC,yBAAO;AAAA,oBACL,MAAM;AAAA;AAAA,oBAEN,WAAW;AAAA;AAAA,oBAEX,UAAU;AAAA;AAAA,oBAEV,WAAW;AAAA;AAAA,oBAEX,WAAW;AAAA,oBACX,QAAQ;AAAA;AAAA,oBAER,QAAQ;AAAA;AAAA,oBAER,UAAU;AAAA,oBACV,aAAa,IAAI;AAAA;AAAA,oBAEjB,WAAW;AAAA,oBACX,WAAW;AAAA,oBACX,WAAW;AAAA;AAAA,oBAEX,eAAe;AAAA,oBACf,cAAc;AAAA,kBAChB;AAAA,gBACF;AACA,gBAAAA,SAAQ,gBAAgB;AACxB,yBAAS,SAAS,MAAM;AACtB,sBAAI,SAAS;AAAA,oBACX,SAAS;AAAA,sBACP,OAAO;AAAA,wBACL,WAAW;AAAA,wBACX,WAAW;AAAA,wBACX,WAAW;AAAA,sBACb;AAAA,sBACA,MAAM;AAAA,wBACJ,WAAW;AAAA,wBACX,WAAW,CAAC,IAAI,KAAK,GAAG;AAAA,wBACxB,WAAW;AAAA,sBACb;AAAA,sBACA,MAAM,CAAC;AAAA,sBACP,MAAM;AAAA,wBACJ,WAAW;AAAA,wBACX,WAAW,CAAC,IAAI,KAAK,GAAG;AAAA,wBACxB,WAAW;AAAA,sBACb;AAAA,sBACA,cAAc;AAAA,wBACZ,WAAW;AAAA,sBACb;AAAA,oBACF;AAAA,oBACA,MAAM;AAAA,sBACJ,OAAO;AAAA,wBACL,WAAW;AAAA,wBACX,WAAW;AAAA,wBACX,WAAW;AAAA,wBACX,WAAW;AAAA,sBACb;AAAA,sBACA,MAAM;AAAA,wBACJ,WAAW;AAAA,wBACX,WAAW,CAAC,IAAI,KAAK,GAAG;AAAA,wBACxB,WAAW;AAAA,wBACX,WAAW;AAAA,sBACb;AAAA,sBACA,MAAM,CAAC;AAAA,sBACP,MAAM;AAAA,wBACJ,WAAW;AAAA,wBACX,WAAW,CAAC,IAAI,KAAK,GAAG;AAAA,wBACxB,WAAW;AAAA,wBACX,WAAW;AAAA,sBACb;AAAA,sBACA,cAAc,CAAC;AAAA,oBACjB;AAAA,oBACA,OAAO;AAAA,sBACL,MAAM;AAAA,wBACJ,WAAW;AAAA,sBACb;AAAA,sBACA,MAAM;AAAA,wBACJ,WAAW;AAAA,sBACb;AAAA,oBACF;AAAA,kBACF;AACA,yBAAO,OAAO,IAAI;AAAA,gBACpB;AACA,gBAAAA,SAAQ,WAAW;AAAA,cAGrB;AAAA;AAAA;AAAA,YACK;AAAA;AAAA,cAAW,SAAU,yBAAyBA,UAASK,sBAAqB;AAC/E,uBAAO,eAAeL,UAAS,cAAc;AAAA,kBAC3C,OAAO;AAAA,gBACT,CAAC;AACD,gBAAAA,SAAQ,WAAW;AAInB,oBAAI,WAAWK,qBAAoB,GAAG;AACtC,yBAAS,SAAS,gBAAgB,SAAS,aAAa,OAAOG,SAAQ;AACrE,sBAAI,SAAS,CAAC;AACd,sBAAI,gBAAgB,KAAK;AACzB,sBAAI,kBAAkB,WAAW,SAAS,SAAU,MAAM;AACxD,2BAAOA,QAAO,iBAAiB,IAAI,EAAE,iBAAiB;AAAA,kBACxD,CAAC;AACD,sBAAI,mBAAmB,KAAM,QAAO,YAAY;AAChD,sBAAI,YAAY,WAAW,SAAS,SAAU,MAAM;AAClD,2BAAOA,QAAO,iBAAiB,IAAI,EAAE,OAAO;AAAA,kBAC9C,CAAC;AACD,sBAAI,aAAa,KAAM,QAAO,YAAY;AAC1C,sBAAI,UAAU,aAAa,OAAO,WAAW;AAC7C,sBAAI,QAAS,QAAO,cAAc;AAClC,sBAAI,kBAAkB;AACtB,sBAAI,mBAAmB,gBAAgB;AACvC,sBAAI,MAAM,MAAM;AAChB,sBAAI,MAAM,sBAAsB,OAAO,MAAM,qBAAqB,OAAO,MAAM,oBAAoB,KAAK;AACtG,wBAAI,eAAe,WAAW,GAAG,KAAK,KAAK;AAC3C,wBAAI,YAAa,QAAO,YAAY;AAAA,kBACtC,OAAO;AACL,2BAAO,YAAY;AAAA,sBACjB,MAAM,WAAW,MAAM,cAAc,KAAK,KAAK;AAAA,sBAC/C,QAAQ,WAAW,MAAM,gBAAgB,KAAK,KAAK;AAAA,sBACnD,SAAS,WAAW,MAAM,iBAAiB,KAAK,KAAK;AAAA,sBACrD,OAAO,WAAW,MAAM,eAAe,KAAK,KAAK;AAAA,oBACnD;AAGA,wBAAI,CAAC,OAAO,UAAU,KAAK;AACzB,0BAAI,OAAO,UAAU,OAAO;AAC1B,0CAAkB;AAAA,sBACpB,WAAW,OAAO,UAAU,QAAQ;AAClC,0CAAkB;AAAA,sBACpB,WAAW,OAAO,UAAU,MAAM;AAChC,0CAAkB;AAAA,sBACpB;AAAA,oBACF;AAAA,kBACF;AACA,sBAAI,cAAc,WAAW,SAAS,SAAU,MAAM;AACpD,2BAAOA,QAAO,iBAAiB,IAAI,EAAE,eAAe;AAAA,kBACtD,CAAC;AACD,sBAAI,eAAe,KAAM,QAAO,YAAY;AAC5C,sBAAI,WAAW,CAAC,QAAQ,SAAS,UAAU,SAAS;AACpD,sBAAI,SAAS,QAAQ,MAAM,SAAS,MAAM,IAAI;AAC5C,2BAAO,SAAS,MAAM;AAAA,kBACxB;AACA,6BAAW,CAAC,UAAU,UAAU,KAAK;AACrC,sBAAI,SAAS,QAAQ,MAAM,aAAa,MAAM,IAAI;AAChD,2BAAO,SAAS,MAAM;AAAA,kBACxB;AACA,sBAAI,MAAM,SAAS,MAAM,YAAY,EAAE;AACvC,sBAAI,CAAC,MAAM,GAAG,EAAG,QAAO,WAAW,MAAM;AACzC,sBAAI,YAAY,eAAe,KAAK;AACpC,sBAAI,UAAW,QAAO,YAAY;AAClC,sBAAI,QAAQ,MAAM,cAAc,IAAI,YAAY;AAChD,sBAAI,eAAe,QAAQ,IAAI,MAAM,IAAI;AACvC,2BAAO,OAAO;AAAA,kBAChB;AACA,yBAAO;AAAA,gBACT;AACA,gBAAAR,SAAQ,WAAW;AACnB,yBAAS,eAAe,OAAO;AAC7B,sBAAI,MAAM;AACV,sBAAI,MAAM,eAAe,UAAU,MAAM,eAAe,YAAY,SAAS,MAAM,UAAU,KAAK,KAAK;AACrG,0BAAM;AAAA,kBACR;AACA,sBAAI,MAAM,cAAc,YAAY,MAAM,cAAc,WAAW;AACjE,2BAAO;AAAA,kBACT;AACA,yBAAO;AAAA,gBACT;AACA,yBAAS,WAAW,SAAS,aAAa;AACxC,sBAAI,WAAW,UAAU,SAAS,WAAW;AAC7C,sBAAI,CAAC,SAAU,QAAO;AACtB,sBAAI,OAAO,SAAS,MAAM,wDAAwD;AAClF,sBAAI,CAAC,QAAQ,CAAC,MAAM,QAAQ,IAAI,GAAG;AACjC,2BAAO;AAAA,kBACT;AACA,sBAAI,QAAQ,CAAC,SAAS,KAAK,CAAC,CAAC,GAAG,SAAS,KAAK,CAAC,CAAC,GAAG,SAAS,KAAK,CAAC,CAAC,CAAC;AACpE,sBAAI,QAAQ,SAAS,KAAK,CAAC,CAAC;AAC5B,sBAAI,UAAU,KAAK,MAAM,MAAM,CAAC,CAAC,KAAK,MAAM,MAAM,CAAC,CAAC,KAAK,MAAM,MAAM,CAAC,CAAC,GAAG;AACxE,2BAAO;AAAA,kBACT;AACA,yBAAO;AAAA,gBACT;AACA,yBAAS,UAAU,MAAM,aAAa;AACpC,sBAAI,KAAK,YAAY,IAAI;AACzB,sBAAI,OAAO,sBAAsB,OAAO,iBAAiB,OAAO,aAAa,OAAO,WAAW;AAC7F,wBAAI,KAAK,iBAAiB,MAAM;AAC9B,6BAAO;AAAA,oBACT;AACA,2BAAO,UAAU,KAAK,eAAe,WAAW;AAAA,kBAClD,OAAO;AACL,2BAAO;AAAA,kBACT;AAAA,gBACF;AACA,yBAAS,aAAa,OAAO,aAAa;AACxC,sBAAI,MAAM,CAAC,MAAM,YAAY,MAAM,cAAc,MAAM,eAAe,MAAM,WAAW;AACvF,sBAAI,gBAAgB,MAAM,KAAK;AAC/B,sBAAI,eAAe,SAAS,MAAM,UAAU,IAAI,SAAS,MAAM,QAAQ,KAAK,cAAc;AAC1F,sBAAI,eAAe,IAAI,IAAI,SAAU,GAAG;AACtC,2BAAO,SAAS,KAAK,GAAG,IAAI;AAAA,kBAC9B,CAAC;AACD,sBAAI,WAAW,GAAG,SAAS,cAAc,cAAc,CAAC;AACxD,sBAAI,cAAc,QAAQ,KAAK;AAC7B,4BAAQ,MAAM;AAAA,kBAChB;AACA,sBAAI,cAAc,QAAQ,QAAQ;AAChC,4BAAQ,SAAS;AAAA,kBACnB;AACA,yBAAO;AAAA,gBACT;AAAA,cAGF;AAAA;AAAA;AAAA,YACK;AAAA;AAAA,cAAW,SAAU,yBAAyBA,UAAS;AAC1D,uBAAO,eAAeA,UAAS,cAAc;AAAA,kBAC3C,OAAO;AAAA,gBACT,CAAC;AACD,gBAAAA,SAAQ,aAAa;AACrB,oBAAI,iBAAiB,CAAC;AACtB,oBAAI;AAAA;AAAA,kBAA0B,WAAY;AACxC,6BAASS,YAAW,eAAe;AACjC,2BAAK,gBAAgB;AACrB,2BAAK,aAAa;AAAA;AAAA,wBAEhB,WAAW,cAAc,eAAe,KAAK,cAAc,aAAa,IAAI;AAAA,wBAC5E,UAAU,cAAc,SAAS,YAAY;AAAA,wBAC7C,WAAW,cAAc,SAAS,QAAQ,EAAE;AAAA,wBAC5C,MAAM,cAAc,SAAS,QAAQ,EAAE;AAAA;AAAA,wBAEvC,WAAW,cAAc,eAAe,KAAK,cAAc,aAAa,IAAI;AAAA;AAAA,wBAE5E,WAAW,cAAc,eAAe,KAAK,cAAc,aAAa,IAAI;AAAA,sBAC9E;AAAA,oBACF;AACA,oBAAAA,YAAW,cAAc,SAAU,UAAU,KAAK;AAChD,0BAAI,QAAQ,QAAQ;AAClB,8BAAM;AAAA,sBACR;AACA,0BAAI,KAAK;AACP,4BAAI,8BAA8B;AAAA,sBACpC,OAAO;AACL,yCAAiB;AAAA,sBACnB;AAAA,oBACF;AACA,oBAAAA,YAAW,aAAa,SAAU,GAAG;AACnC,0BAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,+BAAO;AAAA,sBACT,WAAW,OAAO,MAAM,UAAU;AAChC,+BAAO,CAAC,GAAG,GAAG,CAAC;AAAA,sBACjB,WAAW,OAAO,MAAM,UAAU;AAChC,+BAAO,CAAC,CAAC;AAAA,sBACX,OAAO;AACL,+BAAO;AAAA,sBACT;AAAA,oBACF;AACA,oBAAAA,YAAW,UAAU,cAAc,SAAU,QAAQ,UAAU;AAG7D,0BAAI,IAAI,IAAI;AACZ,0BAAI,aAAa,QAAQ;AACvB,mCAAW;AAAA,sBACb;AACA,0BAAI,OAAO,UAAW,MAAK,cAAc,gBAAgB,KAAK,cAAc,aAAa,OAAO,SAAS;AACzG,0BAAI,KAAK,KAAK,cAAc,SAAS,QAAQ,GAC3C,YAAY,GAAG,WACf,WAAW,GAAG;AAChB,0BAAI,OAAO,KAAM,YAAW,OAAO;AACnC,0BAAI,OAAO,WAAW;AACpB,oCAAY,OAAO;AACnB,4BAAI,sBAAsB,KAAK,YAAY,EAAE,QAAQ;AACrD,4BAAI,uBAAuB,oBAAoB,QAAQ,SAAS,MAAM,IAAI;AAIxE,+BAAK,cAAc,gBAAgB,KAAK,cAAc,aAAa,oBAAoB,CAAC,CAAC;AACzF,sCAAY,oBAAoB,CAAC;AAAA,wBACnC;AAAA,sBACF;AACA,2BAAK,cAAc,QAAQ,UAAU,SAAS;AAC9C,0BAAI,OAAO,SAAU,MAAK,cAAc,YAAY,OAAO,QAAQ;AACnE,0BAAI,UAAU;AACZ;AAAA,sBACF;AACA,0BAAI,QAAQA,YAAW,WAAW,OAAO,SAAS;AAClD,0BAAI,MAAO,EAAC,KAAK,KAAK,eAAe,aAAa,MAAM,IAAI,KAAK;AACjE,8BAAQA,YAAW,WAAW,OAAO,SAAS;AAC9C,0BAAI,MAAO,EAAC,KAAK,KAAK,eAAe,aAAa,MAAM,IAAI,KAAK;AACjE,8BAAQA,YAAW,WAAW,OAAO,SAAS;AAC9C,0BAAI,MAAO,EAAC,KAAK,KAAK,eAAe,aAAa,MAAM,IAAI,KAAK;AACjE,0BAAI,OAAO,OAAO,cAAc,UAAU;AACxC,6BAAK,cAAc,aAAa,OAAO,SAAS;AAAA,sBAClD;AAAA,oBACF;AACA,oBAAAA,YAAW,UAAU,kBAAkB,SAAU,MAAM,MAAM,MAAM;AACjE,6BAAO,KAAK,cAAc,gBAAgB,MAAM,MAAM,IAAI;AAAA,oBAC5D;AASA,oBAAAA,YAAW,UAAU,OAAO,SAAU,GAAG,GAAG,OAAO,QAAQ,WAAW;AAIpE,6BAAO,KAAK,cAAc,KAAK,GAAG,GAAG,OAAO,QAAQ,SAAS;AAAA,oBAC/D;AACA,oBAAAA,YAAW,UAAU,mBAAmB,WAAY;AAClD,6BAAO,KAAK,cAAc,iBAAiB;AAAA,oBAC7C;AACA,oBAAAA,YAAW,UAAU,eAAe,SAAU,MAAM;AAClD,6BAAO,KAAK,cAAc,aAAa,IAAI;AAAA,oBAC7C;AACA,oBAAAA,YAAW,UAAU,cAAc,WAAY;AAC7C,6BAAO,KAAK;AAAA,oBACd;AACA,oBAAAA,YAAW,UAAU,UAAU,SAAU,MAAM;AAC7C,2BAAK,cAAc,QAAQ,IAAI;AAAA,oBACjC;AACA,oBAAAA,YAAW,UAAU,UAAU,WAAY;AACzC,6BAAO,KAAK,cAAc,QAAQ;AAAA,oBACpC;AACA,oBAAAA,YAAW,UAAU,cAAc,WAAY;AAC7C,6BAAO,KAAK,cAAc,YAAY;AAAA,oBACxC;AACA,oBAAAA,YAAW,UAAU,mBAAmB,WAAY;AAClD,6BAAO,kBAAkB,CAAC;AAAA,oBAC5B;AACA,oBAAAA,YAAW,UAAU,qBAAqB,WAAY;AACpD,6BAAO,KAAK,cAAc,+BAA+B,CAAC;AAAA,oBAC5D;AACA,oBAAAA,YAAW,UAAU,WAAW,WAAY;AAC1C,0BAAI,WAAW,KAAK,cAAc,SAAS;AAE3C,0BAAI,SAAS,SAAS,MAAM;AAC1B,mCAAW;AAAA,0BACT,OAAO,SAAS,SAAS;AAAA,0BACzB,QAAQ,SAAS,UAAU;AAAA,wBAC7B;AAAA,sBACF;AACA,6BAAO;AAAA,oBACT;AACA,oBAAAA,YAAW,UAAU,cAAc,WAAY;AAC7C,6BAAO,KAAK,cAAc,SAAS;AAAA,oBACrC;AACA,oBAAAA,YAAW,UAAU,sBAAsB,WAAY;AACrD,0BAAI,MAAM,KAAK;AACf,6BAAO,IAAI,sBAAsB,IAAI,oBAAoB,IAAI;AAAA,oBAC/D;AACA,oBAAAA,YAAW,UAAU,gBAAgB,SAAU,UAAU;AACvD,6BAAO,WAAW,KAAK,YAAY,IAAI,KAAK,oBAAoB;AAAA,oBAClE;AACA,oBAAAA,YAAW,UAAU,aAAa,WAAY;AAC5C,0BAAI,WAAW,KAAK,cAAc,SAAS,mBAAmB;AAC9D,0BAAI,CAAC,UAAU;AAEb,+BAAO,KAAK,cAAc,SAAS,iBAAiB;AAAA,sBACtD;AACA,6BAAO,SAAS;AAAA,oBAClB;AACA,2BAAOA;AAAA,kBACT,EAAE;AAAA;AACF,gBAAAT,SAAQ,aAAa;AAAA,cAGvB;AAAA;AAAA;AAAA,YACK;AAAA;AAAA,cAAS,SAAU,yBAAyBA,UAASK,sBAAqB;AAC7E,uBAAO,eAAeL,UAAS,cAAc;AAAA,kBAC3C,OAAO;AAAA,gBACT,CAAC;AACD,gBAAAA,SAAQ,YAAY;AACpB,oBAAI,cAAcK,qBAAoB,GAAG;AACzC,oBAAI,WAAWA,qBAAoB,GAAG;AACtC,yBAAS,UAAU,KAAK,OAAOG,SAAQ,mBAAmB,QAAQ;AAChE,sBAAI,IAAI;AACR,sBAAI,sBAAsB,QAAQ;AAChC,wCAAoB;AAAA,kBACtB;AACA,sBAAI,WAAW,QAAQ;AACrB,6BAAS;AAAA,kBACX;AACA,sBAAI;AACJ,sBAAI,OAAO,UAAU,UAAU;AAC7B,mCAAeA,QAAO,SAAS,cAAc,KAAK;AAAA,kBACpD,OAAO;AACL,mCAAe;AAAA,kBACjB;AACA,sBAAI,iBAAiB,OAAO,KAAK,IAAI,YAAY,CAAC;AAClD,sBAAI,cAAc,IAAI,YAAY;AAClC,sBAAI,OAAO,CAAC,GACV,OAAO,CAAC,GACR,OAAO,CAAC;AACV,sBAAI,CAAC,cAAc;AACjB,4BAAQ,MAAM,8CAA8C,KAAK;AACjE,2BAAO;AAAA,sBACL;AAAA,sBACA;AAAA,sBACA;AAAA,oBACF;AAAA,kBACF;AACA,2BAAS,IAAI,GAAG,IAAI,aAAa,KAAK,QAAQ,KAAK;AACjD,wBAAI,UAAU,aAAa,KAAK,CAAC;AACjC,wBAAI,WAAW,MAAM,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY;AACxM,wBAAI,MAAM,gBAAgB,gBAAgB,aAAaA,SAAQ,SAAS,mBAAmB,MAAM;AACjG,wBAAI,CAAC,IAAK;AACV,wBAAI,YAAY,SAAS;AACvB,2BAAK,KAAK,GAAG;AAAA,oBACf,WAAW,YAAY,SAAS;AAC9B,2BAAK,KAAK,GAAG;AAAA,oBACf,OAAO;AAEL,2BAAK,KAAK,GAAG;AAAA,oBACf;AAAA,kBACF;AACA,yBAAO;AAAA,oBACL;AAAA,oBACA;AAAA,oBACA;AAAA,kBACF;AAAA,gBACF;AACA,gBAAAR,SAAQ,YAAY;AACpB,yBAAS,gBAAgB,gBAAgB,aAAaQ,SAAQ,KAAK,eAAe,QAAQ;AACxF,sBAAI,YAAY,IAAI,SAAS,aAAa,GAAG;AAC7C,2BAAS,IAAI,GAAG,IAAI,IAAI,MAAM,QAAQ,KAAK;AACzC,wBAAI,OAAO,IAAI,MAAM,CAAC;AACtB,wBAAI,UAAUA,QAAO,iBAAiB,IAAI;AAC1C,wBAAI,iBAAiB,QAAQ,YAAY,QAAQ;AAC/C,0BAAI,aAAa;AACjB,0BAAI,QAAQ;AACV,sCAAc,GAAG,YAAY,UAAU,gBAAgB,MAAM,aAAa,SAASA,OAAM;AAAA,sBAC3F;AACA,gCAAU,KAAK;AAAA,wBACb,SAAS,KAAK;AAAA,wBACd,SAAS,KAAK;AAAA,wBACd,QAAQ;AAAA,wBACR,UAAU;AAAA,wBACV,SAAS,iBAAiB,IAAI;AAAA,sBAChC,CAAC;AAAA,oBACH;AAAA,kBACF;AACA,sBAAI,QAAQA,QAAO,iBAAiB,GAAG;AACvC,sBAAI,UAAU,SAAS,MAAM,iBAAiB,MAAM,YAAY,SAAS;AACvE,2BAAO;AAAA,kBACT;AAAA,gBACF;AACA,yBAAS,iBAAiB,SAAS;AAEjC,sBAAI,OAAO,QAAQ,UAAU,IAAI;AAGjC,uBAAK,YAAY,KAAK,UAAU,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,GAAG;AAErE,uBAAK,YAAY,KAAK,UAAU,MAAM,SAAS,EAC9C,IAAI,SAAU,MAAM;AACnB,2BAAO,KAAK,KAAK;AAAA,kBACnB,CAAC,EAAE,KAAK,IAAI;AAEZ,yBAAO,KAAK,aAAa,KAAK,eAAe;AAAA,gBAC/C;AAAA,cAGF;AAAA;AAAA;AAAA,YACK;AAAA;AAAA,cAAW,SAAU,yBAAyBR,UAASK,sBAAqB;AAC/E,uBAAO,eAAeL,UAAS,cAAc;AAAA,kBAC3C,OAAO;AAAA,gBACT,CAAC;AACD,gBAAAA,SAAQ,aAAa;AACrB,oBAAI,eAAeK,qBAAoB,CAAC;AACxC,oBAAI,cAAcA,qBAAoB,GAAG;AACzC,oBAAI,WAAWA,qBAAoB,GAAG;AACtC,oBAAI,oBAAoBA,qBAAoB,GAAG;AAC/C,oBAAI,mBAAmBA,qBAAoB,GAAG;AAC9C,yBAAS,WAAW,GAAG,SAAS;AAC9B,sBAAI,MAAM,IAAI,kBAAkB,WAAW,CAAC;AAC5C,sBAAI,WAAW,IAAI,mBAAmB;AACtC,sBAAIK,UAAS,IAAI,iBAAiB;AAClC,mBAAC,GAAG,iBAAiB,SAAS,KAAKA,SAAQ,UAAU,OAAO;AAC5D,sBAAI,WAAW,GAAG,YAAY,QAAQ,CAAC,GAAGA,SAAQ,UAAU,OAAO;AACnE,sBAAI;AACJ,sBAAI,OAAO,WAAW,aAAa;AACjC,0BAAM;AAAA,kBACR;AACA,sBAAI,SAAS,YAAYA,SAAQ,UAAU,OAAO;AAClD,sBAAI,QAAQ,WAAWA,SAAQ,UAAU,OAAO;AAChD,sBAAI,WAAW,cAAc,KAAK,OAAO;AACzC,sBAAI,UAAU,aAAa,KAAK,SAAS,GAAG;AAC5C,yBAAO;AAAA,oBACL,IAAI,QAAQ;AAAA,oBACZ;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,kBACF;AAAA,gBACF;AACA,gBAAAV,SAAQ,aAAa;AACrB,yBAAS,YAAY,QAAQ,QAAQ,QAAQ;AAC3C,sBAAI,eAAe;AAAA,oBACjB,QAAQ,CAAC;AAAA,oBACT,YAAY,CAAC;AAAA,oBACb,YAAY,CAAC;AAAA,oBACb,YAAY,CAAC;AAAA,oBACb,oBAAoB,CAAC;AAAA,oBACrB,cAAc,CAAC;AAAA,kBACjB;AACA,sBAAI,UAAU,SAAUW,OAAM;AAC5B,wBAAIA,UAAS,gBAAgB;AAC3B,0BAAI,WAAW,OAAOA,KAAI;AAC1B,0BAAI,aAAa,OAAOA,KAAI;AAC5B,0BAAI,UAAU,OAAOA,KAAI;AACzB,mCAAa,gBAAgB,GAAG,YAAY,QAAQ,CAAC,GAAG,UAAU,YAAY,OAAO;AAAA,oBACvF,OAAO;AACL,0BAAI,aAAa,CAAC,QAAQ,QAAQ,MAAM;AACxC,0BAAI,SAAS,WAAW,IAAI,SAAU,MAAM;AAC1C,+BAAO,KAAKA,KAAI,KAAK,CAAC;AAAA,sBACxB,CAAC;AACD,mCAAaA,KAAI,KAAK,GAAG,YAAY,QAAQ,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,oBAClF;AAAA,kBACF;AACA,2BAAS,KAAK,GAAG,KAAK,OAAO,KAAK,YAAY,GAAG,KAAK,GAAG,QAAQ,MAAM;AACrE,wBAAI,OAAO,GAAG,EAAE;AAChB,4BAAQ,IAAI;AAAA,kBACd;AACA,yBAAO;AAAA,gBACT;AACA,yBAAS,WAAWD,SAAQ,UAAU,SAAS;AAC7C,sBAAI,aAAa,CAACA,SAAQ,UAAU,OAAO;AAC3C,sBAAI,SAAS;AAAA,oBACX,cAAc,CAAC;AAAA,oBACf,cAAc,CAAC;AAAA,oBACf,aAAa,CAAC;AAAA,oBACd,cAAc,CAAC;AAAA,oBACf,aAAa,CAAC;AAAA,kBAChB;AACA,2BAAS,KAAK,GAAG,eAAe,YAAY,KAAK,aAAa,QAAQ,MAAM;AAC1E,wBAAI,UAAU,aAAa,EAAE;AAC7B,wBAAI,QAAQ,aAAc,QAAO,aAAa,KAAK,QAAQ,YAAY;AACvE,wBAAI,QAAQ,aAAc,QAAO,aAAa,KAAK,QAAQ,YAAY;AACvE,wBAAI,QAAQ,YAAa,QAAO,YAAY,KAAK,QAAQ,WAAW;AACpE,wBAAI,QAAQ,aAAc,QAAO,aAAa,KAAK,QAAQ,YAAY;AACvE,wBAAI,QAAQ,YAAa,QAAO,YAAY,KAAK,QAAQ,WAAW;AAAA,kBACtE;AACA,yBAAO;AAAA,gBACT;AACA,yBAAS,cAAc,KAAK,SAAS;AACnC,sBAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAChD,sBAAI,UAAU,GAAG,SAAS,cAAc,QAAQ,QAAQ,KAAK,IAAI,YAAY,CAAC;AAC9E,sBAAI,UAAU,KAAK,UAAU,KAAK,QAAQ,MAAM,OAAO,QAAQ,OAAO,SAAS,KAAK,OAAO;AAC3F,sBAAI;AACJ,sBAAI,QAAQ,aAAa,MAAM;AAC7B,+BAAW;AAAA,kBACb,WAAW,QAAQ,aAAa,OAAO;AACrC,+BAAW;AAAA,kBACb,OAAO;AACL,gCAAY,KAAK,QAAQ,cAAc,QAAQ,OAAO,SAAS,KAAK;AAAA,kBACtE;AACA,sBAAI;AACJ,sBAAI,QAAQ,aAAa,MAAM;AAC7B,+BAAW;AAAA,kBACb,WAAW,QAAQ,aAAa,OAAO;AACrC,+BAAW;AAAA,kBACb,OAAO;AACL,gCAAY,KAAK,QAAQ,cAAc,QAAQ,OAAO,SAAS,KAAK;AAAA,kBACtE;AACA,sBAAI,UAAU,KAAK,QAAQ,YAAY,QAAQ,OAAO,SAAS,KAAK;AACpE,sBAAI,QAAQ,QAAQ,UAAU,SAAS,UAAU;AACjD,sBAAI,sBAAsB,CAAC,CAAC,QAAQ;AACpC,sBAAI,6BAA6B,KAAK,QAAQ,+BAA+B,QAAQ,OAAO,SAAS,KAAK;AAC1G,yBAAO;AAAA,oBACL,oBAAoB,KAAK,QAAQ,uBAAuB,QAAQ,OAAO,SAAS,KAAK;AAAA,oBACrF;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA,YAAY,KAAK,QAAQ,eAAe,QAAQ,OAAO,SAAS,KAAK;AAAA,oBACrE,eAAe,KAAK,QAAQ,kBAAkB,QAAQ,OAAO,SAAS,KAAK;AAAA,oBAC3E,aAAa,KAAK,QAAQ,gBAAgB,QAAQ,OAAO,SAAS,KAAK;AAAA,oBACvE;AAAA,oBACA;AAAA,oBACA,iBAAiB,KAAK,QAAQ,oBAAoB,QAAQ,OAAO,SAAS,KAAK;AAAA,oBAC/E,iBAAiB,KAAK,QAAQ,oBAAoB,QAAQ,OAAO,SAAS,KAAK;AAAA,oBAC/E;AAAA,oBACA;AAAA,oBACA,+BAA+B,KAAK,QAAQ,kCAAkC,QAAQ,OAAO,SAAS,KAAK;AAAA,kBAC7G;AAAA,gBACF;AACA,yBAAS,UAAU,KAAK,YAAY;AAClC,sBAAI,WAAW,IAAI,iBAAiB;AACpC,sBAAI,KAAK,IAAI,YAAY;AACzB,sBAAI,cAAc,IAAI,WAAW;AACjC,sBAAI,4BAA4B;AAChC,sBAAI,YAAY,SAAS,iBAAiB;AACxC,wBAAI,aAAa,SAAS,kBAAkB,SAAS,aAAa;AAClE,gDAA4B,eAAe;AAAA,kBAC7C;AACA,sBAAI,OAAO,eAAe,UAAU;AAClC,2BAAO;AAAA,kBACT,WAAW,cAAc,QAAQ,eAAe,OAAO;AACrD,wBAAI,8BAA8B,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,WAAW,MAAM;AAG9G,6BAAO,SAAS,SAAS,KAAK;AAAA,oBAChC;AAAA,kBACF;AACA,yBAAO;AAAA,gBACT;AACA,yBAAS,aAAa,KAAK,SAASF,SAAQ;AAC1C,sBAAI,OAAO,QAAQ,QAAQ,CAAC;AAC5B,sBAAI,OAAO,QAAQ,QAAQ,CAAC;AAC5B,sBAAI,OAAO,QAAQ,QAAQ,CAAC;AAC5B,sBAAI,QAAQ,MAAM;AAChB,wBAAI,SAAS,QAAQ;AACrB,wBAAIA,SAAQ;AACV,0BAAI,eAAe,GAAG,aAAa,WAAW,KAAK,QAAQ,MAAMA,SAAQ,QAAQ,QAAQ,MAAM,KAAK,CAAC;AACrG,6BAAO,YAAY,QAAQ;AAC3B,6BAAO,YAAY,QAAQ;AAC3B,6BAAO,YAAY,QAAQ;AAAA,oBAC7B,OAAO;AACL,8BAAQ,MAAM,8CAA8C;AAAA,oBAC9D;AAAA,kBACF;AACA,sBAAI,UAAU,QAAQ,WAAW,aAAa,MAAM,MAAM,IAAI;AAC9D,yBAAO;AAAA,oBACL;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,kBACF;AAAA,gBACF;AACA,yBAAS,aAAa,MAAM,MAAM,MAAM;AACtC,sBAAI,WAAW,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC;AACjD,sBAAI,SAAS,CAAC;AACd,yBAAO,KAAK,QAAQ,EAAE,OAAO,SAAU,KAAK;AAC1C,2BAAO,QAAQ;AAAA,kBACjB,CAAC,EAAE,QAAQ,SAAU,KAAK;AACxB,wBAAI,UAAU;AACd,wBAAI;AACJ,wBAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,8BAAQ,SAAS,SAAS,GAAG,CAAC;AAAA,oBAChC,OAAO;AACL,8BAAQ,SAAS,GAAG;AAAA,oBACtB;AACA,wBAAI,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,KAAK,GAAG;AACtD,iCAAW,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,YAAY;AAAA,oBAC7E;AACA,6BAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAChC,0BAAI,KAAK;AACT,0BAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,6BAAK,OAAO;AAAA,sBACd,OAAO;AACL,6BAAK,OAAO,IAAI,IAAI,IAAI,OAAO,CAAC,IAAI;AAAA,sBACtC;AACA,0BAAI,YAAY;AAAA,wBACd,SAAS;AAAA,sBACX;AACA,6BAAO,KAAK,SAAS;AAAA,oBACvB;AAAA,kBACF,CAAC;AACD,yBAAO;AAAA,gBACT;AAAA,cAGF;AAAA;AAAA;AAAA,YACK;AAAA;AAAA,cAAW,SAAU,yBAAyBR,UAAS;AAC1D,uBAAO,eAAeA,UAAS,cAAc;AAAA,kBAC3C,OAAO;AAAA,gBACT,CAAC;AACD,yBAAS,UAAU,KAAKU,SAAQ,UAAU,SAAS;AACjD,sBAAI,UAAU,SAAUE,UAAS;AAC/B,wBAAIA,YAAW,OAAOA,aAAY,UAAU;AAC1C,8BAAQ,MAAM,yDAAyD,OAAOA,QAAO;AAAA,oBACvF;AACA,wBAAI,OAAOA,SAAQ,gBAAgB,aAAa;AAC9C,sBAAAA,SAAQ,aAAaA,SAAQ,cAAc,SAAS;AACpD,8BAAQ,MAAM,gEAAgE;AAAA,oBAChF;AACA,wBAAI,OAAOA,SAAQ,YAAY,aAAa;AAC1C,0BAAI,OAAOA,SAAQ,WAAW,YAAa,CAAAA,SAAQ,SAASA,SAAQ;AACpE,8BAAQ,MAAM,wDAAwD;AAAA,oBACxE;AACA,wBAAIA,SAAQ,UAAU,OAAOA,SAAQ,WAAW,UAAU;AACxD,8BAAQ,MAAM,mCAAmCA,SAAQ,MAAM;AAC/D,6BAAOA,SAAQ;AAAA,oBACjB;AACA,wBAAI,CAACA,SAAQ,gBAAgBA,SAAQ,oBAAoBA,SAAQ,qBAAqBA,SAAQ,eAAe;AAC3G,8BAAQ,MAAM,wGAAwG;AACtH,sBAAAA,SAAQ,cAAc,SAAU,MAAM;AACpC,4BAAI,YAAY,IAAI,UAAU;AAC9B,4BAAIA,SAAQ,kBAAmB,CAAAA,SAAQ,kBAAkB,IAAI;AAC7D,4BAAI,YAAY,IAAI,UAAU;AAC9B,4BAAIA,SAAQ,iBAAkB,CAAAA,SAAQ,iBAAiB,IAAI;AAC3D,4BAAI,YAAY,IAAI,UAAU;AAC9B,4BAAIA,SAAQ,gBAAgB,KAAK,aAAa,GAAG;AAC/C;AACA,+BAAK,aAAa,IAAI;AAAA,wBACxB;AACA,4BAAI,YAAY,IAAI,UAAU;AAAA,sBAChC;AAAA,oBACF;AACA;AACA,qBAAC,qBAAqB,iBAAiB,WAAW,gBAAgB,EAAE,QAAQ,SAAU,MAAM;AAC1F,0BAAIA,SAAQ,IAAI,GAAG;AACjB,gCAAQ,MAAM,QAAS,OAAO,MAAM,4EAA6E,CAAC;AAAA,sBACpH;AAAA,oBACF,CAAC;AACD,qBAAC,CAAC,YAAY,YAAY,GAAG,CAAC,YAAY,YAAY,GAAG,CAAC,eAAe,gBAAgB,GAAG,CAAC,gBAAgB,aAAa,GAAG,CAAC,cAAc,cAAc,CAAC,EAAE,QAAQ,SAAUC,KAAI;AACjL,0BAAIC,WAAUD,IAAG,CAAC,GAChB,aAAaA,IAAG,CAAC;AACnB,0BAAID,SAAQ,UAAU,GAAG;AACvB,gCAAQ,MAAM,4BAA4B,OAAO,YAAY,QAAQ,EAAE,OAAOE,UAAS,UAAU,CAAC;AAClG,wBAAAF,SAAQE,QAAO,IAAIF,SAAQ,UAAU;AAAA,sBACvC;AAAA,oBACF,CAAC;AACD,qBAAC,CAAC,WAAW,aAAa,GAAG,CAAC,cAAc,WAAW,GAAG,YAAY,UAAU,EAAE,QAAQ,SAAU,GAAG;AACrG,0BAAI,mBAAmB,OAAO,MAAM,WAAW,IAAI,EAAE,CAAC;AACtD,0BAAI,QAAQ,OAAO,MAAM,WAAW,IAAI,EAAE,CAAC;AAC3C,0BAAI,OAAOA,SAAQ,gBAAgB,MAAM,aAAa;AACpD,4BAAI,OAAOA,SAAQ,OAAO,KAAK,MAAM,aAAa;AAChD,0BAAAA,SAAQ,OAAO,KAAK,IAAIA,SAAQ,gBAAgB;AAAA,wBAClD;AACA,gCAAQ,MAAM,+BAA+B,mBAAmB,qBAAqB,QAAQ,WAAW;AAAA,sBAC1G;AAAA,oBACF,CAAC;AACD,6BAAS,KAAK,GAAG,KAAK,CAAC,UAAU,cAAc,cAAc,YAAY,GAAG,KAAK,GAAG,QAAQ,MAAM;AAChG,0BAAI,YAAY,GAAG,EAAE;AACrB,kCAAYA,SAAQ,SAAS,KAAK,CAAC,CAAC;AAAA,oBACtC;AACA,wBAAI,eAAeA,SAAQ,cAAc,KAAK,CAAC;AAC/C,6BAAS,KAAK,GAAG,KAAK,OAAO,KAAK,YAAY,GAAG,KAAK,GAAG,QAAQ,MAAM;AACrE,0BAAI,MAAM,GAAG,EAAE;AACf,kCAAY,aAAa,GAAG,KAAK,CAAC,CAAC;AAAA,oBACrC;AAAA,kBACF;AACA,2BAAS,KAAK,GAAG,KAAK,CAACF,SAAQ,UAAU,OAAO,GAAG,KAAK,GAAG,QAAQ,MAAM;AACvE,wBAAI,UAAU,GAAG,EAAE;AACnB,4BAAQ,OAAO;AAAA,kBACjB;AAAA,gBACF;AACA,gBAAAV,SAAQ,SAAS,IAAI;AACrB,yBAAS,YAAY,QAAQ;AAC3B,sBAAI,OAAO,WAAW;AACpB,4BAAQ,MAAM,oEAAoE;AAClF,wBAAI,CAAC,OAAO,eAAe;AACzB,6BAAO,gBAAgB,OAAO;AAAA,oBAChC;AAAA,kBACF,WAAW,OAAO,aAAa;AAC7B,4BAAQ,MAAM,kEAAkE;AAChF,wBAAI,CAAC,OAAO,WAAW;AACrB,6BAAO,YAAY,OAAO;AAAA,oBAC5B;AAAA,kBACF;AAAA,gBACF;AAAA,cAGF;AAAA;AAAA;AAAA,YACK;AAAA;AAAA,cAAW,SAAU,yBAAyBA,UAASK,sBAAqB;AAC/E,uBAAO,eAAeL,UAAS,cAAc;AAAA,kBAC3C,OAAO;AAAA,gBACT,CAAC;AACD,gBAAAA,SAAQ,SAASA,SAAQ,OAAOA,SAAQ,MAAMA,SAAQ,QAAQ;AAC9D,oBAAI,WAAWK,qBAAoB,GAAG;AACtC,oBAAI,aAAaA,qBAAoB,GAAG;AACxC,oBAAI,WAAWA,qBAAoB,GAAG;AACtC,oBAAI;AAAA;AAAA,kBAAqB,WAAY;AACnC,6BAASU,OAAM,OAAO,SAAS;AAC7B,2BAAK,aAAa;AAIlB,2BAAK,YAAY;AACjB,2BAAK,KAAK,MAAM;AAChB,2BAAK,WAAW,MAAM;AACtB,2BAAK,SAAS,MAAM;AACpB,2BAAK,QAAQ,MAAM;AACnB,2BAAK,UAAU,QAAQ;AACvB,2BAAK,OAAO,QAAQ;AACpB,2BAAK,OAAO,QAAQ;AACpB,2BAAK,OAAO,QAAQ;AAAA,oBACtB;AACA,oBAAAA,OAAM,UAAU,gBAAgB,SAAU,SAAS;AACjD,6BAAO,KAAK,KAAK,OAAO,SAAU,KAAK,KAAK;AAC1C,+BAAO,MAAM,IAAI,iBAAiB,OAAO;AAAA,sBAC3C,GAAG,CAAC;AAAA,oBACN;AACA,oBAAAA,OAAM,UAAU,gBAAgB,SAAU,SAAS;AACjD,6BAAO,KAAK,KAAK,OAAO,SAAU,KAAK,KAAK;AAC1C,+BAAO,MAAM,IAAI,iBAAiB,OAAO;AAAA,sBAC3C,GAAG,CAAC;AAAA,oBACN;AACA,oBAAAA,OAAM,UAAU,UAAU,WAAY;AACpC,6BAAO,KAAK,KAAK,OAAO,KAAK,IAAI,EAAE,OAAO,KAAK,IAAI;AAAA,oBACrD;AACA,oBAAAA,OAAM,UAAU,gBAAgB,SAAU,KAAK,UAAU,MAAM,KAAK,QAAQ,QAAQ;AAClF,+BAAS,KAAK,GAAG,aAAa,UAAU,KAAK,WAAW,QAAQ,MAAM;AACpE,4BAAI,UAAU,WAAW,EAAE;AAC3B,4BAAI,OAAO,IAAI,WAAW,aAAa,KAAK,MAAM,MAAM,KAAK,QAAQ,MAAM;AAC3E,4BAAI,SAAS,QAAQ,IAAI,MAAM;AAE/B,6BAAK,OAAO,MAAM,QAAQ,KAAK,IAAI,IAAI,KAAK,OAAO,CAAC,KAAK,IAAI;AAC7D,4BAAI,QAAQ;AACV,iCAAO;AAAA,wBACT;AAAA,sBACF;AACA,6BAAO;AAAA,oBACT;AACA,oBAAAA,OAAM,UAAU,mBAAmB,SAAU,KAAK,QAAQ;AACxD,0BAAI,YAAY,IAAI,UAAU;AAC9B,+BAAS,KAAK,GAAG,KAAK,KAAK,MAAM,aAAa,KAAK,GAAG,QAAQ,MAAM;AAClE,4BAAI,UAAU,GAAG,EAAE;AACnB,gCAAQ,IAAI,WAAW,SAAS,KAAK,MAAM,MAAM,CAAC;AAAA,sBACpD;AAAA,oBACF;AACA,oBAAAA,OAAM,UAAU,wBAAwB,SAAU,KAAK,QAAQ;AAC7D,+BAAS,KAAK,GAAG,KAAK,KAAK,MAAM,cAAc,KAAK,GAAG,QAAQ,MAAM;AACnE,4BAAI,UAAU,GAAG,EAAE;AACnB,gCAAQ,IAAI,WAAW,SAAS,KAAK,MAAM,MAAM,CAAC;AAAA,sBACpD;AAAA,oBACF;AACA,oBAAAA,OAAM,UAAU,WAAW,SAAU,WAAW;AAC9C,0BAAI,OAAO,KAAK,SAAS,eAAe,UAAU;AAChD,+BAAO,KAAK,SAAS;AAAA,sBACvB,WAAW,KAAK,SAAS,eAAe,QAAQ;AAC9C,4BAAI,eAAe,KAAK,QAAQ,OAAO,SAAU,OAAO,KAAK;AAC3D,iCAAO,QAAQ,IAAI;AAAA,wBACrB,GAAG,CAAC;AACJ,+BAAO;AAAA,sBACT,OAAO;AACL,4BAAI,SAAS,KAAK,SAAS;AAC3B,+BAAO,YAAY,OAAO,OAAO,OAAO;AAAA,sBAC1C;AAAA,oBACF;AACA,2BAAOA;AAAA,kBACT,EAAE;AAAA;AACF,gBAAAf,SAAQ,QAAQ;AAChB,oBAAI;AAAA;AAAA,kBAAmB,WAAY;AACjC,6BAASgB,KAAI,KAAK,OAAO,SAAS,OAAO,oBAAoB;AAC3D,0BAAI,uBAAuB,QAAQ;AACjC,6CAAqB;AAAA,sBACvB;AACA,2BAAK,SAAS;AACd,2BAAK,MAAM;AACX,0BAAI,eAAe,SAAS,cAAc;AACxC,6BAAK,MAAM,IAAI;AACf,6BAAK,UAAU,IAAI;AAAA,sBACrB;AACA,2BAAK,QAAQ;AACb,2BAAK,UAAU;AACf,2BAAK,QAAQ;AACb,2BAAK,qBAAqB;AAAA,oBAC5B;AACA,oBAAAA,KAAI,UAAU,mBAAmB,SAAU,SAAS;AAClD,0BAAI,QAAQ;AACZ,6BAAO,QAAQ,OAAO,SAAU,KAAK,QAAQ;AAC3C,4BAAI;AACJ,+BAAO,KAAK,IAAI,OAAO,KAAK,MAAM,MAAM,OAAO,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,CAAC;AAAA,sBAC7G,GAAG,CAAC;AAAA,oBACN;AACA,oBAAAA,KAAI,UAAU,aAAa,SAAU,SAAS;AAC5C,0BAAI,QAAQ;AACZ,6BAAO,QAAQ,OAAO,SAAU,QAAQ;AACtC,4BAAI,OAAO,MAAM,MAAM,OAAO,KAAK;AACnC,4BAAI,CAAC,KAAM,QAAO;AAClB,+BAAO,KAAK,UAAU;AAAA,sBACxB,CAAC,EAAE,SAAS;AAAA,oBACd;AACA,oBAAAA,KAAI,UAAU,kBAAkB,SAAU,QAAQ,SAAS;AACzD,6BAAO,KAAK,iBAAiB,OAAO,KAAK;AAAA,oBAC3C;AACA,oBAAAA,KAAI,UAAU,sBAAsB,SAAU,SAAS,KAAK;AAC1D,0BAAI,QAAQ;AACZ,6BAAO,QAAQ,OAAO,SAAU,KAAK,QAAQ;AAC3C,4BAAI,OAAO,MAAM,MAAM,OAAO,KAAK;AACnC,4BAAI,CAAC,KAAM,QAAO;AAClB,4BAAI,aAAa,IAAI,cAAc,KAAK,OAAO,QAAQ;AACvD,4BAAI,WAAW,KAAK,QAAQ,UAAU;AACtC,4BAAI,eAAe,WAAW;AAC9B,+BAAO,eAAe,MAAM,eAAe;AAAA,sBAC7C,GAAG,CAAC;AAAA,oBACN;AACA,2BAAOA;AAAA,kBACT,EAAE;AAAA;AACF,gBAAAhB,SAAQ,MAAM;AACd,oBAAI;AAAA;AAAA,kBAAoB,WAAY;AAClC,6BAASiB,MAAK,KAAK,QAAQ,SAAS;AAClC,0BAAI,IAAI;AACR,2BAAK,gBAAgB;AACrB,2BAAK,eAAe;AACpB,2BAAK,eAAe;AACpB,2BAAK,mBAAmB;AACxB,2BAAK,WAAW;AAChB,2BAAK,QAAQ;AACb,2BAAK,SAAS;AACd,2BAAK,IAAI;AACT,2BAAK,IAAI;AACT,2BAAK,SAAS;AACd,2BAAK,UAAU;AACf,2BAAK,MAAM;AACX,0BAAI,UAAU;AACd,0BAAI,OAAO,QAAQ,OAAO,QAAQ,YAAY,CAAC,MAAM,QAAQ,GAAG,GAAG;AACjE,6BAAK,UAAU,IAAI,WAAW;AAC9B,6BAAK,UAAU,IAAI,WAAW;AAC9B,mCAAW,MAAM,KAAK,IAAI,aAAa,QAAQ,OAAO,SAAS,KAAK,IAAI,WAAW,QAAQ,OAAO,SAAS,KAAK;AAChH,4BAAI,IAAI,UAAU;AAChB,+BAAK,MAAM,IAAI;AAAA,wBACjB;AAAA,sBACF,OAAO;AACL,6BAAK,UAAU;AACf,6BAAK,UAAU;AAAA,sBACjB;AAEA,0BAAI,OAAO,WAAW,OAAO,KAAK,UAAU;AAC5C,0BAAI,aAAa;AACjB,2BAAK,OAAO,KAAK,MAAM,UAAU;AAAA,oBACnC;AACA,oBAAAA,MAAK,UAAU,aAAa,WAAY;AACtC,0BAAI;AACJ,0BAAI,KAAK,OAAO,WAAW,OAAO;AAChC,4BAAI,KAAK,IAAI,KAAK,QAAQ,KAAK;AAAA,sBACjC,WAAW,KAAK,OAAO,WAAW,UAAU;AAC1C,4BAAI,KAAK,IAAI,KAAK,SAAS,KAAK,QAAQ,QAAQ;AAAA,sBAClD,OAAO;AACL,4BAAI,YAAY,KAAK,SAAS,KAAK,QAAQ,UAAU;AACrD,4BAAI,KAAK,IAAI,YAAY,IAAI,KAAK,QAAQ,KAAK;AAAA,sBACjD;AACA,0BAAI;AACJ,0BAAI,KAAK,OAAO,WAAW,SAAS;AAClC,4BAAI,KAAK,IAAI,KAAK,QAAQ,KAAK,QAAQ,OAAO;AAAA,sBAChD,WAAW,KAAK,OAAO,WAAW,UAAU;AAC1C,4BAAI,WAAW,KAAK,QAAQ,KAAK,QAAQ,YAAY;AACrD,4BAAI,KAAK,IAAI,WAAW,IAAI,KAAK,QAAQ,MAAM;AAAA,sBACjD,OAAO;AACL,4BAAI,KAAK,IAAI,KAAK,QAAQ,MAAM;AAAA,sBAClC;AACA,6BAAO;AAAA,wBACL;AAAA,wBACA;AAAA,sBACF;AAAA,oBACF;AAEA,oBAAAA,MAAK,UAAU,mBAAmB,SAAU,aAAa,kBAAkB;AACzE,0BAAI,qBAAqB,QAAQ;AAC/B,2CAAmB;AAAA,sBACrB;AACA,0BAAI,YAAY,MAAM,QAAQ,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;AAC9D,0BAAI,aAAa,KAAK,OAAO,WAAW,cAAc;AACtD,0BAAI,SAAS,YAAY,aAAa,KAAK,QAAQ,UAAU;AAC7D,6BAAO,KAAK,IAAI,QAAQ,KAAK,OAAO,aAAa;AAAA,oBACnD;AACA,oBAAAA,MAAK,UAAU,UAAU,SAAU,MAAM;AACvC,0BAAI,WAAW,GAAG,SAAS,cAAc,KAAK,OAAO,aAAa,CAAC;AACnE,0BAAI,SAAS,YAAY;AACvB,+BAAO,QAAQ,MAAM,QAAQ;AAAA,sBAC/B,WAAW,SAAS,cAAc;AAChC,+BAAO,QAAQ,OAAO,QAAQ;AAAA,sBAChC,OAAO;AACL,+BAAO,QAAQ,IAAI;AAAA,sBACrB;AAAA,oBACF;AACA,2BAAOA;AAAA,kBACT,EAAE;AAAA;AACF,gBAAAjB,SAAQ,OAAO;AACf,oBAAI;AAAA;AAAA,kBAAsB,WAAY;AACpC,6BAASkB,QAAO,SAAS,KAAK,OAAO;AACnC,2BAAK,eAAe;AACpB,2BAAK,mBAAmB;AACxB,2BAAK,WAAW;AAChB,2BAAK,QAAQ;AACb,2BAAK,UAAU;AACf,2BAAK,MAAM;AACX,2BAAK,QAAQ;AAAA,oBACf;AACA,oBAAAA,QAAO,UAAU,wBAAwB,SAAU,OAAO;AACxD,0BAAI,MAAM;AACV,+BAAS,KAAK,GAAG,KAAK,MAAM,QAAQ,GAAG,KAAK,GAAG,QAAQ,MAAM;AAC3D,4BAAI,MAAM,GAAG,EAAE;AACf,4BAAI,OAAO,IAAI,MAAM,KAAK,KAAK;AAC/B,4BAAI,QAAQ,OAAO,KAAK,OAAO,cAAc,UAAU;AACrD,gCAAM,KAAK,IAAI,KAAK,KAAK,OAAO,SAAS;AAAA,wBAC3C;AAAA,sBACF;AACA,6BAAO;AAAA,oBACT;AACA,2BAAOA;AAAA,kBACT,EAAE;AAAA;AACF,gBAAAlB,SAAQ,SAAS;AAAA,cAGnB;AAAA;AAAA;AAAA,YACK;AAAA;AAAA,cAAW,SAAU,yBAAyBA,UAAS;AAE1D,uBAAO,eAAeA,UAAS,cAAc;AAAA,kBAC3C,OAAO;AAAA,gBACT,CAAC;AACD,gBAAAA,SAAQ,SAAS;AAEjB,yBAAS,OAAO,QAAQ,GAAG,IAAI,IAAI,IAAI;AACrC,sBAAI,UAAU,MAAM;AAClB,0BAAM,IAAI,UAAU,4CAA4C;AAAA,kBAClE;AACA,sBAAI,KAAK,OAAO,MAAM;AACtB,2BAAS,QAAQ,GAAG,QAAQ,UAAU,QAAQ,SAAS;AAErD,wBAAI,aAAa,UAAU,KAAK;AAChC,wBAAI,cAAc,MAAM;AAEtB,+BAAS,WAAW,YAAY;AAE9B,4BAAI,OAAO,UAAU,eAAe,KAAK,YAAY,OAAO,GAAG;AAC7D,6BAAG,OAAO,IAAI,WAAW,OAAO;AAAA,wBAClC;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF;AACA,yBAAO;AAAA,gBACT;AACA,gBAAAA,SAAQ,SAAS;AAAA,cAGnB;AAAA;AAAA;AAAA,YACK;AAAA;AAAA,cAAW,SAAU,yBAAyBA,UAASK,sBAAqB;AAC/E,uBAAO,eAAeL,UAAS,cAAc;AAAA,kBAC3C,OAAO;AAAA,gBACT,CAAC;AACD,gBAAAA,SAAQ,cAAc;AACtB,oBAAI,oBAAoBK,qBAAoB,GAAG;AAC/C,oBAAI,WAAWA,qBAAoB,GAAG;AACtC,oBAAI,oBAAoBA,qBAAoB,GAAG;AAC/C,oBAAI,WAAWA,qBAAoB,GAAG;AACtC,oBAAI,cAAcA,qBAAoB,GAAG;AACzC,yBAAS,YAAY,UAAU,OAAO;AACpC,sBAAI,MAAM,IAAI,kBAAkB,WAAW,QAAQ;AACnD,sBAAI,UAAU,aAAa,OAAO,IAAI,YAAY,CAAC;AACnD,sBAAI,QAAQ,IAAI,SAAS,MAAM,OAAO,OAAO;AAC7C,mBAAC,GAAG,kBAAkB,iBAAiB,KAAK,KAAK;AACjD,sBAAI,YAAY,IAAI,UAAU;AAC9B,yBAAO;AAAA,gBACT;AACA,gBAAAL,SAAQ,cAAc;AACtB,yBAAS,aAAa,OAAO,IAAI;AAC/B,sBAAI,UAAU,MAAM;AACpB,sBAAI,UAAU,cAAc,QAAQ,OAAO;AAE3C,sBAAI,QAAQ,KAAK,WAAW,GAAG;AAC7B,wBAAI,aAAa,mBAAmB,SAAS,MAAM;AACnD,wBAAI,WAAY,SAAQ,KAAK,KAAK,UAAU;AAAA,kBAC9C;AACA,sBAAI,QAAQ,KAAK,WAAW,GAAG;AAC7B,wBAAI,aAAa,mBAAmB,SAAS,MAAM;AACnD,wBAAI,WAAY,SAAQ,KAAK,KAAK,UAAU;AAAA,kBAC9C;AACA,sBAAI,QAAQ,MAAM,SAAS;AAC3B,sBAAI,SAAS,MAAM;AACnB,yBAAO;AAAA,oBACL;AAAA,oBACA,MAAM,aAAa,QAAQ,QAAQ,MAAM,SAAS,QAAQ,OAAO,EAAE;AAAA,oBACnE,MAAM,aAAa,QAAQ,QAAQ,MAAM,SAAS,QAAQ,OAAO,EAAE;AAAA,oBACnE,MAAM,aAAa,QAAQ,QAAQ,MAAM,SAAS,QAAQ,OAAO,EAAE;AAAA,kBACrE;AAAA,gBACF;AACA,yBAAS,aAAa,aAAa,aAAa,SAAS,YAAY,OAAO,aAAa;AACvF,sBAAI,wBAAwB,CAAC;AAC7B,sBAAI,SAAS,YAAY,IAAI,SAAU,QAAQ,UAAU;AACvD,wBAAI,wBAAwB;AAC5B,wBAAI,QAAQ,CAAC;AACb,wBAAI,gBAAgB;AACpB,wBAAI,kBAAkB;AACtB,6BAAS,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,QAAQ,MAAM;AACjE,0BAAI,SAAS,UAAU,EAAE;AACzB,0BAAI,sBAAsB,OAAO,KAAK,KAAK,QAAQ,sBAAsB,OAAO,KAAK,EAAE,SAAS,GAAG;AACjG,4BAAI,oBAAoB,GAAG;AACzB,8BAAI,UAAU;AACd,8BAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,sCAAU,OAAO,OAAO,QAAQ,gBAAgB,qBAAqB;AAAA,0BACvE,OAAO;AACL,sCAAU,OAAO,OAAO,OAAO;AAAA,0BACjC;AACA,8BAAI,kBAAkB,CAAC;AACvB,8BAAI,OAAO,YAAY,YAAY,CAAC,MAAM,QAAQ,OAAO,GAAG;AAC1D,+CAAmB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,WAAW,CAAC;AAAA,0BAC3F;AACA,8BAAI,SAAS,WAAW,aAAa,QAAQ,UAAU,OAAO,YAAY,aAAa,eAAe;AACtG,8BAAI,OAAO,IAAI,SAAS,KAAK,SAAS,QAAQ,WAAW;AAGzD,gCAAM,OAAO,OAAO,IAAI;AACxB,gCAAM,OAAO,KAAK,IAAI;AACtB,4CAAkB,KAAK,UAAU;AACjC,gDAAsB,OAAO,KAAK,IAAI;AAAA,4BACpC,MAAM,KAAK,UAAU;AAAA,4BACrB,OAAO;AAAA,0BACT;AAAA,wBACF,OAAO;AACL;AACA;AAAA,wBACF;AAAA,sBACF,OAAO;AACL,8CAAsB,OAAO,KAAK,EAAE;AACpC,0CAAkB,sBAAsB,OAAO,KAAK,EAAE;AACtD;AAAA,sBACF;AAAA,oBACF;AACA,2BAAO,IAAI,SAAS,IAAI,QAAQ,UAAU,aAAa,KAAK;AAAA,kBAC9D,CAAC;AACD,yBAAO;AAAA,gBACT;AACA,yBAAS,mBAAmB,SAAS,SAAS;AAC5C,sBAAI,aAAa,CAAC;AAClB,0BAAQ,QAAQ,SAAU,KAAK;AAC7B,wBAAI,IAAI,OAAO,MAAM;AACnB,0BAAI,QAAQ,gBAAgB,SAAS,IAAI,GAAG;AAC5C,0BAAI,SAAS,KAAM,YAAW,IAAI,OAAO,IAAI;AAAA,oBAC/C;AAAA,kBACF,CAAC;AACD,yBAAO,OAAO,KAAK,UAAU,EAAE,SAAS,IAAI,aAAa;AAAA,gBAC3D;AACA,yBAAS,gBAAgB,SAAS,QAAQ;AACxC,sBAAI,YAAY,QAAQ;AACtB,wBAAI,OAAO,WAAW,UAAU;AAC9B,6BAAO,OAAO,UAAU,OAAO,SAAS;AAAA,oBAC1C,WAAW,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;AACnE,6BAAO;AAAA,oBACT;AAAA,kBACF,WAAW,YAAY,UAAU,OAAO,WAAW,UAAU;AAC3D,2BAAO,OAAO;AAAA,kBAChB;AACA,yBAAO;AAAA,gBACT;AACA,yBAAS,cAAc,SAAS;AAC9B,yBAAO,QAAQ,IAAI,SAAU,OAAO,OAAO;AACzC,wBAAI,IAAI;AACR,wBAAI;AACJ,wBAAI,OAAO,UAAU,UAAU;AAC7B,6BAAO,MAAM,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,KAAK,MAAM,SAAS,QAAQ,OAAO,SAAS,KAAK;AAAA,oBAChH,OAAO;AACL,4BAAM;AAAA,oBACR;AACA,2BAAO,IAAI,SAAS,OAAO,KAAK,OAAO,KAAK;AAAA,kBAC9C,CAAC;AAAA,gBACH;AACA,yBAAS,WAAW,aAAa,QAAQ,UAAU,WAAW,QAAQ,aAAa,iBAAiB;AAClG,sBAAI,SAAS,GAAG,SAAS,UAAU,SAAS;AAC5C,sBAAI;AACJ,sBAAI,gBAAgB,QAAQ;AAC1B,oCAAgB,OAAO;AAAA,kBACzB,WAAW,gBAAgB,QAAQ;AACjC,oCAAgB,OAAO;AAAA,kBACzB,WAAW,gBAAgB,QAAQ;AACjC,oCAAgB,OAAO;AAAA,kBACzB;AACA,sBAAI,eAAe,GAAG,YAAY,QAAQ,CAAC,GAAG,MAAM,OAAO,MAAM,WAAW,GAAG,OAAO,QAAQ,aAAa;AAC3G,sBAAI,eAAe,OAAO,aAAa,OAAO,OAAO,KAAK,OAAO,aAAa,OAAO,KAAK,KAAK,CAAC;AAChG,sBAAI,YAAY,gBAAgB,SAAS,eAAe,CAAC;AACzD,sBAAI,YAAY,gBAAgB,UAAU,WAAW,MAAM,KAAK,GAAG,YAAY,QAAQ,CAAC,GAAG,MAAM,cAAc,OAAO,kBAAkB,IAAI,CAAC;AAC7I,sBAAI,gBAAgB,GAAG,SAAS,eAAe,WAAW;AAC1D,sBAAI,eAAe,GAAG,YAAY,QAAQ,CAAC,GAAG,cAAc,aAAa,WAAW,SAAS;AAC7F,0BAAQ,GAAG,YAAY,QAAQ,aAAa,eAAe;AAAA,gBAC7D;AAAA,cAGF;AAAA;AAAA;AAAA,YACK;AAAA;AAAA,cAAW,SAAU,yBAAyBA,UAASK,sBAAqB;AAC/E,uBAAO,eAAeL,UAAS,cAAc;AAAA,kBAC3C,OAAO;AAAA,gBACT,CAAC;AACD,gBAAAA,SAAQ,UAAUA,SAAQ,YAAY;AACtC,oBAAI,WAAWK,qBAAoB,GAAG;AACtC,oBAAI,WAAWA,qBAAoB,GAAG;AACtC,oBAAI,oBAAoBA,qBAAoB,GAAG;AAC/C,oBAAI,cAAcA,qBAAoB,GAAG;AACzC,oBAAI,kBAAkBA,qBAAoB,GAAG;AAC7C,oBAAI,iBAAiBA,qBAAoB,GAAG;AAC5C,yBAAS,UAAU,UAAU,OAAO;AAClC,sBAAI,WAAW,MAAM;AACrB,sBAAI,SAAS,SAAS;AACtB,sBAAI,SAAS,SAAS;AACtB,sBAAI,SAAS;AAAA,oBACX,GAAG,OAAO;AAAA,oBACV,GAAG;AAAA,kBACL;AACA,sBAAI,iBAAiB,MAAM,cAAc,MAAM,OAAO,IAAI,MAAM,cAAc,MAAM,OAAO;AAC3F,sBAAI,oBAAoB,SAAS,OAAO,SAAS;AACjD,sBAAI,SAAS,cAAc,SAAS;AAClC,wBAAI,OAAO,MAAM;AACjB,wBAAI,cAAc,KAAK,OAAO,SAAU,KAAK,KAAK;AAChD,6BAAO,MAAM,IAAI;AAAA,oBACnB,GAAG,CAAC;AACJ,yCAAqB;AAAA,kBACvB;AACA,sBAAI,MAAM,IAAI,kBAAkB,WAAW,QAAQ;AACnD,sBAAI,SAAS,cAAc,YAAY,SAAS,UAAU,QAAQ,oBAAoB,IAAI,SAAS,EAAE,QAAQ;AAC3G,6BAAS,GAAG;AACZ,2BAAO,IAAI,OAAO;AAAA,kBACpB;AACA,wBAAM,sBAAsB,KAAK,MAAM;AACvC,sBAAI,YAAY,GAAG,YAAY,QAAQ,CAAC,GAAG,MAAM;AACjD,wBAAM,kBAAkB,IAAI,WAAW;AACvC,sBAAI,SAAS,qBAAqB;AAEhC,sDAAkC,KAAK,OAAO,UAAU,MAAM;AAAA,kBAChE,OAAO;AAEL,wBAAI,YAAY,IAAI,UAAU;AAC9B,wBAAI,SAAS,aAAa,eAAe,SAAS,aAAa,aAAa;AAC1E,4BAAM,KAAK,QAAQ,SAAU,KAAK;AAChC,+BAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,MAAM,OAAO;AAAA,sBACxD,CAAC;AAAA,oBACH;AACA,wBAAI,YAAY,IAAI,UAAU;AAC9B,0BAAM,KAAK,QAAQ,SAAU,KAAK,OAAO;AACvC,0BAAI,YAAY,UAAU,MAAM,KAAK,SAAS;AAC9C,mCAAa,KAAK,OAAO,KAAK,WAAW,UAAU,QAAQ,MAAM,OAAO;AAAA,oBAC1E,CAAC;AACD,wBAAI,YAAY,IAAI,UAAU;AAC9B,wBAAI,SAAS,aAAa,cAAc,SAAS,aAAa,aAAa;AACzE,4BAAM,KAAK,QAAQ,SAAU,KAAK;AAChC,+BAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,MAAM,OAAO;AAAA,sBACxD,CAAC;AAAA,oBACH;AAAA,kBACF;AACA,mBAAC,GAAG,SAAS,gBAAgB,KAAK,OAAO,UAAU,MAAM;AACzD,wBAAM,iBAAiB,KAAK,MAAM;AAClC,wBAAM,SAAS,OAAO;AACtB,2BAAS,gBAAgB;AACzB,2BAAS,oBAAoB;AAC7B,sBAAI,SAAS,UAAW,UAAS,UAAU,WAAW;AACtD,sBAAI,YAAY,IAAI,UAAU;AAAA,gBAChC;AACA,gBAAAL,SAAQ,YAAY;AACpB,yBAAS,kCAAkC,KAAK,OAAO,UAAU,QAAQ;AAEvE,sBAAI,0BAA0B,GAAG,eAAe,iCAAiC,KAAK,KAAK;AAC3F,sBAAI,WAAW,MAAM;AACrB,sBAAI,SAAS,iCAAiC,gBAAgB;AAC5D,2CAAuB,QAAQ,SAAU,gBAAgB,OAAO;AAC9D,0BAAI,YAAY,IAAI,UAAU;AAE9B,0BAAI,QAAQ,GAAG;AAGb,gCAAQ,KAAK,OAAO,UAAU,QAAQ,eAAe,SAAS,IAAI;AAAA,sBACpE,OAAO;AAEL,kCAAU,KAAK,OAAO,QAAQ,eAAe,OAAO;AAAA,sBACtD;AAEA,gCAAU,KAAK,OAAO,UAAU,QAAQ,eAAe,OAAO;AAC9D,gCAAU,KAAK,OAAO,QAAQ,eAAe,OAAO;AAAA,oBACtD,CAAC;AAAA,kBACH,OAAO;AACL,wBAAI,2BAA2B;AAC/B,wBAAI,0BAA0B,uBAAuB,CAAC;AACtD,wBAAI,UAAU,WAAY;AAExB,0BAAI,sBAAsB;AAC1B,0BAAI,yBAAyB;AAC3B,4BAAI,YAAY,IAAI,UAAU;AAC9B,4BAAI,oBAAoB,wBAAwB;AAChD,4BAAI,4BAA4B,GAAG;AAGjC,kCAAQ,KAAK,OAAO,UAAU,QAAQ,mBAAmB,IAAI;AAAA,wBAC/D,OAAO;AACL,oCAAU,KAAK,OAAO,QAAQ,iBAAiB;AAAA,wBACjD;AACA,8CAAsB,2BAA2B,KAAK,OAAO,2BAA2B,GAAG,QAAQ,iBAAiB;AACpH,kCAAU,KAAK,OAAO,QAAQ,iBAAiB;AAAA,sBACjD;AAEA,0BAAI,kBAAkB,sBAAsB;AAE5C,6CAAuB,MAAM,CAAC,EAAE,QAAQ,SAAU,gBAAgB;AAChE,4BAAI,YAAY,IAAI,UAAU;AAG9B,gCAAQ,KAAK,OAAO,UAAU,QAAQ,eAAe,SAAS,IAAI;AAClE,mDAA2B,KAAK,OAAO,2BAA2B,GAAG,QAAQ,eAAe,SAAS,eAAe;AACpH,kCAAU,KAAK,OAAO,QAAQ,eAAe,OAAO;AAAA,sBACtD,CAAC;AACD,iDAA2B;AAAA,oBAC7B;AACA,2BAAO,2BAA2B,MAAM,KAAK,SAAS,GAAG;AACvD,8BAAQ;AAAA,oBACV;AAAA,kBACF;AAAA,gBACF;AACA,yBAAS,UAAU,KAAK,OAAO,QAAQ,SAAS;AAC9C,sBAAI,WAAW,MAAM;AACrB,sBAAI,YAAY,IAAI,UAAU;AAC9B,sBAAI,SAAS,aAAa,eAAe,SAAS,aAAa,aAAa;AAC1E,0BAAM,KAAK,QAAQ,SAAU,KAAK;AAChC,6BAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AAAA,oBAClD,CAAC;AAAA,kBACH;AAAA,gBACF;AACA,yBAAS,UAAU,KAAK,OAAO,UAAU,QAAQ,SAAS;AACxD,sBAAI,YAAY,IAAI,UAAU;AAC9B,wBAAM,KAAK,QAAQ,SAAU,KAAK,OAAO;AACvC,wBAAI,YAAY,UAAU,MAAM,KAAK,SAAS;AAC9C,iCAAa,KAAK,OAAO,KAAK,WAAW,UAAU,QAAQ,OAAO;AAAA,kBACpE,CAAC;AAAA,gBACH;AACA,yBAAS,2BAA2B,KAAK,OAAO,eAAe,QAAQ,SAAS,iBAAiB;AAC/F,sBAAI,YAAY,IAAI,UAAU;AAC9B,oCAAkB,oBAAoB,QAAQ,oBAAoB,SAAS,kBAAkB,MAAM,KAAK;AACxG,sBAAI,cAAc,KAAK,IAAI,gBAAgB,iBAAiB,MAAM,KAAK,MAAM;AAC7E,sBAAI,sBAAsB;AAC1B,wBAAM,KAAK,MAAM,eAAe,WAAW,EAAE,QAAQ,SAAU,KAAK,OAAO;AACzE,wBAAI,YAAY,gBAAgB,UAAU,MAAM,KAAK,SAAS;AAC9D,wBAAI,iBAAiB,sBAAsB,KAAK,OAAO,WAAW,MAAM;AACxE,wBAAI,IAAI,gBAAgB,gBAAgB,OAAO,GAAG;AAChD,+BAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AACzC,4CAAsB,gBAAgB;AAAA,oBACxC;AAAA,kBACF,CAAC;AACD,yBAAO;AAAA,gBACT;AACA,yBAAS,UAAU,KAAK,OAAO,QAAQ,SAAS;AAC9C,sBAAI,WAAW,MAAM;AACrB,sBAAI,YAAY,IAAI,UAAU;AAC9B,sBAAI,SAAS,aAAa,cAAc,SAAS,aAAa,aAAa;AACzE,0BAAM,KAAK,QAAQ,SAAU,KAAK;AAChC,6BAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AAAA,oBAClD,CAAC;AAAA,kBACH;AAAA,gBACF;AACA,yBAAS,sBAAsB,MAAM,oBAAoB,KAAK;AAC5D,sBAAI,aAAa,IAAI,cAAc,KAAK,OAAO,QAAQ;AACvD,sBAAI,WAAW,KAAK,QAAQ,UAAU;AACtC,sBAAI,iBAAiB,KAAK,OAAO,qBAAqB,YAAY,UAAU;AAC5E,yBAAO,KAAK,IAAI,GAAG,cAAc;AAAA,gBACnC;AACA,yBAAS,eAAe,KAAK,oBAAoB,OAAO,KAAK;AAC3D,sBAAI,QAAQ,CAAC;AACb,sBAAI,qBAAqB;AACzB,sBAAI,SAAS;AACb,sBAAI,YAAY;AAChB,2BAAS,KAAK,GAAG,KAAK,MAAM,SAAS,KAAK,GAAG,QAAQ,MAAM;AACzD,wBAAI,SAAS,GAAG,EAAE;AAClB,wBAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,wBAAI,CAAC,KAAM;AACX,wBAAI,CAAC,MAAM,QAAQ,KAAK,IAAI,GAAG;AAC7B,2BAAK,OAAO,CAAC,KAAK,IAAI;AAAA,oBACxB;AACA,wBAAI,gBAAgB,IAAI,SAAS,KAAK,KAAK,KAAK,KAAK,QAAQ,KAAK,OAAO;AACzE,qCAAiB,GAAG,YAAY,QAAQ,eAAe,IAAI;AAC3D,kCAAc,OAAO,CAAC;AACtB,wBAAI,qBAAqB,sBAAsB,MAAM,oBAAoB,GAAG;AAC5E,wBAAI,KAAK,KAAK,SAAS,oBAAoB;AACzC,oCAAc,OAAO,KAAK,KAAK,OAAO,oBAAoB,KAAK,KAAK,MAAM;AAAA,oBAC5E;AACA,wBAAI,cAAc,IAAI,YAAY;AAClC,wBAAI,mBAAmB,IAAI,oBAAoB;AAC/C,yBAAK,gBAAgB,KAAK,iBAAiB,aAAa,gBAAgB;AACxE,wBAAI,KAAK,iBAAiB,oBAAoB;AAC5C,2BAAK,gBAAgB;AACrB,oCAAc,OAAO,iBAAiB;AAAA,oBACxC;AACA,wBAAI,KAAK,gBAAgB,IAAI,QAAQ;AACnC,0BAAI,SAAS,KAAK;AAAA,oBACpB;AACA,kCAAc,gBAAgB,cAAc,iBAAiB,aAAa,gBAAgB;AAC1F,wBAAI,cAAc,gBAAgB,WAAW;AAC3C,kCAAY,cAAc;AAAA,oBAC5B;AACA,0BAAM,OAAO,KAAK,IAAI;AAAA,kBACxB;AACA,sBAAI,eAAe,IAAI,SAAS,IAAI,IAAI,KAAK,IAAI,IAAI,SAAS,OAAO,IAAI;AACzE,+BAAa,SAAS;AACtB,2BAAS,KAAK,GAAG,KAAK,MAAM,SAAS,KAAK,GAAG,QAAQ,MAAM;AACzD,wBAAI,SAAS,GAAG,EAAE;AAClB,wBAAI,gBAAgB,aAAa,MAAM,OAAO,KAAK;AACnD,wBAAI,eAAe;AACjB,oCAAc,SAAS,aAAa;AAAA,oBACtC;AACA,wBAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,wBAAI,MAAM;AACR,2BAAK,SAAS,IAAI;AAAA,oBACpB;AAAA,kBACF;AACA,yBAAO;AAAA,gBACT;AACA,yBAAS,yBAAyB,KAAK,KAAK,oBAAoB,OAAO;AACrE,sBAAI,aAAa,IAAI,SAAS,EAAE;AAChC,sBAAI,SAAS,MAAM,SAAS;AAC5B,sBAAI,eAAe,OAAO,MAAM,OAAO;AACvC,sBAAI,eAAe,aAAa;AAChC,sBAAI,IAAI,YAAY,QAAQ;AAG1B,oCAAgB,MAAM,cAAc,MAAM,OAAO,IAAI,MAAM,cAAc,MAAM,OAAO;AAAA,kBACxF;AACA,sBAAI,eAAe,IAAI,oBAAoB,MAAM,SAAS,GAAG;AAC7D,sBAAI,aAAa,eAAe;AAChC,sBAAI,eAAe,cAAc;AAC/B,4BAAQ,MAAM,iCAAiC,OAAO,IAAI,OAAO,iEAAiE,CAAC;AACnI,2BAAO;AAAA,kBACT;AACA,sBAAI,CAAC,YAAY;AACf,2BAAO;AAAA,kBACT;AACA,sBAAI,oBAAoB,IAAI,WAAW,MAAM,OAAO;AACpD,sBAAI,oBAAoB,IAAI,iBAAiB,MAAM,OAAO,IAAI;AAC9D,sBAAI,mBAAmB;AACrB,wBAAI,mBAAmB;AACrB,8BAAQ,MAAM,sBAAsB,OAAO,IAAI,OAAO,yIAAyI,CAAC;AAAA,oBAClM;AACA,2BAAO;AAAA,kBACT;AACA,sBAAI,mBAAmB;AAErB,2BAAO;AAAA,kBACT;AACA,sBAAI,MAAM,SAAS,iBAAiB,SAAS;AAC3C,2BAAO;AAAA,kBACT;AAEA,yBAAO;AAAA,gBACT;AACA,yBAAS,aAAa,KAAK,OAAO,KAAK,WAAW,UAAU,QAAQ,SAAS;AAC3E,sBAAI,iBAAiB,sBAAsB,KAAK,OAAO,WAAW,MAAM;AACxE,sBAAI,IAAI,gBAAgB,gBAAgB,OAAO,GAAG;AAEhD,6BAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AAAA,kBAC3C,WAAW,yBAAyB,KAAK,KAAK,gBAAgB,KAAK,GAAG;AAEpE,wBAAI,eAAe,eAAe,KAAK,gBAAgB,OAAO,GAAG;AACjE,6BAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AACzC,4BAAQ,KAAK,OAAO,UAAU,QAAQ,OAAO;AAC7C,iCAAa,KAAK,OAAO,cAAc,WAAW,UAAU,QAAQ,OAAO;AAAA,kBAC7E,OAAO;AAEL,4BAAQ,KAAK,OAAO,UAAU,QAAQ,OAAO;AAC7C,iCAAa,KAAK,OAAO,KAAK,WAAW,UAAU,QAAQ,OAAO;AAAA,kBACpE;AAAA,gBACF;AACA,yBAAS,SAAS,KAAK,OAAO,KAAK,QAAQ,SAAS;AAClD,yBAAO,IAAI,MAAM,SAAS,OAAO;AACjC,2BAAS,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,QAAQ,MAAM;AACjE,wBAAI,SAAS,UAAU,EAAE;AACzB,wBAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,wBAAI,CAAC,MAAM;AACT,6BAAO,KAAK,OAAO;AACnB;AAAA,oBACF;AACA,wBAAI,YAAY,KAAK,MAAM;AAC3B,yBAAK,IAAI,OAAO;AAChB,yBAAK,IAAI,OAAO;AAChB,wBAAI,SAAS,MAAM,cAAc,KAAK,MAAM,MAAM,cAAc,MAAM,KAAK,QAAQ,MAAM;AACzF,wBAAI,WAAW,OAAO;AACpB,6BAAO,KAAK,OAAO;AACnB;AAAA,oBACF;AACA,iCAAa,KAAK,MAAM,MAAM;AAC9B,wBAAI,UAAU,KAAK,WAAW;AAC9B,qBAAC,GAAG,gBAAgB,SAAS,KAAK,MAAM,QAAQ,GAAG,QAAQ,GAAG;AAAA,sBAC5D,QAAQ,KAAK,OAAO;AAAA,sBACpB,QAAQ,KAAK,OAAO;AAAA,sBACpB,UAAU,KAAK,KAAK,KAAK,QAAQ,KAAK,QAAQ,MAAM,IAAI,KAAK,QAAQ,OAAO,CAAC;AAAA,oBAC/E,GAAG,IAAI,YAAY,CAAC;AACpB,0BAAM,cAAc,KAAK,MAAM,MAAM,aAAa,MAAM,KAAK,QAAQ,MAAM;AAC3E,2BAAO,KAAK,OAAO;AAAA,kBACrB;AACA,yBAAO,KAAK,IAAI;AAAA,gBAClB;AACA,yBAAS,aAAa,KAAK,MAAM,QAAQ;AACvC,sBAAI,aAAa,KAAK;AAGtB,sBAAI,YAAY,EAAE,aAAa,IAAI,YAAY,EAAE,aAAa,CAAC;AAC/D,sBAAI,OAAO,WAAW,cAAc,UAAU;AAE5C,wBAAI,aAAa,GAAG,SAAS,cAAc,WAAW,WAAW,WAAW,SAAS;AACrF,wBAAI,WAAW;AACb,0BAAI,KAAK,KAAK,GAAG,OAAO,GAAG,KAAK,OAAO,KAAK,QAAQ,SAAS;AAAA,oBAC/D;AAAA,kBACF,WAAW,OAAO,WAAW,cAAc,UAAU;AAEnD,wBAAI,WAAW,WAAW;AACxB,0BAAI,KAAK,KAAK,GAAG,OAAO,GAAG,KAAK,OAAO,KAAK,QAAQ,GAAG;AAAA,oBACzD;AAEA,oCAAgB,KAAK,MAAM,QAAQ,WAAW,SAAS;AAAA,kBACzD;AAAA,gBACF;AAUA,yBAAS,gBAAgB,KAAK,MAAM,QAAQ,WAAW;AACrD,sBAAI,IAAI,IAAI,IAAI;AAChB,sBAAI,UAAU,KAAK;AACjB,yBAAK,OAAO;AACZ,yBAAK,OAAO;AACZ,yBAAK,OAAO,IAAI,KAAK;AACrB,yBAAK,OAAO;AACZ,wBAAI,UAAU,OAAO;AACnB,4BAAM,MAAM,UAAU;AAAA,oBACxB;AACA,wBAAI,UAAU,MAAM;AAClB,4BAAM,MAAM,UAAU;AAAA,oBACxB;AACA,6BAAS,UAAU,KAAK,IAAI,IAAI,IAAI,EAAE;AAAA,kBACxC;AACA,sBAAI,UAAU,QAAQ;AACpB,yBAAK,OAAO;AACZ,yBAAK,OAAO,IAAI,KAAK;AACrB,yBAAK,OAAO,IAAI,KAAK;AACrB,yBAAK,OAAO,IAAI,KAAK;AACrB,wBAAI,UAAU,OAAO;AACnB,4BAAM,MAAM,UAAU;AAAA,oBACxB;AACA,wBAAI,UAAU,MAAM;AAClB,4BAAM,MAAM,UAAU;AAAA,oBACxB;AACA,6BAAS,UAAU,QAAQ,IAAI,IAAI,IAAI,EAAE;AAAA,kBAC3C;AACA,sBAAI,UAAU,MAAM;AAClB,yBAAK,OAAO;AACZ,yBAAK,OAAO;AACZ,yBAAK,OAAO;AACZ,yBAAK,OAAO,IAAI,KAAK;AACrB,wBAAI,UAAU,KAAK;AACjB,4BAAM,MAAM,UAAU;AAAA,oBACxB;AACA,wBAAI,UAAU,QAAQ;AACpB,4BAAM,MAAM,UAAU;AAAA,oBACxB;AACA,6BAAS,UAAU,MAAM,IAAI,IAAI,IAAI,EAAE;AAAA,kBACzC;AACA,sBAAI,UAAU,OAAO;AACnB,yBAAK,OAAO,IAAI,KAAK;AACrB,yBAAK,OAAO;AACZ,yBAAK,OAAO,IAAI,KAAK;AACrB,yBAAK,OAAO,IAAI,KAAK;AACrB,wBAAI,UAAU,KAAK;AACjB,4BAAM,MAAM,UAAU;AAAA,oBACxB;AACA,wBAAI,UAAU,QAAQ;AACpB,4BAAM,MAAM,UAAU;AAAA,oBACxB;AACA,6BAAS,UAAU,OAAO,IAAI,IAAI,IAAI,EAAE;AAAA,kBAC1C;AACA,2BAAS,SAAS,OAAOmB,KAAIC,KAAIC,KAAIC,KAAI;AACvC,wBAAI,YAAY,EAAE,aAAa,KAAK;AACpC,wBAAI,YAAY,EAAE,KAAKH,KAAIC,KAAIC,KAAIC,KAAI,GAAG;AAAA,kBAC5C;AAAA,gBACF;AACA,yBAAS,sBAAsB,KAAK,OAAO,WAAW,QAAQ;AAC5D,sBAAI,sBAAsB,MAAM,SAAS,OAAO;AAChD,sBAAI,WAAW,MAAM,SAAS;AAC9B,sBAAI,aAAa,eAAe,aAAa,cAAc,WAAW;AACpE,2CAAuB,MAAM,cAAc,MAAM,OAAO;AAAA,kBAC1D;AACA,yBAAO,IAAI,SAAS,EAAE,SAAS,OAAO,IAAI;AAAA,gBAC5C;AACA,yBAAS,QAAQ,KAAK,OAAO,UAAU,QAAQ,SAAS,gBAAgB;AACtE,sBAAI,YAAY,QAAQ;AACtB,8BAAU,CAAC;AAAA,kBACb;AACA,sBAAI,mBAAmB,QAAQ;AAC7B,qCAAiB;AAAA,kBACnB;AACA,sBAAI,YAAY,IAAI,UAAU;AAC9B,sBAAI,MAAM,SAAS,aAAa,eAAe,CAAC,gBAAgB;AAC9D,0BAAM,KAAK,QAAQ,SAAU,KAAK;AAChC,6BAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AAAA,oBAClD,CAAC;AAAA,kBACH;AAGA,wBAAM,iBAAiB,KAAK,MAAM;AAClC,sBAAI,SAAS,MAAM,SAAS;AAC5B,mBAAC,GAAG,SAAS,gBAAgB,KAAK,OAAO,UAAU,MAAM;AACzD,2BAAS,GAAG;AACZ,wBAAM;AACN,wBAAM;AACN,yBAAO,IAAI,OAAO;AAClB,yBAAO,IAAI,OAAO;AAClB,2BAAS,IAAI,OAAO;AAEpB,wBAAM,sBAAsB,KAAK,MAAM;AACvC,sBAAI,MAAM,SAAS,aAAa,aAAa;AAC3C,0BAAM,KAAK,QAAQ,SAAU,KAAK;AAChC,6BAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AAAA,oBAClD,CAAC;AACD,wBAAI,YAAY,IAAI,UAAU;AAAA,kBAChC;AAAA,gBACF;AACA,gBAAAtB,SAAQ,UAAU;AAClB,yBAAS,SAAS,KAAK;AACrB,sBAAI,UAAU,IAAI,WAAW;AAC7B,sBAAI,QAAQ,UAAU,CAAC;AACvB,sBAAI,aAAa,IAAI,WAAW;AAChC,sBAAI,eAAe,SAAS;AAC1B,wBAAI,QAAQ;AACZ,2BAAO;AAAA,kBACT;AACA,yBAAO;AAAA,gBACT;AAAA,cAGF;AAAA;AAAA;AAAA,YACK;AAAA;AAAA,cAAW,SAAU,yBAAyBA,UAASK,sBAAqB;AAC/E,uBAAO,eAAeL,UAAS,cAAc;AAAA,kBAC3C,OAAO;AAAA,gBACT,CAAC;AACD,gBAAAA,SAAQ,kCAAkC;AAC1C,oBAAI,WAAWK,qBAAoB,GAAG;AAEtC,yBAAS,uBAAuB,KAAK,OAAO,QAAQ;AAClD,sBAAI;AACJ,sBAAI,WAAW,QAAQ;AACrB,6BAAS,CAAC;AAAA,kBACZ;AAEA,sBAAI,kBAAkB,GAAG,SAAS,uBAAuB,KAAK,KAAK;AAEnE,sBAAI,mBAAmB,oBAAI,IAAI;AAC/B,sBAAI,aAAa,CAAC;AAClB,sBAAI,UAAU,CAAC;AACf,sBAAI,4BAA4B,CAAC;AACjC,wBAAM,SAAS;AACf,sBAAI,MAAM,QAAQ,MAAM,SAAS,yBAAyB,GAAG;AAC3D,gDAA4B,MAAM,SAAS;AAAA,kBAE7C,WAAW,OAAO,MAAM,SAAS,8BAA8B,YAAY,OAAO,MAAM,SAAS,8BAA8B,UAAU;AACvI,gDAA4B,CAAC,MAAM,SAAS,yBAAyB;AAAA,kBACvE;AAEA,4CAA0B,QAAQ,SAAU,OAAO;AACjD,wBAAI,MAAM,MAAM,QAAQ,KAAK,SAAU,MAAM;AAC3C,6BAAO,KAAK,YAAY,SAAS,KAAK,UAAU;AAAA,oBAClD,CAAC;AACD,wBAAI,OAAO,CAAC,iBAAiB,IAAI,IAAI,KAAK,GAAG;AAC3C,uCAAiB,IAAI,IAAI,OAAO,IAAI;AACpC,iCAAW,KAAK,IAAI,KAAK;AACzB,8BAAQ,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC;AACrC,wCAAkB,IAAI;AAAA,oBACxB;AAAA,kBACF,CAAC;AACD,sBAAI,QAAQ;AACZ,sBAAI,KAAK,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,WAAW,QAAQ,OAAO,SAAS,KAAK;AAC7G,yBAAO,IAAI,MAAM,QAAQ,QAAQ;AAE/B,wBAAI,iBAAiB,IAAI,CAAC,GAAG;AAC3B;AACA;AAAA,oBACF;AACA,wBAAI,WAAW,MAAM,QAAQ,CAAC,EAAE;AAEhC,wBAAI,SAAS,kBAAkB,UAAU;AACvC,8BAAQ;AACR,iCAAW,KAAK,CAAC;AACjB,8BAAQ,KAAK,MAAM,QAAQ,CAAC,CAAC;AAC7B,wCAAkB;AAAA,oBACpB,OAAO;AACL;AAAA,oBACF;AACA;AAAA,kBACF;AACA,yBAAO;AAAA,oBACL;AAAA,oBACA;AAAA,oBACA,WAAW,IAAI;AAAA,kBACjB;AAAA,gBACF;AACA,yBAAS,gCAAgC,KAAK,OAAO;AACnD,sBAAI,aAAa,CAAC;AAClB,2BAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,QAAQ,KAAK;AAC7C,wBAAI,SAAS,uBAAuB,KAAK,OAAO;AAAA,sBAC9C,OAAO;AAAA,oBACT,CAAC;AACD,wBAAI,OAAO,QAAQ,QAAQ;AACzB,iCAAW,KAAK,MAAM;AACtB,0BAAI,OAAO;AAAA,oBACb;AAAA,kBACF;AACA,yBAAO;AAAA,gBACT;AACA,gBAAAL,SAAQ,kCAAkC;AAAA,cAG5C;AAAA;AAAA;AAAA,YACK;AAAA;AAAA,cAAW,SAAU,yBAAyBA,UAASK,sBAAqB;AAC/E,uBAAO,eAAeL,UAAS,cAAc;AAAA,kBAC3C,OAAO;AAAA,gBACT,CAAC;AACD,gBAAAA,SAAQ,YAAYA,SAAQ,gBAAgBA,SAAQ,kBAAkB;AACtE,oBAAI,WAAWK,qBAAoB,GAAG;AAItC,yBAAS,gBAAgB,KAAK,OAAO;AACnC,4BAAU,KAAK,KAAK;AACpB,sBAAI,mBAAmB,CAAC;AACxB,sBAAI,oBAAoB;AACxB,wBAAM,QAAQ,QAAQ,SAAU,QAAQ;AACtC,wBAAI,cAAc,OAAO,sBAAsB,KAAK;AACpD,wBAAI,aAAa;AAEf,6BAAO,QAAQ;AAAA,oBACjB,OAAO;AAEL,6BAAO,QAAQ,OAAO;AACtB,uCAAiB,KAAK,MAAM;AAAA,oBAC9B;AACA,yCAAqB,OAAO;AAAA,kBAC9B,CAAC;AAED,sBAAI,cAAc,MAAM,SAAS,IAAI,SAAS,EAAE,KAAK,IAAI;AAEzD,sBAAI,aAAa;AACf,kCAAc,cAAc,kBAAkB,aAAa,SAAU,QAAQ;AAC3E,6BAAO,KAAK,IAAI,OAAO,kBAAkB,OAAO,QAAQ;AAAA,oBAC1D,CAAC;AAAA,kBACH;AAEA,sBAAI,aAAa;AACf,kCAAc,cAAc,kBAAkB,aAAa,SAAU,QAAQ;AAC3E,6BAAO,OAAO;AAAA,oBAChB,CAAC;AAAA,kBACH;AACA,gCAAc,KAAK,IAAI,WAAW;AAClC,sBAAI,CAAC,MAAM,SAAS,uBAAuB,cAAc,MAAM,IAAI,YAAY,GAAG;AAKhF,kCAAc,cAAc,IAAI,cAAc,KAAK,MAAM,WAAW;AACpE,4BAAQ,KAAK,yBAAyB,OAAO,aAAa,iCAAiC,CAAC;AAAA,kBAC9F;AACA,gCAAc,KAAK;AACnB,6BAAW,OAAO,GAAG;AACrB,gCAAc,KAAK;AAAA,gBACrB;AACA,gBAAAL,SAAQ,kBAAkB;AAC1B,yBAAS,UAAU,KAAK,OAAO;AAC7B,sBAAI,KAAK,IAAI,YAAY;AACzB,sBAAI,sBAAsB,MAAM,SAAS;AACzC,sBAAI,sBAAsB,GAAG,SAAS,uBAAuB,KAAK,KAAK;AACvE,wBAAM,QAAQ,EAAE,QAAQ,SAAU,KAAK;AACrC,6BAAS,KAAK,GAAG,KAAK,MAAM,SAAS,KAAK,GAAG,QAAQ,MAAM;AACzD,0BAAI,SAAS,GAAG,EAAE;AAClB,0BAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,0BAAI,CAAC,KAAM;AACX,0BAAI,QAAQ,MAAM,MAAM;AACxB,4BAAM,cAAc,KAAK,OAAO,MAAM,KAAK,QAAQ,IAAI;AACvD,0BAAI,UAAU,KAAK,QAAQ,YAAY;AACvC,2BAAK,gBAAgB,GAAG,SAAS,gBAAgB,KAAK,MAAM,KAAK,QAAQ,GAAG,IAAI;AAKhF,0BAAI,oBAAoB,GAAG,SAAS,gBAAgB,KAAK,KAAK,KAAK,GAAG,EAAE,MAAM,cAAc,GAAG,KAAK,QAAQ,GAAG;AAC/G,2BAAK,mBAAmB,mBAAmB,KAAK,QAAQ,YAAY;AACpE,0BAAI,OAAO,KAAK,OAAO,cAAc,UAAU;AAC7C,6BAAK,WAAW,KAAK,OAAO;AAC5B,6BAAK,eAAe,KAAK,OAAO;AAAA,sBAClC,WAAW,KAAK,OAAO,cAAc,UAAU,wBAAwB,MAAM;AAE3E,4BAAI,KAAK,eAAe,oBAAoB;AAC1C,+BAAK,WAAW;AAChB,+BAAK,eAAe;AAAA,wBACtB,OAAO;AACL,+BAAK,WAAW,KAAK;AACrB,+BAAK,eAAe,KAAK;AAAA,wBAC3B;AAAA,sBACF,OAAO;AAEL,4BAAI,kBAAkB,KAAK;AAC3B,6BAAK,WAAW,KAAK,OAAO,gBAAgB;AAC5C,6BAAK,eAAe,KAAK;AACzB,4BAAI,KAAK,WAAW,KAAK,cAAc;AACrC,+BAAK,eAAe,KAAK;AAAA,wBAC3B;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF,CAAC;AACD,wBAAM,QAAQ,EAAE,QAAQ,SAAU,KAAK;AACrC,6BAAS,KAAK,GAAG,KAAK,MAAM,SAAS,KAAK,GAAG,QAAQ,MAAM;AACzD,0BAAI,SAAS,GAAG,EAAE;AAClB,0BAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AAGjC,0BAAI,QAAQ,KAAK,YAAY,GAAG;AAC9B,+BAAO,eAAe,KAAK,IAAI,OAAO,cAAc,KAAK,YAAY;AACrE,+BAAO,WAAW,KAAK,IAAI,OAAO,UAAU,KAAK,QAAQ;AACzD,+BAAO,mBAAmB,KAAK,IAAI,OAAO,kBAAkB,KAAK,gBAAgB;AAAA,sBACnF,OAAO;AAOL,4BAAI,eAAe,MAAM,OAAO,aAAa,OAAO,OAAO,KAAK,MAAM,OAAO,aAAa,OAAO,KAAK,KAAK,CAAC;AAC5G,4BAAI,YAAY,aAAa,aAAa,aAAa;AACvD,4BAAI,aAAa,OAAO,cAAc,UAAU;AAC9C,iCAAO,WAAW;AAClB,iCAAO,eAAe;AAAA,wBACxB;AAAA,sBACF;AACA,0BAAI,MAAM;AAER,4BAAI,KAAK,UAAU,KAAK,CAAC,OAAO,UAAU;AACxC,iCAAO,WAAW,KAAK;AAAA,wBACzB;AACA,4BAAI,KAAK,UAAU,KAAK,CAAC,OAAO,cAAc;AAC5C,iCAAO,eAAe,KAAK;AAAA,wBAC7B;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF,CAAC;AAAA,gBACH;AAIA,yBAAS,cAAc,SAAS,aAAa,aAAa;AACxD,sBAAI,qBAAqB;AACzB,sBAAI,kBAAkB,QAAQ,OAAO,SAAU,KAAKuB,SAAQ;AAC1D,2BAAO,MAAMA,QAAO;AAAA,kBACtB,GAAG,CAAC;AACJ,2BAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,wBAAI,SAAS,QAAQ,CAAC;AACtB,wBAAI,QAAQ,OAAO,eAAe;AAClC,wBAAI,kBAAkB,qBAAqB;AAC3C,wBAAI,iBAAiB,OAAO,QAAQ;AACpC,wBAAI,WAAW,YAAY,MAAM;AACjC,wBAAI,WAAW,iBAAiB,WAAW,WAAW;AACtD,mCAAe,WAAW,OAAO;AACjC,2BAAO,QAAQ;AAAA,kBACjB;AACA,gCAAc,KAAK,MAAM,cAAc,IAAI,IAAI;AAG/C,sBAAI,aAAa;AACf,wBAAI,mBAAmB,QAAQ,OAAO,SAAUA,SAAQ;AACtD,6BAAO,cAAc,IAAIA,QAAO,QAAQ,YAAYA,OAAM,IACxD;AAAA,oBACJ,CAAC;AACD,wBAAI,iBAAiB,QAAQ;AAC3B,oCAAc,cAAc,kBAAkB,aAAa,WAAW;AAAA,oBACxE;AAAA,kBACF;AACA,yBAAO;AAAA,gBACT;AACA,gBAAAvB,SAAQ,gBAAgB;AACxB,yBAAS,cAAc,OAAO;AAC5B,sBAAI,eAAe,CAAC;AACpB,sBAAI,kBAAkB;AACtB,sBAAI,MAAM,MAAM,QAAQ;AACxB,2BAAS,WAAW,GAAG,WAAW,IAAI,QAAQ,YAAY;AACxD,wBAAI,MAAM,IAAI,QAAQ;AACtB,6BAAS,KAAK,GAAG,KAAK,MAAM,SAAS,KAAK,GAAG,QAAQ,MAAM;AACzD,0BAAI,SAAS,GAAG,EAAE;AAClB,0BAAI,OAAO,aAAa,OAAO,KAAK;AACpC,0BAAI,kBAAkB,GAAG;AACvB;AACA,+BAAO,IAAI,MAAM,OAAO,KAAK;AAAA,sBAC/B,WAAW,MAAM;AACf,6BAAK,KAAK,UAAU,IAAI;AACxB,0CAAkB,KAAK,KAAK;AAC5B,+BAAO,IAAI,MAAM,OAAO,KAAK;AAC7B,6BAAK;AACL,4BAAI,KAAK,QAAQ,GAAG;AAClB,iCAAO,aAAa,OAAO,KAAK;AAAA,wBAClC;AAAA,sBACF,OAAO;AACL,4BAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,4BAAI,CAAC,MAAM;AACT;AAAA,wBACF;AACA,6BAAK,SAAS,IAAI;AAClB,4BAAI,KAAK,UAAU,GAAG;AACpB,8BAAI,YAAY,IAAI,SAAS;AAC7B,8BAAI,OAAO,KAAK,UAAU,YAAY,YAAY,KAAK;AACvD,uCAAa,OAAO,KAAK,IAAI;AAAA,4BAC3B;AAAA,4BACA;AAAA,4BACA;AAAA,0BACF;AAAA,wBACF;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AACA,yBAAS,cAAc,OAAO;AAC5B,sBAAI,MAAM,MAAM,QAAQ;AACxB,2BAAS,WAAW,GAAG,WAAW,IAAI,QAAQ,YAAY;AACxD,wBAAI,MAAM,IAAI,QAAQ;AACtB,wBAAI,cAAc;AAClB,wBAAI,uBAAuB;AAC3B,wBAAI,eAAe;AACnB,6BAAS,cAAc,GAAG,cAAc,MAAM,QAAQ,QAAQ,eAAe;AAC3E,0BAAI,SAAS,MAAM,QAAQ,WAAW;AAEtC,sCAAgB;AAChB,0BAAI,eAAe,KAAK,MAAM,QAAQ,cAAc,CAAC,GAAG;AACtD,gDAAwB,OAAO;AAC/B,+BAAO,IAAI,MAAM,OAAO,KAAK;AAAA,sBAC/B,WAAW,aAAa;AACtB,4BAAI,OAAO;AACX,+BAAO,IAAI,MAAM,OAAO,KAAK;AAC7B,sCAAc;AACd,6BAAK,QAAQ,OAAO,QAAQ;AAAA,sBAC9B,OAAO;AACL,4BAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,4BAAI,CAAC,KAAM;AACX,uCAAe,KAAK;AACpB,+CAAuB;AACvB,4BAAI,KAAK,UAAU,GAAG;AACpB,wCAAc;AACd,kDAAwB,OAAO;AAC/B;AAAA,wBACF;AACA,6BAAK,QAAQ,OAAO,QAAQ;AAAA,sBAC9B;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AACA,yBAAS,WAAW,OAAO,KAAK;AAC9B,sBAAI,gBAAgB;AAAA,oBAClB,OAAO;AAAA,oBACP,QAAQ;AAAA,kBACV;AACA,2BAAS,KAAK,GAAG,KAAK,MAAM,QAAQ,GAAG,KAAK,GAAG,QAAQ,MAAM;AAC3D,wBAAI,MAAM,GAAG,EAAE;AACf,6BAAS,KAAK,GAAG,KAAK,MAAM,SAAS,KAAK,GAAG,QAAQ,MAAM;AACzD,0BAAI,SAAS,GAAG,EAAE;AAClB,0BAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,0BAAI,CAAC,KAAM;AACX,0BAAI,YAAY,KAAK,QAAQ,IAAI;AACjC,0BAAI,YAAY,KAAK,QAAQ,KAAK,QAAQ,YAAY;AACtD,0BAAI,KAAK,OAAO,aAAa,aAAa;AAExC,6BAAK,OAAO,IAAI,gBAAgB,KAAK,MAAM,YAAY,IAAI,IAAI,YAAY,GAAG;AAAA,0BAC5E,UAAU,KAAK,OAAO;AAAA,wBACxB,CAAC;AAAA,sBACH,WAAW,KAAK,OAAO,aAAa,aAAa;AAC/C,6BAAK,OAAO,UAAU,KAAK,MAAM,WAAW,KAAK,QAAQ,KAAK,KAAK;AAAA,sBACrE,WAAW,KAAK,OAAO,aAAa,UAAU;AAC5C,6BAAK,OAAO,UAAU,KAAK,MAAM,WAAW,KAAK,QAAQ,KAAK,EAAE;AAAA,sBAClE,WAAW,OAAO,KAAK,OAAO,aAAa,YAAY;AACrD,4BAAI,SAAS,KAAK,OAAO,SAAS,KAAK,MAAM,SAAS;AACtD,4BAAI,OAAO,WAAW,UAAU;AAC9B,+BAAK,OAAO,CAAC,MAAM;AAAA,wBACrB,OAAO;AACL,+BAAK,OAAO;AAAA,wBACd;AAAA,sBACF;AACA,2BAAK,gBAAgB,KAAK,iBAAiB,IAAI,YAAY,GAAG,IAAI,oBAAoB,CAAC;AACvF,0BAAI,oBAAoB,KAAK,gBAAgB,KAAK;AAClD,0BAAI,KAAK,UAAU,KAAK,cAAc,QAAQ,cAAc,SAAS,oBAAoB,KAAK,SAAS;AACrG,wCAAgB;AAAA,0BACd,QAAQ;AAAA,0BACR,OAAO,KAAK;AAAA,wBACd;AAAA,sBACF,WAAW,iBAAiB,cAAc,QAAQ,GAAG;AACnD,4BAAI,cAAc,SAAS,mBAAmB;AAC5C,8CAAoB,cAAc;AAAA,wBACpC;AAAA,sBACF;AACA,0BAAI,oBAAoB,IAAI,QAAQ;AAClC,4BAAI,SAAS;AAAA,sBACf;AAAA,oBACF;AACA,kCAAc;AAAA,kBAChB;AAAA,gBACF;AACA,yBAAS,UAAU,MAAM,OAAO,QAAQ,KAAK,UAAU;AACrD,yBAAO,KAAK,IAAI,SAAU,KAAK;AAC7B,2BAAO,aAAa,KAAK,OAAO,QAAQ,KAAK,QAAQ;AAAA,kBACvD,CAAC;AAAA,gBACH;AACA,gBAAAA,SAAQ,YAAY;AACpB,yBAAS,aAAa,MAAM,OAAO,QAAQ,KAAK,UAAU;AACxD,sBAAI,YAAY,MAAQ,IAAI,YAAY;AACxC,0BAAQ,KAAK,KAAK,QAAQ,SAAS,IAAI;AACvC,sBAAI,UAAU,GAAG,SAAS,gBAAgB,MAAM,QAAQ,GAAG,GAAG;AAC5D,2BAAO;AAAA,kBACT;AACA,yBAAO,SAAS,GAAG,SAAS,gBAAgB,OAAO,UAAU,QAAQ,GAAG,GAAG;AACzE,wBAAI,KAAK,UAAU,GAAG;AACpB;AAAA,oBACF;AACA,2BAAO,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC;AAAA,kBAC1C;AACA,yBAAO,KAAK,KAAK,IAAI;AAAA,gBACvB;AAAA,cAGF;AAAA;AAAA;AAAA,YACK;AAAA;AAAA,cAAW,SAAUwB,SAAQ;AAChC,oBAAI,OAAO,qCAAqC,aAAa;AAC3D,sBAAI,IAAI,IAAI,MAAM,gCAAgC;AAClD,oBAAE,OAAO;AACT,wBAAM;AAAA,gBACR;AACA,gBAAAA,QAAO,UAAU;AAAA,cAGnB;AAAA;AAAA;AAAA,UAGF;AAIA,cAAI,2BAA2B,CAAC;AAIhC,mBAAS,oBAAoB,UAAU;AAE7B,gBAAI,eAAe,yBAAyB,QAAQ;AAE5D,gBAAI,iBAAiB,QAAW;AACtB,qBAAO,aAAa;AAAA,YAE9B;AAGA,gBAAIA,UAAS,yBAAyB,QAAQ,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAGxC,SAAS,CAAC;AAAA;AAAA,YAEpB;AAIA,gCAAoB,QAAQ,EAAE,KAAKA,QAAO,SAASA,SAAQA,QAAO,SAAS,mBAAmB;AAI9F,mBAAOA,QAAO;AAAA,UAEhB;AAGA,cAAI,sBAAsB,CAAC;AAE3B,WAAC,WAAY;AACX,gBAAIxB,WAAU;AACd,mBAAO,eAAeA,UAAS,cAAc;AAAA,cAC3C,OAAO;AAAA,YACT,CAAC;AACD,YAAAA,SAAQ,OAAOA,SAAQ,SAASA,SAAQ,MAAMA,SAAQ,QAAQA,SAAQ,eAAeA,SAAQ,cAAcA,SAAQ,gBAAgBA,SAAQ,cAAc;AACzJ,gBAAI,gBAAgB,oBAAoB,GAAG;AAC3C,gBAAI,gBAAgB,oBAAoB,GAAG;AAC3C,gBAAI,gBAAgB,oBAAoB,GAAG;AAC3C,gBAAI,oBAAoB,oBAAoB,GAAG;AAC/C,gBAAI,WAAW,oBAAoB,GAAG;AACtC,mBAAO,eAAeA,UAAS,SAAS;AAAA,cACtC,YAAY;AAAA,cACZ,KAAK,WAAY;AACf,uBAAO,SAAS;AAAA,cAClB;AAAA,YACF,CAAC;AACD,gBAAI,aAAa,oBAAoB,GAAG;AACxC,mBAAO,eAAeA,UAAS,gBAAgB;AAAA,cAC7C,YAAY;AAAA,cACZ,KAAK,WAAY;AACf,uBAAO,WAAW;AAAA,cACpB;AAAA,YACF,CAAC;AACD,gBAAI,WAAW,oBAAoB,GAAG;AACtC,mBAAO,eAAeA,UAAS,QAAQ;AAAA,cACrC,YAAY;AAAA,cACZ,KAAK,WAAY;AACf,uBAAO,SAAS;AAAA,cAClB;AAAA,YACF,CAAC;AACD,mBAAO,eAAeA,UAAS,UAAU;AAAA,cACvC,YAAY;AAAA,cACZ,KAAK,WAAY;AACf,uBAAO,SAAS;AAAA,cAClB;AAAA,YACF,CAAC;AACD,mBAAO,eAAeA,UAAS,OAAO;AAAA,cACpC,YAAY;AAAA,cACZ,KAAK,WAAY;AACf,uBAAO,SAAS;AAAA,cAClB;AAAA,YACF,CAAC;AAGD,qBAAS,YAAYyB,QAAO;AAC1B,eAAC,GAAG,cAAc,SAASA,MAAK;AAAA,YAClC;AACA,YAAAzB,SAAQ,cAAc;AACtB,qBAAS,UAAU,GAAG,SAAS;AAC7B,kBAAI,SAAS,GAAG,cAAc,YAAY,GAAG,OAAO;AACpD,kBAAI,SAAS,GAAG,kBAAkB,aAAa,GAAG,KAAK;AACvD,eAAC,GAAG,cAAc,WAAW,GAAG,KAAK;AAAA,YACvC;AAEA,qBAAS,cAAc,GAAG,SAAS;AACjC,kBAAI,SAAS,GAAG,cAAc,YAAY,GAAG,OAAO;AACpD,sBAAQ,GAAG,kBAAkB,aAAa,GAAG,KAAK;AAAA,YACpD;AACA,YAAAA,SAAQ,gBAAgB;AACxB,qBAAS,YAAY,GAAG,OAAO;AAC7B,eAAC,GAAG,cAAc,WAAW,GAAG,KAAK;AAAA,YACvC;AACA,YAAAA,SAAQ,cAAc;AACtB,gBAAI;AAEF,kBAAI,QAAQ,oBAAoB,GAAG;AAGnC,kBAAI,MAAM,MAAO,SAAQ,MAAM;AAC/B,0BAAY,KAAK;AAAA,YACnB,SAAS,OAAO;AAAA,YAIhB;AACA,YAAAA,SAAQ,SAAS,IAAI;AAAA,UACvB,EAAE;AAEF,iBAAO;AAAA,QAET,EAAE;AAAA;AAAA,IACJ,CAAC;AAAA;AAAA;", "names": ["exports", "d", "b", "HookData", "CellHookData", "__webpack_require__", "text", "HtmlRowInput", "window", "<PERSON><PERSON><PERSON><PERSON>", "global", "prop", "options", "_a", "current", "Table", "Row", "Cell", "Column", "x1", "y1", "x2", "y2", "column", "module", "jsPDF"]}