{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/sl.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    v = val.toString().replace(/^[^.]*\\.?/, '').length;\n  if (v === 0 && i % 100 === 1) return 1;\n  if (v === 0 && i % 100 === 2) return 2;\n  if (v === 0 && i % 100 === Math.floor(i % 100) && i % 100 >= 3 && i % 100 <= 4 || !(v === 0)) return 3;\n  return 5;\n}\nexport default [\"sl\", [[\"d\", \"p\"], [\"dop.\", \"pop.\"], u], [[\"d\", \"p\"], [\"dop.\", \"pop.\"], [\"dopoldne\", \"popoldne\"]], [[\"n\", \"p\", \"t\", \"s\", \"č\", \"p\", \"s\"], [\"ned.\", \"pon.\", \"tor.\", \"sre.\", \"čet.\", \"pet.\", \"sob.\"], [\"nedelja\", \"ponedeljek\", \"torek\", \"sreda\", \"četrtek\", \"petek\", \"sobota\"], [\"ned.\", \"pon.\", \"tor.\", \"sre.\", \"čet.\", \"pet.\", \"sob.\"]], u, [[\"j\", \"f\", \"m\", \"a\", \"m\", \"j\", \"j\", \"a\", \"s\", \"o\", \"n\", \"d\"], [\"jan.\", \"feb.\", \"mar.\", \"apr.\", \"maj\", \"jun.\", \"jul.\", \"avg.\", \"sep.\", \"okt.\", \"nov.\", \"dec.\"], [\"januar\", \"februar\", \"marec\", \"april\", \"maj\", \"junij\", \"julij\", \"avgust\", \"september\", \"oktober\", \"november\", \"december\"]], u, [[\"pr. Kr.\", \"po Kr.\"], u, [\"pred Kristusom\", \"po Kristusu\"]], 1, [6, 0], [\"d. MM. yy\", \"d. MMM y\", \"d. MMMM y\", \"EEEE, d. MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1}, {0}\", u, \"{1} {0}\", u], [\",\", \".\", \";\", \"%\", \"+\", \"−\", \"e\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"evro\", {\n  \"AUD\": [u, \"$\"],\n  \"BRL\": [u, \"R$\"],\n  \"BYN\": [u, \"р.\"],\n  \"CAD\": [u, \"$\"],\n  \"GBP\": [u, \"£\"],\n  \"MXN\": [u, \"$\"],\n  \"NZD\": [u, \"$\"],\n  \"PHP\": [u, \"₱\"],\n  \"TWD\": [u, \"NT$\"],\n  \"XCD\": [u, \"$\"]\n}, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACnB,QAAM,IAAI,KACR,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,GAC5B,IAAI,IAAI,SAAS,EAAE,QAAQ,aAAa,EAAE,EAAE;AAC9C,MAAI,MAAM,KAAK,IAAI,QAAQ,EAAG,QAAO;AACrC,MAAI,MAAM,KAAK,IAAI,QAAQ,EAAG,QAAO;AACrC,MAAI,MAAM,KAAK,IAAI,QAAQ,KAAK,MAAM,IAAI,GAAG,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,EAAE,MAAM,GAAI,QAAO;AACrG,SAAO;AACT;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,GAAG,CAAC,WAAW,cAAc,SAAS,SAAS,WAAW,SAAS,QAAQ,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,GAAG,CAAC,UAAU,WAAW,SAAS,SAAS,OAAO,SAAS,SAAS,UAAU,aAAa,WAAW,YAAY,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,WAAW,QAAQ,GAAG,GAAG,CAAC,kBAAkB,aAAa,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa,YAAY,aAAa,iBAAiB,GAAG,CAAC,SAAS,YAAY,cAAc,eAAe,GAAG,CAAC,YAAY,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,WAAW,cAAc,KAAK,GAAG,OAAO,KAAK,QAAQ;AAAA,EACv9B,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,GAAG,GAAG;AAChB,GAAG,OAAO,MAAM;", "names": []}