import { Component, DestroyRef, inject } from '@angular/core';
import { AlertService, LOADING, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { requireAllOperator } from '@shared';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatDialog } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { EmployeePinService } from '@proxy/hr/employee-pins';
import { employeePins } from './employee-pins.model';
import { EmployeePinsCreateDialogComponent } from './employee-pins-create-dialog/employee-pins-create-dialog.component';
import { EmployeePinsUpdateDialogComponent } from './employee-pins-update-dialog/employee-pins-update-dialog.component';

@Component({
  selector: 'app-employee-pins',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `<ttwr-grid class="actions-end" [config]="config"/>`,
})
export class EmployeePinsComponent {
  private employeePin = inject(EmployeePinService);
  private dialog = inject(MatDialog);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);

  private refreshSubject = new Subject<void>();

  protected config = employeePins().grid({
    title: '::EmployeeFingerPrintPin',
    refreshSubject: this.refreshSubject,
    dataFunc: pagination => this.employeePin.getGetAllByInput({
      skipCount: pagination.pageSize * pagination.pageIndex,
      maxResultCount: pagination.pageSize,
    }).pipe(requireAllOperator()),
    actions: [
      {
        matIcon: 'add',
        tooltip: 'Add',
        delegateFunc: () => {
          const ref = this.dialog.open(EmployeePinsCreateDialogComponent, {
            width: '500px'
          });

          ref.afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        }
      }
    ],
    fieldActions: [
      {
        matIcon: 'edit',
        tooltip: 'Edit',
        delegateFunc: (obj) => {
          const ref = this.dialog.open(EmployeePinsUpdateDialogComponent, {
            width: '700px',
            data: obj,
          });

          ref.afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        }
      },
      {
        matIcon: 'delete',
        tooltip: 'Delete',
        color: 'warn',
        confirmation: {
          title: '::Warning',
          message: '::AreYouSureYouWantToDelete?',
        },
        delegateFunc: (obj) => {
          this.loading.set(true);

          this.employeePin.deleteDelete(obj.id).pipe(
            takeUntilDestroyed(this.destroyRef)
          ).subscribe(() => {
            this.refreshSubject.next();
            this.alert.success('::SuccessDelete');
          })
        }
      }
    ],
    fields: {
      pin: {
        columnName: '::GoldenOwl:EmployeePin',
      },
      employeeId: {
        columnName: '::GoldenOwl:Employee',
      }
    },
  });
}
