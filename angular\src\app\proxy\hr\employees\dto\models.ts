import type { Gender } from '../gender.enum';
import type { MaritalStatus } from '../marital-status.enum';
import type { MilitaryServiceStatus } from '../military-service-status.enum';
import type { EntityDto, FullAuditedEntityDto } from '@abp/ng.core';
import type { EmployeeStatus } from '../employee-status.enum';

export interface ContactInformationDto {
  email?: string;
  workEmail?: string;
  phoneNumber?: string;
  workPhoneNumber?: string;
}

export interface CreateOrUpdateEmployeeDto {
  name?: string;
  surname?: string;
  fatherName?: string;
  motherName?: string;
  nationalNumber?: string;
  gender: Gender;
  dateOfBirth?: string;
  contactInformation: ContactInformationDto;
  emergencyContactInformation: EmergencyContactInformationDto;
  maritalStatus: MaritalStatus;
  militaryServiceStatus: MilitaryServiceStatus;
  managerId?: string;
  userId?: string;
  jobTitleId?: string;
}

export interface EmergencyContactInformationDto {
  emergencyPhone?: string;
  contactName?: string;
  relationShip?: string;
}

export interface EmployeeDto extends FullAuditedEntityDto<string> {
  name?: string;
  surname?: string;
  fatherName?: string;
  motherName?: string;
  nationalNumber?: string;
  gender: Gender;
  dateOfBirth?: string;
  contactInformation: ContactInformationDto;
  emergencyContactInformation: EmergencyContactInformationDto;
  maritalStatus: MaritalStatus;
  militaryServiceStatus: MilitaryServiceStatus;
  employeeStatue: EmployeeStatus;
  managerId?: string;
  userId?: string;
  departmentId?: string;
  jobTitleName?: string;
  jobTitleId?: string;
}

export interface EmployeeNameAndIdDto extends EntityDto<string> {
  fullName?: string;
}

export interface EmployeeNameDto {
  name?: string;
  surname?: string;
  fatherName?: string;
  motherName?: string;
}
