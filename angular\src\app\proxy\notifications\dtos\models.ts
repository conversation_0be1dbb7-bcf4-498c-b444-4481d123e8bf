import type { CreationAuditedEntityDto, PagedResultRequestDto } from '@abp/ng.core';
import type { EntityType } from '../entity-type.enum';

export interface CreateNotificationDto {
  title?: string;
  message?: string;
  targetUserId?: string;
}

export interface NotificationDto extends CreationAuditedEntityDto<string> {
  title?: string;
  message?: string;
  entityType: EntityType;
  isSeen: boolean;
}

export interface NotificationPagedResultRequestDto extends PagedResultRequestDto {
  isSeen?: boolean;
}
