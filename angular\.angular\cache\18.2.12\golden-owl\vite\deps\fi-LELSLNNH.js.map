{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/fi.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    v = val.toString().replace(/^[^.]*\\.?/, '').length;\n  if (i === 1 && v === 0) return 1;\n  return 5;\n}\nexport default [\"fi\", [[\"ap.\", \"ip.\"], u, u], u, [[\"S\", \"M\", \"T\", \"K\", \"T\", \"P\", \"L\"], [\"su\", \"ma\", \"ti\", \"ke\", \"to\", \"pe\", \"la\"], [\"sunnuntaina\", \"maanantaina\", \"tiistaina\", \"keskiviikkona\", \"torstaina\", \"perjantaina\", \"lauantaina\"], [\"su\", \"ma\", \"ti\", \"ke\", \"to\", \"pe\", \"la\"]], [[\"S\", \"M\", \"T\", \"K\", \"T\", \"P\", \"L\"], [\"su\", \"ma\", \"ti\", \"ke\", \"to\", \"pe\", \"la\"], [\"sunnuntai\", \"maanantai\", \"tiistai\", \"keskiviikko\", \"torstai\", \"perjantai\", \"lauantai\"], [\"su\", \"ma\", \"ti\", \"ke\", \"to\", \"pe\", \"la\"]], [[\"T\", \"H\", \"M\", \"H\", \"T\", \"K\", \"H\", \"E\", \"S\", \"L\", \"M\", \"J\"], [\"tammik.\", \"helmik.\", \"maalisk.\", \"huhtik.\", \"toukok.\", \"kesäk.\", \"heinäk.\", \"elok.\", \"syysk.\", \"lokak.\", \"marrask.\", \"jouluk.\"], [\"tammikuuta\", \"helmikuuta\", \"maaliskuuta\", \"huhtikuuta\", \"toukokuuta\", \"kesäkuuta\", \"heinäkuuta\", \"elokuuta\", \"syyskuuta\", \"lokakuuta\", \"marraskuuta\", \"joulukuuta\"]], [[\"T\", \"H\", \"M\", \"H\", \"T\", \"K\", \"H\", \"E\", \"S\", \"L\", \"M\", \"J\"], [\"tammi\", \"helmi\", \"maalis\", \"huhti\", \"touko\", \"kesä\", \"heinä\", \"elo\", \"syys\", \"loka\", \"marras\", \"joulu\"], [\"tammikuu\", \"helmikuu\", \"maaliskuu\", \"huhtikuu\", \"toukokuu\", \"kesäkuu\", \"heinäkuu\", \"elokuu\", \"syyskuu\", \"lokakuu\", \"marraskuu\", \"joulukuu\"]], [[\"eKr\", \"jKr\"], [\"eKr.\", \"jKr.\"], [\"ennen Kristuksen syntymää\", \"jälkeen Kristuksen syntymän\"]], 1, [6, 0], [\"d.M.y\", u, \"d. MMMM y\", \"cccc d. MMMM y\"], [\"H.mm\", \"H.mm.ss\", \"H.mm.ss z\", \"H.mm.ss zzzz\"], [\"{1} {0}\", \"{1} 'klo' {0}\", u, u], [\",\", \" \", \";\", \"%\", \"+\", \"−\", \"E\", \"×\", \"‰\", \"∞\", \"epäluku\", \".\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"euro\", {\n  \"AOA\": [],\n  \"ARS\": [],\n  \"AUD\": [],\n  \"BAM\": [],\n  \"BBD\": [],\n  \"BDT\": [],\n  \"BMD\": [],\n  \"BND\": [],\n  \"BOB\": [],\n  \"BRL\": [],\n  \"BSD\": [],\n  \"BWP\": [],\n  \"BZD\": [],\n  \"CAD\": [],\n  \"CLP\": [],\n  \"CNY\": [],\n  \"COP\": [],\n  \"CRC\": [],\n  \"CUC\": [],\n  \"CUP\": [],\n  \"CZK\": [],\n  \"DKK\": [],\n  \"DOP\": [],\n  \"EGP\": [],\n  \"ESP\": [],\n  \"FIM\": [\"mk\"],\n  \"FJD\": [],\n  \"FKP\": [],\n  \"GEL\": [],\n  \"GIP\": [],\n  \"GNF\": [],\n  \"GTQ\": [],\n  \"GYD\": [],\n  \"HKD\": [],\n  \"HNL\": [],\n  \"HRK\": [],\n  \"HUF\": [],\n  \"IDR\": [],\n  \"ILS\": [],\n  \"INR\": [],\n  \"ISK\": [],\n  \"JMD\": [],\n  \"KHR\": [],\n  \"KMF\": [],\n  \"KPW\": [],\n  \"KRW\": [],\n  \"KYD\": [],\n  \"KZT\": [],\n  \"LAK\": [],\n  \"LBP\": [],\n  \"LKR\": [],\n  \"LRD\": [],\n  \"LTL\": [],\n  \"LVL\": [],\n  \"MGA\": [],\n  \"MMK\": [],\n  \"MNT\": [],\n  \"MUR\": [],\n  \"MXN\": [],\n  \"MYR\": [],\n  \"NAD\": [],\n  \"NGN\": [],\n  \"NIO\": [],\n  \"NOK\": [],\n  \"NPR\": [],\n  \"NZD\": [],\n  \"PHP\": [],\n  \"PKR\": [],\n  \"PLN\": [],\n  \"PYG\": [],\n  \"RON\": [],\n  \"RWF\": [],\n  \"SBD\": [],\n  \"SEK\": [],\n  \"SGD\": [],\n  \"SHP\": [],\n  \"SRD\": [],\n  \"SSP\": [],\n  \"STN\": [u, \"STD\"],\n  \"SYP\": [],\n  \"THB\": [],\n  \"TOP\": [],\n  \"TRY\": [],\n  \"TTD\": [],\n  \"TWD\": [],\n  \"UAH\": [],\n  \"UYU\": [],\n  \"VEF\": [],\n  \"VND\": [],\n  \"XCD\": [],\n  \"XPF\": [],\n  \"XXX\": [],\n  \"ZAR\": [],\n  \"ZMW\": []\n}, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACnB,QAAM,IAAI,KACR,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,GAC5B,IAAI,IAAI,SAAS,EAAE,QAAQ,aAAa,EAAE,EAAE;AAC9C,MAAI,MAAM,KAAK,MAAM,EAAG,QAAO;AAC/B,SAAO;AACT;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,GAAG,CAAC,eAAe,eAAe,aAAa,iBAAiB,aAAa,eAAe,YAAY,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,GAAG,CAAC,aAAa,aAAa,WAAW,eAAe,WAAW,aAAa,UAAU,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,WAAW,WAAW,YAAY,WAAW,WAAW,UAAU,WAAW,SAAS,UAAU,UAAU,YAAY,SAAS,GAAG,CAAC,cAAc,cAAc,eAAe,cAAc,cAAc,aAAa,cAAc,YAAY,aAAa,aAAa,eAAe,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,SAAS,SAAS,UAAU,SAAS,SAAS,QAAQ,SAAS,OAAO,QAAQ,QAAQ,UAAU,OAAO,GAAG,CAAC,YAAY,YAAY,aAAa,YAAY,YAAY,WAAW,YAAY,UAAU,WAAW,WAAW,aAAa,UAAU,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,6BAA6B,6BAA6B,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,GAAG,aAAa,gBAAgB,GAAG,CAAC,QAAQ,WAAW,aAAa,cAAc,GAAG,CAAC,WAAW,iBAAiB,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,WAAW,GAAG,GAAG,CAAC,aAAa,WAAW,cAAc,KAAK,GAAG,OAAO,KAAK,QAAQ;AAAA,EAC1gD,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC,IAAI;AAAA,EACZ,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AACV,GAAG,OAAO,MAAM;", "names": []}