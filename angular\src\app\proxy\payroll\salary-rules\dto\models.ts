import type { RuleConditionType } from '../rule-condition-type.enum';
import type { RuleValueType } from '../rule-value-type.enum';
import type { AuditedEntityDto } from '@abp/ng.core';

export interface CreateUpdateSalaryRuleDto {
  name?: string;
  categoryId?: string;
  code?: string;
  isActive: boolean;
  appearForEmployee: boolean;
  conditionBasedOn: RuleConditionType;
  conditionCode?: string;
  valueBasedOn: RuleValueType;
  valueFixedAmount?: number;
  valueCode?: string;
}

export interface RuleConditionDto {
  basedOn: RuleConditionType;
  code?: string;
}

export interface RuleValueDto {
  basedOn: RuleValueType;
  fixedAmount?: number;
  code?: string;
}

export interface SalaryRuleDetailsDto extends AuditedEntityDto<string> {
  name?: string;
  categoryId?: string;
  categoryName?: string;
  code?: string;
  isActive: boolean;
  appearForEmployee: boolean;
  ruleCondition: RuleConditionDto;
  ruleValue: RuleValueDto;
}

export interface SalaryRuleDto extends AuditedEntityDto<string> {
  name?: string;
  code?: string;
  isActive: boolean;
  categoryName?: string;
}
