import {
  MAT_BUTTO<PERSON>_CONFIG,
  MAT_FAB_DEFAULT_OPTIONS,
  MAT_FAB_DEFAULT_OPTIONS_FACTORY,
  <PERSON><PERSON>nch<PERSON>,
  Mat<PERSON>utton,
  MatButtonModule,
  MatFabAnchor,
  MatFabButton,
  <PERSON><PERSON>conAnch<PERSON>,
  <PERSON><PERSON><PERSON>Button,
  Mat<PERSON>iniFabAnchor,
  MatMiniFabButton
} from "./chunk-JEURQPSQ.js";
import "./chunk-KW6MAD7U.js";
import "./chunk-2MXHA5U4.js";
import "./chunk-DNW4SGAD.js";
import "./chunk-VTW5CIPD.js";
import "./chunk-7YC2NMXI.js";
import "./chunk-ILOBLWYU.js";
import "./chunk-ZTELYOIP.js";
export {
  MAT_BUTTON_CONFIG,
  MAT_FAB_DEFAULT_OPTIONS,
  MAT_FAB_DEFAULT_OPTIONS_FACTORY,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  MatButtonModule,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>conAnch<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>FabAnchor,
  MatMiniFabButton
};
//# sourceMappingURL=@angular_material_button.js.map
