{"version": 3, "sources": ["../../../../../../node_modules/humanize-duration/humanize-duration.js"], "sourcesContent": ["// HumanizeDuration.js - https://git.io/j0HgmQ\n\n// @ts-check\n\n/**\n * @typedef {string | ((unitCount: number) => string)} Unit\n */\n\n/**\n * @typedef {(\"y\" | \"mo\" | \"w\" | \"d\" | \"h\" | \"m\" | \"s\" | \"ms\")} UnitName\n */\n\n/**\n * @typedef {Object} UnitMeasures\n * @prop {number} y\n * @prop {number} mo\n * @prop {number} w\n * @prop {number} d\n * @prop {number} h\n * @prop {number} m\n * @prop {number} s\n * @prop {number} ms\n */\n\n/**\n * @internal\n * @typedef {[string, string, string, string, string, string, string, string, string, string]} DigitReplacements\n */\n\n/**\n * @typedef {Object} Language\n * @prop {Unit} y\n * @prop {Unit} mo\n * @prop {Unit} w\n * @prop {Unit} d\n * @prop {Unit} h\n * @prop {Unit} m\n * @prop {Unit} s\n * @prop {Unit} ms\n * @prop {string} [decimal]\n * @prop {string} [delimiter]\n * @prop {DigitReplacements} [_digitReplacements]\n * @prop {boolean} [_numberFirst]\n * @prop {boolean} [_hideCountIf2]\n */\n\n/**\n * @typedef {Object} Options\n * @prop {string} [language]\n * @prop {Record<string, Language>} [languages]\n * @prop {string[]} [fallbacks]\n * @prop {string} [delimiter]\n * @prop {string} [spacer]\n * @prop {boolean} [round]\n * @prop {number} [largest]\n * @prop {UnitName[]} [units]\n * @prop {string} [decimal]\n * @prop {string} [conjunction]\n * @prop {number} [maxDecimalPoints]\n * @prop {UnitMeasures} [unitMeasures]\n * @prop {boolean} [serialComma]\n * @prop {DigitReplacements} [digitReplacements]\n */\n\n/**\n * @internal\n * @typedef {Required<Options>} NormalizedOptions\n */\n\n(function () {\n  // Fallback for `Object.assign` if relevant.\n  var assign = Object.assign || /** @param {...any} destination */\n  function (destination) {\n    var source;\n    for (var i = 1; i < arguments.length; i++) {\n      source = arguments[i];\n      for (var prop in source) {\n        if (has(source, prop)) {\n          destination[prop] = source[prop];\n        }\n      }\n    }\n    return destination;\n  };\n\n  // Fallback for `Array.isArray` if relevant.\n  var isArray = Array.isArray || function (arg) {\n    return Object.prototype.toString.call(arg) === \"[object Array]\";\n  };\n\n  // This has to be defined separately because of a bug: we want to alias\n  // `gr` and `el` for backwards-compatiblity. In a breaking change, we can\n  // remove `gr` entirely.\n  // See https://github.com/EvanHahn/HumanizeDuration.js/issues/143 for more.\n  var GREEK = language(function (c) {\n    return c === 1 ? \"χρόνος\" : \"χρόνια\";\n  }, function (c) {\n    return c === 1 ? \"μήνας\" : \"μήνες\";\n  }, function (c) {\n    return c === 1 ? \"εβδομάδα\" : \"εβδομάδες\";\n  }, function (c) {\n    return c === 1 ? \"μέρα\" : \"μέρες\";\n  }, function (c) {\n    return c === 1 ? \"ώρα\" : \"ώρες\";\n  }, function (c) {\n    return c === 1 ? \"λεπτό\" : \"λεπτά\";\n  }, function (c) {\n    return c === 1 ? \"δευτερόλεπτο\" : \"δευτερόλεπτα\";\n  }, function (c) {\n    return (c === 1 ? \"χιλιοστό\" : \"χιλιοστά\") + \" του δευτερολέπτου\";\n  }, \",\");\n\n  /**\n   * @internal\n   * @type {Record<string, Language>}\n   */\n  var LANGUAGES = {\n    af: language(\"jaar\", function (c) {\n      return \"maand\" + (c === 1 ? \"\" : \"e\");\n    }, function (c) {\n      return c === 1 ? \"week\" : \"weke\";\n    }, function (c) {\n      return c === 1 ? \"dag\" : \"dae\";\n    }, function (c) {\n      return c === 1 ? \"uur\" : \"ure\";\n    }, function (c) {\n      return c === 1 ? \"minuut\" : \"minute\";\n    }, function (c) {\n      return \"sekonde\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"millisekonde\" + (c === 1 ? \"\" : \"s\");\n    }, \",\"),\n    am: language(\"ዓመት\", \"ወር\", \"ሳምንት\", \"ቀን\", \"ሰዓት\", \"ደቂቃ\", \"ሰከንድ\", \"ሚሊሰከንድ\"),\n    ar: assign(language(function (c) {\n      return [\"سنة\", \"سنتان\", \"سنوات\"][getArabicForm(c)];\n    }, function (c) {\n      return [\"شهر\", \"شهران\", \"أشهر\"][getArabicForm(c)];\n    }, function (c) {\n      return [\"أسبوع\", \"أسبوعين\", \"أسابيع\"][getArabicForm(c)];\n    }, function (c) {\n      return [\"يوم\", \"يومين\", \"أيام\"][getArabicForm(c)];\n    }, function (c) {\n      return [\"ساعة\", \"ساعتين\", \"ساعات\"][getArabicForm(c)];\n    }, function (c) {\n      return [\"دقيقة\", \"دقيقتان\", \"دقائق\"][getArabicForm(c)];\n    }, function (c) {\n      return [\"ثانية\", \"ثانيتان\", \"ثواني\"][getArabicForm(c)];\n    }, function (c) {\n      return [\"جزء من الثانية\", \"جزآن من الثانية\", \"أجزاء من الثانية\"][getArabicForm(c)];\n    }, \",\"), {\n      delimiter: \" ﻭ \",\n      _hideCountIf2: true,\n      _digitReplacements: [\"۰\", \"١\", \"٢\", \"٣\", \"٤\", \"٥\", \"٦\", \"٧\", \"٨\", \"٩\"]\n    }),\n    bg: language(function (c) {\n      return [\"години\", \"година\", \"години\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"месеца\", \"месец\", \"месеца\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"седмици\", \"седмица\", \"седмици\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"дни\", \"ден\", \"дни\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"часа\", \"час\", \"часа\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"минути\", \"минута\", \"минути\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"секунди\", \"секунда\", \"секунди\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"милисекунди\", \"милисекунда\", \"милисекунди\"][getSlavicForm(c)];\n    }, \",\"),\n    bn: language(\"বছর\", \"মাস\", \"সপ্তাহ\", \"দিন\", \"ঘন্টা\", \"মিনিট\", \"সেকেন্ড\", \"মিলিসেকেন্ড\"),\n    ca: language(function (c) {\n      return \"any\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"mes\" + (c === 1 ? \"\" : \"os\");\n    }, function (c) {\n      return \"setman\" + (c === 1 ? \"a\" : \"es\");\n    }, function (c) {\n      return \"di\" + (c === 1 ? \"a\" : \"es\");\n    }, function (c) {\n      return \"hor\" + (c === 1 ? \"a\" : \"es\");\n    }, function (c) {\n      return \"minut\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"segon\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"milisegon\" + (c === 1 ? \"\" : \"s\");\n    }, \",\"),\n    ckb: language(\"ساڵ\", \"مانگ\", \"هەفتە\", \"ڕۆژ\", \"کاژێر\", \"خولەک\", \"چرکە\", \"میلی چرکە\", \".\"),\n    cs: language(function (c) {\n      return [\"rok\", \"roku\", \"roky\", \"let\"][getCzechOrSlovakForm(c)];\n    }, function (c) {\n      return [\"měsíc\", \"měsíce\", \"měsíce\", \"měsíců\"][getCzechOrSlovakForm(c)];\n    }, function (c) {\n      return [\"týden\", \"týdne\", \"týdny\", \"týdnů\"][getCzechOrSlovakForm(c)];\n    }, function (c) {\n      return [\"den\", \"dne\", \"dny\", \"dní\"][getCzechOrSlovakForm(c)];\n    }, function (c) {\n      return [\"hodina\", \"hodiny\", \"hodiny\", \"hodin\"][getCzechOrSlovakForm(c)];\n    }, function (c) {\n      return [\"minuta\", \"minuty\", \"minuty\", \"minut\"][getCzechOrSlovakForm(c)];\n    }, function (c) {\n      return [\"sekunda\", \"sekundy\", \"sekundy\", \"sekund\"][getCzechOrSlovakForm(c)];\n    }, function (c) {\n      return [\"milisekunda\", \"milisekundy\", \"milisekundy\", \"milisekund\"][getCzechOrSlovakForm(c)];\n    }, \",\"),\n    cy: language(\"flwyddyn\", \"mis\", \"wythnos\", \"diwrnod\", \"awr\", \"munud\", \"eiliad\", \"milieiliad\"),\n    da: language(\"år\", function (c) {\n      return \"måned\" + (c === 1 ? \"\" : \"er\");\n    }, function (c) {\n      return \"uge\" + (c === 1 ? \"\" : \"r\");\n    }, function (c) {\n      return \"dag\" + (c === 1 ? \"\" : \"e\");\n    }, function (c) {\n      return \"time\" + (c === 1 ? \"\" : \"r\");\n    }, function (c) {\n      return \"minut\" + (c === 1 ? \"\" : \"ter\");\n    }, function (c) {\n      return \"sekund\" + (c === 1 ? \"\" : \"er\");\n    }, function (c) {\n      return \"millisekund\" + (c === 1 ? \"\" : \"er\");\n    }, \",\"),\n    de: language(function (c) {\n      return \"Jahr\" + (c === 1 ? \"\" : \"e\");\n    }, function (c) {\n      return \"Monat\" + (c === 1 ? \"\" : \"e\");\n    }, function (c) {\n      return \"Woche\" + (c === 1 ? \"\" : \"n\");\n    }, function (c) {\n      return \"Tag\" + (c === 1 ? \"\" : \"e\");\n    }, function (c) {\n      return \"Stunde\" + (c === 1 ? \"\" : \"n\");\n    }, function (c) {\n      return \"Minute\" + (c === 1 ? \"\" : \"n\");\n    }, function (c) {\n      return \"Sekunde\" + (c === 1 ? \"\" : \"n\");\n    }, function (c) {\n      return \"Millisekunde\" + (c === 1 ? \"\" : \"n\");\n    }, \",\"),\n    el: GREEK,\n    en: language(function (c) {\n      return \"year\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"month\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"week\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"day\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"hour\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"minute\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"second\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"millisecond\" + (c === 1 ? \"\" : \"s\");\n    }),\n    eo: language(function (c) {\n      return \"jaro\" + (c === 1 ? \"\" : \"j\");\n    }, function (c) {\n      return \"monato\" + (c === 1 ? \"\" : \"j\");\n    }, function (c) {\n      return \"semajno\" + (c === 1 ? \"\" : \"j\");\n    }, function (c) {\n      return \"tago\" + (c === 1 ? \"\" : \"j\");\n    }, function (c) {\n      return \"horo\" + (c === 1 ? \"\" : \"j\");\n    }, function (c) {\n      return \"minuto\" + (c === 1 ? \"\" : \"j\");\n    }, function (c) {\n      return \"sekundo\" + (c === 1 ? \"\" : \"j\");\n    }, function (c) {\n      return \"milisekundo\" + (c === 1 ? \"\" : \"j\");\n    }, \",\"),\n    es: language(function (c) {\n      return \"año\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"mes\" + (c === 1 ? \"\" : \"es\");\n    }, function (c) {\n      return \"semana\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"día\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"hora\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"minuto\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"segundo\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"milisegundo\" + (c === 1 ? \"\" : \"s\");\n    }, \",\"),\n    et: language(function (c) {\n      return \"aasta\" + (c === 1 ? \"\" : \"t\");\n    }, function (c) {\n      return \"kuu\" + (c === 1 ? \"\" : \"d\");\n    }, function (c) {\n      return \"nädal\" + (c === 1 ? \"\" : \"at\");\n    }, function (c) {\n      return \"päev\" + (c === 1 ? \"\" : \"a\");\n    }, function (c) {\n      return \"tund\" + (c === 1 ? \"\" : \"i\");\n    }, function (c) {\n      return \"minut\" + (c === 1 ? \"\" : \"it\");\n    }, function (c) {\n      return \"sekund\" + (c === 1 ? \"\" : \"it\");\n    }, function (c) {\n      return \"millisekund\" + (c === 1 ? \"\" : \"it\");\n    }, \",\"),\n    eu: language(\"urte\", \"hilabete\", \"aste\", \"egun\", \"ordu\", \"minutu\", \"segundo\", \"milisegundo\", \",\"),\n    fa: language(\"سال\", \"ماه\", \"هفته\", \"روز\", \"ساعت\", \"دقیقه\", \"ثانیه\", \"میلی ثانیه\"),\n    fi: language(function (c) {\n      return c === 1 ? \"vuosi\" : \"vuotta\";\n    }, function (c) {\n      return c === 1 ? \"kuukausi\" : \"kuukautta\";\n    }, function (c) {\n      return \"viikko\" + (c === 1 ? \"\" : \"a\");\n    }, function (c) {\n      return \"päivä\" + (c === 1 ? \"\" : \"ä\");\n    }, function (c) {\n      return \"tunti\" + (c === 1 ? \"\" : \"a\");\n    }, function (c) {\n      return \"minuutti\" + (c === 1 ? \"\" : \"a\");\n    }, function (c) {\n      return \"sekunti\" + (c === 1 ? \"\" : \"a\");\n    }, function (c) {\n      return \"millisekunti\" + (c === 1 ? \"\" : \"a\");\n    }, \",\"),\n    fo: language(\"ár\", function (c) {\n      return c === 1 ? \"mánaður\" : \"mánaðir\";\n    }, function (c) {\n      return c === 1 ? \"vika\" : \"vikur\";\n    }, function (c) {\n      return c === 1 ? \"dagur\" : \"dagar\";\n    }, function (c) {\n      return c === 1 ? \"tími\" : \"tímar\";\n    }, function (c) {\n      return c === 1 ? \"minuttur\" : \"minuttir\";\n    }, \"sekund\", \"millisekund\", \",\"),\n    fr: language(function (c) {\n      return \"an\" + (c >= 2 ? \"s\" : \"\");\n    }, \"mois\", function (c) {\n      return \"semaine\" + (c >= 2 ? \"s\" : \"\");\n    }, function (c) {\n      return \"jour\" + (c >= 2 ? \"s\" : \"\");\n    }, function (c) {\n      return \"heure\" + (c >= 2 ? \"s\" : \"\");\n    }, function (c) {\n      return \"minute\" + (c >= 2 ? \"s\" : \"\");\n    }, function (c) {\n      return \"seconde\" + (c >= 2 ? \"s\" : \"\");\n    }, function (c) {\n      return \"milliseconde\" + (c >= 2 ? \"s\" : \"\");\n    }, \",\"),\n    gr: GREEK,\n    he: language(function (c) {\n      return c === 1 ? \"שנה\" : \"שנים\";\n    }, function (c) {\n      return c === 1 ? \"חודש\" : \"חודשים\";\n    }, function (c) {\n      return c === 1 ? \"שבוע\" : \"שבועות\";\n    }, function (c) {\n      return c === 1 ? \"יום\" : \"ימים\";\n    }, function (c) {\n      return c === 1 ? \"שעה\" : \"שעות\";\n    }, function (c) {\n      return c === 1 ? \"דקה\" : \"דקות\";\n    }, function (c) {\n      return c === 1 ? \"שניה\" : \"שניות\";\n    }, function (c) {\n      return c === 1 ? \"מילישנייה\" : \"מילישניות\";\n    }),\n    hr: language(function (c) {\n      if (c % 10 === 2 || c % 10 === 3 || c % 10 === 4) {\n        return \"godine\";\n      }\n      return \"godina\";\n    }, function (c) {\n      if (c === 1) {\n        return \"mjesec\";\n      } else if (c === 2 || c === 3 || c === 4) {\n        return \"mjeseca\";\n      }\n      return \"mjeseci\";\n    }, function (c) {\n      if (c % 10 === 1 && c !== 11) {\n        return \"tjedan\";\n      }\n      return \"tjedna\";\n    }, function (c) {\n      return c === 1 ? \"dan\" : \"dana\";\n    }, function (c) {\n      if (c === 1) {\n        return \"sat\";\n      } else if (c === 2 || c === 3 || c === 4) {\n        return \"sata\";\n      }\n      return \"sati\";\n    }, function (c) {\n      var mod10 = c % 10;\n      if ((mod10 === 2 || mod10 === 3 || mod10 === 4) && (c < 10 || c > 14)) {\n        return \"minute\";\n      }\n      return \"minuta\";\n    }, function (c) {\n      var mod10 = c % 10;\n      if (mod10 === 5 || Math.floor(c) === c && c >= 10 && c <= 19) {\n        return \"sekundi\";\n      } else if (mod10 === 1) {\n        return \"sekunda\";\n      } else if (mod10 === 2 || mod10 === 3 || mod10 === 4) {\n        return \"sekunde\";\n      }\n      return \"sekundi\";\n    }, function (c) {\n      if (c === 1) {\n        return \"milisekunda\";\n      } else if (c % 10 === 2 || c % 10 === 3 || c % 10 === 4) {\n        return \"milisekunde\";\n      }\n      return \"milisekundi\";\n    }, \",\"),\n    hi: language(\"साल\", function (c) {\n      return c === 1 ? \"महीना\" : \"महीने\";\n    }, function (c) {\n      return c === 1 ? \"हफ़्ता\" : \"हफ्ते\";\n    }, \"दिन\", function (c) {\n      return c === 1 ? \"घंटा\" : \"घंटे\";\n    }, \"मिनट\", \"सेकंड\", \"मिलीसेकंड\"),\n    hu: language(\"év\", \"hónap\", \"hét\", \"nap\", \"óra\", \"perc\", \"másodperc\", \"ezredmásodperc\", \",\"),\n    id: language(\"tahun\", \"bulan\", \"minggu\", \"hari\", \"jam\", \"menit\", \"detik\", \"milidetik\"),\n    is: language(\"ár\", function (c) {\n      return \"mánuð\" + (c === 1 ? \"ur\" : \"ir\");\n    }, function (c) {\n      return \"vik\" + (c === 1 ? \"a\" : \"ur\");\n    }, function (c) {\n      return \"dag\" + (c === 1 ? \"ur\" : \"ar\");\n    }, function (c) {\n      return \"klukkutím\" + (c === 1 ? \"i\" : \"ar\");\n    }, function (c) {\n      return \"mínút\" + (c === 1 ? \"a\" : \"ur\");\n    }, function (c) {\n      return \"sekúnd\" + (c === 1 ? \"a\" : \"ur\");\n    }, function (c) {\n      return \"millisekúnd\" + (c === 1 ? \"a\" : \"ur\");\n    }),\n    it: language(function (c) {\n      return \"ann\" + (c === 1 ? \"o\" : \"i\");\n    }, function (c) {\n      return \"mes\" + (c === 1 ? \"e\" : \"i\");\n    }, function (c) {\n      return \"settiman\" + (c === 1 ? \"a\" : \"e\");\n    }, function (c) {\n      return \"giorn\" + (c === 1 ? \"o\" : \"i\");\n    }, function (c) {\n      return \"or\" + (c === 1 ? \"a\" : \"e\");\n    }, function (c) {\n      return \"minut\" + (c === 1 ? \"o\" : \"i\");\n    }, function (c) {\n      return \"second\" + (c === 1 ? \"o\" : \"i\");\n    }, function (c) {\n      return \"millisecond\" + (c === 1 ? \"o\" : \"i\");\n    }, \",\"),\n    ja: language(\"年\", \"ヶ月\", \"週\", \"日\", \"時間\", \"分\", \"秒\", \"ミリ秒\"),\n    km: language(\"ឆ្នាំ\", \"ខែ\", \"សប្តាហ៍\", \"ថ្ងៃ\", \"ម៉ោង\", \"នាទី\", \"វិនាទី\", \"មិល្លីវិនាទី\"),\n    kn: language(function (c) {\n      return c === 1 ? \"ವರ್ಷ\" : \"ವರ್ಷಗಳು\";\n    }, function (c) {\n      return c === 1 ? \"ತಿಂಗಳು\" : \"ತಿಂಗಳುಗಳು\";\n    }, function (c) {\n      return c === 1 ? \"ವಾರ\" : \"ವಾರಗಳು\";\n    }, function (c) {\n      return c === 1 ? \"ದಿನ\" : \"ದಿನಗಳು\";\n    }, function (c) {\n      return c === 1 ? \"ಗಂಟೆ\" : \"ಗಂಟೆಗಳು\";\n    }, function (c) {\n      return c === 1 ? \"ನಿಮಿಷ\" : \"ನಿಮಿಷಗಳು\";\n    }, function (c) {\n      return c === 1 ? \"ಸೆಕೆಂಡ್\" : \"ಸೆಕೆಂಡುಗಳು\";\n    }, function (c) {\n      return c === 1 ? \"ಮಿಲಿಸೆಕೆಂಡ್\" : \"ಮಿಲಿಸೆಕೆಂಡುಗಳು\";\n    }),\n    ko: language(\"년\", \"개월\", \"주일\", \"일\", \"시간\", \"분\", \"초\", \"밀리 초\"),\n    ku: language(\"sal\", \"meh\", \"hefte\", \"roj\", \"seet\", \"deqe\", \"saniye\", \"mîlîçirk\", \",\"),\n    lo: language(\"ປີ\", \"ເດືອນ\", \"ອາທິດ\", \"ມື້\", \"ຊົ່ວໂມງ\", \"ນາທີ\", \"ວິນາທີ\", \"ມິນລິວິນາທີ\", \",\"),\n    lt: language(function (c) {\n      return c % 10 === 0 || c % 100 >= 10 && c % 100 <= 20 ? \"metų\" : \"metai\";\n    }, function (c) {\n      return [\"mėnuo\", \"mėnesiai\", \"mėnesių\"][getLithuanianForm(c)];\n    }, function (c) {\n      return [\"savaitė\", \"savaitės\", \"savaičių\"][getLithuanianForm(c)];\n    }, function (c) {\n      return [\"diena\", \"dienos\", \"dienų\"][getLithuanianForm(c)];\n    }, function (c) {\n      return [\"valanda\", \"valandos\", \"valandų\"][getLithuanianForm(c)];\n    }, function (c) {\n      return [\"minutė\", \"minutės\", \"minučių\"][getLithuanianForm(c)];\n    }, function (c) {\n      return [\"sekundė\", \"sekundės\", \"sekundžių\"][getLithuanianForm(c)];\n    }, function (c) {\n      return [\"milisekundė\", \"milisekundės\", \"milisekundžių\"][getLithuanianForm(c)];\n    }, \",\"),\n    lv: language(function (c) {\n      return getLatvianForm(c) ? \"gads\" : \"gadi\";\n    }, function (c) {\n      return getLatvianForm(c) ? \"mēnesis\" : \"mēneši\";\n    }, function (c) {\n      return getLatvianForm(c) ? \"nedēļa\" : \"nedēļas\";\n    }, function (c) {\n      return getLatvianForm(c) ? \"diena\" : \"dienas\";\n    }, function (c) {\n      return getLatvianForm(c) ? \"stunda\" : \"stundas\";\n    }, function (c) {\n      return getLatvianForm(c) ? \"minūte\" : \"minūtes\";\n    }, function (c) {\n      return getLatvianForm(c) ? \"sekunde\" : \"sekundes\";\n    }, function (c) {\n      return getLatvianForm(c) ? \"milisekunde\" : \"milisekundes\";\n    }, \",\"),\n    mk: language(function (c) {\n      return c === 1 ? \"година\" : \"години\";\n    }, function (c) {\n      return c === 1 ? \"месец\" : \"месеци\";\n    }, function (c) {\n      return c === 1 ? \"недела\" : \"недели\";\n    }, function (c) {\n      return c === 1 ? \"ден\" : \"дена\";\n    }, function (c) {\n      return c === 1 ? \"час\" : \"часа\";\n    }, function (c) {\n      return c === 1 ? \"минута\" : \"минути\";\n    }, function (c) {\n      return c === 1 ? \"секунда\" : \"секунди\";\n    }, function (c) {\n      return c === 1 ? \"милисекунда\" : \"милисекунди\";\n    }, \",\"),\n    mn: language(\"жил\", \"сар\", \"долоо хоног\", \"өдөр\", \"цаг\", \"минут\", \"секунд\", \"миллисекунд\"),\n    mr: language(function (c) {\n      return c === 1 ? \"वर्ष\" : \"वर्षे\";\n    }, function (c) {\n      return c === 1 ? \"महिना\" : \"महिने\";\n    }, function (c) {\n      return c === 1 ? \"आठवडा\" : \"आठवडे\";\n    }, \"दिवस\", \"तास\", function (c) {\n      return c === 1 ? \"मिनिट\" : \"मिनिटे\";\n    }, \"सेकंद\", \"मिलिसेकंद\"),\n    ms: language(\"tahun\", \"bulan\", \"minggu\", \"hari\", \"jam\", \"minit\", \"saat\", \"milisaat\"),\n    nl: language(\"jaar\", function (c) {\n      return c === 1 ? \"maand\" : \"maanden\";\n    }, function (c) {\n      return c === 1 ? \"week\" : \"weken\";\n    }, function (c) {\n      return c === 1 ? \"dag\" : \"dagen\";\n    }, \"uur\", function (c) {\n      return c === 1 ? \"minuut\" : \"minuten\";\n    }, function (c) {\n      return c === 1 ? \"seconde\" : \"seconden\";\n    }, function (c) {\n      return c === 1 ? \"milliseconde\" : \"milliseconden\";\n    }, \",\"),\n    no: language(\"år\", function (c) {\n      return \"måned\" + (c === 1 ? \"\" : \"er\");\n    }, function (c) {\n      return \"uke\" + (c === 1 ? \"\" : \"r\");\n    }, function (c) {\n      return \"dag\" + (c === 1 ? \"\" : \"er\");\n    }, function (c) {\n      return \"time\" + (c === 1 ? \"\" : \"r\");\n    }, function (c) {\n      return \"minutt\" + (c === 1 ? \"\" : \"er\");\n    }, function (c) {\n      return \"sekund\" + (c === 1 ? \"\" : \"er\");\n    }, function (c) {\n      return \"millisekund\" + (c === 1 ? \"\" : \"er\");\n    }, \",\"),\n    pl: language(function (c) {\n      return [\"rok\", \"roku\", \"lata\", \"lat\"][getPolishForm(c)];\n    }, function (c) {\n      return [\"miesiąc\", \"miesiąca\", \"miesiące\", \"miesięcy\"][getPolishForm(c)];\n    }, function (c) {\n      return [\"tydzień\", \"tygodnia\", \"tygodnie\", \"tygodni\"][getPolishForm(c)];\n    }, function (c) {\n      return [\"dzień\", \"dnia\", \"dni\", \"dni\"][getPolishForm(c)];\n    }, function (c) {\n      return [\"godzina\", \"godziny\", \"godziny\", \"godzin\"][getPolishForm(c)];\n    }, function (c) {\n      return [\"minuta\", \"minuty\", \"minuty\", \"minut\"][getPolishForm(c)];\n    }, function (c) {\n      return [\"sekunda\", \"sekundy\", \"sekundy\", \"sekund\"][getPolishForm(c)];\n    }, function (c) {\n      return [\"milisekunda\", \"milisekundy\", \"milisekundy\", \"milisekund\"][getPolishForm(c)];\n    }, \",\"),\n    pt: language(function (c) {\n      return \"ano\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return c === 1 ? \"mês\" : \"meses\";\n    }, function (c) {\n      return \"semana\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"dia\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"hora\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"minuto\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"segundo\" + (c === 1 ? \"\" : \"s\");\n    }, function (c) {\n      return \"milissegundo\" + (c === 1 ? \"\" : \"s\");\n    }, \",\"),\n    ro: language(function (c) {\n      return c === 1 ? \"an\" : \"ani\";\n    }, function (c) {\n      return c === 1 ? \"lună\" : \"luni\";\n    }, function (c) {\n      return c === 1 ? \"săptămână\" : \"săptămâni\";\n    }, function (c) {\n      return c === 1 ? \"zi\" : \"zile\";\n    }, function (c) {\n      return c === 1 ? \"oră\" : \"ore\";\n    }, function (c) {\n      return c === 1 ? \"minut\" : \"minute\";\n    }, function (c) {\n      return c === 1 ? \"secundă\" : \"secunde\";\n    }, function (c) {\n      return c === 1 ? \"milisecundă\" : \"milisecunde\";\n    }, \",\"),\n    ru: language(function (c) {\n      return [\"лет\", \"год\", \"года\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"месяцев\", \"месяц\", \"месяца\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"недель\", \"неделя\", \"недели\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"дней\", \"день\", \"дня\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"часов\", \"час\", \"часа\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"минут\", \"минута\", \"минуты\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"секунд\", \"секунда\", \"секунды\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"миллисекунд\", \"миллисекунда\", \"миллисекунды\"][getSlavicForm(c)];\n    }, \",\"),\n    sq: language(function (c) {\n      return c === 1 ? \"vit\" : \"vjet\";\n    }, \"muaj\", \"javë\", \"ditë\", \"orë\", function (c) {\n      return \"minut\" + (c === 1 ? \"ë\" : \"a\");\n    }, function (c) {\n      return \"sekond\" + (c === 1 ? \"ë\" : \"a\");\n    }, function (c) {\n      return \"milisekond\" + (c === 1 ? \"ë\" : \"a\");\n    }, \",\"),\n    sr: language(function (c) {\n      return [\"години\", \"година\", \"године\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"месеци\", \"месец\", \"месеца\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"недељи\", \"недеља\", \"недеље\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"дани\", \"дан\", \"дана\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"сати\", \"сат\", \"сата\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"минута\", \"минут\", \"минута\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"секунди\", \"секунда\", \"секунде\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"милисекунди\", \"милисекунда\", \"милисекунде\"][getSlavicForm(c)];\n    }, \",\"),\n    ta: language(function (c) {\n      return c === 1 ? \"வருடம்\" : \"ஆண்டுகள்\";\n    }, function (c) {\n      return c === 1 ? \"மாதம்\" : \"மாதங்கள்\";\n    }, function (c) {\n      return c === 1 ? \"வாரம்\" : \"வாரங்கள்\";\n    }, function (c) {\n      return c === 1 ? \"நாள்\" : \"நாட்கள்\";\n    }, function (c) {\n      return c === 1 ? \"மணி\" : \"மணிநேரம்\";\n    }, function (c) {\n      return \"நிமிட\" + (c === 1 ? \"ம்\" : \"ங்கள்\");\n    }, function (c) {\n      return \"வினாடி\" + (c === 1 ? \"\" : \"கள்\");\n    }, function (c) {\n      return \"மில்லி விநாடி\" + (c === 1 ? \"\" : \"கள்\");\n    }),\n    te: language(function (c) {\n      return \"సంవత్స\" + (c === 1 ? \"రం\" : \"రాల\");\n    }, function (c) {\n      return \"నెల\" + (c === 1 ? \"\" : \"ల\");\n    }, function (c) {\n      return c === 1 ? \"వారం\" : \"వారాలు\";\n    }, function (c) {\n      return \"రోజు\" + (c === 1 ? \"\" : \"లు\");\n    }, function (c) {\n      return \"గంట\" + (c === 1 ? \"\" : \"లు\");\n    }, function (c) {\n      return c === 1 ? \"నిమిషం\" : \"నిమిషాలు\";\n    }, function (c) {\n      return c === 1 ? \"సెకను\" : \"సెకన్లు\";\n    }, function (c) {\n      return c === 1 ? \"మిల్లీసెకన్\" : \"మిల్లీసెకన్లు\";\n    }),\n    uk: language(function (c) {\n      return [\"років\", \"рік\", \"роки\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"місяців\", \"місяць\", \"місяці\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"тижнів\", \"тиждень\", \"тижні\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"днів\", \"день\", \"дні\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"годин\", \"година\", \"години\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"хвилин\", \"хвилина\", \"хвилини\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"секунд\", \"секунда\", \"секунди\"][getSlavicForm(c)];\n    }, function (c) {\n      return [\"мілісекунд\", \"мілісекунда\", \"мілісекунди\"][getSlavicForm(c)];\n    }, \",\"),\n    ur: language(\"سال\", function (c) {\n      return c === 1 ? \"مہینہ\" : \"مہینے\";\n    }, function (c) {\n      return c === 1 ? \"ہفتہ\" : \"ہفتے\";\n    }, \"دن\", function (c) {\n      return c === 1 ? \"گھنٹہ\" : \"گھنٹے\";\n    }, \"منٹ\", \"سیکنڈ\", \"ملی سیکنڈ\"),\n    sk: language(function (c) {\n      return [\"rok\", \"roky\", \"roky\", \"rokov\"][getCzechOrSlovakForm(c)];\n    }, function (c) {\n      return [\"mesiac\", \"mesiace\", \"mesiace\", \"mesiacov\"][getCzechOrSlovakForm(c)];\n    }, function (c) {\n      return [\"týždeň\", \"týždne\", \"týždne\", \"týždňov\"][getCzechOrSlovakForm(c)];\n    }, function (c) {\n      return [\"deň\", \"dni\", \"dni\", \"dní\"][getCzechOrSlovakForm(c)];\n    }, function (c) {\n      return [\"hodina\", \"hodiny\", \"hodiny\", \"hodín\"][getCzechOrSlovakForm(c)];\n    }, function (c) {\n      return [\"minúta\", \"minúty\", \"minúty\", \"minút\"][getCzechOrSlovakForm(c)];\n    }, function (c) {\n      return [\"sekunda\", \"sekundy\", \"sekundy\", \"sekúnd\"][getCzechOrSlovakForm(c)];\n    }, function (c) {\n      return [\"milisekunda\", \"milisekundy\", \"milisekundy\", \"milisekúnd\"][getCzechOrSlovakForm(c)];\n    }, \",\"),\n    sl: language(function (c) {\n      if (c % 10 === 1) {\n        return \"leto\";\n      } else if (c % 100 === 2) {\n        return \"leti\";\n      } else if (c % 100 === 3 || c % 100 === 4 || Math.floor(c) !== c && c % 100 <= 5) {\n        return \"leta\";\n      } else {\n        return \"let\";\n      }\n    }, function (c) {\n      if (c % 10 === 1) {\n        return \"mesec\";\n      } else if (c % 100 === 2 || Math.floor(c) !== c && c % 100 <= 5) {\n        return \"meseca\";\n      } else if (c % 10 === 3 || c % 10 === 4) {\n        return \"mesece\";\n      } else {\n        return \"mesecev\";\n      }\n    }, function (c) {\n      if (c % 10 === 1) {\n        return \"teden\";\n      } else if (c % 10 === 2 || Math.floor(c) !== c && c % 100 <= 4) {\n        return \"tedna\";\n      } else if (c % 10 === 3 || c % 10 === 4) {\n        return \"tedne\";\n      } else {\n        return \"tednov\";\n      }\n    }, function (c) {\n      return c % 100 === 1 ? \"dan\" : \"dni\";\n    }, function (c) {\n      if (c % 10 === 1) {\n        return \"ura\";\n      } else if (c % 100 === 2) {\n        return \"uri\";\n      } else if (c % 10 === 3 || c % 10 === 4 || Math.floor(c) !== c) {\n        return \"ure\";\n      } else {\n        return \"ur\";\n      }\n    }, function (c) {\n      if (c % 10 === 1) {\n        return \"minuta\";\n      } else if (c % 10 === 2) {\n        return \"minuti\";\n      } else if (c % 10 === 3 || c % 10 === 4 || Math.floor(c) !== c && c % 100 <= 4) {\n        return \"minute\";\n      } else {\n        return \"minut\";\n      }\n    }, function (c) {\n      if (c % 10 === 1) {\n        return \"sekunda\";\n      } else if (c % 100 === 2) {\n        return \"sekundi\";\n      } else if (c % 100 === 3 || c % 100 === 4 || Math.floor(c) !== c) {\n        return \"sekunde\";\n      } else {\n        return \"sekund\";\n      }\n    }, function (c) {\n      if (c % 10 === 1) {\n        return \"milisekunda\";\n      } else if (c % 100 === 2) {\n        return \"milisekundi\";\n      } else if (c % 100 === 3 || c % 100 === 4 || Math.floor(c) !== c) {\n        return \"milisekunde\";\n      } else {\n        return \"milisekund\";\n      }\n    }, \",\"),\n    sv: language(\"år\", function (c) {\n      return \"månad\" + (c === 1 ? \"\" : \"er\");\n    }, function (c) {\n      return \"veck\" + (c === 1 ? \"a\" : \"or\");\n    }, function (c) {\n      return \"dag\" + (c === 1 ? \"\" : \"ar\");\n    }, function (c) {\n      return \"timm\" + (c === 1 ? \"e\" : \"ar\");\n    }, function (c) {\n      return \"minut\" + (c === 1 ? \"\" : \"er\");\n    }, function (c) {\n      return \"sekund\" + (c === 1 ? \"\" : \"er\");\n    }, function (c) {\n      return \"millisekund\" + (c === 1 ? \"\" : \"er\");\n    }, \",\"),\n    sw: assign(language(function (c) {\n      return c === 1 ? \"mwaka\" : \"miaka\";\n    }, function (c) {\n      return c === 1 ? \"mwezi\" : \"miezi\";\n    }, \"wiki\", function (c) {\n      return c === 1 ? \"siku\" : \"masiku\";\n    }, function (c) {\n      return c === 1 ? \"saa\" : \"masaa\";\n    }, \"dakika\", \"sekunde\", \"milisekunde\"), {\n      _numberFirst: true\n    }),\n    tr: language(\"yıl\", \"ay\", \"hafta\", \"gün\", \"saat\", \"dakika\", \"saniye\", \"milisaniye\", \",\"),\n    th: language(\"ปี\", \"เดือน\", \"สัปดาห์\", \"วัน\", \"ชั่วโมง\", \"นาที\", \"วินาที\", \"มิลลิวินาที\"),\n    uz: language(\"yil\", \"oy\", \"hafta\", \"kun\", \"soat\", \"minut\", \"sekund\", \"millisekund\"),\n    uz_CYR: language(\"йил\", \"ой\", \"ҳафта\", \"кун\", \"соат\", \"минут\", \"секунд\", \"миллисекунд\"),\n    vi: language(\"năm\", \"tháng\", \"tuần\", \"ngày\", \"giờ\", \"phút\", \"giây\", \"mili giây\", \",\"),\n    zh_CN: language(\"年\", \"个月\", \"周\", \"天\", \"小时\", \"分钟\", \"秒\", \"毫秒\"),\n    zh_TW: language(\"年\", \"個月\", \"周\", \"天\", \"小時\", \"分鐘\", \"秒\", \"毫秒\")\n  };\n\n  /**\n   * Helper function for creating language definitions.\n   *\n   * @internal\n   * @param {Unit} y\n   * @param {Unit} mo\n   * @param {Unit} w\n   * @param {Unit} d\n   * @param {Unit} h\n   * @param {Unit} m\n   * @param {Unit} s\n   * @param {Unit} ms\n   * @param {string} [decimal]\n   * @returns {Language}\n   */\n  function language(y, mo, w, d, h, m, s, ms, decimal) {\n    /** @type {Language} */\n    var result = {\n      y: y,\n      mo: mo,\n      w: w,\n      d: d,\n      h: h,\n      m: m,\n      s: s,\n      ms: ms\n    };\n    if (typeof decimal !== \"undefined\") {\n      result.decimal = decimal;\n    }\n    return result;\n  }\n\n  /**\n   * Helper function for Arabic.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {0 | 1 | 2}\n   */\n  function getArabicForm(c) {\n    if (c === 2) {\n      return 1;\n    }\n    if (c > 2 && c < 11) {\n      return 2;\n    }\n    return 0;\n  }\n\n  /**\n   * Helper function for Polish.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {0 | 1 | 2 | 3}\n   */\n  function getPolishForm(c) {\n    if (c === 1) {\n      return 0;\n    }\n    if (Math.floor(c) !== c) {\n      return 1;\n    }\n    if (c % 10 >= 2 && c % 10 <= 4 && !(c % 100 > 10 && c % 100 < 20)) {\n      return 2;\n    }\n    return 3;\n  }\n\n  /**\n   * Helper function for Slavic languages.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {0 | 1 | 2 | 3}\n   */\n  function getSlavicForm(c) {\n    if (Math.floor(c) !== c) {\n      return 2;\n    }\n    if (c % 100 >= 5 && c % 100 <= 20 || c % 10 >= 5 && c % 10 <= 9 || c % 10 === 0) {\n      return 0;\n    }\n    if (c % 10 === 1) {\n      return 1;\n    }\n    if (c > 1) {\n      return 2;\n    }\n    return 0;\n  }\n\n  /**\n   * Helper function for Czech or Slovak.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {0 | 1 | 2 | 3}\n   */\n  function getCzechOrSlovakForm(c) {\n    if (c === 1) {\n      return 0;\n    }\n    if (Math.floor(c) !== c) {\n      return 1;\n    }\n    if (c % 10 >= 2 && c % 10 <= 4 && c % 100 < 10) {\n      return 2;\n    }\n    return 3;\n  }\n\n  /**\n   * Helper function for Lithuanian.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {0 | 1 | 2}\n   */\n  function getLithuanianForm(c) {\n    if (c === 1 || c % 10 === 1 && c % 100 > 20) {\n      return 0;\n    }\n    if (Math.floor(c) !== c || c % 10 >= 2 && c % 100 > 20 || c % 10 >= 2 && c % 100 < 10) {\n      return 1;\n    }\n    return 2;\n  }\n\n  /**\n   * Helper function for Latvian.\n   *\n   * @internal\n   * @param {number} c\n   * @returns {boolean}\n   */\n  function getLatvianForm(c) {\n    return c % 10 === 1 && c % 100 !== 11;\n  }\n\n  /**\n   * @internal\n   * @template T\n   * @param {T} obj\n   * @param {keyof T} key\n   * @returns {boolean}\n   */\n  function has(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n  }\n\n  /**\n   * @internal\n   * @param {Pick<Required<Options>, \"language\" | \"fallbacks\" | \"languages\">} options\n   * @throws {Error} Throws an error if language is not found.\n   * @returns {Language}\n   */\n  function getLanguage(options) {\n    var possibleLanguages = [options.language];\n    if (has(options, \"fallbacks\")) {\n      if (isArray(options.fallbacks) && options.fallbacks.length) {\n        possibleLanguages = possibleLanguages.concat(options.fallbacks);\n      } else {\n        throw new Error(\"fallbacks must be an array with at least one element\");\n      }\n    }\n    for (var i = 0; i < possibleLanguages.length; i++) {\n      var languageToTry = possibleLanguages[i];\n      if (has(options.languages, languageToTry)) {\n        return options.languages[languageToTry];\n      }\n      if (has(LANGUAGES, languageToTry)) {\n        return LANGUAGES[languageToTry];\n      }\n    }\n    throw new Error(\"No language found.\");\n  }\n\n  /**\n   * @internal\n   * @param {Piece} piece\n   * @param {Language} language\n   * @param {Pick<Required<Options>, \"decimal\" | \"spacer\" | \"maxDecimalPoints\" | \"digitReplacements\">} options\n   */\n  function renderPiece(piece, language, options) {\n    var unitName = piece.unitName;\n    var unitCount = piece.unitCount;\n    var spacer = options.spacer;\n    var maxDecimalPoints = options.maxDecimalPoints;\n\n    /** @type {string} */\n    var decimal;\n    if (has(options, \"decimal\")) {\n      decimal = options.decimal;\n    } else if (has(language, \"decimal\")) {\n      decimal = language.decimal;\n    } else {\n      decimal = \".\";\n    }\n\n    /** @type {undefined | DigitReplacements} */\n    var digitReplacements;\n    if (\"digitReplacements\" in options) {\n      digitReplacements = options.digitReplacements;\n    } else if (\"_digitReplacements\" in language) {\n      digitReplacements = language._digitReplacements;\n    }\n\n    /** @type {string} */\n    var formattedCount;\n    var normalizedUnitCount = maxDecimalPoints === void 0 ? unitCount : Math.floor(unitCount * Math.pow(10, maxDecimalPoints)) / Math.pow(10, maxDecimalPoints);\n    var countStr = normalizedUnitCount.toString();\n    if (language._hideCountIf2 && unitCount === 2) {\n      formattedCount = \"\";\n      spacer = \"\";\n    } else {\n      if (digitReplacements) {\n        formattedCount = \"\";\n        for (var i = 0; i < countStr.length; i++) {\n          var char = countStr[i];\n          if (char === \".\") {\n            formattedCount += decimal;\n          } else {\n            // @ts-ignore because `char` should always be 0-9 at this point.\n            formattedCount += digitReplacements[char];\n          }\n        }\n      } else {\n        formattedCount = countStr.replace(\".\", decimal);\n      }\n    }\n    var languageWord = language[unitName];\n    var word;\n    if (typeof languageWord === \"function\") {\n      word = languageWord(unitCount);\n    } else {\n      word = languageWord;\n    }\n    if (language._numberFirst) {\n      return word + spacer + formattedCount;\n    }\n    return formattedCount + spacer + word;\n  }\n\n  /**\n   * @internal\n   * @typedef {Object} Piece\n   * @prop {UnitName} unitName\n   * @prop {number} unitCount\n   */\n\n  /**\n   * @internal\n   * @param {number} ms\n   * @param {Pick<Required<Options>, \"units\" | \"unitMeasures\" | \"largest\" | \"round\">} options\n   * @returns {Piece[]}\n   */\n  function getPieces(ms, options) {\n    /** @type {UnitName} */\n    var unitName;\n\n    /** @type {number} */\n    var i;\n\n    /** @type {number} */\n    var unitCount;\n\n    /** @type {number} */\n    var msRemaining;\n    var units = options.units;\n    var unitMeasures = options.unitMeasures;\n    var largest = \"largest\" in options ? options.largest : Infinity;\n    if (!units.length) return [];\n\n    // Get the counts for each unit. Doesn't round or truncate anything.\n    // For example, might create an object like `{ y: 7, m: 6, w: 0, d: 5, h: 23.99 }`.\n    /** @type {Partial<Record<UnitName, number>>} */\n    var unitCounts = {};\n    msRemaining = ms;\n    for (i = 0; i < units.length; i++) {\n      unitName = units[i];\n      var unitMs = unitMeasures[unitName];\n      var isLast = i === units.length - 1;\n      unitCount = isLast ? msRemaining / unitMs : Math.floor(msRemaining / unitMs);\n      unitCounts[unitName] = unitCount;\n      msRemaining -= unitCount * unitMs;\n    }\n    if (options.round) {\n      // Update counts based on the `largest` option.\n      // For example, if `largest === 2` and `unitCount` is `{ y: 7, m: 6, w: 0, d: 5, h: 23.99 }`,\n      // updates to something like `{ y: 7, m: 6.2 }`.\n      var unitsRemainingBeforeRound = largest;\n      for (i = 0; i < units.length; i++) {\n        unitName = units[i];\n        unitCount = unitCounts[unitName];\n        if (unitCount === 0) continue;\n        unitsRemainingBeforeRound--;\n\n        // \"Take\" the rest of the units into this one.\n        if (unitsRemainingBeforeRound === 0) {\n          for (var j = i + 1; j < units.length; j++) {\n            var smallerUnitName = units[j];\n            var smallerUnitCount = unitCounts[smallerUnitName];\n            unitCounts[unitName] += smallerUnitCount * unitMeasures[smallerUnitName] / unitMeasures[unitName];\n            unitCounts[smallerUnitName] = 0;\n          }\n          break;\n        }\n      }\n\n      // Round the last piece (which should be the only non-integer).\n      //\n      // This can be a little tricky if the last piece \"bubbles up\" to a larger\n      // unit. For example, \"3 days, 23.99 hours\" should be rounded to \"4 days\".\n      // It can also require multiple passes. For example, \"6 days, 23.99 hours\"\n      // should become \"1 week\".\n      for (i = units.length - 1; i >= 0; i--) {\n        unitName = units[i];\n        unitCount = unitCounts[unitName];\n        if (unitCount === 0) continue;\n        var rounded = Math.round(unitCount);\n        unitCounts[unitName] = rounded;\n        if (i === 0) break;\n        var previousUnitName = units[i - 1];\n        var previousUnitMs = unitMeasures[previousUnitName];\n        var amountOfPreviousUnit = Math.floor(rounded * unitMeasures[unitName] / previousUnitMs);\n        if (amountOfPreviousUnit) {\n          unitCounts[previousUnitName] += amountOfPreviousUnit;\n          unitCounts[unitName] = 0;\n        } else {\n          break;\n        }\n      }\n    }\n\n    /** @type {Piece[]} */\n    var result = [];\n    for (i = 0; i < units.length && result.length < largest; i++) {\n      unitName = units[i];\n      unitCount = unitCounts[unitName];\n      if (unitCount) {\n        result.push({\n          unitName: unitName,\n          unitCount: unitCount\n        });\n      }\n    }\n    return result;\n  }\n\n  /**\n   * @internal\n   * @param {Piece[]} pieces\n   * @param {Pick<Required<Options>, \"units\" | \"language\" | \"languages\" | \"fallbacks\" | \"delimiter\" | \"spacer\" | \"decimal\" | \"conjunction\" | \"maxDecimalPoints\" | \"serialComma\" | \"digitReplacements\">} options\n   * @returns {string}\n   */\n  function formatPieces(pieces, options) {\n    var language = getLanguage(options);\n    if (!pieces.length) {\n      var units = options.units;\n      var smallestUnitName = units[units.length - 1];\n      return renderPiece({\n        unitName: smallestUnitName,\n        unitCount: 0\n      }, language, options);\n    }\n    var conjunction = options.conjunction;\n    var serialComma = options.serialComma;\n    var delimiter;\n    if (has(options, \"delimiter\")) {\n      delimiter = options.delimiter;\n    } else if (has(language, \"delimiter\")) {\n      delimiter = language.delimiter;\n    } else {\n      delimiter = \", \";\n    }\n\n    /** @type {string[]} */\n    var renderedPieces = [];\n    for (var i = 0; i < pieces.length; i++) {\n      renderedPieces.push(renderPiece(pieces[i], language, options));\n    }\n    if (!conjunction || pieces.length === 1) {\n      return renderedPieces.join(delimiter);\n    }\n    if (pieces.length === 2) {\n      return renderedPieces.join(conjunction);\n    }\n    return renderedPieces.slice(0, -1).join(delimiter) + (serialComma ? \",\" : \"\") + conjunction + renderedPieces.slice(-1);\n  }\n\n  /**\n   * Create a humanizer, which lets you change the default options.\n   *\n   * @param {Options} [passedOptions]\n   */\n  function humanizer(passedOptions) {\n    /**\n     * @param {number} ms\n     * @param {Options} [humanizerOptions]\n     * @returns {string}\n     */\n    var result = function humanizer(ms, humanizerOptions) {\n      // Make sure we have a positive number.\n      //\n      // Has the nice side-effect of converting things to numbers. For example,\n      // converts `\"123\"` and `Number(123)` to `123`.\n      ms = Math.abs(ms);\n      var options = assign({}, result, humanizerOptions || {});\n      var pieces = getPieces(ms, options);\n      return formatPieces(pieces, options);\n    };\n    return assign(result, {\n      language: \"en\",\n      spacer: \" \",\n      conjunction: \"\",\n      serialComma: true,\n      units: [\"y\", \"mo\", \"w\", \"d\", \"h\", \"m\", \"s\"],\n      languages: {},\n      round: false,\n      unitMeasures: {\n        y: 31557600000,\n        mo: 2629800000,\n        w: 604800000,\n        d: 86400000,\n        h: 3600000,\n        m: 60000,\n        s: 1000,\n        ms: 1\n      }\n    }, passedOptions);\n  }\n\n  /**\n   * Humanize a duration.\n   *\n   * This is a wrapper around the default humanizer.\n   */\n  var humanizeDuration = assign(humanizer({}), {\n    getSupportedLanguages: function getSupportedLanguages() {\n      var result = [];\n      for (var language in LANGUAGES) {\n        if (has(LANGUAGES, language) && language !== \"gr\") {\n          result.push(language);\n        }\n      }\n      return result;\n    },\n    humanizer: humanizer\n  });\n\n  // @ts-ignore\n  if (typeof define === \"function\" && define.amd) {\n    // @ts-ignore\n    define(function () {\n      return humanizeDuration;\n    });\n  } else if (typeof module !== \"undefined\" && module.exports) {\n    module.exports = humanizeDuration;\n  } else {\n    this.humanizeDuration = humanizeDuration;\n  }\n})();"], "mappings": ";;;;;AAAA;AAAA;AAqEA,KAAC,WAAY;AAEX,UAAI,SAAS,OAAO;AAAA,MACpB,SAAU,aAAa;AACrB,YAAI;AACJ,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,mBAAS,UAAU,CAAC;AACpB,mBAAS,QAAQ,QAAQ;AACvB,gBAAI,IAAI,QAAQ,IAAI,GAAG;AACrB,0BAAY,IAAI,IAAI,OAAO,IAAI;AAAA,YACjC;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAGA,UAAI,UAAU,MAAM,WAAW,SAAU,KAAK;AAC5C,eAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AAAA,MACjD;AAMA,UAAI,QAAQ,SAAS,SAAU,GAAG;AAChC,eAAO,MAAM,IAAI,WAAW;AAAA,MAC9B,GAAG,SAAU,GAAG;AACd,eAAO,MAAM,IAAI,UAAU;AAAA,MAC7B,GAAG,SAAU,GAAG;AACd,eAAO,MAAM,IAAI,aAAa;AAAA,MAChC,GAAG,SAAU,GAAG;AACd,eAAO,MAAM,IAAI,SAAS;AAAA,MAC5B,GAAG,SAAU,GAAG;AACd,eAAO,MAAM,IAAI,QAAQ;AAAA,MAC3B,GAAG,SAAU,GAAG;AACd,eAAO,MAAM,IAAI,UAAU;AAAA,MAC7B,GAAG,SAAU,GAAG;AACd,eAAO,MAAM,IAAI,iBAAiB;AAAA,MACpC,GAAG,SAAU,GAAG;AACd,gBAAQ,MAAM,IAAI,aAAa,cAAc;AAAA,MAC/C,GAAG,GAAG;AAMN,UAAI,YAAY;AAAA,QACd,IAAI,SAAS,QAAQ,SAAU,GAAG;AAChC,iBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,QACnC,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,SAAS;AAAA,QAC5B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,QAAQ;AAAA,QAC3B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,QAAQ;AAAA,QAC3B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,WAAW;AAAA,QAC9B,GAAG,SAAU,GAAG;AACd,iBAAO,aAAa,MAAM,IAAI,KAAK;AAAA,QACrC,GAAG,SAAU,GAAG;AACd,iBAAO,kBAAkB,MAAM,IAAI,KAAK;AAAA,QAC1C,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,OAAO,MAAM,QAAQ,MAAM,OAAO,OAAO,QAAQ,QAAQ;AAAA,QACtE,IAAI,OAAO,SAAS,SAAU,GAAG;AAC/B,iBAAO,CAAC,OAAO,SAAS,OAAO,EAAE,cAAc,CAAC,CAAC;AAAA,QACnD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,OAAO,SAAS,MAAM,EAAE,cAAc,CAAC,CAAC;AAAA,QAClD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,SAAS,WAAW,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,QACxD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,OAAO,SAAS,MAAM,EAAE,cAAc,CAAC,CAAC;AAAA,QAClD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,QAAQ,UAAU,OAAO,EAAE,cAAc,CAAC,CAAC;AAAA,QACrD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,SAAS,WAAW,OAAO,EAAE,cAAc,CAAC,CAAC;AAAA,QACvD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,SAAS,WAAW,OAAO,EAAE,cAAc,CAAC,CAAC;AAAA,QACvD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,kBAAkB,mBAAmB,kBAAkB,EAAE,cAAc,CAAC,CAAC;AAAA,QACnF,GAAG,GAAG,GAAG;AAAA,UACP,WAAW;AAAA,UACX,eAAe;AAAA,UACf,oBAAoB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,QACvE,CAAC;AAAA,QACD,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,CAAC,UAAU,UAAU,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,QACxD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,UAAU,SAAS,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,QACvD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,WAAW,WAAW,SAAS,EAAE,cAAc,CAAC,CAAC;AAAA,QAC3D,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,OAAO,OAAO,KAAK,EAAE,cAAc,CAAC,CAAC;AAAA,QAC/C,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,QAAQ,OAAO,MAAM,EAAE,cAAc,CAAC,CAAC;AAAA,QACjD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,UAAU,UAAU,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,QACxD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,WAAW,WAAW,SAAS,EAAE,cAAc,CAAC,CAAC;AAAA,QAC3D,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,eAAe,eAAe,aAAa,EAAE,cAAc,CAAC,CAAC;AAAA,QACvE,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,OAAO,OAAO,UAAU,OAAO,SAAS,SAAS,WAAW,aAAa;AAAA,QACtF,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,QACjC,GAAG,SAAU,GAAG;AACd,iBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,QACjC,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,MAAM,IAAI,MAAM;AAAA,QACrC,GAAG,SAAU,GAAG;AACd,iBAAO,QAAQ,MAAM,IAAI,MAAM;AAAA,QACjC,GAAG,SAAU,GAAG;AACd,iBAAO,SAAS,MAAM,IAAI,MAAM;AAAA,QAClC,GAAG,SAAU,GAAG;AACd,iBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,QACnC,GAAG,SAAU,GAAG;AACd,iBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,QACnC,GAAG,SAAU,GAAG;AACd,iBAAO,eAAe,MAAM,IAAI,KAAK;AAAA,QACvC,GAAG,GAAG;AAAA,QACN,KAAK,SAAS,OAAO,QAAQ,SAAS,OAAO,SAAS,SAAS,QAAQ,aAAa,GAAG;AAAA,QACvF,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,CAAC,OAAO,QAAQ,QAAQ,KAAK,EAAE,qBAAqB,CAAC,CAAC;AAAA,QAC/D,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,SAAS,UAAU,UAAU,QAAQ,EAAE,qBAAqB,CAAC,CAAC;AAAA,QACxE,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,SAAS,SAAS,SAAS,OAAO,EAAE,qBAAqB,CAAC,CAAC;AAAA,QACrE,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,OAAO,OAAO,OAAO,KAAK,EAAE,qBAAqB,CAAC,CAAC;AAAA,QAC7D,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,UAAU,UAAU,UAAU,OAAO,EAAE,qBAAqB,CAAC,CAAC;AAAA,QACxE,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,UAAU,UAAU,UAAU,OAAO,EAAE,qBAAqB,CAAC,CAAC;AAAA,QACxE,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,WAAW,WAAW,WAAW,QAAQ,EAAE,qBAAqB,CAAC,CAAC;AAAA,QAC5E,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,eAAe,eAAe,eAAe,YAAY,EAAE,qBAAqB,CAAC,CAAC;AAAA,QAC5F,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,YAAY,OAAO,WAAW,WAAW,OAAO,SAAS,UAAU,YAAY;AAAA,QAC5F,IAAI,SAAS,MAAM,SAAU,GAAG;AAC9B,iBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,QACnC,GAAG,SAAU,GAAG;AACd,iBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,QACjC,GAAG,SAAU,GAAG;AACd,iBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,QACjC,GAAG,SAAU,GAAG;AACd,iBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,QAClC,GAAG,SAAU,GAAG;AACd,iBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,QACnC,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,iBAAiB,MAAM,IAAI,KAAK;AAAA,QACzC,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,QAClC,GAAG,SAAU,GAAG;AACd,iBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,QACnC,GAAG,SAAU,GAAG;AACd,iBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,QACnC,GAAG,SAAU,GAAG;AACd,iBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,QACjC,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,aAAa,MAAM,IAAI,KAAK;AAAA,QACrC,GAAG,SAAU,GAAG;AACd,iBAAO,kBAAkB,MAAM,IAAI,KAAK;AAAA,QAC1C,GAAG,GAAG;AAAA,QACN,IAAI;AAAA,QACJ,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,QAClC,GAAG,SAAU,GAAG;AACd,iBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,QACnC,GAAG,SAAU,GAAG;AACd,iBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,QAClC,GAAG,SAAU,GAAG;AACd,iBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,QACjC,GAAG,SAAU,GAAG;AACd,iBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,QAClC,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,iBAAiB,MAAM,IAAI,KAAK;AAAA,QACzC,CAAC;AAAA,QACD,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,QAClC,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,aAAa,MAAM,IAAI,KAAK;AAAA,QACrC,GAAG,SAAU,GAAG;AACd,iBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,QAClC,GAAG,SAAU,GAAG;AACd,iBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,QAClC,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,aAAa,MAAM,IAAI,KAAK;AAAA,QACrC,GAAG,SAAU,GAAG;AACd,iBAAO,iBAAiB,MAAM,IAAI,KAAK;AAAA,QACzC,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,QACjC,GAAG,SAAU,GAAG;AACd,iBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,QACjC,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,QACjC,GAAG,SAAU,GAAG;AACd,iBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,QAClC,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,aAAa,MAAM,IAAI,KAAK;AAAA,QACrC,GAAG,SAAU,GAAG;AACd,iBAAO,iBAAiB,MAAM,IAAI,KAAK;AAAA,QACzC,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,QACnC,GAAG,SAAU,GAAG;AACd,iBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,QACjC,GAAG,SAAU,GAAG;AACd,iBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,QACnC,GAAG,SAAU,GAAG;AACd,iBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,QAClC,GAAG,SAAU,GAAG;AACd,iBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,QAClC,GAAG,SAAU,GAAG;AACd,iBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,QACnC,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,iBAAiB,MAAM,IAAI,KAAK;AAAA,QACzC,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,QAAQ,YAAY,QAAQ,QAAQ,QAAQ,UAAU,WAAW,eAAe,GAAG;AAAA,QAChG,IAAI,SAAS,OAAO,OAAO,QAAQ,OAAO,QAAQ,SAAS,SAAS,YAAY;AAAA,QAChF,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC7B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,aAAa;AAAA,QAChC,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,QACnC,GAAG,SAAU,GAAG;AACd,iBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,QACnC,GAAG,SAAU,GAAG;AACd,iBAAO,cAAc,MAAM,IAAI,KAAK;AAAA,QACtC,GAAG,SAAU,GAAG;AACd,iBAAO,aAAa,MAAM,IAAI,KAAK;AAAA,QACrC,GAAG,SAAU,GAAG;AACd,iBAAO,kBAAkB,MAAM,IAAI,KAAK;AAAA,QAC1C,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,MAAM,SAAU,GAAG;AAC9B,iBAAO,MAAM,IAAI,YAAY;AAAA,QAC/B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,SAAS;AAAA,QAC5B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC7B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,SAAS;AAAA,QAC5B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,aAAa;AAAA,QAChC,GAAG,UAAU,eAAe,GAAG;AAAA,QAC/B,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,QAAQ,KAAK,IAAI,MAAM;AAAA,QAChC,GAAG,QAAQ,SAAU,GAAG;AACtB,iBAAO,aAAa,KAAK,IAAI,MAAM;AAAA,QACrC,GAAG,SAAU,GAAG;AACd,iBAAO,UAAU,KAAK,IAAI,MAAM;AAAA,QAClC,GAAG,SAAU,GAAG;AACd,iBAAO,WAAW,KAAK,IAAI,MAAM;AAAA,QACnC,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,KAAK,IAAI,MAAM;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,aAAa,KAAK,IAAI,MAAM;AAAA,QACrC,GAAG,SAAU,GAAG;AACd,iBAAO,kBAAkB,KAAK,IAAI,MAAM;AAAA,QAC1C,GAAG,GAAG;AAAA,QACN,IAAI;AAAA,QACJ,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,MAAM,IAAI,QAAQ;AAAA,QAC3B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,SAAS;AAAA,QAC5B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,SAAS;AAAA,QAC5B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,QAAQ;AAAA,QAC3B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,QAAQ;AAAA,QAC3B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,QAAQ;AAAA,QAC3B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,SAAS;AAAA,QAC5B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,cAAc;AAAA,QACjC,CAAC;AAAA,QACD,IAAI,SAAS,SAAU,GAAG;AACxB,cAAI,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,GAAG;AAChD,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT,GAAG,SAAU,GAAG;AACd,cAAI,MAAM,GAAG;AACX,mBAAO;AAAA,UACT,WAAW,MAAM,KAAK,MAAM,KAAK,MAAM,GAAG;AACxC,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT,GAAG,SAAU,GAAG;AACd,cAAI,IAAI,OAAO,KAAK,MAAM,IAAI;AAC5B,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,QAAQ;AAAA,QAC3B,GAAG,SAAU,GAAG;AACd,cAAI,MAAM,GAAG;AACX,mBAAO;AAAA,UACT,WAAW,MAAM,KAAK,MAAM,KAAK,MAAM,GAAG;AACxC,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT,GAAG,SAAU,GAAG;AACd,cAAI,QAAQ,IAAI;AAChB,eAAK,UAAU,KAAK,UAAU,KAAK,UAAU,OAAO,IAAI,MAAM,IAAI,KAAK;AACrE,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT,GAAG,SAAU,GAAG;AACd,cAAI,QAAQ,IAAI;AAChB,cAAI,UAAU,KAAK,KAAK,MAAM,CAAC,MAAM,KAAK,KAAK,MAAM,KAAK,IAAI;AAC5D,mBAAO;AAAA,UACT,WAAW,UAAU,GAAG;AACtB,mBAAO;AAAA,UACT,WAAW,UAAU,KAAK,UAAU,KAAK,UAAU,GAAG;AACpD,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT,GAAG,SAAU,GAAG;AACd,cAAI,MAAM,GAAG;AACX,mBAAO;AAAA,UACT,WAAW,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,GAAG;AACvD,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,OAAO,SAAU,GAAG;AAC/B,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC7B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC7B,GAAG,OAAO,SAAU,GAAG;AACrB,iBAAO,MAAM,IAAI,SAAS;AAAA,QAC5B,GAAG,QAAQ,SAAS,WAAW;AAAA,QAC/B,IAAI,SAAS,MAAM,SAAS,OAAO,OAAO,OAAO,QAAQ,aAAa,kBAAkB,GAAG;AAAA,QAC3F,IAAI,SAAS,SAAS,SAAS,UAAU,QAAQ,OAAO,SAAS,SAAS,WAAW;AAAA,QACrF,IAAI,SAAS,MAAM,SAAU,GAAG;AAC9B,iBAAO,WAAW,MAAM,IAAI,OAAO;AAAA,QACrC,GAAG,SAAU,GAAG;AACd,iBAAO,SAAS,MAAM,IAAI,MAAM;AAAA,QAClC,GAAG,SAAU,GAAG;AACd,iBAAO,SAAS,MAAM,IAAI,OAAO;AAAA,QACnC,GAAG,SAAU,GAAG;AACd,iBAAO,eAAe,MAAM,IAAI,MAAM;AAAA,QACxC,GAAG,SAAU,GAAG;AACd,iBAAO,WAAW,MAAM,IAAI,MAAM;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,MAAM,IAAI,MAAM;AAAA,QACrC,GAAG,SAAU,GAAG;AACd,iBAAO,iBAAiB,MAAM,IAAI,MAAM;AAAA,QAC1C,CAAC;AAAA,QACD,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,SAAS,MAAM,IAAI,MAAM;AAAA,QAClC,GAAG,SAAU,GAAG;AACd,iBAAO,SAAS,MAAM,IAAI,MAAM;AAAA,QAClC,GAAG,SAAU,GAAG;AACd,iBAAO,cAAc,MAAM,IAAI,MAAM;AAAA,QACvC,GAAG,SAAU,GAAG;AACd,iBAAO,WAAW,MAAM,IAAI,MAAM;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,QAAQ,MAAM,IAAI,MAAM;AAAA,QACjC,GAAG,SAAU,GAAG;AACd,iBAAO,WAAW,MAAM,IAAI,MAAM;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,MAAM,IAAI,MAAM;AAAA,QACrC,GAAG,SAAU,GAAG;AACd,iBAAO,iBAAiB,MAAM,IAAI,MAAM;AAAA,QAC1C,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK;AAAA,QACvD,IAAI,SAAS,SAAS,MAAM,WAAW,QAAQ,QAAQ,QAAQ,UAAU,cAAc;AAAA,QACvF,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,MAAM,IAAI,SAAS;AAAA,QAC5B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,WAAW;AAAA,QAC9B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,QAAQ;AAAA,QAC3B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,QAAQ;AAAA,QAC3B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,SAAS;AAAA,QAC5B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC7B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,YAAY;AAAA,QAC/B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,gBAAgB;AAAA,QACnC,CAAC;AAAA,QACD,IAAI,SAAS,KAAK,MAAM,MAAM,KAAK,MAAM,KAAK,KAAK,MAAM;AAAA,QACzD,IAAI,SAAS,OAAO,OAAO,SAAS,OAAO,QAAQ,QAAQ,UAAU,YAAY,GAAG;AAAA,QACpF,IAAI,SAAS,MAAM,SAAS,SAAS,OAAO,WAAW,QAAQ,UAAU,eAAe,GAAG;AAAA,QAC3F,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,IAAI,OAAO,KAAK,IAAI,OAAO,MAAM,IAAI,OAAO,KAAK,SAAS;AAAA,QACnE,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,SAAS,YAAY,SAAS,EAAE,kBAAkB,CAAC,CAAC;AAAA,QAC9D,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,WAAW,YAAY,UAAU,EAAE,kBAAkB,CAAC,CAAC;AAAA,QACjE,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,SAAS,UAAU,OAAO,EAAE,kBAAkB,CAAC,CAAC;AAAA,QAC1D,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,WAAW,YAAY,SAAS,EAAE,kBAAkB,CAAC,CAAC;AAAA,QAChE,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,UAAU,WAAW,SAAS,EAAE,kBAAkB,CAAC,CAAC;AAAA,QAC9D,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,WAAW,YAAY,WAAW,EAAE,kBAAkB,CAAC,CAAC;AAAA,QAClE,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,eAAe,gBAAgB,eAAe,EAAE,kBAAkB,CAAC,CAAC;AAAA,QAC9E,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,eAAe,CAAC,IAAI,SAAS;AAAA,QACtC,GAAG,SAAU,GAAG;AACd,iBAAO,eAAe,CAAC,IAAI,YAAY;AAAA,QACzC,GAAG,SAAU,GAAG;AACd,iBAAO,eAAe,CAAC,IAAI,WAAW;AAAA,QACxC,GAAG,SAAU,GAAG;AACd,iBAAO,eAAe,CAAC,IAAI,UAAU;AAAA,QACvC,GAAG,SAAU,GAAG;AACd,iBAAO,eAAe,CAAC,IAAI,WAAW;AAAA,QACxC,GAAG,SAAU,GAAG;AACd,iBAAO,eAAe,CAAC,IAAI,WAAW;AAAA,QACxC,GAAG,SAAU,GAAG;AACd,iBAAO,eAAe,CAAC,IAAI,YAAY;AAAA,QACzC,GAAG,SAAU,GAAG;AACd,iBAAO,eAAe,CAAC,IAAI,gBAAgB;AAAA,QAC7C,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,MAAM,IAAI,WAAW;AAAA,QAC9B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC7B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,WAAW;AAAA,QAC9B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,QAAQ;AAAA,QAC3B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,QAAQ;AAAA,QAC3B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,WAAW;AAAA,QAC9B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,YAAY;AAAA,QAC/B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,gBAAgB;AAAA,QACnC,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,OAAO,OAAO,eAAe,QAAQ,OAAO,SAAS,UAAU,aAAa;AAAA,QACzF,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,MAAM,IAAI,SAAS;AAAA,QAC5B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC7B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC7B,GAAG,QAAQ,OAAO,SAAU,GAAG;AAC7B,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC7B,GAAG,SAAS,WAAW;AAAA,QACvB,IAAI,SAAS,SAAS,SAAS,UAAU,QAAQ,OAAO,SAAS,QAAQ,UAAU;AAAA,QACnF,IAAI,SAAS,QAAQ,SAAU,GAAG;AAChC,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC7B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,SAAS;AAAA,QAC5B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,QAAQ;AAAA,QAC3B,GAAG,OAAO,SAAU,GAAG;AACrB,iBAAO,MAAM,IAAI,WAAW;AAAA,QAC9B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,YAAY;AAAA,QAC/B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,iBAAiB;AAAA,QACpC,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,MAAM,SAAU,GAAG;AAC9B,iBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,QACnC,GAAG,SAAU,GAAG;AACd,iBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,QACjC,GAAG,SAAU,GAAG;AACd,iBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,QACjC,GAAG,SAAU,GAAG;AACd,iBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,QAClC,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,iBAAiB,MAAM,IAAI,KAAK;AAAA,QACzC,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,CAAC,OAAO,QAAQ,QAAQ,KAAK,EAAE,cAAc,CAAC,CAAC;AAAA,QACxD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,WAAW,YAAY,YAAY,UAAU,EAAE,cAAc,CAAC,CAAC;AAAA,QACzE,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,WAAW,YAAY,YAAY,SAAS,EAAE,cAAc,CAAC,CAAC;AAAA,QACxE,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,SAAS,QAAQ,OAAO,KAAK,EAAE,cAAc,CAAC,CAAC;AAAA,QACzD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,WAAW,WAAW,WAAW,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,QACrE,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,UAAU,UAAU,UAAU,OAAO,EAAE,cAAc,CAAC,CAAC;AAAA,QACjE,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,WAAW,WAAW,WAAW,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,QACrE,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,eAAe,eAAe,eAAe,YAAY,EAAE,cAAc,CAAC,CAAC;AAAA,QACrF,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,QACjC,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,QAAQ;AAAA,QAC3B,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,QACjC,GAAG,SAAU,GAAG;AACd,iBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,QAClC,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,aAAa,MAAM,IAAI,KAAK;AAAA,QACrC,GAAG,SAAU,GAAG;AACd,iBAAO,kBAAkB,MAAM,IAAI,KAAK;AAAA,QAC1C,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,MAAM,IAAI,OAAO;AAAA,QAC1B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,SAAS;AAAA,QAC5B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,cAAc;AAAA,QACjC,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,OAAO;AAAA,QAC1B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,QAAQ;AAAA,QAC3B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC7B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,YAAY;AAAA,QAC/B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,gBAAgB;AAAA,QACnC,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,CAAC,OAAO,OAAO,MAAM,EAAE,cAAc,CAAC,CAAC;AAAA,QAChD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,WAAW,SAAS,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,QACxD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,UAAU,UAAU,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,QACxD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,QAAQ,QAAQ,KAAK,EAAE,cAAc,CAAC,CAAC;AAAA,QACjD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,SAAS,OAAO,MAAM,EAAE,cAAc,CAAC,CAAC;AAAA,QAClD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,SAAS,UAAU,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,QACvD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,UAAU,WAAW,SAAS,EAAE,cAAc,CAAC,CAAC;AAAA,QAC1D,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,eAAe,gBAAgB,cAAc,EAAE,cAAc,CAAC,CAAC;AAAA,QACzE,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,MAAM,IAAI,QAAQ;AAAA,QAC3B,GAAG,QAAQ,QAAQ,QAAQ,OAAO,SAAU,GAAG;AAC7C,iBAAO,WAAW,MAAM,IAAI,MAAM;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,MAAM,IAAI,MAAM;AAAA,QACrC,GAAG,SAAU,GAAG;AACd,iBAAO,gBAAgB,MAAM,IAAI,MAAM;AAAA,QACzC,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,CAAC,UAAU,UAAU,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,QACxD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,UAAU,SAAS,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,QACvD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,UAAU,UAAU,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,QACxD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,QAAQ,OAAO,MAAM,EAAE,cAAc,CAAC,CAAC;AAAA,QACjD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,QAAQ,OAAO,MAAM,EAAE,cAAc,CAAC,CAAC;AAAA,QACjD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,UAAU,SAAS,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,QACvD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,WAAW,WAAW,SAAS,EAAE,cAAc,CAAC,CAAC;AAAA,QAC3D,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,eAAe,eAAe,aAAa,EAAE,cAAc,CAAC,CAAC;AAAA,QACvE,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,MAAM,IAAI,WAAW;AAAA,QAC9B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC7B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC7B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,SAAS;AAAA,QAC5B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,QAAQ;AAAA,QAC3B,GAAG,SAAU,GAAG;AACd,iBAAO,WAAW,MAAM,IAAI,OAAO;AAAA,QACrC,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,mBAAmB,MAAM,IAAI,KAAK;AAAA,QAC3C,CAAC;AAAA,QACD,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,YAAY,MAAM,IAAI,OAAO;AAAA,QACtC,GAAG,SAAU,GAAG;AACd,iBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,QACjC,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,SAAS;AAAA,QAC5B,GAAG,SAAU,GAAG;AACd,iBAAO,UAAU,MAAM,IAAI,KAAK;AAAA,QAClC,GAAG,SAAU,GAAG;AACd,iBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,QACjC,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,WAAW;AAAA,QAC9B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC7B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,gBAAgB;AAAA,QACnC,CAAC;AAAA,QACD,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,CAAC,SAAS,OAAO,MAAM,EAAE,cAAc,CAAC,CAAC;AAAA,QAClD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,WAAW,UAAU,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,QACzD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,UAAU,WAAW,OAAO,EAAE,cAAc,CAAC,CAAC;AAAA,QACxD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,QAAQ,QAAQ,KAAK,EAAE,cAAc,CAAC,CAAC;AAAA,QACjD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,SAAS,UAAU,QAAQ,EAAE,cAAc,CAAC,CAAC;AAAA,QACvD,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,UAAU,WAAW,SAAS,EAAE,cAAc,CAAC,CAAC;AAAA,QAC1D,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,UAAU,WAAW,SAAS,EAAE,cAAc,CAAC,CAAC;AAAA,QAC1D,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,cAAc,eAAe,aAAa,EAAE,cAAc,CAAC,CAAC;AAAA,QACtE,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,OAAO,SAAU,GAAG;AAC/B,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC7B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,SAAS;AAAA,QAC5B,GAAG,MAAM,SAAU,GAAG;AACpB,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC7B,GAAG,OAAO,SAAS,WAAW;AAAA,QAC9B,IAAI,SAAS,SAAU,GAAG;AACxB,iBAAO,CAAC,OAAO,QAAQ,QAAQ,OAAO,EAAE,qBAAqB,CAAC,CAAC;AAAA,QACjE,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,UAAU,WAAW,WAAW,UAAU,EAAE,qBAAqB,CAAC,CAAC;AAAA,QAC7E,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,UAAU,UAAU,UAAU,SAAS,EAAE,qBAAqB,CAAC,CAAC;AAAA,QAC1E,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,OAAO,OAAO,OAAO,KAAK,EAAE,qBAAqB,CAAC,CAAC;AAAA,QAC7D,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,UAAU,UAAU,UAAU,OAAO,EAAE,qBAAqB,CAAC,CAAC;AAAA,QACxE,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,UAAU,UAAU,UAAU,OAAO,EAAE,qBAAqB,CAAC,CAAC;AAAA,QACxE,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,WAAW,WAAW,WAAW,QAAQ,EAAE,qBAAqB,CAAC,CAAC;AAAA,QAC5E,GAAG,SAAU,GAAG;AACd,iBAAO,CAAC,eAAe,eAAe,eAAe,YAAY,EAAE,qBAAqB,CAAC,CAAC;AAAA,QAC5F,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,SAAU,GAAG;AACxB,cAAI,IAAI,OAAO,GAAG;AAChB,mBAAO;AAAA,UACT,WAAW,IAAI,QAAQ,GAAG;AACxB,mBAAO;AAAA,UACT,WAAW,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,KAAK,IAAI,OAAO,GAAG;AAChF,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF,GAAG,SAAU,GAAG;AACd,cAAI,IAAI,OAAO,GAAG;AAChB,mBAAO;AAAA,UACT,WAAW,IAAI,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,KAAK,IAAI,OAAO,GAAG;AAC/D,mBAAO;AAAA,UACT,WAAW,IAAI,OAAO,KAAK,IAAI,OAAO,GAAG;AACvC,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF,GAAG,SAAU,GAAG;AACd,cAAI,IAAI,OAAO,GAAG;AAChB,mBAAO;AAAA,UACT,WAAW,IAAI,OAAO,KAAK,KAAK,MAAM,CAAC,MAAM,KAAK,IAAI,OAAO,GAAG;AAC9D,mBAAO;AAAA,UACT,WAAW,IAAI,OAAO,KAAK,IAAI,OAAO,GAAG;AACvC,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF,GAAG,SAAU,GAAG;AACd,iBAAO,IAAI,QAAQ,IAAI,QAAQ;AAAA,QACjC,GAAG,SAAU,GAAG;AACd,cAAI,IAAI,OAAO,GAAG;AAChB,mBAAO;AAAA,UACT,WAAW,IAAI,QAAQ,GAAG;AACxB,mBAAO;AAAA,UACT,WAAW,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,KAAK,MAAM,CAAC,MAAM,GAAG;AAC9D,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF,GAAG,SAAU,GAAG;AACd,cAAI,IAAI,OAAO,GAAG;AAChB,mBAAO;AAAA,UACT,WAAW,IAAI,OAAO,GAAG;AACvB,mBAAO;AAAA,UACT,WAAW,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,KAAK,MAAM,CAAC,MAAM,KAAK,IAAI,OAAO,GAAG;AAC9E,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF,GAAG,SAAU,GAAG;AACd,cAAI,IAAI,OAAO,GAAG;AAChB,mBAAO;AAAA,UACT,WAAW,IAAI,QAAQ,GAAG;AACxB,mBAAO;AAAA,UACT,WAAW,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,GAAG;AAChE,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF,GAAG,SAAU,GAAG;AACd,cAAI,IAAI,OAAO,GAAG;AAChB,mBAAO;AAAA,UACT,WAAW,IAAI,QAAQ,GAAG;AACxB,mBAAO;AAAA,UACT,WAAW,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,GAAG;AAChE,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF,GAAG,GAAG;AAAA,QACN,IAAI,SAAS,MAAM,SAAU,GAAG;AAC9B,iBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,QACnC,GAAG,SAAU,GAAG;AACd,iBAAO,UAAU,MAAM,IAAI,MAAM;AAAA,QACnC,GAAG,SAAU,GAAG;AACd,iBAAO,SAAS,MAAM,IAAI,KAAK;AAAA,QACjC,GAAG,SAAU,GAAG;AACd,iBAAO,UAAU,MAAM,IAAI,MAAM;AAAA,QACnC,GAAG,SAAU,GAAG;AACd,iBAAO,WAAW,MAAM,IAAI,KAAK;AAAA,QACnC,GAAG,SAAU,GAAG;AACd,iBAAO,YAAY,MAAM,IAAI,KAAK;AAAA,QACpC,GAAG,SAAU,GAAG;AACd,iBAAO,iBAAiB,MAAM,IAAI,KAAK;AAAA,QACzC,GAAG,GAAG;AAAA,QACN,IAAI,OAAO,SAAS,SAAU,GAAG;AAC/B,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC7B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC7B,GAAG,QAAQ,SAAU,GAAG;AACtB,iBAAO,MAAM,IAAI,SAAS;AAAA,QAC5B,GAAG,SAAU,GAAG;AACd,iBAAO,MAAM,IAAI,QAAQ;AAAA,QAC3B,GAAG,UAAU,WAAW,aAAa,GAAG;AAAA,UACtC,cAAc;AAAA,QAChB,CAAC;AAAA,QACD,IAAI,SAAS,OAAO,MAAM,SAAS,OAAO,QAAQ,UAAU,UAAU,cAAc,GAAG;AAAA,QACvF,IAAI,SAAS,MAAM,SAAS,WAAW,OAAO,WAAW,QAAQ,UAAU,aAAa;AAAA,QACxF,IAAI,SAAS,OAAO,MAAM,SAAS,OAAO,QAAQ,SAAS,UAAU,aAAa;AAAA,QAClF,QAAQ,SAAS,OAAO,MAAM,SAAS,OAAO,QAAQ,SAAS,UAAU,aAAa;AAAA,QACtF,IAAI,SAAS,OAAO,SAAS,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,aAAa,GAAG;AAAA,QACpF,OAAO,SAAS,KAAK,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,IAAI;AAAA,QAC1D,OAAO,SAAS,KAAK,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,IAAI;AAAA,MAC5D;AAiBA,eAAS,SAAS,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,SAAS;AAEnD,YAAI,SAAS;AAAA,UACX;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,YAAI,OAAO,YAAY,aAAa;AAClC,iBAAO,UAAU;AAAA,QACnB;AACA,eAAO;AAAA,MACT;AASA,eAAS,cAAc,GAAG;AACxB,YAAI,MAAM,GAAG;AACX,iBAAO;AAAA,QACT;AACA,YAAI,IAAI,KAAK,IAAI,IAAI;AACnB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AASA,eAAS,cAAc,GAAG;AACxB,YAAI,MAAM,GAAG;AACX,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,MAAM,CAAC,MAAM,GAAG;AACvB,iBAAO;AAAA,QACT;AACA,YAAI,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,EAAE,IAAI,MAAM,MAAM,IAAI,MAAM,KAAK;AACjE,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AASA,eAAS,cAAc,GAAG;AACxB,YAAI,KAAK,MAAM,CAAC,MAAM,GAAG;AACvB,iBAAO;AAAA,QACT;AACA,YAAI,IAAI,OAAO,KAAK,IAAI,OAAO,MAAM,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,OAAO,GAAG;AAC/E,iBAAO;AAAA,QACT;AACA,YAAI,IAAI,OAAO,GAAG;AAChB,iBAAO;AAAA,QACT;AACA,YAAI,IAAI,GAAG;AACT,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AASA,eAAS,qBAAqB,GAAG;AAC/B,YAAI,MAAM,GAAG;AACX,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,MAAM,CAAC,MAAM,GAAG;AACvB,iBAAO;AAAA,QACT;AACA,YAAI,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,IAAI;AAC9C,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AASA,eAAS,kBAAkB,GAAG;AAC5B,YAAI,MAAM,KAAK,IAAI,OAAO,KAAK,IAAI,MAAM,IAAI;AAC3C,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,MAAM,CAAC,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,MAAM,IAAI,MAAM,KAAK,IAAI,MAAM,IAAI;AACrF,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AASA,eAAS,eAAe,GAAG;AACzB,eAAO,IAAI,OAAO,KAAK,IAAI,QAAQ;AAAA,MACrC;AASA,eAAS,IAAI,KAAK,KAAK;AACrB,eAAO,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG;AAAA,MACtD;AAQA,eAAS,YAAY,SAAS;AAC5B,YAAI,oBAAoB,CAAC,QAAQ,QAAQ;AACzC,YAAI,IAAI,SAAS,WAAW,GAAG;AAC7B,cAAI,QAAQ,QAAQ,SAAS,KAAK,QAAQ,UAAU,QAAQ;AAC1D,gCAAoB,kBAAkB,OAAO,QAAQ,SAAS;AAAA,UAChE,OAAO;AACL,kBAAM,IAAI,MAAM,sDAAsD;AAAA,UACxE;AAAA,QACF;AACA,iBAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AACjD,cAAI,gBAAgB,kBAAkB,CAAC;AACvC,cAAI,IAAI,QAAQ,WAAW,aAAa,GAAG;AACzC,mBAAO,QAAQ,UAAU,aAAa;AAAA,UACxC;AACA,cAAI,IAAI,WAAW,aAAa,GAAG;AACjC,mBAAO,UAAU,aAAa;AAAA,UAChC;AAAA,QACF;AACA,cAAM,IAAI,MAAM,oBAAoB;AAAA,MACtC;AAQA,eAAS,YAAY,OAAOA,WAAU,SAAS;AAC7C,YAAI,WAAW,MAAM;AACrB,YAAI,YAAY,MAAM;AACtB,YAAI,SAAS,QAAQ;AACrB,YAAI,mBAAmB,QAAQ;AAG/B,YAAI;AACJ,YAAI,IAAI,SAAS,SAAS,GAAG;AAC3B,oBAAU,QAAQ;AAAA,QACpB,WAAW,IAAIA,WAAU,SAAS,GAAG;AACnC,oBAAUA,UAAS;AAAA,QACrB,OAAO;AACL,oBAAU;AAAA,QACZ;AAGA,YAAI;AACJ,YAAI,uBAAuB,SAAS;AAClC,8BAAoB,QAAQ;AAAA,QAC9B,WAAW,wBAAwBA,WAAU;AAC3C,8BAAoBA,UAAS;AAAA,QAC/B;AAGA,YAAI;AACJ,YAAI,sBAAsB,qBAAqB,SAAS,YAAY,KAAK,MAAM,YAAY,KAAK,IAAI,IAAI,gBAAgB,CAAC,IAAI,KAAK,IAAI,IAAI,gBAAgB;AAC1J,YAAI,WAAW,oBAAoB,SAAS;AAC5C,YAAIA,UAAS,iBAAiB,cAAc,GAAG;AAC7C,2BAAiB;AACjB,mBAAS;AAAA,QACX,OAAO;AACL,cAAI,mBAAmB;AACrB,6BAAiB;AACjB,qBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,kBAAI,OAAO,SAAS,CAAC;AACrB,kBAAI,SAAS,KAAK;AAChB,kCAAkB;AAAA,cACpB,OAAO;AAEL,kCAAkB,kBAAkB,IAAI;AAAA,cAC1C;AAAA,YACF;AAAA,UACF,OAAO;AACL,6BAAiB,SAAS,QAAQ,KAAK,OAAO;AAAA,UAChD;AAAA,QACF;AACA,YAAI,eAAeA,UAAS,QAAQ;AACpC,YAAI;AACJ,YAAI,OAAO,iBAAiB,YAAY;AACtC,iBAAO,aAAa,SAAS;AAAA,QAC/B,OAAO;AACL,iBAAO;AAAA,QACT;AACA,YAAIA,UAAS,cAAc;AACzB,iBAAO,OAAO,SAAS;AAAA,QACzB;AACA,eAAO,iBAAiB,SAAS;AAAA,MACnC;AAeA,eAAS,UAAU,IAAI,SAAS;AAE9B,YAAI;AAGJ,YAAI;AAGJ,YAAI;AAGJ,YAAI;AACJ,YAAI,QAAQ,QAAQ;AACpB,YAAI,eAAe,QAAQ;AAC3B,YAAI,UAAU,aAAa,UAAU,QAAQ,UAAU;AACvD,YAAI,CAAC,MAAM,OAAQ,QAAO,CAAC;AAK3B,YAAI,aAAa,CAAC;AAClB,sBAAc;AACd,aAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACjC,qBAAW,MAAM,CAAC;AAClB,cAAI,SAAS,aAAa,QAAQ;AAClC,cAAI,SAAS,MAAM,MAAM,SAAS;AAClC,sBAAY,SAAS,cAAc,SAAS,KAAK,MAAM,cAAc,MAAM;AAC3E,qBAAW,QAAQ,IAAI;AACvB,yBAAe,YAAY;AAAA,QAC7B;AACA,YAAI,QAAQ,OAAO;AAIjB,cAAI,4BAA4B;AAChC,eAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACjC,uBAAW,MAAM,CAAC;AAClB,wBAAY,WAAW,QAAQ;AAC/B,gBAAI,cAAc,EAAG;AACrB;AAGA,gBAAI,8BAA8B,GAAG;AACnC,uBAAS,IAAI,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACzC,oBAAI,kBAAkB,MAAM,CAAC;AAC7B,oBAAI,mBAAmB,WAAW,eAAe;AACjD,2BAAW,QAAQ,KAAK,mBAAmB,aAAa,eAAe,IAAI,aAAa,QAAQ;AAChG,2BAAW,eAAe,IAAI;AAAA,cAChC;AACA;AAAA,YACF;AAAA,UACF;AAQA,eAAK,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AACtC,uBAAW,MAAM,CAAC;AAClB,wBAAY,WAAW,QAAQ;AAC/B,gBAAI,cAAc,EAAG;AACrB,gBAAI,UAAU,KAAK,MAAM,SAAS;AAClC,uBAAW,QAAQ,IAAI;AACvB,gBAAI,MAAM,EAAG;AACb,gBAAI,mBAAmB,MAAM,IAAI,CAAC;AAClC,gBAAI,iBAAiB,aAAa,gBAAgB;AAClD,gBAAI,uBAAuB,KAAK,MAAM,UAAU,aAAa,QAAQ,IAAI,cAAc;AACvF,gBAAI,sBAAsB;AACxB,yBAAW,gBAAgB,KAAK;AAChC,yBAAW,QAAQ,IAAI;AAAA,YACzB,OAAO;AACL;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAGA,YAAI,SAAS,CAAC;AACd,aAAK,IAAI,GAAG,IAAI,MAAM,UAAU,OAAO,SAAS,SAAS,KAAK;AAC5D,qBAAW,MAAM,CAAC;AAClB,sBAAY,WAAW,QAAQ;AAC/B,cAAI,WAAW;AACb,mBAAO,KAAK;AAAA,cACV;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAQA,eAAS,aAAa,QAAQ,SAAS;AACrC,YAAIA,YAAW,YAAY,OAAO;AAClC,YAAI,CAAC,OAAO,QAAQ;AAClB,cAAI,QAAQ,QAAQ;AACpB,cAAI,mBAAmB,MAAM,MAAM,SAAS,CAAC;AAC7C,iBAAO,YAAY;AAAA,YACjB,UAAU;AAAA,YACV,WAAW;AAAA,UACb,GAAGA,WAAU,OAAO;AAAA,QACtB;AACA,YAAI,cAAc,QAAQ;AAC1B,YAAI,cAAc,QAAQ;AAC1B,YAAI;AACJ,YAAI,IAAI,SAAS,WAAW,GAAG;AAC7B,sBAAY,QAAQ;AAAA,QACtB,WAAW,IAAIA,WAAU,WAAW,GAAG;AACrC,sBAAYA,UAAS;AAAA,QACvB,OAAO;AACL,sBAAY;AAAA,QACd;AAGA,YAAI,iBAAiB,CAAC;AACtB,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,yBAAe,KAAK,YAAY,OAAO,CAAC,GAAGA,WAAU,OAAO,CAAC;AAAA,QAC/D;AACA,YAAI,CAAC,eAAe,OAAO,WAAW,GAAG;AACvC,iBAAO,eAAe,KAAK,SAAS;AAAA,QACtC;AACA,YAAI,OAAO,WAAW,GAAG;AACvB,iBAAO,eAAe,KAAK,WAAW;AAAA,QACxC;AACA,eAAO,eAAe,MAAM,GAAG,EAAE,EAAE,KAAK,SAAS,KAAK,cAAc,MAAM,MAAM,cAAc,eAAe,MAAM,EAAE;AAAA,MACvH;AAOA,eAAS,UAAU,eAAe;AAMhC,YAAI,SAAS,SAASC,WAAU,IAAI,kBAAkB;AAKpD,eAAK,KAAK,IAAI,EAAE;AAChB,cAAI,UAAU,OAAO,CAAC,GAAG,QAAQ,oBAAoB,CAAC,CAAC;AACvD,cAAI,SAAS,UAAU,IAAI,OAAO;AAClC,iBAAO,aAAa,QAAQ,OAAO;AAAA,QACrC;AACA,eAAO,OAAO,QAAQ;AAAA,UACpB,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,aAAa;AAAA,UACb,OAAO,CAAC,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,UAC1C,WAAW,CAAC;AAAA,UACZ,OAAO;AAAA,UACP,cAAc;AAAA,YACZ,GAAG;AAAA,YACH,IAAI;AAAA,YACJ,GAAG;AAAA,YACH,GAAG;AAAA,YACH,GAAG;AAAA,YACH,GAAG;AAAA,YACH,GAAG;AAAA,YACH,IAAI;AAAA,UACN;AAAA,QACF,GAAG,aAAa;AAAA,MAClB;AAOA,UAAI,mBAAmB,OAAO,UAAU,CAAC,CAAC,GAAG;AAAA,QAC3C,uBAAuB,SAAS,wBAAwB;AACtD,cAAI,SAAS,CAAC;AACd,mBAASD,aAAY,WAAW;AAC9B,gBAAI,IAAI,WAAWA,SAAQ,KAAKA,cAAa,MAAM;AACjD,qBAAO,KAAKA,SAAQ;AAAA,YACtB;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAAA,QACA;AAAA,MACF,CAAC;AAGD,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAE9C,eAAO,WAAY;AACjB,iBAAO;AAAA,QACT,CAAC;AAAA,MACH,WAAW,OAAO,WAAW,eAAe,OAAO,SAAS;AAC1D,eAAO,UAAU;AAAA,MACnB,OAAO;AACL,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF,GAAG;AAAA;AAAA;", "names": ["language", "humanizer"]}