import { arrayMap, fields, model, takeOptions } from '@ttwr-framework/ngx-main-visuals';
import { AttachmentTypeService, EntityType } from '@proxy/attachment-types';
import { inject } from '@angular/core';

export const attachmentTypes = () => {
  const attachmentType = inject(AttachmentTypeService);

  return model({
    id: fields.text(),
    name: fields.text(),
    entityType: fields.select('single', takeOptions(EntityType)),
    allowedSize: fields.number(),
    allowedExtensions: fields.selectFetch('multiple', () => attachmentType.getAvailableExtension().pipe(
      arrayMap(ext => ({
        label: ext,
        value: ext,
      }))
    )),
  })
}
