import { Component, DestroyRef, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  MatDialogContent,
  MatDialogRef,
  MatDialogTitle,
} from '@angular/material/dialog';
import {
  AlertService,
  LanguagePipe,
  LOADING,
  requiredValidator,
  TtwrFormComponent,
  arrayMap
} from '@ttwr-framework/ngx-main-visuals';
import { finalize, map } from 'rxjs';
import { EmployeeCustodyService } from '@proxy/hr/employee-custodies';
import { custody } from '../custody.model';
import { clearDate, requireAllOperator } from '@shared';
import { CustomSelectSearchComponent } from '@shared/components/custom-select-search.component';
import { EmployeeService } from '@proxy/hr/employees';

@Component({
  selector: 'app-custody-create-dialog',
  standalone: true,
  imports: [TtwrFormComponent, MatDialogTitle, MatDialogContent, LanguagePipe],
  template: `
    <h1 mat-dialog-title>
      {{ '::EmployeeCustodyManagement:AddCustody' | i18n }}
    </h1>
    <mat-dialog-content>
      <ttwr-form [config]="config" />
    </mat-dialog-content>
  `,
  styles: `
   :host {
     --ttwr-form-gap: 1rem;
   }
 `,
})
export class CustodyCreateDialogComponent {
  private custody = inject(EmployeeCustodyService);
  private alert = inject(AlertService);
  private dialogRef = inject(MatDialogRef);
  private destroyRef = inject(DestroyRef);
  private loading = inject(LOADING);
  private employees = inject(EmployeeService);

  protected config = custody
    .exclude({
      releaseDate: true,
      releaseReason: true,
    })
    .form({
      submitAction: {
        onSubmit: (body) => {
          this.loading.set(true);

          this.custody
            .postCreateEmployeeCustodyByDto({
              ...body,
              receiptDate: clearDate(body.receiptDate),
            })
            .pipe(
              finalize(() => this.loading.set(false)),
              takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(() => {
              this.alert.success('::CreatedSuccessfully');
              this.dialogRef.close(true);
            });
        },
      },
      fields: {
        employeeName: {
          hiddenSignal: signal(true),
        },
        employeeId: {
          label: '::GoldenOwl:Employee',
          validators: [requiredValidator],
          customInputComponent: CustomSelectSearchComponent,
          customInputComponentExtraInputs: {
            dataFunc: (searchTerm: string) =>
              this.employees
                .getNames({  skipCount: 0, maxResultCount: 20 }, searchTerm)
                .pipe(
                  requireAllOperator(),
                  map((res) => res.items),
                  arrayMap((employee) => ({
                    label: employee.fullName,
                    value: employee.id,
                  }))
                ),
          },
        },
        receiptDate: {
          label: '::EmployeeCustody:ReceiptDate',
          validators: [requiredValidator],
        },
        description: {
          label: '::EmployeeCustody:Description',
        },
      },
    });
}
