import { Component, DestroyRef, inject, signal } from '@angular/core';
import {
  AlertService,
  LanguagePipe,
  LOADING,
  requiredValidator,
  TtwrFormComponent
} from '@ttwr-framework/ngx-main-visuals';
import { MatCard, MatCardContent } from '@angular/material/card';
import { attendanceRequests } from '../../attendance-requests.model';
import { MatExpansionPanel, MatExpansionPanelHeader, MatExpansionPanelTitle } from '@angular/material/expansion';
import { AttendanceRequestService } from '@proxy/self-service/attendance-requests';
import {
  AttendanceRequestsAdvancedOptionsComponent
} from './attendance-requests-advanced-options/attendance-requests-advanced-options.component';
import { NonNullableFormBuilder } from '@angular/forms';
import { finalize } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-attendance-requests-create',
  standalone: true,
  templateUrl: './attendance-requests-create.component.html',
  styleUrl: './attendance-requests-create.component.scss',
  imports: [
    LanguagePipe,
    MatCard,
    MatCardContent,
    TtwrFormComponent,
    MatExpansionPanel,
    MatExpansionPanelHeader,
    MatExpansionPanelTitle,
    AttendanceRequestsAdvancedOptionsComponent,
  ],
})
export class AttendanceRequestsCreateComponent {
  private attendanceRequest = inject(AttendanceRequestService);
  private fb = inject(NonNullableFormBuilder);
  private destroyRef = inject(DestroyRef);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private alert = inject(AlertService);
  private loading = inject(LOADING);

  public modeControl = this.fb.control<Mode>('remove-all');
  public fixAffectedDays = signal('');

  public config = attendanceRequests.exclude({
    creationTime: true,
    state: true,
  }).form({
    submitAction: {
      onSubmit: (body) => {
        const currentMode = this.modeControl.value;

        const data = currentMode === 'remove-all' ? {
          checkIn: body.checkIn,
          checkOut: body.checkOut,
          fixAttendanceFrom: this.setIsoTimeToStart(body.checkIn),
          fixAttendanceTo: this.setIsoTimeToEnd(body.checkOut),
        } : currentMode === 'remove-overlapping-only' ? {
          checkIn: body.checkIn,
          checkOut: body.checkOut,
          fixAttendanceFrom: body.checkIn,
          fixAttendanceTo: body.checkOut,
        } : {};

        this.loading.set(true);
        this.attendanceRequest.createSelfRequest(data)
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::CreatedSuccessfully');
            this.router.navigate(['.'], { relativeTo: this.route.parent });
          });
      },
    },
    fields: {
      checkIn: {
        label: '::GoldenOwl:CheckIn',
        twelveHour: true,
        validators: [
          requiredValidator,
          {
            name: 'invalidCheckIn',
            message: '::InvalidCheckIn',
            validator: control => {
              if (!control.value) return null;

              const checkOutValue = (control.parent?.controls as any)['checkOut'].value;

              if (!checkOutValue) return null;

              if (checkOutValue <= control.value) return {
                invalidCheckIn: true,
              };

              return null;
            },
          }
        ],
        onChange: (value, _, formGroup) => {
          if (!value || !formGroup.controls.checkOut.value) return;

          this.calculateAffectedDays(value, formGroup.controls.checkOut.value);
        }
      },
      checkOut: {
        label: '::GoldenOwl:CheckOut',
        twelveHour: true,
        validators: [
          requiredValidator,
          {
            name: 'invalidCheckOut',
            message: '::InvalidCheckOut',
            validator: control => {
              if (!control.value) return null;

              const checkInValue = (control.parent?.controls as any)['checkIn'].value;

              if (!checkInValue) return null;

              if (checkInValue >= control.value) return {
                invalidCheckOut: true,
              };

              return null;
            },
          }
        ],
        onChange: (value, _, formGroup) => {
          if (!formGroup.controls.checkIn.value || !value) return;

          this.calculateAffectedDays(formGroup.controls.checkIn.value, value);
        }
      },
      attendanceId: {
        disabledSignal: signal(true),
      },
    },
  });

  calculateAffectedDays(from: Date, to: Date) {
    // hours * minutes * seconds * milliseconds
    const oneDay = 24 * 60 * 60 * 1000;

    const diffDays = Math.round(Math.abs((from.getTime() - to.getTime()) / oneDay));

    this.fixAffectedDays.set((diffDays + 1).toString());
  }

  setIsoTimeToStart(iso: string): string {
    return iso.split('T')[0] + 'T' + '00:00:00';
  }

  setIsoTimeToEnd(iso: string): string {
    return iso.split('T')[0] + 'T' + '23:59:59';
  }
}

type Mode = 'remove-all' | 'remove-overlapping-only' | 'fix';
