import { Component, inject } from '@angular/core';
import { <PERSON><PERSON><PERSON>, MatCardContent } from '@angular/material/card';
import { LanguagePipe, LOADING } from '@ttwr-framework/ngx-main-visuals';
import { SalaryRuleService } from '@proxy/payroll/salary-rules';
import { ActivatedRoute, Router } from '@angular/router';
import { map, switchMap, tap } from 'rxjs';
import { SalaryRuleFormComponent } from '../salary-rule-form/salary-rule-form.component';
import { AsyncPipe } from '@angular/common';

@Component({
  selector: 'app-salary-rules-update',
  standalone: true,
  templateUrl: './salary-rules-update.component.html',
  imports: [
    Mat<PERSON><PERSON>,
    MatCardContent,
    LanguagePipe,
    SalaryRuleFormComponent,
    AsyncPipe,
  ],
  styles: `
    :host {
      --ttwr-form-grid-template-columns: 1fr 1fr;
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class SalaryRulesUpdateComponent {
  private salaryRule = inject(SalaryRuleService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private loading = inject(LOADING);

  protected initialValue$ = this.route.params.pipe(
    tap(() => {
      this.loading.set(true);
    }),
    switchMap(params => this.salaryRule.get(params['id'])),
    map(res => ({
      ...res,
      conditionBasedOn: res.ruleCondition.basedOn,
      conditionCode: res.ruleCondition.code,
      valueBasedOn: res.ruleValue.basedOn,
      valueFixedAmount: res.ruleValue.fixedAmount,
      valueCode: res.ruleValue.code,
    })),
    tap(() => {
      this.loading.set(false);
    }),
  );

  onFinish() {
    this.router.navigate(['.'], { relativeTo: this.route.parent });
  }
}
