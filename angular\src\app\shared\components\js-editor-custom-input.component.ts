import { Component, DestroyRef, ElementRef, inject, input, OnInit, viewChild } from '@angular/core';
import {
  FormFieldWithControlAndName,
  ICustomInputComponent,
  LanguagePipe,
  TextFieldType
} from '@ttwr-framework/ngx-main-visuals';
import { javascript } from '@codemirror/lang-javascript';
import { EditorView, basicSetup } from 'codemirror';
import { MatError } from '@angular/material/form-field';
import { Validators } from '@angular/forms';
import { filter, fromEvent, take } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { EditorState, Extension } from '@codemirror/state';

@Component({
  selector: 'app-js-editor-custom-input',
  standalone: true,
  imports: [
    LanguagePipe,
    MatError
  ],
  template: `
    <span>
      {{ (field().label ?? field().name) | i18n }}@if (field().control.hasValidator(ngRequiredValidator)) {*}
    </span>
    <div dir="ltr" #editor></div>
    @if (field().control.errors && !field().control.untouched) {
      <mat-error>
        @for (validator of field().validators ?? []; track validator.name) {
          <span [hidden]="!field().control.hasError(validator.name)">
            {{ validator.message | i18n }}
          </span>
        }
      </mat-error>
    }
  `,
  styles: `
    :host {
      color: var(--mdc-outlined-text-field-input-text-color, var(--mat-app-on-surface));
      display: block;
      border: 1px solid rgba(0, 0, 0, 0.3);
      border-radius: 0.25rem;
      padding: 0.5rem;

      & ::ng-deep {
        .cm-focused {
          outline: none;
        }
      }
    }
  `,
})
export class JsEditorCustomInputComponent implements ICustomInputComponent<TextFieldType>, OnInit {
  private elementRef = inject(ElementRef);
  private destroyRef = inject(DestroyRef);

  public field = input.required<FormFieldWithControlAndName<TextFieldType>>();

  private editorRef = viewChild.required<ElementRef>('editor');

  protected ngRequiredValidator = Validators.required;

  ngOnInit() {
    const extensions: Extension = [
      basicSetup,
      javascript(),
      EditorView.updateListener.of(view => {
        if (view.focusChanged) {
          this.field().control.markAsTouched();
        }
        this.field().control.setValue(view.state.doc.toString())
      }),
    ];

    const view = new EditorView({
      doc: this.field().control.value ?? '',
      parent: this.editorRef().nativeElement,
      extensions,
    });

    fromEvent(this.elementRef.nativeElement, 'click').pipe(
      takeUntilDestroyed(this.destroyRef),
    ).subscribe(() => {
      view.focus();
    });

    this.field().control.valueChanges.pipe(
      filter(Boolean),
      take(1),
      takeUntilDestroyed(this.destroyRef),
    ).subscribe(value => {
      if (value !== view.state.doc.toString()) {
        view.setState(EditorState.create({
          doc: value,
          extensions,
        }))
      }
    })
  }
}
