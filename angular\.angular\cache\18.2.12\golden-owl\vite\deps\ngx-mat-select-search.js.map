{"version": 3, "sources": ["../../../../../../node_modules/ngx-mat-select-search/fesm2022/ngx-mat-select-search.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, InjectionToken, EventEmitter, ElementRef, forwardRef, ContentChild, ViewChild, Output, Input, Inject, Optional, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport * as i3 from '@angular/forms';\nimport { FormControl, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';\nimport * as i11 from '@angular/material/core';\nimport { MatOption } from '@angular/material/core';\nimport * as i12 from '@angular/material/form-field';\nimport { MatFormField } from '@angular/material/form-field';\nimport * as i10 from '@angular/material/select';\nimport { MatSelect } from '@angular/material/select';\nimport { BehaviorSubject, of, combineLatest, Subject } from 'rxjs';\nimport { switchMap, map, startWith, delay, takeUntil, take, tap, filter } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/scrolling';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i4 from '@angular/material/button';\nimport { MatButtonModule } from '@angular/material/button';\nimport * as i5 from '@angular/material/checkbox';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport * as i6 from '@angular/material/icon';\nimport { MatIconModule } from '@angular/material/icon';\nimport * as i7 from '@angular/material/progress-spinner';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport * as i8 from '@angular/material/tooltip';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport * as i9 from '@angular/material/divider';\nimport { MatDividerModule } from '@angular/material/divider';\n\n/**\n * Directive for providing a custom clear-icon.\n * e.g.\n * <ngx-mat-select-search [formControl]=\"bankFilterCtrl\">\n *   <mat-icon ngxMatSelectSearchClear>delete</mat-icon>\n * </ngx-mat-select-search>\n */\nconst _c0 = [\"searchSelectInput\"];\nconst _c1 = [\"innerSelectSearch\"];\nconst _c2 = [[[\"\", 8, \"mat-select-search-custom-header-content\"]], [[\"\", \"ngxMatSelectSearchClear\", \"\"]], [[\"\", \"ngxMatSelectNoEntriesFound\", \"\"]]];\nconst _c3 = [\".mat-select-search-custom-header-content\", \"[ngxMatSelectSearchClear]\", \"[ngxMatSelectNoEntriesFound]\"];\nconst _c4 = (a0, a1) => ({\n  \"mat-select-search-inner-multiple\": a0,\n  \"mat-select-search-inner-toggle-all\": a1\n});\nfunction MatSelectSearchComponent_mat_checkbox_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 12);\n    i0.ɵɵlistener(\"change\", function MatSelectSearchComponent_mat_checkbox_4_Template_mat_checkbox_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2._emitSelectAllBooleanToParent($event.checked));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"color\", ctx_r2.matFormField == null ? null : ctx_r2.matFormField.color)(\"checked\", ctx_r2.toggleAllCheckboxChecked)(\"indeterminate\", ctx_r2.toggleAllCheckboxIndeterminate)(\"matTooltip\", ctx_r2.toggleAllCheckboxTooltipMessage)(\"matTooltipPosition\", ctx_r2.toggleAllCheckboxTooltipPosition);\n  }\n}\nfunction MatSelectSearchComponent_mat_spinner_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 13);\n  }\n}\nfunction MatSelectSearchComponent_button_8_ng_content_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1, [\"*ngIf\", \"clearIcon; else defaultIcon\"]);\n  }\n}\nfunction MatSelectSearchComponent_button_8_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"svgIcon\", ctx_r2.closeSvgIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", !ctx_r2.closeSvgIcon ? ctx_r2.closeIcon : null, \" \");\n  }\n}\nfunction MatSelectSearchComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function MatSelectSearchComponent_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2._reset(true));\n    });\n    i0.ɵɵtemplate(1, MatSelectSearchComponent_button_8_ng_content_1_Template, 1, 0, \"ng-content\", 15)(2, MatSelectSearchComponent_button_8_ng_template_2_Template, 2, 2, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const defaultIcon_r5 = i0.ɵɵreference(3);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.clearIcon)(\"ngIfElse\", defaultIcon_r5);\n  }\n}\nfunction MatSelectSearchComponent_div_11_ng_content_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 2, [\"*ngIf\", \"noEntriesFound; else defaultNoEntriesFound\"]);\n  }\n}\nfunction MatSelectSearchComponent_div_11_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵtextInterpolate(ctx_r2.noEntriesFoundLabel);\n  }\n}\nfunction MatSelectSearchComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtemplate(1, MatSelectSearchComponent_div_11_ng_content_1_Template, 1, 0, \"ng-content\", 15)(2, MatSelectSearchComponent_div_11_ng_template_2_Template, 1, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const defaultNoEntriesFound_r6 = i0.ɵɵreference(3);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.noEntriesFound)(\"ngIfElse\", defaultNoEntriesFound_r6);\n  }\n}\nclass MatSelectSearchClearDirective {\n  static ɵfac = function MatSelectSearchClearDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSelectSearchClearDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatSelectSearchClearDirective,\n    selectors: [[\"\", \"ngxMatSelectSearchClear\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectSearchClearDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ngxMatSelectSearchClear]',\n      standalone: false\n    }]\n  }], null, null);\n})();\n\n/** List of inputs of NgxMatSelectSearchComponent that can be configured with a global default. */\nconst configurableDefaultOptions = ['ariaLabel', 'clearSearchInput', 'closeIcon', 'closeSvgIcon', 'disableInitialFocus', 'disableScrollToActiveOnOptionsChanged', 'enableClearOnEscapePressed', 'hideClearSearchButton', 'noEntriesFoundLabel', 'placeholderLabel', 'preventHomeEndKeyPropagation', 'searching'];\n/**\n * InjectionToken that can be used to specify global options. e.g.\n *\n * ```typescript\n * providers: [\n *   {\n *     provide: MAT_SELECTSEARCH_DEFAULT_OPTIONS,\n *     useValue: <MatSelectSearchOptions>{\n *       closeIcon: 'delete',\n *       noEntriesFoundLabel: 'No options found'\n *     }\n *   }\n * ]\n * ```\n *\n * See the corresponding inputs of `MatSelectSearchComponent` for documentation.\n */\nconst MAT_SELECTSEARCH_DEFAULT_OPTIONS = new InjectionToken('mat-selectsearch-default-options');\n\n/**\n * Directive for providing a custom no entries found element.\n * e.g.\n * <ngx-mat-select-search [formControl]=\"bankFilterCtrl\">\n *   <span ngxMatSelectNoEntriesFound>\n *     No entries found <button>Add</button>\n *   </span>\n * </ngx-mat-select-search>\n */\nclass MatSelectNoEntriesFoundDirective {\n  static ɵfac = function MatSelectNoEntriesFoundDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSelectNoEntriesFoundDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatSelectNoEntriesFoundDirective,\n    selectors: [[\"\", \"ngxMatSelectNoEntriesFound\", \"\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectNoEntriesFoundDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ngxMatSelectNoEntriesFound]',\n      standalone: false\n    }]\n  }], null, null);\n})();\n\n/* tslint:disable:member-ordering component-selector */\n/**\n * Component providing an input field for searching MatSelect options.\n *\n * Example usage:\n *\n * interface Bank {\n *  id: string;\n *  name: string;\n * }\n *\n * @Component({\n *   selector: 'my-app-data-selection',\n *   template: `\n *     <mat-form-field>\n *       <mat-select [formControl]=\"bankCtrl\" placeholder=\"Bank\">\n *         <mat-option>\n *           <ngx-mat-select-search [formControl]=\"bankFilterCtrl\"></ngx-mat-select-search>\n *         </mat-option>\n *         <mat-option *ngFor=\"let bank of filteredBanks | async\" [value]=\"bank.id\">\n *           {{bank.name}}\n *         </mat-option>\n *       </mat-select>\n *     </mat-form-field>\n *   `\n * })\n * export class DataSelectionComponent implements OnInit, OnDestroy {\n *\n *   // control for the selected bank\n *   public bankCtrl: FormControl = new FormControl();\n *   // control for the MatSelect filter keyword\n *   public bankFilterCtrl: FormControl = new FormControl();\n *\n *   // list of banks\n *   private banks: Bank[] = [{name: 'Bank A', id: 'A'}, {name: 'Bank B', id: 'B'}, {name: 'Bank C', id: 'C'}];\n *   // list of banks filtered by search keyword\n *   public filteredBanks: ReplaySubject<Bank[]> = new ReplaySubject<Bank[]>(1);\n *\n *   // Subject that emits when the component has been destroyed.\n *   private _onDestroy = new Subject<void>();\n *\n *\n *   ngOnInit() {\n *     // load the initial bank list\n *     this.filteredBanks.next(this.banks.slice());\n *     // listen for search field value changes\n *     this.bankFilterCtrl.valueChanges\n *       .pipe(takeUntil(this._onDestroy))\n *       .subscribe(() => {\n *         this.filterBanks();\n *       });\n *   }\n *\n *   ngOnDestroy() {\n *     this._onDestroy.next();\n *     this._onDestroy.complete();\n *   }\n *\n *   private filterBanks() {\n *     if (!this.banks) {\n *       return;\n *     }\n *\n *     // get the search keyword\n *     let search = this.bankFilterCtrl.value;\n *     if (!search) {\n *       this.filteredBanks.next(this.banks.slice());\n *       return;\n *     } else {\n *       search = search.toLowerCase();\n *     }\n *\n *     // filter the banks\n *     this.filteredBanks.next(\n *       this.banks.filter(bank => bank.name.toLowerCase().indexOf(search) > -1)\n *     );\n *   }\n * }\n */\nclass MatSelectSearchComponent {\n  matSelect;\n  changeDetectorRef;\n  _viewportRuler;\n  matOption;\n  matFormField;\n  /** Label of the search placeholder */\n  placeholderLabel = 'Suche';\n  /** Type of the search input field */\n  type = 'text';\n  /** Font-based icon used for displaying Close-Icon */\n  closeIcon = 'close';\n  /** SVG-based icon used for displaying Close-Icon. If set, closeIcon is overridden */\n  closeSvgIcon;\n  /** Label to be shown when no entries are found. Set to null if no message should be shown. */\n  noEntriesFoundLabel = 'Keine Optionen gefunden';\n  /**\n    * Whether the search field should be cleared after the dropdown menu is closed.\n    * Useful for server-side filtering. See [#3](https://github.com/bithost-gmbh/ngx-mat-select-search/issues/3)\n    */\n  clearSearchInput = true;\n  /** Whether to show the search-in-progress indicator */\n  searching = false;\n  /** Disables initial focusing of the input field */\n  disableInitialFocus = false;\n  /** Enable clear input on escape pressed */\n  enableClearOnEscapePressed = false;\n  /**\n   * Prevents home / end key being propagated to mat-select,\n   * allowing to move the cursor within the search input instead of navigating the options\n   */\n  preventHomeEndKeyPropagation = false;\n  /** Disables scrolling to active options when option list changes. Useful for server-side search */\n  disableScrollToActiveOnOptionsChanged = false;\n  /** Adds 508 screen reader support for search box */\n  ariaLabel = 'dropdown search';\n  /** Whether to show Select All Checkbox (for mat-select[multi=true]) */\n  showToggleAllCheckbox = false;\n  /** Select all checkbox checked state */\n  toggleAllCheckboxChecked = false;\n  /** select all checkbox indeterminate state */\n  toggleAllCheckboxIndeterminate = false;\n  /** Display a message in a tooltip on the toggle-all checkbox */\n  toggleAllCheckboxTooltipMessage = '';\n  /** Define the position of the tooltip on the toggle-all checkbox. */\n  toggleAllCheckboxTooltipPosition = 'below';\n  /** Show/Hide the search clear button of the search input */\n  hideClearSearchButton = false;\n  /**\n   * Always restore selected options on selectionChange for mode multi (e.g. for lazy loading/infinity scrolling).\n   * Defaults to false, so selected options are only restored while filtering is active.\n   */\n  alwaysRestoreSelectedOptionsMulti = false;\n  /**\n   * Recreate array of selected values for multi-selects.\n   *\n   * This is useful if the selected values are stored in an immutable data structure.\n   */\n  recreateValuesArray = false;\n  /** Output emitter to send to parent component with the toggle all boolean */\n  toggleAll = new EventEmitter();\n  /** Reference to the search input field */\n  searchSelectInput;\n  /** Reference to the search input field */\n  innerSelectSearch;\n  /** Reference to custom search input clear icon */\n  clearIcon;\n  /** Reference to custom no entries found element */\n  noEntriesFound;\n  /** Current search value */\n  get value() {\n    return this._formControl.value;\n  }\n  _lastExternalInputValue;\n  onTouched = _ => {};\n  /** Reference to the MatSelect options */\n  set _options(_options) {\n    this._options$.next(_options);\n  }\n  get _options() {\n    return this._options$.getValue();\n  }\n  _options$ = new BehaviorSubject(null);\n  optionsList$ = this._options$.pipe(switchMap(_options => _options ? _options.changes.pipe(map(options => options.toArray()), startWith(_options.toArray())) : of(null)));\n  optionsLength$ = this.optionsList$.pipe(map(options => options ? options.length : 0));\n  /** Previously selected values when using <mat-select [multiple]=\"true\">*/\n  previousSelectedValues;\n  _formControl = new FormControl('', {\n    nonNullable: true\n  });\n  /** Whether to show the no entries found message */\n  _showNoEntriesFound$ = combineLatest([this._formControl.valueChanges, this.optionsLength$]).pipe(map(([value, optionsLength]) => !!(this.noEntriesFoundLabel && value && optionsLength === this.getOptionsLengthOffset())));\n  /** Subject that emits when the component has been destroyed. */\n  _onDestroy = new Subject();\n  /** Reference to active descendant for ARIA Support. */\n  activeDescendant;\n  constructor(matSelect, changeDetectorRef, _viewportRuler, matOption, matFormField, defaultOptions) {\n    this.matSelect = matSelect;\n    this.changeDetectorRef = changeDetectorRef;\n    this._viewportRuler = _viewportRuler;\n    this.matOption = matOption;\n    this.matFormField = matFormField;\n    this.applyDefaultOptions(defaultOptions);\n  }\n  applyDefaultOptions(defaultOptions) {\n    if (!defaultOptions) {\n      return;\n    }\n    for (const key of configurableDefaultOptions) {\n      if (defaultOptions.hasOwnProperty(key)) {\n        this[key] = defaultOptions[key];\n      }\n    }\n  }\n  ngOnInit() {\n    // set custom mat-option class if the component was placed inside a mat-option\n    if (this.matOption) {\n      this.matOption.disabled = true;\n      this.matOption._getHostElement().classList.add('contains-mat-select-search');\n      this.matOption._getHostElement().setAttribute('role', 'presentation');\n    } else {\n      console.error('<ngx-mat-select-search> must be placed inside a <mat-option> element');\n    }\n    // when the select dropdown panel is opened or closed\n    this.matSelect.openedChange.pipe(delay(1), takeUntil(this._onDestroy)).subscribe(opened => {\n      if (opened) {\n        this.updateInputWidth();\n        // focus the search field when opening\n        if (!this.disableInitialFocus) {\n          this._focus();\n        }\n      } else {\n        // clear it when closing\n        if (this.clearSearchInput) {\n          this._reset();\n        }\n      }\n    });\n    // set the first item active after the options changed\n    this.matSelect.openedChange.pipe(take(1), switchMap(_ => {\n      this._options = this.matSelect.options;\n      // Closure variable for tracking the most recent first option.\n      // In order to avoid causing the list to\n      // scroll to the top when options are added to the bottom of\n      // the list (eg: infinite scroll), we compare only\n      // the changes to the first options to determine if we\n      // should set the first item as active.\n      // This prevents unnecessary scrolling to the top of the list\n      // when options are appended, but allows the first item\n      // in the list to be set as active by default when there\n      // is no active selection\n      let previousFirstOption = this._options.toArray()[this.getOptionsLengthOffset()];\n      return this._options.changes.pipe(tap(() => {\n        // avoid \"expression has been changed\" error\n        setTimeout(() => {\n          // Convert the QueryList to an array\n          const options = this._options.toArray();\n          // The true first item is offset by 1\n          const currentFirstOption = options[this.getOptionsLengthOffset()];\n          const keyManager = this.matSelect._keyManager;\n          if (keyManager && this.matSelect.panelOpen && currentFirstOption) {\n            // set first item active and input width\n            // Check to see if the first option in these changes is different from the previous.\n            const firstOptionIsChanged = !previousFirstOption || !this.matSelect.compareWith(previousFirstOption.value, currentFirstOption.value);\n            // CASE: The first option is different now.\n            // Indicates we should set it as active and scroll to the top.\n            if (firstOptionIsChanged || !keyManager.activeItem || !options.find(option => this.matSelect.compareWith(option.value, keyManager.activeItem?.value))) {\n              keyManager.setActiveItem(this.getOptionsLengthOffset());\n            }\n            // wait for panel width changes\n            setTimeout(() => {\n              this.updateInputWidth();\n            });\n          }\n          // Update our reference\n          previousFirstOption = currentFirstOption;\n        });\n      }));\n    })).pipe(takeUntil(this._onDestroy)).subscribe();\n    // add or remove css class depending on whether to show the no entries found message\n    // note: this is hacky\n    this._showNoEntriesFound$.pipe(takeUntil(this._onDestroy)).subscribe(showNoEntriesFound => {\n      // set no entries found class on mat option\n      if (this.matOption) {\n        if (showNoEntriesFound) {\n          this.matOption._getHostElement().classList.add('mat-select-search-no-entries-found');\n        } else {\n          this.matOption._getHostElement().classList.remove('mat-select-search-no-entries-found');\n        }\n      }\n    });\n    // resize the input width when the viewport is resized, i.e. the trigger width could potentially be resized\n    this._viewportRuler.change().pipe(takeUntil(this._onDestroy)).subscribe(() => {\n      if (this.matSelect.panelOpen) {\n        this.updateInputWidth();\n      }\n    });\n    this.initMultipleHandling();\n    this.optionsList$.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n      // update view when available options change\n      this.changeDetectorRef.markForCheck();\n    });\n  }\n  _emitSelectAllBooleanToParent(state) {\n    this.toggleAll.emit(state);\n  }\n  ngOnDestroy() {\n    this._onDestroy.next();\n    this._onDestroy.complete();\n  }\n  _isToggleAllCheckboxVisible() {\n    return this.matSelect.multiple && this.showToggleAllCheckbox;\n  }\n  /**\n   * Handles the key down event with MatSelect.\n   * Allows e.g. selecting with enter key, navigation with arrow keys, etc.\n   * @param event\n   */\n  _handleKeydown(event) {\n    // Prevent propagation for all alphanumeric characters in order to avoid selection issues\n    // tslint:disable-next-line:max-line-length\n    // Needed to avoid handling in https://github.com/angular/components/blob/5439460d1fe166f8ec34ab7d48f05e0dd7f6a946/src/material/select/select.ts#L965\n    if (event.key && event.key.length === 1 || this.preventHomeEndKeyPropagation && (event.key === 'Home' || event.key === 'End')) {\n      event.stopPropagation();\n    }\n    if (this.matSelect.multiple && event.key && event.key === 'Enter') {\n      // Regain focus after multiselect, so we can further type\n      setTimeout(() => this._focus());\n    }\n    // Special case if click Escape, if input is empty, close the dropdown, if not, empty out the search field\n    if (this.enableClearOnEscapePressed && event.key === 'Escape' && this.value) {\n      this._reset(true);\n      event.stopPropagation();\n    }\n  }\n  /**\n   * Handles the key up event with MatSelect.\n   * Allows e.g. the announcing of the currently activeDescendant by screen readers.\n   */\n  _handleKeyup(event) {\n    if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {\n      const ariaActiveDescendantId = this.matSelect._getAriaActiveDescendant();\n      const index = this._options.toArray().findIndex(item => item.id === ariaActiveDescendantId);\n      if (index !== -1) {\n        this.unselectActiveDescendant();\n        this.activeDescendant = this._options.toArray()[index]._getHostElement();\n        this.activeDescendant.setAttribute('aria-selected', 'true');\n        this.searchSelectInput.nativeElement.setAttribute('aria-activedescendant', ariaActiveDescendantId);\n      }\n    }\n  }\n  writeValue(value) {\n    this._lastExternalInputValue = value;\n    this._formControl.setValue(value);\n    this.changeDetectorRef.markForCheck();\n  }\n  onBlur() {\n    this.unselectActiveDescendant();\n    this.onTouched();\n  }\n  registerOnChange(fn) {\n    this._formControl.valueChanges.pipe(filter(value => value !== this._lastExternalInputValue), tap(() => this._lastExternalInputValue = undefined), takeUntil(this._onDestroy)).subscribe(fn);\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  /**\n   * Focuses the search input field\n   */\n  _focus() {\n    if (!this.searchSelectInput || !this.matSelect.panel) {\n      return;\n    }\n    // save and restore scrollTop of panel, since it will be reset by focus()\n    // note: this is hacky\n    const panel = this.matSelect.panel.nativeElement;\n    const scrollTop = panel.scrollTop;\n    // focus\n    this.searchSelectInput.nativeElement.focus();\n    panel.scrollTop = scrollTop;\n  }\n  /**\n   * Resets the current search value\n   * @param focus whether to focus after resetting\n   */\n  _reset(focus) {\n    this._formControl.setValue('');\n    if (focus) {\n      this._focus();\n    }\n  }\n  /**\n   * Initializes handling <mat-select [multiple]=\"true\">\n   * Note: to improve this code, mat-select should be extended to allow disabling resetting the selection while filtering.\n   */\n  initMultipleHandling() {\n    if (!this.matSelect.ngControl) {\n      if (this.matSelect.multiple) {\n        // note: the access to matSelect.ngControl (instead of matSelect.value / matSelect.valueChanges)\n        // is necessary to properly work in multi-selection mode.\n        console.error('the mat-select containing ngx-mat-select-search must have a ngModel or formControl directive when multiple=true');\n      }\n      return;\n    }\n    // if <mat-select [multiple]=\"true\">\n    // store previously selected values and restore them when they are deselected\n    // because the option is not available while we are currently filtering\n    this.previousSelectedValues = this.matSelect.ngControl.value;\n    if (!this.matSelect.ngControl.valueChanges) {\n      return;\n    }\n    this.matSelect.ngControl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(values => {\n      let restoreSelectedValues = false;\n      if (this.matSelect.multiple) {\n        if ((this.alwaysRestoreSelectedOptionsMulti || this._formControl.value && this._formControl.value.length) && this.previousSelectedValues && Array.isArray(this.previousSelectedValues)) {\n          if (!values || !Array.isArray(values)) {\n            values = [];\n          }\n          const optionValues = this.matSelect.options.map(option => option.value);\n          this.previousSelectedValues.forEach(previousValue => {\n            if (!values.some(v => this.matSelect.compareWith(v, previousValue)) && !optionValues.some(v => this.matSelect.compareWith(v, previousValue))) {\n              // if a value that was selected before is deselected and not found in the options, it was deselected\n              // due to the filtering, so we restore it.\n              if (this.recreateValuesArray) {\n                values = [...values, previousValue];\n              } else {\n                values.push(previousValue);\n              }\n              restoreSelectedValues = true;\n            }\n          });\n        }\n      }\n      this.previousSelectedValues = values;\n      if (restoreSelectedValues) {\n        this.matSelect._onChange(values);\n      }\n    });\n  }\n  /**\n   *  Set the width of the innerSelectSearch to fit even custom scrollbars\n   *  And support all Operating Systems\n   */\n  updateInputWidth() {\n    if (!this.innerSelectSearch || !this.innerSelectSearch.nativeElement) {\n      return;\n    }\n    let element = this.innerSelectSearch.nativeElement;\n    let panelElement = null;\n    while (element && element.parentElement) {\n      element = element.parentElement;\n      if (element.classList.contains('mat-select-panel')) {\n        panelElement = element;\n        break;\n      }\n    }\n    if (panelElement) {\n      this.innerSelectSearch.nativeElement.style.width = panelElement.clientWidth + 'px';\n    }\n  }\n  /**\n   * Determine the offset to length that can be caused by the optional matOption used as a search input.\n   */\n  getOptionsLengthOffset() {\n    if (this.matOption) {\n      return 1;\n    } else {\n      return 0;\n    }\n  }\n  unselectActiveDescendant() {\n    this.activeDescendant?.removeAttribute('aria-selected');\n    this.searchSelectInput.nativeElement.removeAttribute('aria-activedescendant');\n  }\n  static ɵfac = function MatSelectSearchComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSelectSearchComponent)(i0.ɵɵdirectiveInject(MatSelect), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ViewportRuler), i0.ɵɵdirectiveInject(MatOption, 8), i0.ɵɵdirectiveInject(MatFormField, 8), i0.ɵɵdirectiveInject(MAT_SELECTSEARCH_DEFAULT_OPTIONS, 8));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatSelectSearchComponent,\n    selectors: [[\"ngx-mat-select-search\"]],\n    contentQueries: function MatSelectSearchComponent_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatSelectSearchClearDirective, 5);\n        i0.ɵɵcontentQuery(dirIndex, MatSelectNoEntriesFoundDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.clearIcon = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.noEntriesFound = _t.first);\n      }\n    },\n    viewQuery: function MatSelectSearchComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7, ElementRef);\n        i0.ɵɵviewQuery(_c1, 7, ElementRef);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchSelectInput = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.innerSelectSearch = _t.first);\n      }\n    },\n    inputs: {\n      placeholderLabel: \"placeholderLabel\",\n      type: \"type\",\n      closeIcon: \"closeIcon\",\n      closeSvgIcon: \"closeSvgIcon\",\n      noEntriesFoundLabel: \"noEntriesFoundLabel\",\n      clearSearchInput: \"clearSearchInput\",\n      searching: \"searching\",\n      disableInitialFocus: \"disableInitialFocus\",\n      enableClearOnEscapePressed: \"enableClearOnEscapePressed\",\n      preventHomeEndKeyPropagation: \"preventHomeEndKeyPropagation\",\n      disableScrollToActiveOnOptionsChanged: \"disableScrollToActiveOnOptionsChanged\",\n      ariaLabel: \"ariaLabel\",\n      showToggleAllCheckbox: \"showToggleAllCheckbox\",\n      toggleAllCheckboxChecked: \"toggleAllCheckboxChecked\",\n      toggleAllCheckboxIndeterminate: \"toggleAllCheckboxIndeterminate\",\n      toggleAllCheckboxTooltipMessage: \"toggleAllCheckboxTooltipMessage\",\n      toggleAllCheckboxTooltipPosition: \"toggleAllCheckboxTooltipPosition\",\n      hideClearSearchButton: \"hideClearSearchButton\",\n      alwaysRestoreSelectedOptionsMulti: \"alwaysRestoreSelectedOptionsMulti\",\n      recreateValuesArray: \"recreateValuesArray\"\n    },\n    outputs: {\n      toggleAll: \"toggleAll\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => MatSelectSearchComponent),\n      multi: true\n    }])],\n    ngContentSelectors: _c3,\n    decls: 13,\n    vars: 14,\n    consts: [[\"innerSelectSearch\", \"\"], [\"searchSelectInput\", \"\"], [\"defaultIcon\", \"\"], [\"defaultNoEntriesFound\", \"\"], [\"matInput\", \"\", 1, \"mat-select-search-input\", \"mat-select-search-hidden\"], [1, \"mat-select-search-inner\", \"mat-typography\", \"mat-datepicker-content\", \"mat-tab-header\", 3, \"ngClass\"], [1, \"mat-select-search-inner-row\"], [\"class\", \"mat-select-search-toggle-all-checkbox\", \"matTooltipClass\", \"ngx-mat-select-search-toggle-all-tooltip\", 3, \"color\", \"checked\", \"indeterminate\", \"matTooltip\", \"matTooltipPosition\", \"change\", 4, \"ngIf\"], [\"autocomplete\", \"off\", 1, \"mat-select-search-input\", 3, \"keydown\", \"keyup\", \"blur\", \"type\", \"formControl\", \"placeholder\"], [\"class\", \"mat-select-search-spinner\", \"diameter\", \"16\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Clear\", \"class\", \"mat-select-search-clear\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"mat-select-search-no-entries-found\", 4, \"ngIf\"], [\"matTooltipClass\", \"ngx-mat-select-search-toggle-all-tooltip\", 1, \"mat-select-search-toggle-all-checkbox\", 3, \"change\", \"color\", \"checked\", \"indeterminate\", \"matTooltip\", \"matTooltipPosition\"], [\"diameter\", \"16\", 1, \"mat-select-search-spinner\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Clear\", 1, \"mat-select-search-clear\", 3, \"click\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"svgIcon\"], [1, \"mat-select-search-no-entries-found\"]],\n    template: function MatSelectSearchComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef(_c2);\n        i0.ɵɵelement(0, \"input\", 4);\n        i0.ɵɵelementStart(1, \"div\", 5, 0)(3, \"div\", 6);\n        i0.ɵɵtemplate(4, MatSelectSearchComponent_mat_checkbox_4_Template, 1, 5, \"mat-checkbox\", 7);\n        i0.ɵɵelementStart(5, \"input\", 8, 1);\n        i0.ɵɵlistener(\"keydown\", function MatSelectSearchComponent_Template_input_keydown_5_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handleKeydown($event));\n        })(\"keyup\", function MatSelectSearchComponent_Template_input_keyup_5_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handleKeyup($event));\n        })(\"blur\", function MatSelectSearchComponent_Template_input_blur_5_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onBlur());\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(7, MatSelectSearchComponent_mat_spinner_7_Template, 1, 0, \"mat-spinner\", 9)(8, MatSelectSearchComponent_button_8_Template, 4, 2, \"button\", 10);\n        i0.ɵɵprojection(9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(10, \"mat-divider\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(11, MatSelectSearchComponent_div_11_Template, 4, 2, \"div\", 11);\n        i0.ɵɵpipe(12, \"async\");\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(11, _c4, ctx.matSelect.multiple, ctx._isToggleAllCheckboxVisible()));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx._isToggleAllCheckboxVisible());\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"type\", ctx.type)(\"formControl\", ctx._formControl)(\"placeholder\", ctx.placeholderLabel);\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.searching);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.hideClearSearchButton && ctx.value && !ctx.searching);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(12, 9, ctx._showNoEntriesFound$));\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgIf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.FormControlDirective, i4.MatIconButton, i5.MatCheckbox, i6.MatIcon, i7.MatProgressSpinner, i8.MatTooltip, i9.MatDivider, i2.AsyncPipe],\n    styles: [\".mat-select-search-hidden[_ngcontent-%COMP%]{visibility:hidden}.mat-select-search-inner[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;z-index:100;font-size:inherit;box-shadow:none;background-color:var(--mat-select-panel-background-color)}.mat-select-search-inner.mat-select-search-inner-multiple.mat-select-search-inner-toggle-all[_ngcontent-%COMP%]   .mat-select-search-inner-row[_ngcontent-%COMP%]{display:flex;align-items:center}.mat-select-search-input[_ngcontent-%COMP%]{box-sizing:border-box;width:100%;border:none;font-family:inherit;font-size:inherit;color:currentColor;outline:none;background-color:var(--mat-select-panel-background-color);padding:0 44px 0 16px;height:calc(3em - 1px);line-height:calc(3em - 1px)}[dir=rtl][_nghost-%COMP%]   .mat-select-search-input[_ngcontent-%COMP%], [dir=rtl]   [_nghost-%COMP%]   .mat-select-search-input[_ngcontent-%COMP%]{padding-right:16px;padding-left:44px}.mat-select-search-input[_ngcontent-%COMP%]::placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color)}.mat-select-search-inner-toggle-all[_ngcontent-%COMP%]   .mat-select-search-input[_ngcontent-%COMP%]{padding-left:5px}.mat-select-search-no-entries-found[_ngcontent-%COMP%]{padding-top:8px}.mat-select-search-clear[_ngcontent-%COMP%]{position:absolute;right:4px;top:0}[dir=rtl][_nghost-%COMP%]   .mat-select-search-clear[_ngcontent-%COMP%], [dir=rtl]   [_nghost-%COMP%]   .mat-select-search-clear[_ngcontent-%COMP%]{right:auto;left:4px}.mat-select-search-spinner[_ngcontent-%COMP%]{position:absolute;right:16px;top:calc(50% - 8px)}[dir=rtl][_nghost-%COMP%]   .mat-select-search-spinner[_ngcontent-%COMP%], [dir=rtl]   [_nghost-%COMP%]   .mat-select-search-spinner[_ngcontent-%COMP%]{right:auto;left:16px}  .mat-mdc-option[aria-disabled=true].contains-mat-select-search{position:sticky;top:-8px;z-index:1;opacity:1;margin-top:-8px;pointer-events:all}  .mat-mdc-option[aria-disabled=true].contains-mat-select-search .mat-icon{margin-right:0;margin-left:0}  .mat-mdc-option[aria-disabled=true].contains-mat-select-search mat-pseudo-checkbox{display:none}  .mat-mdc-option[aria-disabled=true].contains-mat-select-search .mdc-list-item__primary-text{opacity:1}.mat-select-search-toggle-all-checkbox[_ngcontent-%COMP%]{padding-left:5px}[dir=rtl][_nghost-%COMP%]   .mat-select-search-toggle-all-checkbox[_ngcontent-%COMP%], [dir=rtl]   [_nghost-%COMP%]   .mat-select-search-toggle-all-checkbox[_ngcontent-%COMP%]{padding-left:0;padding-right:5px}\"],\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectSearchComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-mat-select-search',\n      standalone: false,\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => MatSelectSearchComponent),\n        multi: true\n      }],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<!--\\nCopyright (c) 2018 Bithost GmbH All Rights Reserved.\\n\\nUse of this source code is governed by an MIT-style license that can be\\nfound in the LICENSE file at https://angular.io/license\\n-->\\n<!-- Placeholder to adjust vertical offset of the mat-option elements -->\\n<input matInput class=\\\"mat-select-search-input mat-select-search-hidden\\\"/>\\n\\n<!-- Note: the  mat-datepicker-content mat-tab-header are needed to inherit the material theme colors, see PR #22 -->\\n<div\\n      #innerSelectSearch\\n      class=\\\"mat-select-search-inner mat-typography mat-datepicker-content mat-tab-header\\\"\\n      [ngClass]=\\\"{'mat-select-search-inner-multiple': matSelect.multiple, 'mat-select-search-inner-toggle-all': _isToggleAllCheckboxVisible() }\\\">\\n\\n  <div class=\\\"mat-select-search-inner-row\\\">\\n    <mat-checkbox *ngIf=\\\"_isToggleAllCheckboxVisible()\\\"\\n                  [color]=\\\"matFormField?.color\\\"\\n                  class=\\\"mat-select-search-toggle-all-checkbox\\\"\\n                  [checked]=\\\"toggleAllCheckboxChecked\\\"\\n                  [indeterminate]=\\\"toggleAllCheckboxIndeterminate\\\"\\n                  [matTooltip]=\\\"toggleAllCheckboxTooltipMessage\\\"\\n                  matTooltipClass=\\\"ngx-mat-select-search-toggle-all-tooltip\\\"\\n                  [matTooltipPosition]=\\\"toggleAllCheckboxTooltipPosition\\\"\\n                  (change)=\\\"_emitSelectAllBooleanToParent($event.checked)\\\"\\n    ></mat-checkbox>\\n\\n    <input class=\\\"mat-select-search-input\\\"\\n           autocomplete=\\\"off\\\"\\n           [type]=\\\"type\\\"\\n           [formControl]=\\\"_formControl\\\"\\n           #searchSelectInput\\n           (keydown)=\\\"_handleKeydown($event)\\\"\\n           (keyup)=\\\"_handleKeyup($event)\\\"\\n           (blur)=\\\"onBlur()\\\"\\n           [placeholder]=\\\"placeholderLabel\\\"\\n           [attr.aria-label]=\\\"ariaLabel\\\"\\n    />\\n    <mat-spinner *ngIf=\\\"searching\\\"\\n            class=\\\"mat-select-search-spinner\\\"\\n            diameter=\\\"16\\\"></mat-spinner>\\n\\n    <button *ngIf=\\\"!hideClearSearchButton && value && !searching\\\"\\n            mat-icon-button\\n            aria-label=\\\"Clear\\\"\\n            (click)=\\\"_reset(true)\\\"\\n            class=\\\"mat-select-search-clear\\\">\\n      <ng-content *ngIf=\\\"clearIcon; else defaultIcon\\\" select=\\\"[ngxMatSelectSearchClear]\\\"></ng-content>\\n      <ng-template #defaultIcon>\\n        <mat-icon [svgIcon]=\\\"closeSvgIcon\\\">\\n          {{!closeSvgIcon ? closeIcon : null}}\\n        </mat-icon>\\n      </ng-template>\\n    </button>\\n\\n    <ng-content select=\\\".mat-select-search-custom-header-content\\\"></ng-content>\\n  </div>\\n\\n  <mat-divider></mat-divider>\\n</div>\\n\\n<div *ngIf=\\\"_showNoEntriesFound$ | async\\\"\\n     class=\\\"mat-select-search-no-entries-found\\\">\\n  <ng-content *ngIf=\\\"noEntriesFound; else defaultNoEntriesFound\\\"\\n              select=\\\"[ngxMatSelectNoEntriesFound]\\\"></ng-content>\\n  <ng-template #defaultNoEntriesFound>{{noEntriesFoundLabel}}</ng-template>\\n</div>\\n\\n\",\n      styles: [\".mat-select-search-hidden{visibility:hidden}.mat-select-search-inner{position:absolute;top:0;left:0;width:100%;z-index:100;font-size:inherit;box-shadow:none;background-color:var(--mat-select-panel-background-color)}.mat-select-search-inner.mat-select-search-inner-multiple.mat-select-search-inner-toggle-all .mat-select-search-inner-row{display:flex;align-items:center}.mat-select-search-input{box-sizing:border-box;width:100%;border:none;font-family:inherit;font-size:inherit;color:currentColor;outline:none;background-color:var(--mat-select-panel-background-color);padding:0 44px 0 16px;height:calc(3em - 1px);line-height:calc(3em - 1px)}:host-context([dir=rtl]) .mat-select-search-input{padding-right:16px;padding-left:44px}.mat-select-search-input::placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color)}.mat-select-search-inner-toggle-all .mat-select-search-input{padding-left:5px}.mat-select-search-no-entries-found{padding-top:8px}.mat-select-search-clear{position:absolute;right:4px;top:0}:host-context([dir=rtl]) .mat-select-search-clear{right:auto;left:4px}.mat-select-search-spinner{position:absolute;right:16px;top:calc(50% - 8px)}:host-context([dir=rtl]) .mat-select-search-spinner{right:auto;left:16px}::ng-deep .mat-mdc-option[aria-disabled=true].contains-mat-select-search{position:sticky;top:-8px;z-index:1;opacity:1;margin-top:-8px;pointer-events:all}::ng-deep .mat-mdc-option[aria-disabled=true].contains-mat-select-search .mat-icon{margin-right:0;margin-left:0}::ng-deep .mat-mdc-option[aria-disabled=true].contains-mat-select-search mat-pseudo-checkbox{display:none}::ng-deep .mat-mdc-option[aria-disabled=true].contains-mat-select-search .mdc-list-item__primary-text{opacity:1}.mat-select-search-toggle-all-checkbox{padding-left:5px}:host-context([dir=rtl]) .mat-select-search-toggle-all-checkbox{padding-left:0;padding-right:5px}\\n\"]\n    }]\n  }], () => [{\n    type: i10.MatSelect,\n    decorators: [{\n      type: Inject,\n      args: [MatSelect]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.ViewportRuler\n  }, {\n    type: i11.MatOption,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MatOption]\n    }]\n  }, {\n    type: i12.MatFormField,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MatFormField]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_SELECTSEARCH_DEFAULT_OPTIONS]\n    }]\n  }], {\n    placeholderLabel: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    closeIcon: [{\n      type: Input\n    }],\n    closeSvgIcon: [{\n      type: Input\n    }],\n    noEntriesFoundLabel: [{\n      type: Input\n    }],\n    clearSearchInput: [{\n      type: Input\n    }],\n    searching: [{\n      type: Input\n    }],\n    disableInitialFocus: [{\n      type: Input\n    }],\n    enableClearOnEscapePressed: [{\n      type: Input\n    }],\n    preventHomeEndKeyPropagation: [{\n      type: Input\n    }],\n    disableScrollToActiveOnOptionsChanged: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    showToggleAllCheckbox: [{\n      type: Input\n    }],\n    toggleAllCheckboxChecked: [{\n      type: Input\n    }],\n    toggleAllCheckboxIndeterminate: [{\n      type: Input\n    }],\n    toggleAllCheckboxTooltipMessage: [{\n      type: Input\n    }],\n    toggleAllCheckboxTooltipPosition: [{\n      type: Input\n    }],\n    hideClearSearchButton: [{\n      type: Input\n    }],\n    alwaysRestoreSelectedOptionsMulti: [{\n      type: Input\n    }],\n    recreateValuesArray: [{\n      type: Input\n    }],\n    toggleAll: [{\n      type: Output\n    }],\n    searchSelectInput: [{\n      type: ViewChild,\n      args: ['searchSelectInput', {\n        read: ElementRef,\n        static: true\n      }]\n    }],\n    innerSelectSearch: [{\n      type: ViewChild,\n      args: ['innerSelectSearch', {\n        read: ElementRef,\n        static: true\n      }]\n    }],\n    clearIcon: [{\n      type: ContentChild,\n      args: [MatSelectSearchClearDirective]\n    }],\n    noEntriesFound: [{\n      type: ContentChild,\n      args: [MatSelectNoEntriesFoundDirective]\n    }]\n  });\n})();\n\n/**\n * Copyright (c) 2018 Bithost GmbH All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst MatSelectSearchVersion = '8.0.0';\nclass NgxMatSelectSearchModule {\n  static ɵfac = function NgxMatSelectSearchModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NgxMatSelectSearchModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NgxMatSelectSearchModule,\n    declarations: [MatSelectSearchComponent, MatSelectSearchClearDirective, MatSelectNoEntriesFoundDirective],\n    imports: [CommonModule, ReactiveFormsModule, MatButtonModule, MatCheckboxModule, MatIconModule, MatProgressSpinnerModule, MatTooltipModule, MatDividerModule],\n    exports: [MatSelectSearchComponent, MatSelectSearchClearDirective, MatSelectNoEntriesFoundDirective]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, ReactiveFormsModule, MatButtonModule, MatCheckboxModule, MatIconModule, MatProgressSpinnerModule, MatTooltipModule, MatDividerModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMatSelectSearchModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, ReactiveFormsModule, MatButtonModule, MatCheckboxModule, MatIconModule, MatProgressSpinnerModule, MatTooltipModule, MatDividerModule],\n      declarations: [MatSelectSearchComponent, MatSelectSearchClearDirective, MatSelectNoEntriesFoundDirective],\n      exports: [MatSelectSearchComponent, MatSelectSearchClearDirective, MatSelectNoEntriesFoundDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SELECTSEARCH_DEFAULT_OPTIONS, MatSelectNoEntriesFoundDirective, MatSelectSearchClearDirective, MatSelectSearchComponent, MatSelectSearchVersion, NgxMatSelectSearchModule, configurableDefaultOptions };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,IAAM,MAAM,CAAC,mBAAmB;AAChC,IAAM,MAAM,CAAC,mBAAmB;AAChC,IAAM,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,yCAAyC,CAAC,GAAG,CAAC,CAAC,IAAI,2BAA2B,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,8BAA8B,EAAE,CAAC,CAAC;AAClJ,IAAM,MAAM,CAAC,4CAA4C,6BAA6B,8BAA8B;AACpH,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,oCAAoC;AAAA,EACpC,sCAAsC;AACxC;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,gBAAgB,EAAE;AACvC,IAAG,WAAW,UAAU,SAAS,gFAAgF,QAAQ;AACvH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,8BAA8B,OAAO,OAAO,CAAC;AAAA,IAC5E,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,SAAS,OAAO,gBAAgB,OAAO,OAAO,OAAO,aAAa,KAAK,EAAE,WAAW,OAAO,wBAAwB,EAAE,iBAAiB,OAAO,8BAA8B,EAAE,cAAc,OAAO,+BAA+B,EAAE,sBAAsB,OAAO,gCAAgC;AAAA,EAChT;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,eAAe,EAAE;AAAA,EACnC;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,GAAG,CAAC,SAAS,6BAA6B,CAAC;AAAA,EAChE;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,YAAY,EAAE;AACnC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,YAAY;AAC5C,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,CAAC,OAAO,eAAe,OAAO,YAAY,MAAM,GAAG;AAAA,EAChF;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,qEAAqE;AACnG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,IAAI,CAAC;AAAA,IAC3C,CAAC;AACD,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,cAAc,EAAE,EAAE,GAAG,0DAA0D,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACtN,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,iBAAoB,YAAY,CAAC;AACvC,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,SAAS,EAAE,YAAY,cAAc;AAAA,EACpE;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,GAAG,CAAC,SAAS,4CAA4C,CAAC;AAAA,EAC/E;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,kBAAkB,OAAO,mBAAmB;AAAA,EACjD;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,uDAAuD,GAAG,GAAG,cAAc,EAAE,EAAE,GAAG,wDAAwD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAClN,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,2BAA8B,YAAY,CAAC;AACjD,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,cAAc,EAAE,YAAY,wBAAwB;AAAA,EACnF;AACF;AACA,IAAM,gCAAN,MAAM,+BAA8B;AAAA,EAClC,OAAO,OAAO,SAAS,sCAAsC,mBAAmB;AAC9E,WAAO,KAAK,qBAAqB,gCAA+B;AAAA,EAClE;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,2BAA2B,EAAE,CAAC;AAAA,EACjD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,6BAA6B,CAAC,aAAa,oBAAoB,aAAa,gBAAgB,uBAAuB,yCAAyC,8BAA8B,yBAAyB,uBAAuB,oBAAoB,gCAAgC,WAAW;AAkB/S,IAAM,mCAAmC,IAAI,eAAe,kCAAkC;AAW9F,IAAM,mCAAN,MAAM,kCAAiC;AAAA,EACrC,OAAO,OAAO,SAAS,yCAAyC,mBAAmB;AACjF,WAAO,KAAK,qBAAqB,mCAAkC;AAAA,EACrE;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,8BAA8B,EAAE,CAAC;AAAA,EACpD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kCAAkC,CAAC;AAAA,IACzG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAiFH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA,mBAAmB;AAAA;AAAA,EAEnB,OAAO;AAAA;AAAA,EAEP,YAAY;AAAA;AAAA,EAEZ;AAAA;AAAA,EAEA,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,mBAAmB;AAAA;AAAA,EAEnB,YAAY;AAAA;AAAA,EAEZ,sBAAsB;AAAA;AAAA,EAEtB,6BAA6B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7B,+BAA+B;AAAA;AAAA,EAE/B,wCAAwC;AAAA;AAAA,EAExC,YAAY;AAAA;AAAA,EAEZ,wBAAwB;AAAA;AAAA,EAExB,2BAA2B;AAAA;AAAA,EAE3B,iCAAiC;AAAA;AAAA,EAEjC,kCAAkC;AAAA;AAAA,EAElC,mCAAmC;AAAA;AAAA,EAEnC,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,oCAAoC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpC,sBAAsB;AAAA;AAAA,EAEtB,YAAY,IAAI,aAAa;AAAA;AAAA,EAE7B;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,YAAY,OAAK;AAAA,EAAC;AAAA;AAAA,EAElB,IAAI,SAAS,UAAU;AACrB,SAAK,UAAU,KAAK,QAAQ;AAAA,EAC9B;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU,SAAS;AAAA,EACjC;AAAA,EACA,YAAY,IAAI,gBAAgB,IAAI;AAAA,EACpC,eAAe,KAAK,UAAU,KAAK,UAAU,cAAY,WAAW,SAAS,QAAQ,KAAK,IAAI,aAAW,QAAQ,QAAQ,CAAC,GAAG,UAAU,SAAS,QAAQ,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;AAAA,EACvK,iBAAiB,KAAK,aAAa,KAAK,IAAI,aAAW,UAAU,QAAQ,SAAS,CAAC,CAAC;AAAA;AAAA,EAEpF;AAAA,EACA,eAAe,IAAI,YAAY,IAAI;AAAA,IACjC,aAAa;AAAA,EACf,CAAC;AAAA;AAAA,EAED,uBAAuB,cAAc,CAAC,KAAK,aAAa,cAAc,KAAK,cAAc,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,OAAO,aAAa,MAAM,CAAC,EAAE,KAAK,uBAAuB,SAAS,kBAAkB,KAAK,uBAAuB,EAAE,CAAC;AAAA;AAAA,EAE1N,aAAa,IAAI,QAAQ;AAAA;AAAA,EAEzB;AAAA,EACA,YAAY,WAAW,mBAAmB,gBAAgB,WAAW,cAAc,gBAAgB;AACjG,SAAK,YAAY;AACjB,SAAK,oBAAoB;AACzB,SAAK,iBAAiB;AACtB,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,oBAAoB,cAAc;AAAA,EACzC;AAAA,EACA,oBAAoB,gBAAgB;AAClC,QAAI,CAAC,gBAAgB;AACnB;AAAA,IACF;AACA,eAAW,OAAO,4BAA4B;AAC5C,UAAI,eAAe,eAAe,GAAG,GAAG;AACtC,aAAK,GAAG,IAAI,eAAe,GAAG;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AAET,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,WAAW;AAC1B,WAAK,UAAU,gBAAgB,EAAE,UAAU,IAAI,4BAA4B;AAC3E,WAAK,UAAU,gBAAgB,EAAE,aAAa,QAAQ,cAAc;AAAA,IACtE,OAAO;AACL,cAAQ,MAAM,sEAAsE;AAAA,IACtF;AAEA,SAAK,UAAU,aAAa,KAAK,MAAM,CAAC,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,YAAU;AACzF,UAAI,QAAQ;AACV,aAAK,iBAAiB;AAEtB,YAAI,CAAC,KAAK,qBAAqB;AAC7B,eAAK,OAAO;AAAA,QACd;AAAA,MACF,OAAO;AAEL,YAAI,KAAK,kBAAkB;AACzB,eAAK,OAAO;AAAA,QACd;AAAA,MACF;AAAA,IACF,CAAC;AAED,SAAK,UAAU,aAAa,KAAK,KAAK,CAAC,GAAG,UAAU,OAAK;AACvD,WAAK,WAAW,KAAK,UAAU;AAW/B,UAAI,sBAAsB,KAAK,SAAS,QAAQ,EAAE,KAAK,uBAAuB,CAAC;AAC/E,aAAO,KAAK,SAAS,QAAQ,KAAK,IAAI,MAAM;AAE1C,mBAAW,MAAM;AAEf,gBAAM,UAAU,KAAK,SAAS,QAAQ;AAEtC,gBAAM,qBAAqB,QAAQ,KAAK,uBAAuB,CAAC;AAChE,gBAAM,aAAa,KAAK,UAAU;AAClC,cAAI,cAAc,KAAK,UAAU,aAAa,oBAAoB;AAGhE,kBAAM,uBAAuB,CAAC,uBAAuB,CAAC,KAAK,UAAU,YAAY,oBAAoB,OAAO,mBAAmB,KAAK;AAGpI,gBAAI,wBAAwB,CAAC,WAAW,cAAc,CAAC,QAAQ,KAAK,YAAU,KAAK,UAAU,YAAY,OAAO,OAAO,WAAW,YAAY,KAAK,CAAC,GAAG;AACrJ,yBAAW,cAAc,KAAK,uBAAuB,CAAC;AAAA,YACxD;AAEA,uBAAW,MAAM;AACf,mBAAK,iBAAiB;AAAA,YACxB,CAAC;AAAA,UACH;AAEA,gCAAsB;AAAA,QACxB,CAAC;AAAA,MACH,CAAC,CAAC;AAAA,IACJ,CAAC,CAAC,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU;AAG/C,SAAK,qBAAqB,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,wBAAsB;AAEzF,UAAI,KAAK,WAAW;AAClB,YAAI,oBAAoB;AACtB,eAAK,UAAU,gBAAgB,EAAE,UAAU,IAAI,oCAAoC;AAAA,QACrF,OAAO;AACL,eAAK,UAAU,gBAAgB,EAAE,UAAU,OAAO,oCAAoC;AAAA,QACxF;AAAA,MACF;AAAA,IACF,CAAC;AAED,SAAK,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AAC5E,UAAI,KAAK,UAAU,WAAW;AAC5B,aAAK,iBAAiB;AAAA,MACxB;AAAA,IACF,CAAC;AACD,SAAK,qBAAqB;AAC1B,SAAK,aAAa,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AAEjE,WAAK,kBAAkB,aAAa;AAAA,IACtC,CAAC;AAAA,EACH;AAAA,EACA,8BAA8B,OAAO;AACnC,SAAK,UAAU,KAAK,KAAK;AAAA,EAC3B;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA,EACA,8BAA8B;AAC5B,WAAO,KAAK,UAAU,YAAY,KAAK;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,OAAO;AAIpB,QAAI,MAAM,OAAO,MAAM,IAAI,WAAW,KAAK,KAAK,iCAAiC,MAAM,QAAQ,UAAU,MAAM,QAAQ,QAAQ;AAC7H,YAAM,gBAAgB;AAAA,IACxB;AACA,QAAI,KAAK,UAAU,YAAY,MAAM,OAAO,MAAM,QAAQ,SAAS;AAEjE,iBAAW,MAAM,KAAK,OAAO,CAAC;AAAA,IAChC;AAEA,QAAI,KAAK,8BAA8B,MAAM,QAAQ,YAAY,KAAK,OAAO;AAC3E,WAAK,OAAO,IAAI;AAChB,YAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,OAAO;AAClB,QAAI,MAAM,QAAQ,aAAa,MAAM,QAAQ,aAAa;AACxD,YAAM,yBAAyB,KAAK,UAAU,yBAAyB;AACvE,YAAM,QAAQ,KAAK,SAAS,QAAQ,EAAE,UAAU,UAAQ,KAAK,OAAO,sBAAsB;AAC1F,UAAI,UAAU,IAAI;AAChB,aAAK,yBAAyB;AAC9B,aAAK,mBAAmB,KAAK,SAAS,QAAQ,EAAE,KAAK,EAAE,gBAAgB;AACvE,aAAK,iBAAiB,aAAa,iBAAiB,MAAM;AAC1D,aAAK,kBAAkB,cAAc,aAAa,yBAAyB,sBAAsB;AAAA,MACnG;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,0BAA0B;AAC/B,SAAK,aAAa,SAAS,KAAK;AAChC,SAAK,kBAAkB,aAAa;AAAA,EACtC;AAAA,EACA,SAAS;AACP,SAAK,yBAAyB;AAC9B,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,aAAa,aAAa,KAAK,OAAO,WAAS,UAAU,KAAK,uBAAuB,GAAG,IAAI,MAAM,KAAK,0BAA0B,MAAS,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,EAAE;AAAA,EAC5L;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACP,QAAI,CAAC,KAAK,qBAAqB,CAAC,KAAK,UAAU,OAAO;AACpD;AAAA,IACF;AAGA,UAAM,QAAQ,KAAK,UAAU,MAAM;AACnC,UAAM,YAAY,MAAM;AAExB,SAAK,kBAAkB,cAAc,MAAM;AAC3C,UAAM,YAAY;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,OAAO;AACZ,SAAK,aAAa,SAAS,EAAE;AAC7B,QAAI,OAAO;AACT,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B,UAAI,KAAK,UAAU,UAAU;AAG3B,gBAAQ,MAAM,iHAAiH;AAAA,MACjI;AACA;AAAA,IACF;AAIA,SAAK,yBAAyB,KAAK,UAAU,UAAU;AACvD,QAAI,CAAC,KAAK,UAAU,UAAU,cAAc;AAC1C;AAAA,IACF;AACA,SAAK,UAAU,UAAU,aAAa,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,YAAU;AACzF,UAAI,wBAAwB;AAC5B,UAAI,KAAK,UAAU,UAAU;AAC3B,aAAK,KAAK,qCAAqC,KAAK,aAAa,SAAS,KAAK,aAAa,MAAM,WAAW,KAAK,0BAA0B,MAAM,QAAQ,KAAK,sBAAsB,GAAG;AACtL,cAAI,CAAC,UAAU,CAAC,MAAM,QAAQ,MAAM,GAAG;AACrC,qBAAS,CAAC;AAAA,UACZ;AACA,gBAAM,eAAe,KAAK,UAAU,QAAQ,IAAI,YAAU,OAAO,KAAK;AACtE,eAAK,uBAAuB,QAAQ,mBAAiB;AACnD,gBAAI,CAAC,OAAO,KAAK,OAAK,KAAK,UAAU,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,aAAa,KAAK,OAAK,KAAK,UAAU,YAAY,GAAG,aAAa,CAAC,GAAG;AAG5I,kBAAI,KAAK,qBAAqB;AAC5B,yBAAS,CAAC,GAAG,QAAQ,aAAa;AAAA,cACpC,OAAO;AACL,uBAAO,KAAK,aAAa;AAAA,cAC3B;AACA,sCAAwB;AAAA,YAC1B;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AACA,WAAK,yBAAyB;AAC9B,UAAI,uBAAuB;AACzB,aAAK,UAAU,UAAU,MAAM;AAAA,MACjC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,QAAI,CAAC,KAAK,qBAAqB,CAAC,KAAK,kBAAkB,eAAe;AACpE;AAAA,IACF;AACA,QAAI,UAAU,KAAK,kBAAkB;AACrC,QAAI,eAAe;AACnB,WAAO,WAAW,QAAQ,eAAe;AACvC,gBAAU,QAAQ;AAClB,UAAI,QAAQ,UAAU,SAAS,kBAAkB,GAAG;AAClD,uBAAe;AACf;AAAA,MACF;AAAA,IACF;AACA,QAAI,cAAc;AAChB,WAAK,kBAAkB,cAAc,MAAM,QAAQ,aAAa,cAAc;AAAA,IAChF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,yBAAyB;AACvB,QAAI,KAAK,WAAW;AAClB,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,2BAA2B;AACzB,SAAK,kBAAkB,gBAAgB,eAAe;AACtD,SAAK,kBAAkB,cAAc,gBAAgB,uBAAuB;AAAA,EAC9E;AAAA,EACA,OAAO,OAAO,SAAS,iCAAiC,mBAAmB;AACzE,WAAO,KAAK,qBAAqB,2BAA6B,kBAAkB,SAAS,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,aAAa,GAAM,kBAAkB,WAAW,CAAC,GAAM,kBAAkB,cAAc,CAAC,GAAM,kBAAkB,kCAAkC,CAAC,CAAC;AAAA,EACtT;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,IACrC,gBAAgB,SAAS,wCAAwC,IAAI,KAAK,UAAU;AAClF,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,+BAA+B,CAAC;AAC5D,QAAG,eAAe,UAAU,kCAAkC,CAAC;AAAA,MACjE;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY,GAAG;AAChE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AAAA,MACvE;AAAA,IACF;AAAA,IACA,WAAW,SAAS,+BAA+B,IAAI,KAAK;AAC1D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,GAAG,UAAU;AACjC,QAAG,YAAY,KAAK,GAAG,UAAU;AAAA,MACnC;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,oBAAoB,GAAG;AACxE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,oBAAoB,GAAG;AAAA,MAC1E;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,kBAAkB;AAAA,MAClB,MAAM;AAAA,MACN,WAAW;AAAA,MACX,cAAc;AAAA,MACd,qBAAqB;AAAA,MACrB,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX,qBAAqB;AAAA,MACrB,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,uCAAuC;AAAA,MACvC,WAAW;AAAA,MACX,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,gCAAgC;AAAA,MAChC,iCAAiC;AAAA,MACjC,kCAAkC;AAAA,MAClC,uBAAuB;AAAA,MACvB,mCAAmC;AAAA,MACnC,qBAAqB;AAAA,IACvB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,IACb;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa,WAAW,MAAM,yBAAwB;AAAA,MACtD,OAAO;AAAA,IACT,CAAC,CAAC,CAAC;AAAA,IACH,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,qBAAqB,EAAE,GAAG,CAAC,qBAAqB,EAAE,GAAG,CAAC,eAAe,EAAE,GAAG,CAAC,yBAAyB,EAAE,GAAG,CAAC,YAAY,IAAI,GAAG,2BAA2B,0BAA0B,GAAG,CAAC,GAAG,2BAA2B,kBAAkB,0BAA0B,kBAAkB,GAAG,SAAS,GAAG,CAAC,GAAG,6BAA6B,GAAG,CAAC,SAAS,yCAAyC,mBAAmB,4CAA4C,GAAG,SAAS,WAAW,iBAAiB,cAAc,sBAAsB,UAAU,GAAG,MAAM,GAAG,CAAC,gBAAgB,OAAO,GAAG,2BAA2B,GAAG,WAAW,SAAS,QAAQ,QAAQ,eAAe,aAAa,GAAG,CAAC,SAAS,6BAA6B,YAAY,MAAM,GAAG,MAAM,GAAG,CAAC,mBAAmB,IAAI,cAAc,SAAS,SAAS,2BAA2B,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,SAAS,sCAAsC,GAAG,MAAM,GAAG,CAAC,mBAAmB,4CAA4C,GAAG,yCAAyC,GAAG,UAAU,SAAS,WAAW,iBAAiB,cAAc,oBAAoB,GAAG,CAAC,YAAY,MAAM,GAAG,2BAA2B,GAAG,CAAC,mBAAmB,IAAI,cAAc,SAAS,GAAG,2BAA2B,GAAG,OAAO,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,oCAAoC,CAAC;AAAA,IAC9yC,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,gBAAgB,GAAG;AACtB,QAAG,UAAU,GAAG,SAAS,CAAC;AAC1B,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;AAC7C,QAAG,WAAW,GAAG,kDAAkD,GAAG,GAAG,gBAAgB,CAAC;AAC1F,QAAG,eAAe,GAAG,SAAS,GAAG,CAAC;AAClC,QAAG,WAAW,WAAW,SAAS,2DAA2D,QAAQ;AACnG,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,eAAe,MAAM,CAAC;AAAA,QAClD,CAAC,EAAE,SAAS,SAAS,yDAAyD,QAAQ;AACpF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,aAAa,MAAM,CAAC;AAAA,QAChD,CAAC,EAAE,QAAQ,SAAS,0DAA0D;AAC5E,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,OAAO,CAAC;AAAA,QACpC,CAAC;AACD,QAAG,aAAa;AAChB,QAAG,WAAW,GAAG,iDAAiD,GAAG,GAAG,eAAe,CAAC,EAAE,GAAG,4CAA4C,GAAG,GAAG,UAAU,EAAE;AAC3J,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa;AAChB,QAAG,UAAU,IAAI,aAAa;AAC9B,QAAG,aAAa;AAChB,QAAG,WAAW,IAAI,0CAA0C,GAAG,GAAG,OAAO,EAAE;AAC3E,QAAG,OAAO,IAAI,OAAO;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU;AACb,QAAG,WAAW,WAAc,gBAAgB,IAAI,KAAK,IAAI,UAAU,UAAU,IAAI,4BAA4B,CAAC,CAAC;AAC/G,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,QAAQ,IAAI,4BAA4B,CAAC;AACvD,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,IAAI,EAAE,eAAe,IAAI,YAAY,EAAE,eAAe,IAAI,gBAAgB;AACpG,QAAG,YAAY,cAAc,IAAI,SAAS;AAC1C,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,QAAQ,IAAI,SAAS;AACnC,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,CAAC,IAAI,yBAAyB,IAAI,SAAS,CAAC,IAAI,SAAS;AAC/E,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,QAAW,YAAY,IAAI,GAAG,IAAI,oBAAoB,CAAC;AAAA,MACvE;AAAA,IACF;AAAA,IACA,cAAc,CAAI,SAAY,MAAS,sBAAyB,iBAAoB,sBAAyB,eAAkB,aAAgB,SAAY,oBAAuB,YAAe,YAAe,SAAS;AAAA,IACzN,QAAQ,CAAC,m7EAAm7E;AAAA,IAC57E,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,wBAAwB;AAAA,QACtD,OAAO;AAAA,MACT,CAAC;AAAA,MACD,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,q1DAAq1D;AAAA,IACh2D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAU;AAAA,IACV,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAU;AAAA,IACV,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAU;AAAA,IACV,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,gCAAgC;AAAA,IACzC,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,4BAA4B,CAAC;AAAA,MAC3B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,8BAA8B,CAAC;AAAA,MAC7B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uCAAuC,CAAC;AAAA,MACtC,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gCAAgC,CAAC;AAAA,MAC/B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iCAAiC,CAAC;AAAA,MAChC,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kCAAkC,CAAC;AAAA,MACjC,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mCAAmC,CAAC;AAAA,MAClC,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,QAC1B,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,QAC1B,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,gCAAgC;AAAA,IACzC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAQH,IAAM,yBAAyB;AAC/B,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,OAAO,OAAO,SAAS,iCAAiC,mBAAmB;AACzE,WAAO,KAAK,qBAAqB,2BAA0B;AAAA,EAC7D;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,cAAc,CAAC,0BAA0B,+BAA+B,gCAAgC;AAAA,IACxG,SAAS,CAAC,cAAc,qBAAqB,iBAAiB,mBAAmB,eAAe,0BAA0B,kBAAkB,gBAAgB;AAAA,IAC5J,SAAS,CAAC,0BAA0B,+BAA+B,gCAAgC;AAAA,EACrG,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,cAAc,qBAAqB,iBAAiB,mBAAmB,eAAe,0BAA0B,kBAAkB,gBAAgB;AAAA,EAC9J,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,qBAAqB,iBAAiB,mBAAmB,eAAe,0BAA0B,kBAAkB,gBAAgB;AAAA,MAC5J,cAAc,CAAC,0BAA0B,+BAA+B,gCAAgC;AAAA,MACxG,SAAS,CAAC,0BAA0B,+BAA+B,gCAAgC;AAAA,IACrG,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}