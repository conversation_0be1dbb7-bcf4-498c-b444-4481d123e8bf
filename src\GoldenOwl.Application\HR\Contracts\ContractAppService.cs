﻿using GoldenOwl.HR.Contracts.DTO;
using GoldenOwl.Permissions;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoldenOwl.HR.WorkingSchedules;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace GoldenOwl.HR.Contracts
{
    public class ContractAppService : GoldenOwlAppService, IContractAppService
    {
        private readonly IContractManager _contractManager;
        private readonly IRepository<WorkingSchedule, Guid> _workingScheduleRepository;
        private readonly IContractRepository _contractDetailRepository;

        public ContractAppService(IRepository<WorkingSchedule, Guid> workingScheduleRepository, IContractManager contractManager, IContractRepository contractDetailRepository)
        {
            _workingScheduleRepository = workingScheduleRepository;
            _contractManager = contractManager;
            _contractDetailRepository = contractDetailRepository;
        }

        [Authorize(GoldenOwlPermissions.ContractsIndex)]
        public async Task<PagedResultDto<ContractDto>> GetListAsync(PagedResultRequestDto dto)
        {
            IQueryable<Contract> query = await _contractDetailRepository.WithDetailsContractAsync();
            int totalCount = await AsyncExecuter.CountAsync(query);
            List<Contract> contracts = await AsyncExecuter.ToListAsync(query.PageBy(dto));
            List<ContractDto> contractDtos =
                ObjectMapper.Map<List<Contract>, List<ContractDto>>(contracts);

            return new PagedResultDto<ContractDto>(totalCount, contractDtos);
        }

        [Authorize(GoldenOwlPermissions.ContractsIndex)]
        public async Task<ContractDto> GetAsync(Guid id)
        {
            var contract = (await _contractDetailRepository.WithDetailsContractAsync())
                           .SingleOrDefault(contract1 => contract1.Id == id) ??
                           throw new EntityNotFoundException(typeof(Contract), id);
            var result = ObjectMapper.Map<Contract, ContractDto>(contract);
            return result;
        }

        [Authorize(GoldenOwlPermissions.ContractManagement)]
        public async Task PutActivateContractAsync(Guid id)
        {
            await _contractManager.ActivateContract(id);
        }

        [Authorize(GoldenOwlPermissions.ContractManagement)]
        public async Task PutExpireContractAsync(Guid id)
        {
            await _contractManager.ExpireContract(id);
        }

        [Authorize(GoldenOwlPermissions.ContractManagement)]
        public virtual async Task PutCancelContractAsync(Guid id, CancelContractDto cancelContractDto)
        {
            await _contractManager.CancelContract(id, cancelContractDto.Reason);
        }

        [Authorize(GoldenOwlPermissions.ContractManagement)]
        public virtual async Task<Guid> CreateAsync(CreateUpdateContractDto dto)
        {
            List<ContractWorkingSchedule> contractWorkingSchedules = null;
            if (dto.ContractWorkingSchedules is not null && dto.ContractWorkingSchedules.Any())
            {
                contractWorkingSchedules = new List<ContractWorkingSchedule>();
                foreach (CreateUpdateContractWorkingScheduleDto schedule in dto.ContractWorkingSchedules)
                {
                    await _workingScheduleRepository.GetAsync(schedule.WorkingScheduleId);
                    contractWorkingSchedules.Add(new ContractWorkingSchedule(GuidGenerator.Create() ,schedule.WorkingScheduleId,
                        schedule.From, schedule.To));
                }
            }

            return await _contractManager.CreateContract(dto.Name, dto.Type, dto.Start, dto.End, dto.EmployeeId,
                dto.WorkEntrySource, contractWorkingSchedules, dto.JobTitleId, dto.SalaryStructureId);
        }

        [Authorize(GoldenOwlPermissions.ContractManagement)]
        public virtual async Task<Guid> UpdateAsync(Guid id, CreateUpdateContractDto dto)
        {
            List<ContractWorkingSchedule> contractWorkingSchedules = null;
            if (dto.ContractWorkingSchedules is not null && dto.ContractWorkingSchedules.Any())
            {
                contractWorkingSchedules = new List<ContractWorkingSchedule>();
                foreach (CreateUpdateContractWorkingScheduleDto schedule in dto.ContractWorkingSchedules)
                {
                    await _workingScheduleRepository.GetAsync(schedule.WorkingScheduleId);
                    contractWorkingSchedules.Add(new ContractWorkingSchedule(GuidGenerator.Create(),schedule.WorkingScheduleId, schedule.From,
                        schedule.To));
                }
            }

            return await _contractManager.UpdateContract(id, dto.Name, dto.Type, dto.Start, dto.End, dto.EmployeeId,
                dto.WorkEntrySource, contractWorkingSchedules, dto.SalaryStructureId);
        }
    }
}