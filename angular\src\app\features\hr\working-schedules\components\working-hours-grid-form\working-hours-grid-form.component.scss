:host {
  display: block;
  border: 1px solid var(--mat-table-header-headline-color);
  border-radius: 1rem;
  margin-top: 0.5rem;

  --mat-table-background-color: var(--mdc-elevated-card-container-color);
}

.top-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;

  p {
    margin: 0;

    font-weight: 500;
    font-size: 1.25rem;
  }
}

:host ::ng-deep {
  ttwr-grid .top-section {
    display: none;
  }
}
