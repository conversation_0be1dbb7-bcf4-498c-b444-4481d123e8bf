import { Component, effect, ElementRef, inject, input } from '@angular/core';
// @ts-ignore
import ApexTree from 'apextree';

export type OrgTreeData = Parameters<typeof ApexTree.prototype.render>[0];

@Component({
  selector: 'app-org-tree',
  standalone: true,
  template: ``,
})
export class OrgTreeComponent {
  private elementRef = inject(ElementRef);

  public data = input.required<OrgTreeData>();

  private tree = new ApexTree(this.elementRef.nativeElement, {
    width: '100%',
    fontSize: '6px',
    enableExpandCollapse: true,
  });

  constructor() {
    effect(() => {
      this.tree.render(this.data());
    });
  }
}
