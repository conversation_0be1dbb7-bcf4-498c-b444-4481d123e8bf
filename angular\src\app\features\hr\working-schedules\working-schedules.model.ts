import { fields, model, takeOptions } from '@ttwr-framework/ngx-main-visuals';
import { DayOfWeek } from './day-of-week.enum';

export const workingHour = model({
  id: fields.number(),
  day: fields.select('single', takeOptions(DayOfWeek)),
  from: fields.datetime(),
  to: fields.datetime(),
  expectedAttendance: fields.datetime(),
})

export const workingSchedules = model({
  id: fields.text(),
  name: fields.text(),
  averageHourNumber: fields.number(),
  workingHours: fields.list(workingHour.getProps()),
})

