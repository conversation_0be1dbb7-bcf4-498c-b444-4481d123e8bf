@let type = type$ | async;
@let label = (field().label ?? field().name) | i18n;

@if (
  type === fieldType.String ||
  type === fieldType.Int ||
  type === fieldType.Decimal
) {
  <mat-form-field>
    <mat-label>{{ label }}</mat-label>
    <input [type]="type === fieldType.String ? 'text': 'number'" matInput [formControl]="field().control"/>
    <mat-error>{{ 'this field is required' | i18n }}</mat-error>
  </mat-form-field>
} @else if (type === fieldType.Bool) {
  <mat-checkbox [formControl]="field().control">
    {{ label }}
  </mat-checkbox>
} @else if (type === fieldType.DateOnly) {
  <mat-form-field>
    <mat-label>{{ label }}</mat-label>
    <input
      matInput
      [matDatepicker]="picker"
      [formControl]="field().control"
    >
    <mat-datepicker-toggle
      matIconSuffix
      [for]="picker"
    />
    <mat-datepicker #picker />
    <mat-error>{{ 'this field is required' | i18n }}</mat-error>
  </mat-form-field>

} @else if (type === fieldType.DateTime) {
  <mat-form-field>
    <mat-label>{{ label }}</mat-label>
    <input
      matInput
      [ttwrDatetimepicker]="picker"
      [formControl]="field().control"
    >
    <ttwr-datetimepicker-toggle
      matIconSuffix
      [for]="picker"
    />
    <ttwr-datetimepicker #picker />
    <mat-error>{{ 'this field is required' | i18n }}</mat-error>
  </mat-form-field>
} @else {
  <div [formGroup]="selectionForm">
    <div formArrayName="selection">
      @for (control of selectionForm.controls.selection.controls; track $index) {
        <mat-form-field class="array-field">
          <mat-label>{{ 'Option' | i18n }}</mat-label>
          <input matInput [formControl]="control" />
          <mat-error>{{ 'this field is required' | i18n }}</mat-error>
          <button type="button" (click)="removeForm($index)" matSuffix mat-icon-button>
            <mat-icon>delete</mat-icon>
          </button>
          @if ($first) {
            <mat-hint>
              {{ 'The default option' }}
            </mat-hint>
          }
        </mat-form-field>
      }
    </div>
    <button mat-mini-fab (click)="addForm()" type="button">
      <mat-icon>add</mat-icon>
    </button>
  </div>
}
