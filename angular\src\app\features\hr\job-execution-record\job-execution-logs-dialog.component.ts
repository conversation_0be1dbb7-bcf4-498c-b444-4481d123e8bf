import { Component, inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogTitle } from '@angular/material/dialog';
import { arrayMap, LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { JobExecutionLogService, JobExecutionLogType } from '@proxy/job-execution-logs';
import { toSignal } from '@angular/core/rxjs-interop';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'app-job-execution-logs-dialog',
  standalone: true,
  imports: [
    MatDialogTitle,
    LanguagePipe,
    MatDialogContent,
    DatePipe
  ],
  template: `
    <h1 mat-dialog-title>
      {{ '::JobExecutionLogs' | i18n }}
    </h1>
    <mat-dialog-content>
      <table class="ttwr-basic-table">
        <thead>
        <tr>
          <th>{{ '::GoldenOwl:JobExecutionLogType' | i18n }}</th>
          <th>{{ '::GoldenOwl:Message' | i18n }}</th>
          <th>{{ '::GoldenOwl:CreationTime' | i18n }}</th>
        </tr>
        </thead>
        <tbody>
          @for (record of records(); track $index) {
            <tr>
              <td>{{ record.logType }}</td>
              <td>{{ record.logMessage }}</td>
              <td>{{ record.creationTime | date: 'yyyy-MM-dd hh:mm a' }}</td>
            </tr>
          }
        </tbody>
      </table>
    </mat-dialog-content>
  `,
})
export class JobExecutionLogsDialogComponent {
  private jobExecutionLog = inject(JobExecutionLogService);
  private data = inject<{ id: string }>(MAT_DIALOG_DATA);

  protected records = toSignal(
    this.jobExecutionLog.getExecutionLogs(this.data.id).pipe(
      arrayMap(log => ({
        ...log,
        logType: JobExecutionLogType[log.logType],
      }))
    )
  )
}
