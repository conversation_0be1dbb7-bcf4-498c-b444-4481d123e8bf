import { Component, inject } from '@angular/core';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import {
  SalaryRuleFormComponent
} from '../../../../salary-rules/components/salary-rule-form/salary-rule-form.component';

@Component({
  selector: 'app-new-salary-rule-dialog',
  standalone: true,
  imports: [
    LanguagePipe,
    MatDialogTitle,
    MatDialogContent,
    SalaryRuleFormComponent,
  ],
  template: `
    <h1 mat-dialog-title>
      {{ '::CreateSalaryRule' | i18n }}
    </h1>
    <mat-dialog-content>
      <app-salary-rule-form (finish)="onFinish($event)" />
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-grid-template-columns: 1fr 1fr;
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class NewSalaryRuleDialogComponent {
  private dialogRef = inject(MatDialogRef);

  onFinish(newItemId: string) {
    this.dialogRef.close(newItemId);
  }
}
