import {
  RestService
} from "./chunk-VITQ7ATO.js";
import {
  Injectable,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-7YC2NMXI.js";

// node_modules/@abp/ng.feature-management/fesm2022/abp-ng.feature-management-proxy.mjs
var FeaturesService = class _FeaturesService {
  constructor(restService) {
    this.restService = restService;
    this.apiName = "AbpFeatureManagement";
    this.delete = (providerName, providerKey) => this.restService.request({
      method: "DELETE",
      url: "/api/feature-management/features",
      params: {
        providerName,
        providerKey
      }
    }, {
      apiName: this.apiName
    });
    this.get = (providerName, providerKey) => this.restService.request({
      method: "GET",
      url: "/api/feature-management/features",
      params: {
        providerName,
        providerKey
      }
    }, {
      apiName: this.apiName
    });
    this.update = (providerName, providerKey, input) => this.restService.request({
      method: "PUT",
      url: "/api/feature-management/features",
      params: {
        providerName,
        providerKey
      },
      body: input
    }, {
      apiName: this.apiName
    });
  }
  static {
    this.ɵfac = function FeaturesService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _FeaturesService)(ɵɵinject(RestService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _FeaturesService,
      factory: _FeaturesService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(FeaturesService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RestService
  }], null);
})();
var index = Object.freeze({
  __proto__: null
});

export {
  FeaturesService,
  index
};
//# sourceMappingURL=chunk-DSZLGUYZ.js.map
