{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/sk.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    v = val.toString().replace(/^[^.]*\\.?/, '').length;\n  if (i === 1 && v === 0) return 1;\n  if (i === Math.floor(i) && i >= 2 && i <= 4 && v === 0) return 3;\n  if (!(v === 0)) return 4;\n  return 5;\n}\nexport default [\"sk\", [[\"AM\", \"PM\"], u, u], u, [[\"n\", \"p\", \"u\", \"s\", \"š\", \"p\", \"s\"], [\"ne\", \"po\", \"ut\", \"st\", \"št\", \"pi\", \"so\"], [\"nedeľa\", \"pondelok\", \"utorok\", \"streda\", \"štvrtok\", \"piatok\", \"sobota\"], [\"ne\", \"po\", \"ut\", \"st\", \"št\", \"pi\", \"so\"]], u, [[\"j\", \"f\", \"m\", \"a\", \"m\", \"j\", \"j\", \"a\", \"s\", \"o\", \"n\", \"d\"], [\"jan\", \"feb\", \"mar\", \"apr\", \"máj\", \"jún\", \"júl\", \"aug\", \"sep\", \"okt\", \"nov\", \"dec\"], [\"januára\", \"februára\", \"marca\", \"apríla\", \"mája\", \"júna\", \"júla\", \"augusta\", \"septembra\", \"októbra\", \"novembra\", \"decembra\"]], [[\"j\", \"f\", \"m\", \"a\", \"m\", \"j\", \"j\", \"a\", \"s\", \"o\", \"n\", \"d\"], [\"jan\", \"feb\", \"mar\", \"apr\", \"máj\", \"jún\", \"júl\", \"aug\", \"sep\", \"okt\", \"nov\", \"dec\"], [\"január\", \"február\", \"marec\", \"apríl\", \"máj\", \"jún\", \"júl\", \"august\", \"september\", \"október\", \"november\", \"december\"]], [[\"pred Kr.\", \"po Kr.\"], u, [\"pred Kristom\", \"po Kristovi\"]], 1, [6, 0], [\"d. M. y\", u, \"d. MMMM y\", \"EEEE d. MMMM y\"], [\"H:mm\", \"H:mm:ss\", \"H:mm:ss z\", \"H:mm:ss zzzz\"], [\"{1} {0}\", \"{1}, {0}\", u, u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"e\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"euro\", {\n  \"AUD\": [u, \"$\"],\n  \"BRL\": [u, \"R$\"],\n  \"BYN\": [u, \"р.\"],\n  \"CAD\": [u, \"$\"],\n  \"CNY\": [u, \"¥\"],\n  \"GBP\": [u, \"£\"],\n  \"HKD\": [u, \"$\"],\n  \"ILS\": [\"NIS\", \"₪\"],\n  \"INR\": [u, \"₹\"],\n  \"JPY\": [u, \"¥\"],\n  \"KRW\": [u, \"₩\"],\n  \"NZD\": [u, \"$\"],\n  \"PHP\": [u, \"₱\"],\n  \"RUR\": [u, \"р.\"],\n  \"TWD\": [u, \"NT$\"],\n  \"USD\": [u, \"$\"],\n  \"VND\": [u, \"₫\"],\n  \"XXX\": []\n}, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACnB,QAAM,IAAI,KACR,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,GAC5B,IAAI,IAAI,SAAS,EAAE,QAAQ,aAAa,EAAE,EAAE;AAC9C,MAAI,MAAM,KAAK,MAAM,EAAG,QAAO;AAC/B,MAAI,MAAM,KAAK,MAAM,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,EAAG,QAAO;AAC/D,MAAI,EAAE,MAAM,GAAI,QAAO;AACvB,SAAO;AACT;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,GAAG,CAAC,UAAU,YAAY,UAAU,UAAU,WAAW,UAAU,QAAQ,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,CAAC,WAAW,YAAY,SAAS,UAAU,QAAQ,QAAQ,QAAQ,WAAW,aAAa,WAAW,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,CAAC,UAAU,WAAW,SAAS,SAAS,OAAO,OAAO,OAAO,UAAU,aAAa,WAAW,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,YAAY,QAAQ,GAAG,GAAG,CAAC,gBAAgB,aAAa,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,GAAG,aAAa,gBAAgB,GAAG,CAAC,QAAQ,WAAW,aAAa,cAAc,GAAG,CAAC,WAAW,YAAY,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,WAAW,cAAc,KAAK,GAAG,OAAO,KAAK,QAAQ;AAAA,EACzmC,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC;AACV,GAAG,OAAO,MAAM;", "names": []}