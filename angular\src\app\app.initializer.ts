import { ABP, eLayoutType, LocalizationService, RoutesService } from '@abp/ng.core';
import { inject } from '@angular/core';
import { abpRoutes as selfServiceAbpRoutes } from './features/self-service/self-service.routes';
import { abpRoutes as hrAbpRoutes } from './features/hr/hr.routes';
import { abpRoutes as settingsAbpRoutes } from './features/settings/settings.routes';
import { abpRoutes as payrollManagementAbpRoutes } from './features/payroll-management/payroll-management.routes';
import { abpRoutes as financeAbpRoutes } from './features/finance/finance.routes';
import {UserService} from '@shared/services/user.service';

export const appInitializer = () => {
  const routesService = inject(RoutesService);
  const localization = inject(LocalizationService);
  const userService = inject(UserService);

  localization.languageChange$.subscribe(() => {
    location.reload();
  })

  return () => {
    routesService.add([
      {
        path: '/',
        name: '::Menu:Home',
        iconClass: 'home',
        order: 1,
      },

      // features routes
      {
        path: '/notifications',
        name: '::GoldenOwl:Notifications',
        iconClass: 'notifications',
        order: 2,
      },
      ...hrAbpRoutes,
      ...settingsAbpRoutes,
      ...payrollManagementAbpRoutes,
      ...financeAbpRoutes,

      ].map(addApplicationLayout),
    );

    if(userService.isEmployee()) {
      routesService.add([
        ...selfServiceAbpRoutes
      ]);
    }

    routesService.remove(['AbpSettingManagement::Settings']);
  };
}

// add layout as application
function addApplicationLayout(route: ABP.Route): ABP.Route {
  return {
    ...route,
    layout: eLayoutType.application,
  }
}
