using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoldenOwl.FN.AccountBalances.DTO;
using GoldenOwl.FN.Accounts;
using GoldenOwl.FN.Currencies;
using GoldenOwl.FN.FinancialPeriods;
using GoldenOwl.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace GoldenOwl.FN.AccountBalances;

public class AccountBalanceAppService : GoldenOwlAppService, IAccountBalanceAppService
{
    private readonly IRepository<AccountBalance, Guid> _accountBalanceRepository;
    private readonly IRepository<Currency, string> _currencyRepository;
    private readonly IRepository<FinancialPeriod, Guid> _financialPeriodRepository;
    private readonly IRepository<Account, Guid> _accountRepository;

    public AccountBalanceAppService(IRepository<AccountBalance, Guid> accountBalanceRepository,
        IRepository<Currency, string> currencyRepository, IRepository<FinancialPeriod, Guid> financialPeriodRepository,
        IRepository<Account, Guid> accountRepository)
    {
        _accountBalanceRepository = accountBalanceRepository;
        _currencyRepository = currencyRepository;
        _financialPeriodRepository = financialPeriodRepository;
        _accountRepository = accountRepository;
    }

    [Authorize(GoldenOwlPermissions.AccountIndex)]
    public async Task<PagedResultDto<AccountBalanceDto>> GetListAsync(PagedResultRequestDto input, Guid? accountId,
        Guid? financialPeriodId)
    {
        IQueryable<AccountBalance> query = await _accountBalanceRepository.WithDetailsAsync(a => a.Account);

        if (accountId != null && accountId != Guid.Empty)
        {
            query = query.Where(c => c.AccountId == accountId);
        }

        if (financialPeriodId != null && financialPeriodId != Guid.Empty)
        {
            query = query.Where(c => c.FinancialPeriodId == financialPeriodId);
        }
        else
        {
            var lastFinancialPeriod = await _financialPeriodRepository.GetQueryableAsync();

            FinancialPeriod? financialPeriod = lastFinancialPeriod.OrderByDescending(f => f.StartDate).FirstOrDefault();
            if (financialPeriod == null)
            {
                throw new UserFriendlyException(L[GoldenOwlApplicationErrorCodes.ThereIsNoFinancialPeriodExists]);
            }

            query = query.Where(c => c.FinancialPeriodId == financialPeriod.Id);
        }

        int totalCount = await AsyncExecuter.CountAsync(query);
        List<AccountBalance> accountBalances = await AsyncExecuter.ToListAsync(query.PageBy(input));
        List<AccountBalanceDto> accountBalancesDto =
            ObjectMapper.Map<List<AccountBalance>, List<AccountBalanceDto>>(accountBalances);

        return new PagedResultDto<AccountBalanceDto>(totalCount, accountBalancesDto);
    }

    [Authorize(GoldenOwlPermissions.AccountIndex)]
    public async Task<AccountBalanceDto> GetBalanceAsync(Guid accountId, string? currencyCode)
    {
        var lastFinancialPeriod = await _financialPeriodRepository.GetQueryableAsync();

        FinancialPeriod? financialPeriod = lastFinancialPeriod.OrderByDescending(f => f.StartDate).FirstOrDefault();
        if (financialPeriod == null)
        {
            throw new UserFriendlyException(L[GoldenOwlApplicationErrorCodes.ThereIsNoFinancialPeriodExists]);
        }

        Account account = await _accountRepository.GetAsync(accountId);

        IQueryable<AccountBalance> query = await
            _accountBalanceRepository.WithDetailsAsync(a => a.Account);
        query = query.Where(c => c.AccountId == accountId && c.FinancialPeriodId == financialPeriod.Id);

        if (!string.IsNullOrWhiteSpace(currencyCode))
        {
            await _currencyRepository.GetAsync(currencyCode);
            if (account.CurrencyCode != currencyCode)
            {
                throw new UserFriendlyException(L[GoldenOwlApplicationErrorCodes.InvalidAccountCurrency]);
            }

            query = query.Where(c => c.CurrencyCode == currencyCode);
        }
        else
        {
            query = query.Where(c => c.CurrencyCode == GoldenOwlConsts.DefaultCurrencyCode);
        }

        AccountBalance accountBalance = query.FirstOrDefault();

        return ObjectMapper.Map<AccountBalance, AccountBalanceDto>(accountBalance);
    }
}