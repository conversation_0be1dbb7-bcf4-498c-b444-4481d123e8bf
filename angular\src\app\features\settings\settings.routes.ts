import { Routes } from '@angular/router';
import type { ABP } from '@abp/ng.core';
import { AttachmentTypesComponent } from './attachment-types/attachment-types.component';
import { ExtraFieldsComponent } from './extra-fields/extra-fields.component';

export const routes: Routes = [
  {
    path: 'attachment-types',
    component: AttachmentTypesComponent,
  },
  {
    path: 'extra-fields',
    component: ExtraFieldsComponent,
  }
]

const settingsRoute = '::Menu:Settings';

export const abpRoutes: ABP.Route[] = [
  {
    group: 'Settings',
    name: settingsRoute,
    iconClass: 'settings',
    order: 5,
  },
  {
    path: '/settings/attachment-types',
    name: '::AttachmentTypes',
    iconClass: 'description',
    parentName: settingsRoute,
    requiredPolicy: 'GoldenOwl.AttachmentType.AttachmentTypeManagement',
  },
  {
    path: '/settings/extra-fields',
    name: '::ExtraFieldDefinitions',
    iconClass: 'grid_view',
    parentName: settingsRoute,
    requiredPolicy: 'GoldenOwl.ExtraFieldDefinition.ExtraFieldDefinition',
  },
]
