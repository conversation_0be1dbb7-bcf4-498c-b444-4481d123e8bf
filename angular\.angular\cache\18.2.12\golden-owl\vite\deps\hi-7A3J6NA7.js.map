{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/hi.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val));\n  if (i === 0 || n === 1) return 1;\n  return 5;\n}\nexport default [\"hi\", [[\"am\", \"pm\"], u, u], u, [[\"र\", \"सो\", \"मं\", \"बु\", \"गु\", \"शु\", \"श\"], [\"रवि\", \"सोम\", \"मंगल\", \"बुध\", \"गुरु\", \"शुक्र\", \"शनि\"], [\"रविवार\", \"सोमवार\", \"मंगलवार\", \"बुधवार\", \"गुरुवार\", \"शुक्रवार\", \"शनिवार\"], [\"र\", \"सो\", \"मं\", \"बु\", \"गु\", \"शु\", \"श\"]], u, [[\"ज\", \"फ़\", \"मा\", \"अ\", \"म\", \"जू\", \"जु\", \"अ\", \"सि\", \"अ\", \"न\", \"दि\"], [\"जन॰\", \"फ़र॰\", \"मार्च\", \"अप्रैल\", \"मई\", \"जून\", \"जुल॰\", \"अग॰\", \"सित॰\", \"अक्तू॰\", \"नव॰\", \"दिस॰\"], [\"जनवरी\", \"फ़रवरी\", \"मार्च\", \"अप्रैल\", \"मई\", \"जून\", \"जुलाई\", \"अगस्त\", \"सितंबर\", \"अक्तूबर\", \"नवंबर\", \"दिसंबर\"]], u, [[\"ईसा-पूर्व\", \"ईस्वी\"], u, [\"ईसा-पूर्व\", \"ईसवी सन\"]], 0, [0, 0], [\"d/M/yy\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1}, {0}\", u, \"{1} को {0}\", u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##,##0.###\", \"#,##,##0%\", \"¤#,##,##0.00\", \"[#E0]\"], \"INR\", \"₹\", \"भारतीय रुपया\", {\n  \"BYN\": [u, \"р.\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"PHP\": [u, \"₱\"],\n  \"RON\": [u, \"लेई\"],\n  \"THB\": [\"฿\"],\n  \"TWD\": [\"NT$\"]\n}, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACnB,QAAM,IAAI,KACR,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC;AAC9B,MAAI,MAAM,KAAK,MAAM,EAAG,QAAO;AAC/B,SAAO;AACT;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,OAAO,QAAQ,OAAO,QAAQ,SAAS,KAAK,GAAG,CAAC,UAAU,UAAU,WAAW,UAAU,WAAW,YAAY,QAAQ,GAAG,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,MAAM,KAAK,KAAK,IAAI,GAAG,CAAC,OAAO,QAAQ,SAAS,UAAU,MAAM,OAAO,QAAQ,OAAO,QAAQ,UAAU,OAAO,MAAM,GAAG,CAAC,SAAS,UAAU,SAAS,UAAU,MAAM,OAAO,SAAS,SAAS,UAAU,WAAW,SAAS,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,aAAa,OAAO,GAAG,GAAG,CAAC,aAAa,SAAS,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,WAAW,YAAY,gBAAgB,GAAG,CAAC,UAAU,aAAa,eAAe,gBAAgB,GAAG,CAAC,YAAY,GAAG,cAAc,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,gBAAgB,aAAa,gBAAgB,OAAO,GAAG,OAAO,KAAK,gBAAgB;AAAA,EACz4B,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,GAAG;AAAA,EACX,OAAO,CAAC,KAAK;AACf,GAAG,OAAO,MAAM;", "names": []}