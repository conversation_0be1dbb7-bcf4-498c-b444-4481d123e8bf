﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoldenOwl.AttachmentTypes.DTO;
using GoldenOwl.DomainServices.Attachments;
using GoldenOwl.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;

namespace GoldenOwl.AttachmentTypes;

public class AttachmentTypeAppService : GoldenOwlAppService, IAttachmentTypeAppService
{
    private readonly IRepository<AttachmentType> _attachmentTypeRepository;
    private readonly IFileTypeInspector _fileTypeInspector;
    private readonly IDataFilter _dataFilter;
    private readonly IAttachmentTypeManager _attachmentTypeManager;

    public AttachmentTypeAppService(IRepository<AttachmentType> attachmentTypeRepository,
        IFileTypeInspector fileTypeInspector, IDataFilter dataFilter, IAttachmentTypeManager attachmentTypeManager)
    {
        _attachmentTypeRepository = attachmentTypeRepository;
        _fileTypeInspector = fileTypeInspector;
        _dataFilter = dataFilter;
        _attachmentTypeManager = attachmentTypeManager;
    }

    public async Task<List<AttachmentTypeDto>> GetListByEntityTypeAsyn(EntityType entityType)
    {
        using (_dataFilter.Disable<ISoftDelete>())
        {
            var query = await _attachmentTypeRepository.GetQueryableAsync();
            var data = await AsyncExecuter
                .ToListAsync(
                    query
                        .Where(x => x.EntityType == entityType)
                        .OrderByDescending(x => x.CreationTime)
                );
            var response = ObjectMapper.Map<List<AttachmentType>, List<AttachmentTypeDto>>(data);
            return response;
        }
    }

    [Authorize(GoldenOwlPermissions.AttachmentManagement)]
    public async Task<PagedResultDto<AttachmentTypeDto>> GetListAsync(PagedResultRequestDto input)
    {
        var totalCount = await _attachmentTypeRepository.GetCountAsync();
        var attachmentTypes = await _attachmentTypeRepository
            .GetPagedListAsync(
                input.SkipCount,
                input.MaxResultCount,
                "CreationTime DESC"
            );
        var data =
            ObjectMapper.Map<List<AttachmentType>, List<AttachmentTypeDto>>
                (attachmentTypes);
        return new PagedResultDto<AttachmentTypeDto>(totalCount, data);
    }

    [Authorize(GoldenOwlPermissions.AttachmentTypeManagement)]
    public virtual async Task<Guid> CreateAsync(AttachmentTypeDto dto)
    {
        var id = await _attachmentTypeManager.CreateAsync
        (
            dto.EntityType,
            dto.Name,
            dto.AllowedSize,
            dto.AllowedExtensions
                .Select(x =>
                    new AttachmentExtension(x.Value))
                .ToList()
        );
        
        return id;
    }

    [Authorize(GoldenOwlPermissions.AttachmentTypeManagement)]
    public async Task DeleteAsync(Guid id)
    {
        var attachment = await _attachmentTypeRepository.SingleAsync(x => x.Id == id);
        await _attachmentTypeRepository.DeleteAsync(attachment);
    }

    [Authorize(GoldenOwlPermissions.AttachmentTypeManagement)]
    public async Task UpdateAsync(AttachmentTypeDto dto)
    {
        await _attachmentTypeManager.UpdateAsync
        (
            dto.Id,
            dto.EntityType,
            dto.Name,
            dto.AllowedSize,
            dto.AllowedExtensions.Select(x =>
                    new AttachmentExtension(x.Value))
                .ToList()
        );
    }

    public Task<IEnumerable<string>> GetAvailableExtensionAsync()
    {
        return Task.FromResult(_fileTypeInspector.GetAllExtensions());
    }
}