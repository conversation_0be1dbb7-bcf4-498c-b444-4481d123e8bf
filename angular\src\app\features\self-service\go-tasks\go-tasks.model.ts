import { fields, model, Option, takeOptions } from '@ttwr-framework/ngx-main-visuals';
import { of } from 'rxjs';
import { GoTaskState } from '@proxy/go-tasks';

export const tasks = model({
  id: fields.text(),
  name: fields.text(),
  creationTime: fields.datetime(),
  state: fields.select('single', takeOptions(GoTaskState)),
  creatorId: fields.selectFetch('single', () => of<Option<string>[]>([])),
  bindingModelId: fields.selectFetch('single', () => of<Option<string>[]>([])),
});

export const task = model({
  id: fields.text(),
  name: fields.text(),
  creationTime: fields.datetime(),
  bindingModelId: fields.text(),
  state: fields.text(),
});

export const request = model({
  checkIn: fields.datetime(),
  fixAttendanceFrom: fields.datetime(),
  checkOut: fields.datetime(),
  fixAttendanceTo: fields.datetime(),
});
