import { Component, DestroyRef, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  MAT_DIALOG_DATA,
  MatDialogContent,
  MatDialogRef,
  MatDialogTitle,
} from '@angular/material/dialog';
import {
  AlertService,
  LanguagePipe,
  LOADING,
  requiredValidator,
  TtwrFormComponent,
} from '@ttwr-framework/ngx-main-visuals';
import { finalize, of } from 'rxjs';
import { EmployeeCustodyService } from '@proxy/hr/employee-custodies';
import { custody } from '../custody.model';
import { clearDate } from '@shared';

@Component({
  selector: 'app-custody-update-dialog',
  standalone: true,
  imports: [TtwrFormComponent, MatDialogTitle, MatDialogContent, LanguagePipe],
  template: `
    <h1 mat-dialog-title>{{ '::Update' | i18n }}</h1>
    <mat-dialog-content>
      <ttwr-form [config]="config" />
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class CustodyUpdateDialogComponent {
  private custody = inject(EmployeeCustodyService);
  private alert = inject(AlertService);
  private dialogRef = inject(MatDialogRef);
  private destroyRef = inject(DestroyRef);
  private data = inject<DialogData>(MAT_DIALOG_DATA);
  private loading = inject(LOADING);

  protected config = custody
    .exclude({
      employeeId: true,
      description: true,
      receiptDate: true,
    })

    .form({
      submitAction: {
        onSubmit: (body) => {
          this.loading.set(true);

          this.custody
            .putReleaseEmployeeCustodyByIdAndDto(this.data.id, {
              ...body,
              releaseDate: clearDate(body.releaseDate),
            })
            .pipe(
              finalize(() => this.loading.set(false)),
              takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(() => {
              this.alert.success('::UpdatedSuccessfully');
              this.dialogRef.close(true);
            });
        },
      },
      viewFunc: () => of(this.data),
      fields: {
        employeeName:{
          label: '::GoldenOwl:Employee',
          readonlySignal: signal(true),
        },
        releaseDate: {
          label: '::EmployeeCustody:ReleaseDate',
          validators: [requiredValidator],
        },
        releaseReason: {
          label: '::EmployeeCustody:ReleaseReason',
        },
      },
    });
}

interface DialogData {
  id: string;
  releaseDate: string;
  releaseReason: string;
}
