import { Component, DestroyRef, inject } from '@angular/core';
import { AttendanceService } from '@proxy/hr/attendances';
import {
  AlertService,
  LanguagePipe,
  LOADING,
  requiredValidator,
  TtwrFormComponent
} from '@ttwr-framework/ngx-main-visuals';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { finalize, of } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { attendance } from '../attendance.model';
import { AttendanceDto } from '@proxy/hr/attendances/dto';

@Component({
  selector: 'app-attendance-update-dialog',
  standalone: true,
  imports: [
    LanguagePipe,
    MatDialogContent,
    MatDialogTitle,
    TtwrFormComponent
  ],
  template: `
    <h2 mat-dialog-title>{{ '::UpdateAttendance' | i18n }}</h2>
    <mat-dialog-content>
      <ttwr-form [config]="config"/>
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class AttendanceUpdateDialogComponent {
  private attendance = inject(AttendanceService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);
  private data = inject<AttendanceDto>(MAT_DIALOG_DATA);
  private dialogRef = inject(MatDialogRef);

  protected config = attendance().select({
    employeeId: true,
    checkIn: true,
    checkOut: true,
  }).form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        this.attendance.update(this.data.id!, body)
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::UpdatedSuccessfully');
            this.dialogRef.close(true);
          })
      },
    },
    fields: {
      employeeId: {
        search: true,
        label: '::GoldenOwl:Employee',
      },
      checkIn: {
        label: '::GoldenOwl:CheckIn',
        onChange: () => {
          this.config.fields.checkOut.control.updateValueAndValidity({
            emitEvent: false,
          });
        },
        validators: [
          requiredValidator,
          {
            name: 'invalidCheckIn',
            message: '::InvalidCheckIn',
            validator: control => {
              if (!control.value) return null;

              const checkOutValue = (control.parent?.controls as any)['checkOut'].value;

              if (!checkOutValue) return null;

              if (checkOutValue <= control.value) return {
                invalidCheckIn: true,
              };

              return null;
            },
          }
        ],
      },
      checkOut: {
        label: '::GoldenOwl:CheckOut',
        onChange: () => {
          this.config.fields.checkIn.control.updateValueAndValidity({
            emitEvent: false,
          });
        },
        validators: [
          requiredValidator,
          {
            name: 'invalidCheckOut',
            message: '::InvalidCheckOut',
            validator: control => {
              if (!control.value) return null;

              const checkInValue = (control.parent?.controls as any)['checkIn'].value;

              if (!checkInValue) return null;

              if (checkInValue >= control.value) return {
                invalidCheckOut: true,
              };

              return null;
            },
          }
        ],
      }
    },
    viewFunc: () => of(this.data),
  })
}
