import { Component, DestroyRef, inject } from '@angular/core';
import { AlertService, LOADING, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { requireAllOperator } from '@shared';
import { ActivatedRoute, Router } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Subject } from 'rxjs';
import { leaveAllocations } from '../../leave-allocations.model';
import { LeaveAllocationService } from '@proxy/hr/leave-allocations';

@Component({
  selector: 'app-leave-allocations-index',
  standalone: true,
  imports: [
    TtwrGridComponent
  ],
  template: `<ttwr-grid [config]="config" class="actions-end" />`,
})
export class LeaveAllocationsIndexComponent {
  private leaveAllocation = inject(LeaveAllocationService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);

  private refreshSubject = new Subject<void>();

  protected config = leaveAllocations().select({
    id: true,
    employeeName: true,
    leaveTypeName: true,
  }).grid({
    title: '::LeaveAllocations',
    refreshSubject: this.refreshSubject,
    dataFunc: (pagination) => this.leaveAllocation.getList({
      skipCount: pagination.pageSize * pagination.pageIndex,
      maxResultCount: pagination.pageSize,
    }).pipe(requireAllOperator()),
    actions: [
      {
        tooltip: 'Add',
        matIcon: 'add',
        delegateFunc: () =>
          this.router.navigate(['create'], { relativeTo: this.route.parent }),
      },
    ],
    fieldActions: [
      {
        matIcon: 'edit',
        tooltip: 'Edit',
        delegateFunc: (obj) =>
          this.router.navigate(['update', obj.id], {
            relativeTo: this.route.parent,
          }),
      },
      {
        matIcon: 'delete',
        tooltip: 'Delete',
        color: 'warn',
        confirmation: {
          title: '::Warning',
          message: '::AreYouSureYouWantToDelete?',
        },
        delegateFunc: (obj) => {
          this.loading.set(true);

          this.leaveAllocation.delete(obj.id).pipe(
            takeUntilDestroyed(this.destroyRef)
          ).subscribe(() => {
            this.refreshSubject.next();
            this.alert.success('::SuccessDelete');
          })
        }
      }
    ],
    fields: {
      employeeName: {
        columnName: '::GoldenOwl:Employee',
      },
      leaveTypeName: {
        columnName: '::GoldenOwl:LeaveType',
      }
    },
  })
}
