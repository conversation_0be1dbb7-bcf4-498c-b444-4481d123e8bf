using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoldenOwl.FN.Accounts;
using GoldenOwl.FN.Entries;
using GoldenOwl.FN.FinancialPeriods;
using GoldenOwl.FN.GeneralLedgers.DTO;
using GoldenOwl.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;

namespace GoldenOwl.FN.GeneralLedgers;

public class GeneralLedgerAppService : GoldenOwlAppService, IGeneralLedgerAppService
{
    private readonly IEntryRepository _entryRepository;
    private readonly IFinancialPeriodManager _financialPeriodManager;
    private readonly IRepository<Account, Guid> _accountRepository;

    public GeneralLedgerAppService(IEntryRepository entryRepository, IFinancialPeriodManager financialPeriodManager,
        IRepository<Account, Guid> accountRepository)
    {
        _entryRepository = entryRepository;
        _financialPeriodManager = financialPeriodManager;
        _accountRepository = accountRepository;
    }

    [Authorize(GoldenOwlPermissions.EntryIndex)]
    public async Task<GeneralLedgerDto> PostGetGeneralLedgerReport(GetGeneralLedgerRequestDto input)
    {
        Account account = await _accountRepository.GetAsync(input.AccountId);

        if (input.CurrencyCode != GoldenOwlConsts.DefaultCurrencyCode && input.CurrencyCode != account.CurrencyCode)
        {
            throw new UserFriendlyException(L[GoldenOwlApplicationErrorCodes.InvalidAccountCurrency]);
        }

        FinancialPeriod activeFinancialPeriod = await _financialPeriodManager.GetActiveFinancialPeriodAsync();
        if (input.FromDate < activeFinancialPeriod.StartDate || input.ToDate > activeFinancialPeriod.EndDate)
            throw new UserFriendlyException(L[GoldenOwlApplicationErrorCodes.DatesMustBeInsideActiveFinancialPeriod]);

        // Get Opening Balance (From start date of financial period until selected date)
        BalancesDto openingBalance = await GetOpeningBalance(activeFinancialPeriod.StartDate, input.FromDate,
            input.AccountId, input.CurrencyCode);

        //Get entries
        List<GeneralLedgerItemDto> entries = await GetEntries(input.FromDate, input.ToDate, input.AccountId, input.CurrencyCode);

        decimal cumulativeBalance = openingBalance.TotalBalance;
        foreach (GeneralLedgerItemDto entry in entries)
        {
            cumulativeBalance += (entry.TotalDebit - entry.TotalCredit);
            entry.Balance = cumulativeBalance;
        }

        
        decimal totalDebit = entries.Sum(t => t.TotalDebit);
        decimal totalCredit = entries.Sum(t => t.TotalCredit);
        decimal totalBalance = cumulativeBalance;

        BalancesDto currentEntriesBalance = new BalancesDto
        {
            TotalDebitBalance = totalDebit,
            TotalCreditBalance = totalCredit,
            TotalBalance = totalBalance
        };

        return new GeneralLedgerDto
        {
            AccountId = account.Id,
            AccountCode = account.Code,
            AccountName = account.Name,
            CurrencyCode = account.CurrencyCode,
            FromDate = input.FromDate,
            ToDate = input.ToDate,
            OpeningBalance = openingBalance,
            TotalBalance = currentEntriesBalance,
            GeneralLedgerItems = entries
        };
    }

    private async Task<List<GeneralLedgerItemDto>> GetEntries(DateOnly fromDate, DateOnly toDate, Guid accountId,
        string currencyCode)
    {
        var query = await _entryRepository.WithDetailsEntryAsync();
        return query
            .Where(e => e.EntryDate >= fromDate && e.EntryDate <= toDate)
            .SelectMany(e => e.EntryItems)
            .Where(ei => ei.AccountId == accountId)
            .Join(query, ei => ei.EntryId, e => e.Id, (ei, e) => new { ei, e })
            .OrderBy(joined => joined.e.EntryDate)
            .Select(joined => new GeneralLedgerItemDto
            {
                EntrySerialNumber = joined.e.EntryNumber,
                EntryItemDescreption = joined.ei.Description,
                EntryDate = joined.e.EntryDate,
                TotalDebit = currencyCode != GoldenOwlConsts.DefaultCurrencyCode
                    ? joined.ei.CurrencyDebit
                    : joined.ei.Debit,
                TotalCredit = currencyCode != GoldenOwlConsts.DefaultCurrencyCode
                    ? joined.ei.CurrencyCredit
                    : joined.ei.Credit,
                Balance = currencyCode != GoldenOwlConsts.DefaultCurrencyCode
                    ? joined.ei.CurrencyDebit - joined.ei.CurrencyCredit
                    : joined.ei.Debit - joined.ei.Credit,
            }).ToList();
    }

    private async Task<BalancesDto> GetOpeningBalance(DateOnly activeFinancialPeriodStartDate,
        DateOnly selectedFromDate, Guid accountId, string currencyCode)
    {
        var query = await _entryRepository.WithDetailsEntryAsync();

        var openingBalanceQuery = query
            .Where(e => e.EntryDate >= activeFinancialPeriodStartDate && e.EntryDate < selectedFromDate)
            .SelectMany(e => e.EntryItems)
            .Where(ei => ei.AccountId == accountId)
            .GroupBy(ei => new { ei.AccountId, ei.CurrencyCode })
            .Select(group => new BalancesDto()
            {
                TotalDebitBalance =
                    currencyCode != GoldenOwlConsts.DefaultCurrencyCode
                        ? group.Sum(x => x.CurrencyDebit)
                        : group.Sum(x => x.Debit),
                TotalCreditBalance =
                    currencyCode != GoldenOwlConsts.DefaultCurrencyCode
                        ? group.Sum(x => x.CurrencyCredit)
                        : group.Sum(x => x.Credit),
                TotalBalance =
                    currencyCode != GoldenOwlConsts.DefaultCurrencyCode
                        ? group.Sum(x => x.CurrencyDebit) - group.Sum(x => x.CurrencyCredit)
                        : group.Sum(x => x.Debit) - group.Sum(x => x.Credit)
            });

        return openingBalanceQuery.FirstOrDefault() ?? new BalancesDto
        {
            TotalDebitBalance = 0m,
            TotalCreditBalance = 0m,
            TotalBalance = 0m
        };
    }
}