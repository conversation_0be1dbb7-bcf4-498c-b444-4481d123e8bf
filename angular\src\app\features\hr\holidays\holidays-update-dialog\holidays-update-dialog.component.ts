import { Component, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import {
  AlertService,
  LanguagePipe,
  LOADING,
  requiredValidator,
  TtwrFormComponent
} from '@ttwr-framework/ngx-main-visuals';
import { finalize, of } from 'rxjs';
import { HolidayService } from '@proxy/hr/holidays';
import { holidays } from '../holidays.model';
import { clearDate } from '@shared';

@Component({
  selector: 'app-holidays-update',
  standalone: true,

  imports: [TtwrFormComponent, MatDialogTitle, MatDialogContent, LanguagePipe],
  template: `
    <h1 mat-dialog-title>{{ '::UpdateHoliday' | i18n }}</h1>
    <mat-dialog-content>
      <ttwr-form [config]="config"/>
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class HolidaysUpdateDialogComponent {
  private holiday = inject(HolidayService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);
  private dialogRef = inject(MatDialogRef);
  private data = inject<DialogData>(MAT_DIALOG_DATA);

  protected config = holidays.select({
    // reorder
    isReOccurring: true,
    name: true,
    from: true,
    to: true,
  }).form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);


        this.holiday.update(this.data.id, {
          name: body.name,
          isReOccuring: body.isReOccurring,
          from: clearDate(body.from),
          to: clearDate(body.to?.trim() || body.from),
        })
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::UpdatedSuccessfully');
            this.dialogRef.close(true);
          })
      },
    },
    fields: {
      isReOccurring: {
        label: '::GoldenOwl:IsReOccurring',
      },
      from: {
        label: '::GoldenOwl:From',
        onChange: () => {
          this.config.fields.to.control.updateValueAndValidity({
            emitEvent: false,
          });
        },
        validators: [
          requiredValidator,
          {
            name: 'invalidFromDate',
            message: '::InvalidFromDate',
            validator: control => {
              if (!control.value) return null;

              const toValue = (control.parent?.controls as any)['to'].value;

              if (!toValue) return null;

              if (toValue < control.value) return {
                invalidFromDate: true,
              };

              return null;
            },
          }
        ],
      },
      to: {
        label: '::GoldenOwl:To',
        onChange: () => {
          this.config.fields.from.control.updateValueAndValidity({
            emitEvent: false,
          });
        },
        validators: [
          {
            name: 'invalidToDate',
            message: '::InvalidToDate',
            validator: control => {
              if (!control.value) return null;

              const fromValue = (control.parent?.controls as any)['from'].value;

              if (!fromValue) return null;

              if (fromValue > control.value) return {
                invalidToDate: true,
              };

              return null;
            },
          }
        ],
      },
    },
    viewFunc: () => of(this.data),
  });
}

interface DialogData {
  id: string;
  name: string;
  dateRange: string;
  isReOccurring: boolean;
}
