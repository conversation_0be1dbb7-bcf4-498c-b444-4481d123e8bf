import { Component, inject, model } from '@angular/core';
import { CdkDrag, CdkDragDrop, CdkDragHandle, CdkDropList, moveItemInArray } from '@angular/cdk/drag-drop';
import { MatIcon } from '@angular/material/icon';
import { arrayMap, LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { toSignal } from '@angular/core/rxjs-interop';
import { map, startWith, Subject, switchMap } from 'rxjs';
import { SalaryRuleService } from '@proxy/payroll/salary-rules';
import { MatError, MatFormField, MatLabel } from '@angular/material/form-field';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatButton, MatMiniFabButton } from '@angular/material/button';
import { MatTooltip } from '@angular/material/tooltip';
import { MatDialog } from '@angular/material/dialog';
import { NewSalaryRuleDialogComponent } from './new-salary-rule-dialog/new-salary-rule-dialog.component';

@Component({
  selector: 'app-salary-rules-list',
  standalone: true,
  imports: [CdkDropList, CdkDragHandle, CdkDrag, MatIcon, LanguagePipe, MatFormField, MatLabel, MatError, MatSelect, MatOption, MatButton, MatMiniFabButton, MatTooltip],
  templateUrl: './salary-rules-list.component.html',
  styleUrl: './salary-rules-list.component.scss',
})
export class SalaryRulesListComponent {
  private salaryRule = inject(SalaryRuleService);
  private dialog = inject(MatDialog);

  public selectedRules = model.required<(string | null)[]>();

  private refreshRulesSubject = new Subject<void>();

  protected rules = toSignal(
    this.refreshRulesSubject.pipe(
      startWith(null),
      switchMap(() => this.salaryRule.getList({
        maxResultCount: 999
      })),
      map(res => res.items!),
      arrayMap(rule => ({
        label: rule.name!,
        value: rule.id!,
      })),
    ),
  );

  drop(event: CdkDragDrop<string[]>) {
    const rules = this.selectedRules();

    moveItemInArray(rules, event.previousIndex, event.currentIndex);

    this.selectedRules.set(rules);
  }

  addItem() {
    this.selectedRules.update(pre => [...pre, null]);
  }

  changeValue(index: number, value: string) {
    const rules = this.selectedRules();
    rules[index] = value;
    this.selectedRules.set(rules);
  }

  removeRule(index: number) {
    this.selectedRules.update(pre => pre.filter((_, i) => i !== index));
  }

  addNewSalaryRule() {
    const ref = this.dialog.open(NewSalaryRuleDialogComponent, {
      width: '100%',
      maxWidth: '800px',
    });

    ref.afterClosed().subscribe(newItemId => {
      if (newItemId) {
        this.refreshRulesSubject.next();
        this.selectedRules.update(pre => [...pre, newItemId]);
      }
    });
  }
}
