h3 {
  margin: 1rem 0 0;
}

[cdkDrag] {
  padding: 20px 10px;
  border-bottom: solid 1px #ccc;
  color: rgba(0, 0, 0, 0.87);
  display: flex;
  align-items: center;
  box-sizing: border-box;
  background: white;
  font-size: 14px;
}

.cdk-drag-preview {
  border: none;
  box-sizing: border-box;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
  0 8px 10px 1px rgba(0, 0, 0, 0.14),
  0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

[cdkDrag]:last-child {
  border: none;
}

.cdk-drop-list-dragging [cdkDrag]:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

[cdkDragHandle] {
  cursor: move;
  opacity: 0.7;
  width: 56px;
  height: 56px;
  display: grid;
  place-items: center;
}

mat-form-field {
  width: 100%;
}

[mat-mini-fab] {
  margin: 0 1rem;
}

.buttons-row {
  margin-top: 1rem;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;

  & > * {
    flex-grow: 1;
  }
}
