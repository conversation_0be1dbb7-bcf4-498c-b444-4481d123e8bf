import { fields, model, Option, takeOptions } from '@ttwr-framework/ngx-main-visuals';
import { AttendanceRequestState } from '@proxy/self-service/attendance-requests';
import { of } from 'rxjs';

export const attendanceRequests = model({
  id: fields.text(),
  checkIn: fields.datetime(),
  checkOut: fields.datetime(),
  creationTime: fields.datetime(),
  state: fields.select('single', takeOptions(AttendanceRequestState)),
  attendanceId: fields.selectFetch('single', () => of<Option<string>[]>([])),
})
