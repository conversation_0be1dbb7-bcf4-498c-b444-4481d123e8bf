import { arrayMap, fields, model } from '@ttwr-framework/ngx-main-visuals';
import { inject } from '@angular/core';
import { EmployeeService } from '@proxy/hr/employees';
import { requireAllOperator } from '@shared';
import { map } from 'rxjs';
import { PenaltyTypeService } from '@proxy/hr/penalty-types';

export const penalties = () => {
  const employees = inject(EmployeeService);
  const penaltyType = inject(PenaltyTypeService);

  return model({
    id: fields.text(),
    employeeId: fields.selectFetch('single', () => employees.getList(
      { maxResultCount: 999 }, '00000000-0000-0000-0000-000000000000', undefined as any,
    ).pipe(
      requireAllOperator(),
      map(res => res.items),
      arrayMap(employee => ({
        label: `${employee.name} ${employee.surname}`,
        value: employee.id,
      }))
    )),
    penaltyTypeId: fields.selectFetch('single', () => penaltyType.getGetAllPenaltyTypesByInput({
      maxResultCount: 999
    }).pipe(
      map(res => res.items!),
      arrayMap(type => ({
        label: type.name!,
        value: type.id!,
      }))
    )),
    penaltyTypeName: fields.text(),
    declarationDate: fields.date(),
    implementationDate: fields.date(),
    expirationDate: fields.date(),
    summary: fields.text(),
  })
}
