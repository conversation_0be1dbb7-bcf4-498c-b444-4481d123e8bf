import { Component, inject, OnDestroy } from '@angular/core';
import { LanguagePipe, LOADING } from '@ttwr-framework/ngx-main-visuals';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogTitle } from '@angular/material/dialog';
import { toSignal } from '@angular/core/rxjs-interop';
import { PayslipService } from '@proxy/payroll/payslips';
import { map, tap } from 'rxjs';

@Component({
  selector: 'app-payslip-items-dialog',
  standalone: true,
  imports: [
    MatDialogTitle,
    LanguagePipe,
    MatDialogContent,
  ],
  template: `
    <h2 mat-dialog-title>
      {{ '::PayslipItems' | i18n }}
    </h2>
    <mat-dialog-content>
      <table class="ttwr-basic-table">
        <thead>
        <tr>
          <th>{{ '::Sequence' | i18n }}</th>
          <th>{{ 'name' | i18n }}</th>
          <th>{{ '::GoldenOwl:Category' | i18n }}</th>
          <th>{{ '::Amount' | i18n }}</th>
        </tr>
        </thead>
        <tbody>
          @for (item of items() ?? []; track $index) {
            <tr>
              <td>{{ item.sequence }}</td>
              <td>{{ item.name }}</td>
              <td>{{ item.categoryName }}</td>
              <td>{{ item.amount }}</td>
            </tr>
          }
        </tbody>
      </table>
    </mat-dialog-content>
  `,
})
export class PayslipItemsDialogComponent implements OnDestroy {
  private payslip = inject(PayslipService);
  private loading = inject(LOADING);
  private id = inject<string>(MAT_DIALOG_DATA);

  constructor() {
    this.loading.set(true);
  }

  protected items = toSignal(
    this.payslip.get(this.id).pipe(
      map(res => res.payslipItems),
      tap(() => this.loading.set(false)),
    ),
  );

  ngOnDestroy() {
    this.loading.set(false);
  }
}
