﻿using Elsa.EntityFrameworkCore.Modules.Runtime;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.DependencyInjection;

namespace ElsaWorkflows.EntityFrameworkCore.DbContext
{
    public class ElsaWorkflowsRuntimeDbContext : RuntimeElsaDbContext, ITransientDependency
    {
        public ElsaWorkflowsRuntimeDbContext(DbContextOptions<RuntimeElsaDbContext> options,
            IServiceProvider serviceProvider) : base(options, serviceProvider)
        {
        }
    }
}