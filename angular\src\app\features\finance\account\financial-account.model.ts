import { inject, signal } from '@angular/core';
import { arrayMap, fields, model } from '@ttwr-framework/ngx-main-visuals';
import { CashFlowType } from '@proxy/fn/accounts';
import { AccountTypeService } from '@proxy/fn/account-types';
import { CurrencyService } from '@proxy/fn/currencies';
import { map } from 'rxjs';
import { takeOptionsWithUndefinedOption } from '@shared';

export const accountTypeOptions = signal<OptionWithPrefix[]>([]);
export const financialAccount = (typeNameValueKey: 'codePrefix' | 'name') => {
  const accountType = inject(AccountTypeService);
  const currency = inject(CurrencyService);

  return model({
    id: fields.text(),

    // typeName: fields.selectFetch('single', () => accountType.getList().pipe(
    //   arrayMap(category => ({
    //     label: category.displayName!,
    //     value: category[typeNameValueKey]!,
    //     // value: `${category[typeNameValueKey]}@#$${category.codePrefix}`!,
    //     // value: typeNameValueKey === 'name'
    //     //   ? `${category.name}@#$${category.codePrefix}`
    //     //   : `${category.codePrefix}`,
    //   }))
    // )),

    typeName: fields.selectFetch('single', () => {
      return accountType.getList().pipe(
        arrayMap((category): OptionWithPrefix => {
          const opt = {
            label: category.displayName!,
            value: category[typeNameValueKey]!,
            prefix: category.codePrefix!,
          };
          accountTypeOptions.update((prev) => [...prev, opt]);
          return opt;
        })
      );
    }),

    code: fields.text(),
    name: fields.text(),
    currencyCode: fields.selectFetch('single', () =>
      currency
        .getList(
          {
            maxResultCount: 999,
          },
          undefined as any,
          undefined as any
        )
        .pipe(
          map((res) => res.items!),
          arrayMap((currency) => ({
            label: currency.unitLabel!,
            value: currency.code!,
          }))
        )
    ),

    cashFlowType: fields.select(
      'single',
      takeOptionsWithUndefinedOption(CashFlowType)
    ),
    isDeprecated: fields.boolean(),
    codePrefix: fields.text(),
  });
};

interface OptionWithPrefix {
  label: string;
  value: string;
  prefix: string;
}
