import { Component, DestroyRef, inject } from '@angular/core';
import { AlertService, LanguagePipe, LOADING, TtwrFormComponent } from '@ttwr-framework/ngx-main-visuals';
import { MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { finalize } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { employeePins } from '../employee-pins.model';
import { EmployeePinService } from '@proxy/hr/employee-pins';

@Component({
  selector: 'app-employee-pins-create-dialog',
  standalone: true,
  imports: [
    LanguagePipe,
    MatDialogContent,
    MatDialogTitle,
    TtwrFormComponent
  ],
  template: `
    <h2 mat-dialog-title>{{ '::CreateEmployeeFingerPrintPin' | i18n }}</h2>
    <mat-dialog-content>
      <ttwr-form [config]="config"/>
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class EmployeePinsCreateDialogComponent {
  private employeePin = inject(EmployeePinService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);
  private dialogRef = inject(MatDialogRef);

  protected config = employeePins().form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        this.employeePin.postCreateEmployeePinByDto(body)
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::CreatedSuccessfully');
            this.dialogRef.close(true);
          })
      },
    },
    fields: {
      pin: {
        label: '::GoldenOwl:EmployeePin',
      },
      employeeId: {
        label: '::GoldenOwl:Employee',
        search: true,
      }
    },
  })
}
