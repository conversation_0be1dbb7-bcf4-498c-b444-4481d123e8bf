import type { CreateEntryDto, EntryDto, UpdateEntryDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class EntryService {
  apiName = 'Default';
  

  create = (input: CreateEntryDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/entry',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, EntryDto>({
      method: 'GET',
      url: `/api/app/entry/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, financialPeriodId?: string, isPosted?: boolean, startDate?: string, endDate?: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<EntryDto>>({
      method: 'GET',
      url: '/api/app/entry',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount, financialPeriodId, isPosted, startDate, endDate },
    },
    { apiName: this.apiName,...config });
  

  putPostEntry = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: `/api/app/entry/${id}/post-entry`,
    },
    { apiName: this.apiName,...config });
  

  putUnPostEntry = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: `/api/app/entry/${id}/un-post-entry`,
    },
    { apiName: this.apiName,...config });
  

  putUpdate = (id: string, input: UpdateEntryDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: `/api/app/entry/${id}/update`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
