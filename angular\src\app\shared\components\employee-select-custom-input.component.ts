import { Component, input } from "@angular/core";
import { FormFieldWithControlAndName, ICustomInputComponent, SelectFetchFieldType } from "@ttwr-framework/ngx-main-visuals";

@Component({
  selector: 'app-employees-select-custom-input',
  standalone: true,
  template: ``,
})
export class EmployeeSelectCustomInput implements ICustomInputComponent<SelectFetchFieldType<string, false>> {
  field = input.required<FormFieldWithControlAndName<SelectFetchFieldType<string, false>>>();

}

