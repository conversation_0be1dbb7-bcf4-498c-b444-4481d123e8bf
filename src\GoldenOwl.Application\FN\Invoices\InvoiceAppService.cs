using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoldenOwl.FN.Accounts;
using GoldenOwl.FN.Entries;
using GoldenOwl.FN.Entries.DTO;
using GoldenOwl.FN.FinancialClients;
using GoldenOwl.FN.Invoices.DTO;
using GoldenOwl.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace GoldenOwl.FN.Invoices;

public class InvoiceAppService : GoldenOwlAppService, IInvoiceAppService
{
    private readonly IRepository<Invoice, Guid> _invoiceRepository;
    private readonly IRepository<FinancialClient, Guid> _financialClientRepository;
    private readonly IRepository<Account, Guid> _accountRepository;
    private readonly IInvoiceManager _invoiceManager;

    public InvoiceAppService(IInvoiceManager invoiceManager, IRepository<Invoice, Guid> invoiceRepository,
        IRepository<FinancialClient, Guid> financialClientRepository, IRepository<Account, Guid> accountRepository)
    {
        _invoiceManager = invoiceManager;
        _invoiceRepository = invoiceRepository;
        _financialClientRepository = financialClientRepository;
        _accountRepository = accountRepository;
    }

    [Authorize(GoldenOwlPermissions.InvoiceIndex)]
    public async Task<PagedResultDto<InvoiceDto>> GetListAsync(PagedResultRequestDto input, int? invoiceNumber)
    {
        var query = await _invoiceRepository.GetQueryableAsync();

        if (invoiceNumber is not null)
            query = query.Where(i => i.InvoiceNumber.ToString().StartsWith(invoiceNumber.ToString()));

        query = query.OrderBy(c => c.CreationTime);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var invoices = await AsyncExecuter.ToListAsync(query.PageBy(input));

        return new PagedResultDto<InvoiceDto>(totalCount,
            ObjectMapper.Map<List<Invoice>, List<InvoiceDto>>(invoices));
    }

    [Authorize(GoldenOwlPermissions.InvoiceIndex)]
    public async Task<InvoiceDto> GetAsync(Guid id)
    {
        var invoice = await _invoiceRepository.GetAsync(id);
        return ObjectMapper.Map<Invoice, InvoiceDto>(invoice);
    }

    public async Task<CreateInvoiceDto> PostGetTemplateForInvoice(CreateInvoiceDto input)
    {
        return await MakeInvoiceTemplateWithEntryItemsForInvoice(input);
    }

    public virtual async Task<Guid> CreateAsync(CreateInvoiceDto input)
    {
        input.Warehouse = input.Warehouse.Trim();
        input.Description = input.Description?.Trim();

        if (input.Entry is null)
        {
            throw new UserFriendlyException(L[GoldenOwlApplicationErrorCodes.EntryDetailsIsRequired]);
        }

        return await _invoiceManager.CreateInvoiceAsync(
            input.InvoiceType,
            input.FinancialClientId,
            input.CurrencyCode,
            input.Warehouse,
            input.Date,
            input.PaymentMethod,
            input.Description,
            input.TotalAmountInCurrency,
            input.FinancialPeriodId,
            input.Entry.EntryType,
            input.Entry.EntryDate,
            input.Entry.Description,
            ObjectMapper.Map<List<CreateUpdateEntryItemDto>, List<EntryItem>>(input.Entry.EntryItems)
        );
    }

    [Authorize(GoldenOwlPermissions.InvoiceManagement)]
    public virtual async Task UpdateAsync(Guid id, UpdateInvoiceDto input)
    {
        input.Warehouse = input.Warehouse.Trim();
        input.Description = input.Description?.Trim();

        if (input.Entry is null)
        {
            throw new UserFriendlyException(L[GoldenOwlApplicationErrorCodes.EntryDetailsIsRequired]);
        }

        await _invoiceManager.UpdateInvoiceAsync(
            id,
            input.InvoiceType,
            input.FinancialClientId,
            input.CurrencyCode,
            input.Warehouse,
            input.Date,
            input.PaymentMethod,
            input.Description,
            input.TotalAmountInCurrency,
            input.FinancialPeriodId,
            input.Entry.EntryDate,
            input.Entry.Description,
            ObjectMapper.Map<List<CreateUpdateEntryItemDto>, List<EntryItem>>(input.Entry.EntryItems)
        );
    }

    private async Task<CreateInvoiceDto> MakeInvoiceTemplateWithEntryItemsForInvoice(CreateInvoiceDto invoiceDto)
    {
        var financialClient = await _financialClientRepository.GetAsync(invoiceDto.FinancialClientId);

        invoiceDto.Entry = new CreateEntryDto()
        {
            Description = "Invoice Entry",
            EntryType = EntryType.General,
            EntryDate = invoiceDto.Date,
            EntryItems = []
        };

        if (financialClient.AccountId is not null)
        {
            var financialClientAccount = await _accountRepository.GetAsync(financialClient.AccountId.Value);
            invoiceDto.Entry.EntryItems.Add(MakeClientEntryItemDtoTemplate(1, invoiceDto, financialClientAccount,
                true));
        }

        var fundAccount =
            await _accountRepository.FirstOrDefaultAsync(a => a.Code == GoldenOwlConsts.DefaultFundAccountCode);
        if (fundAccount is not null)
        {
            invoiceDto.Entry.EntryItems.Add(MakeClientEntryItemDtoTemplate(2, invoiceDto, fundAccount, false));
        }

        return invoiceDto;
    }

    private CreateUpdateEntryItemDto MakeClientEntryItemDtoTemplate(uint serial, CreateInvoiceDto input,
        Account account,
        bool isDebitAccount)
    {
        var isReceivable = input.InvoiceType is InvoiceType.Sales or InvoiceType.PurchasesReturn;
        decimal debit;
        decimal credit;
        decimal currencyDebit;
        decimal currencyCredit;

        if (isDebitAccount)
        {
            debit = isReceivable ? input.TotalAmountInLocal : 0;
            credit = !isReceivable ? input.TotalAmountInLocal : 0;
            currencyDebit = isReceivable ? input.TotalAmountInCurrency : 0;
            currencyCredit = !isReceivable ? input.TotalAmountInCurrency : 0;
        }
        else
        {
            debit = !isReceivable ? input.TotalAmountInLocal : 0;
            credit = isReceivable ? input.TotalAmountInLocal : 0;
            currencyDebit = !isReceivable ? input.TotalAmountInCurrency : 0;
            currencyCredit = isReceivable ? input.TotalAmountInCurrency : 0;
        }

        var entryItem = new CreateUpdateEntryItemDto()
        {
            SerialNumber = serial,
            Description = "Entry item " + serial,
            Debit = debit,
            Credit = credit,
            CurrencyDebit = currencyDebit,
            CurrencyCredit = currencyCredit,
            ExchangeRate = input.ExchangeRate,
            AccountId = account.Id,
        };
        return entryItem;
    }

    [Authorize(GoldenOwlPermissions.InvoiceManagement)]
    public async Task PostInvoiceAsync(Guid id)
    {
        await _invoiceManager.PostInvoiceAsync(id);
    }

    [Authorize(GoldenOwlPermissions.InvoiceManagement)]
    public async Task UnPostInvoiceAsync(Guid id)
    {
        await _invoiceManager.UnPostInvoiceAsync(id);
    }
}