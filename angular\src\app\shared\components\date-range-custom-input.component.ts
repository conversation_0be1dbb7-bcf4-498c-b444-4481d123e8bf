import { Component, DestroyRef, inject, input, OnInit } from '@angular/core';
import {
  dateToISO,
  FormFieldWithControlAndName,
  ICustomInputComponent,
  LanguagePipe,
  TextFieldType
} from '@ttwr-framework/ngx-main-visuals';
import { NonNullableFormBuilder, ReactiveFormsModule } from '@angular/forms';
import {
  MatDatepickerToggle,
  MatDateRangeInput,
  MatDateRangePicker,
  MatEndDate,
  MatStartDate
} from '@angular/material/datepicker';
import { MatE<PERSON>r, MatFormField, MatLabel, MatSuffix } from '@angular/material/form-field';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { clearDate } from '@shared';
import { provideNativeDateAdapter } from '@angular/material/core';
import { filter, take } from 'rxjs';

@Component({
  standalone: true,
  imports: [
    MatFormField,
    MatLabel,
    <PERSON><PERSON><PERSON><PERSON>,
    Language<PERSON>ipe,
    MatDateRangeInput,
    MatDateRangePicker,
    MatDatepickerToggle,
    MatStartDate,
    MatEndDate,
    MatSuffix,
    ReactiveFormsModule,
  ],
  providers: [provideNativeDateAdapter()],
  template: `
    <mat-form-field [formGroup]="rangeFormGroup">
      <mat-label>{{ (field().label ?? field().name) | i18n }}</mat-label>
      <mat-date-range-input [dateFilter]="disableDateFilter() ? disabledDateFilter : dateFilter" [rangePicker]="picker">
        <input [formControl]="rangeFormGroup.controls.start" matStartDate placeholder="Start date">
        <input [formControl]="rangeFormGroup.controls.end" matEndDate placeholder="End date">
      </mat-date-range-input>
      <mat-datepicker-toggle matIconSuffix [for]="picker"/>
      <mat-date-range-picker #picker/>
      <mat-error>
        @for (validator of field().validators ?? []; track validator.name) {
          <span [hidden]="!field().control.hasError(validator.name)">
            {{ validator.message | i18n }}
          </span>
        }
      </mat-error>
    </mat-form-field>
  `,
  styles: `
    mat-form-field {
      width: 100%;
    }
  `,
})
export class DateRangeCustomInputComponent implements ICustomInputComponent<TextFieldType>, OnInit {
  private fb = inject(NonNullableFormBuilder);
  private destroyRef = inject(DestroyRef);

  public field = input.required<FormFieldWithControlAndName<TextFieldType>>();
  public disableDateFilter = input<boolean>();

  protected dateFilter = (date: Date) => {
    const now = new Date();
    now.setHours(0, 0, 0, 0);

    return date >= now;
  };

  protected disabledDateFilter = () => true;

  protected rangeFormGroup = this.fb.group({
    start: this.fb.control<Date | null>(null),
    end: this.fb.control<Date | null>(null),
  })

  ngOnInit() {
    const formFieldValidators = this.field().validators;
    if (formFieldValidators) {
      this.rangeFormGroup.controls.start.addValidators(formFieldValidators.map(v => v.validator));
      this.rangeFormGroup.controls.end.addValidators(formFieldValidators.map(v => v.validator));
    }

    this.field().control.valueChanges.pipe(
      filter(Boolean),
      take(1),
      takeUntilDestroyed(this.destroyRef),
    ).subscribe(value => {
      const result = extractTwoDates(value);
      this.rangeFormGroup.setValue(result as any, {
        emitEvent: false,
      });
    })
  }

  constructor() {
    this.rangeFormGroup.valueChanges.pipe(
      takeUntilDestroyed(),
    ).subscribe(value => {
      if (value.start && value.end) {
        this.field().control.setValue(combineTwoDates(value.start, value.end));
      }
    });
  }
}

const DATE_SEPARATOR = ' --> ';

export function combineTwoDates<T extends Date | string | undefined>(first: T, second: T) {
  const firstPart = first instanceof Date ? clearDate(dateToISO(first)) : first ?? '';
  const secondPart = second instanceof Date ? clearDate(dateToISO(second)) : second ?? '';

  return `${firstPart}${DATE_SEPARATOR}${secondPart}`
}

/**
 * Function that split a concatenated date range value to two clear dates
 *
 * @param value like this value: `2024-12-12T22:00:00.000-->2024-12-15T22:00:00.000`
 *
 * @returns result like this value:
 * ```
 * {
 *    start: '2024-12-12',
 *    end: '2024-12-15'
 * }
 * ```
 */
export function extractTwoDates(value: string) {
  const datesArray = value.split(DATE_SEPARATOR);

  return {
    start: clearDate(datesArray[0]),
    end: clearDate(datesArray[1]),
  }
}
