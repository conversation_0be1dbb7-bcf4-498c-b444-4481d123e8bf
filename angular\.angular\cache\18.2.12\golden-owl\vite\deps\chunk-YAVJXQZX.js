import {
  RestService,
  mapEnumToOptions
} from "./chunk-VITQ7ATO.js";
import {
  Injectable,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-7YC2NMXI.js";

// node_modules/@abp/ng.account.core/fesm2022/abp-ng.account.core-proxy.mjs
var LoginResultType;
(function(LoginResultType2) {
  LoginResultType2[LoginResultType2["Success"] = 1] = "Success";
  LoginResultType2[LoginResultType2["InvalidUserNameOrPassword"] = 2] = "InvalidUserNameOrPassword";
  LoginResultType2[LoginResultType2["NotAllowed"] = 3] = "NotAllowed";
  LoginResultType2[LoginResultType2["LockedOut"] = 4] = "LockedOut";
  LoginResultType2[LoginResultType2["RequiresTwoFactor"] = 5] = "RequiresTwoFactor";
})(LoginResultType || (LoginResultType = {}));
var loginResultTypeOptions = mapEnumToOptions(LoginResultType);
var index$4 = Object.freeze({
  __proto__: null,
  get LoginResultType() {
    return LoginResultType;
  },
  loginResultTypeOptions
});
var AccountService$1 = class AccountService {
  constructor(restService) {
    this.restService = restService;
    this.apiName = "AbpAccount";
    this.checkPasswordByLogin = (login) => this.restService.request({
      method: "POST",
      url: "/api/account/check-password",
      body: login
    }, {
      apiName: this.apiName
    });
    this.loginByLogin = (login) => this.restService.request({
      method: "POST",
      url: "/api/account/login",
      body: login
    }, {
      apiName: this.apiName
    });
    this.logout = () => this.restService.request({
      method: "GET",
      url: "/api/account/logout"
    }, {
      apiName: this.apiName
    });
  }
  static {
    this.ɵfac = function AccountService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || AccountService)(ɵɵinject(RestService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: AccountService,
      factory: AccountService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AccountService$1, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RestService
  }], null);
})();
var index$3 = Object.freeze({
  __proto__: null,
  AccountService: AccountService$1,
  Models: index$4
});
var index$2 = Object.freeze({
  __proto__: null,
  Controllers: index$3
});
var index$1 = Object.freeze({
  __proto__: null,
  Account: index$2
});
var index = Object.freeze({
  __proto__: null,
  Areas: index$1
});
var AccountService2 = class _AccountService {
  constructor(restService) {
    this.restService = restService;
    this.apiName = "AbpAccount";
    this.register = (input) => this.restService.request({
      method: "POST",
      url: "/api/account/register",
      body: input
    }, {
      apiName: this.apiName
    });
    this.resetPassword = (input) => this.restService.request({
      method: "POST",
      url: "/api/account/reset-password",
      body: input
    }, {
      apiName: this.apiName
    });
    this.sendPasswordResetCode = (input) => this.restService.request({
      method: "POST",
      url: "/api/account/send-password-reset-code",
      body: input
    }, {
      apiName: this.apiName
    });
  }
  static {
    this.ɵfac = function AccountService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AccountService)(ɵɵinject(RestService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _AccountService,
      factory: _AccountService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AccountService2, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RestService
  }], null);
})();
var ProfileService = class _ProfileService {
  constructor(restService) {
    this.restService = restService;
    this.apiName = "AbpAccount";
    this.changePassword = (input) => this.restService.request({
      method: "POST",
      url: "/api/account/my-profile/change-password",
      body: input
    }, {
      apiName: this.apiName
    });
    this.get = () => this.restService.request({
      method: "GET",
      url: "/api/account/my-profile"
    }, {
      apiName: this.apiName
    });
    this.update = (input) => this.restService.request({
      method: "PUT",
      url: "/api/account/my-profile",
      body: input
    }, {
      apiName: this.apiName
    });
  }
  static {
    this.ɵfac = function ProfileService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ProfileService)(ɵɵinject(RestService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _ProfileService,
      factory: _ProfileService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ProfileService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RestService
  }], null);
})();

export {
  index,
  AccountService2 as AccountService,
  ProfileService
};
//# sourceMappingURL=chunk-YAVJXQZX.js.map
