import { arrayMap, fields, model } from '@ttwr-framework/ngx-main-visuals';
import { inject } from '@angular/core';
import { EmployeeService } from '@proxy/hr/employees';
import { requireAllOperator } from '@shared';
import { map } from 'rxjs';
import { PenaltyTypeService } from '@proxy/hr/penalty-types';
import { RewardTypeService } from '@proxy/hr/reward-types';

export const rewards = () => {
  const employees = inject(EmployeeService);
  const rewardType = inject(RewardTypeService);

  return model({
    id: fields.text(),
    employeeId: fields.selectFetch('single', () =>
      employees
        .getList(
          { maxResultCount: 999 },
          '00000000-0000-0000-0000-000000000000',
          undefined as any
        )
        .pipe(
          requireAllOperator(),
          map((res) => res.items),
          arrayMap((employee) => ({
            label: `${employee.name} ${employee.surname}`,
            value: employee.id,
          }))
        )
    ),
    rewardTypeId: fields.selectFetch('single', () =>
      rewardType
        .getList({
          maxResultCount: 999,
        })
        .pipe(
          map((res) => res.items!),
          arrayMap((type) => ({
            label: type.name!,
            value: type.id!,
          }))
        )
    ),
    rewardTypeName: fields.text(),
    declarationDate: fields.date(),
    implementationDate: fields.date(),
    expirationDate: fields.date(),
    summary: fields.text(),
  });
};
