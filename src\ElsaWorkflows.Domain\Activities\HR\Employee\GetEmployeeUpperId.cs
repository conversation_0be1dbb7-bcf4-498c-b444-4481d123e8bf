using Elsa.Extensions;
using Elsa.Workflows;
using Elsa.Workflows.Activities.Flowchart.Attributes;
using Elsa.Workflows.Attributes;
using ElsaWorkflows.GoldenOwl.Common.HR.Employee;

namespace ElsaWorkflows.Domain.Activities.HR.Employee;

[Activity("Employee", "Gets the upper userId for the previous employee, input: employeeId, output: userId")]
[FlowNode("Pass", "NoUpper")]
public class GetEmployeeUpperId : CodeActivity<Guid>
{
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        var employeeElsaHelper = context.GetRequiredService<IEmployeeElsaHelper>();

        var input = context.GetLastResult();
        
        if (input?.ToString() is null)
        {
            throw new InvalidOperationException("The last result is null or invalid");
        }

        var employeeId = Guid.Parse(input.ToString());
        var upperId = await employeeElsaHelper.GetEmployeeUpperIdAsync(employeeId);
        context.SetResult(upperId);
        
        if (upperId.HasValue)
        {
            await context.CompleteActivityWithOutcomesAsync("Pass");
        }
        else
        {
            await context.CompleteActivityWithOutcomesAsync("NoUpper");
        }
    }
}