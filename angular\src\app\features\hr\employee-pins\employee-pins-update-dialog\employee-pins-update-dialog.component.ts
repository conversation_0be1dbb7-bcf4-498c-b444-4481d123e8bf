import { Component, DestroyRef, inject } from '@angular/core';
import { AlertService, LanguagePipe, LOADING, TtwrFormComponent } from '@ttwr-framework/ngx-main-visuals';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { finalize, of } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { EmployeePinService } from '@proxy/hr/employee-pins';
import { EmployeePinDto } from '@proxy/hr/employee-pins/dto';
import { employeePins } from '../employee-pins.model';

@Component({
  selector: 'app-employee-pins-update-dialog',
  standalone: true,
  imports: [
    LanguagePipe,
    MatDialogContent,
    MatDialogTitle,
    TtwrFormComponent
  ],
  template: `
    <h2 mat-dialog-title>{{ '::UpdateEmployeeFingerPrintPin' | i18n }}</h2>
    <mat-dialog-content>
      <ttwr-form [config]="config"/>
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class EmployeePinsUpdateDialogComponent {
  private employeePin = inject(EmployeePinService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);
  private data = inject<EmployeePinDto>(MAT_DIALOG_DATA);
  private dialogRef = inject(MatDialogRef);

  protected config = employeePins().form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        this.employeePin.putUpdateEmployeePinByIdAndDto(this.data.id!, body)
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::UpdatedSuccessfully');
            this.dialogRef.close(true);
          })
      },
    },
    viewFunc: () => of(this.data),
    fields: {
      pin: {
        label: '::GoldenOwl:EmployeePin',
      },
      employeeId: {
        label: '::GoldenOwl:Employee',
        search: true,
      }
    },
  })
}
