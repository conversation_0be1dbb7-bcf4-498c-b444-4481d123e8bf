import { Component, DestroyRef, inject } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { AlertService, LOADING, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { Subject } from 'rxjs';
import { requireAllOperator } from '@shared';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { salaryRulesCategories } from './salary-rules-categories.model';
import { SalaryRuleCategoryService } from '@proxy/payroll/salary-rule-categories';
import {
  SalaryRulesCategoriesCreateDialogComponent
} from './salary-rules-categories-create-dialog/salary-rules-categories-create-dialog.component';
import {
  SalaryRulesCategoriesUpdateDialogComponent
} from './salary-rules-categories-update-dialog/salary-rules-categories-update-dialog.component';

@Component({
  selector: 'app-salary-rules-categories',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `<ttwr-grid class="actions-end" [config]="config" />`,
})
export class SalaryRulesCategoriesComponent {
  private salaryRuleCategory = inject(SalaryRuleCategoryService);
  private dialog = inject(MatDialog);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);

  private refreshSubject = new Subject<void>();

  protected config = salaryRulesCategories.grid({
    title: '::PayrollSalaryRuleCategories',
    refreshSubject: this.refreshSubject,
    dataFunc: pagination => this.salaryRuleCategory.getList({
      skipCount: pagination.pageSize * pagination.pageIndex,
      maxResultCount: pagination.pageSize,
    }).pipe(requireAllOperator()),
    actions: [
      {
        matIcon: 'add',
        tooltip: 'Add',
        delegateFunc: () => {
          const ref = this.dialog.open(SalaryRulesCategoriesCreateDialogComponent, {
            width: '500px'
          });

          ref.afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        }
      }
    ],
    fieldActions: [
      {
        matIcon: 'edit',
        tooltip: 'Edit',
        delegateFunc: (obj) => {
          const ref = this.dialog.open(SalaryRulesCategoriesUpdateDialogComponent, {
            width: '700px',
            data: obj,
          });

          ref.afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        }
      },
      {
        matIcon: 'delete',
        tooltip: 'Delete',
        color: 'warn',
        confirmation: {
          title: '::Warning',
          message: '::AreYouSureYouWantToDelete?',
        },
        delegateFunc: (obj) => {
          this.loading.set(true);

          this.salaryRuleCategory.delete(obj.id).pipe(
            takeUntilDestroyed(this.destroyRef)
          ).subscribe(() => {
            this.refreshSubject.next();
            this.alert.success('::SuccessDelete');
          })
        }
      }
    ],
    fields: {
      code: {
        columnName: '::GoldenOwl:Code',
      },
    },
  });
}
