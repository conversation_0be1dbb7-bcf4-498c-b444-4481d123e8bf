import { Component, DestroyRef, inject } from '@angular/core';
import { AlertService, LanguagePipe, LOADING, TtwrFormComponent } from '@ttwr-framework/ngx-main-visuals';
import { MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { TerminalService } from '@proxy/hr/terminals';
import { finalize } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { terminals } from '../terminals.model';

@Component({
  selector: 'app-terminals-create-dialog',
  standalone: true,
  imports: [
    LanguagePipe,
    MatDialogContent,
    MatDialogTitle,
    TtwrFormComponent
  ],
  template: `
    <h2 mat-dialog-title>{{ '::CreateTerminal' | i18n }}</h2>
    <mat-dialog-content>
      <ttwr-form [config]="config"/>
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-gap: 1rem;
      --ttwr-form-grid-template-columns: 1fr 1fr;
    }
  `,
})
export class TerminalsCreateDialogComponent {
  private terminal = inject(TerminalService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);
  private dialogRef = inject(MatDialogRef);

  protected config = terminals.form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        this.terminal.create(body)
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::CreatedSuccessfully');
            this.dialogRef.close(true);
          })
      },
    },
    actions: [
      {
        label: '::TestTerminal',
        causeValidate: true,
        delegateFunc: body => {
          this.loading.set(true);

          this.terminal.testConnection(body).pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          ).subscribe(({ isSuccess, retrievedTime }) => {
            if (isSuccess) {
              this.alert.success('::ConnectionSuccess', {
                translateArguments: [String(retrievedTime)],
              });
            } else {
              this.alert.error('::UnexpectedError')
            }
          });
        },
      }
    ],
    fields: {
      ipAddress: {
        label: '::Terminal:IpAddress',
      },
      comKey: {
        label: '::Terminal:ComKey',
      },
      port: {
        label: '::Terminal:Port',
      },
    },
  })
}
