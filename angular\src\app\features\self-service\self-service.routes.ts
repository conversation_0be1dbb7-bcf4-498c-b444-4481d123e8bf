import { Routes } from '@angular/router';
import { ABP } from '@abp/ng.core';
import { routes as attendanceRequestsRoutes } from './attendance-requests/attendance-requests.routes';
import { routes as ownAllocationsRoutes } from './own-allocations/own-allocations.routes';
import { routes as leaveRequestsRoutes } from './leave-requests/leave-requests.routes';
import { routes as holidaysRoutes } from './holidays/holidays.routes';
import { routes as goTasksRoutes } from './go-tasks/go-tasks.routes';

export const routes: Routes = [
  {
    path: 'attendance-requests',
    children: attendanceRequestsRoutes,
  },
  {
    path: 'my-allocations',
    children: ownAllocationsRoutes,
  },
  {
    path: 'my-leave-requests',
    children: leaveRequestsRoutes,
  },
  {
    path: 'holiday-calendar',
    children: holidaysRoutes,
  },
  {
    path: 'go-tasks',
    children: goTasksRoutes,
  },
];

const selfServiceRoute = '::Menu:SelfService';

export const abpRoutes: ABP.Route[] = [
  {
    group: 'SelfService',
    name: selfServiceRoute,
    iconClass: 'account_circle',
    order: 3,
  },
  {
    path: '/self-service/attendance-requests',
    name: '::AttendanceRequests',
    parentName: selfServiceRoute,
    iconClass: 'fingerprint',
  },
  {
    path: '/self-service/my-allocations',
    name: '::Menu:MyAllocations',
    parentName: selfServiceRoute,
    iconClass: 'calendar_month',
  },
  {
    path: '/self-service/my-leave-requests',
    name: '::Menu:MyLeaveRequests',
    parentName: selfServiceRoute,
    iconClass: 'watch_later',
  },
  {
    path: '/self-service/holiday-calendar',
    name: '::Holiday',
    parentName: selfServiceRoute,
    iconClass: 'event',
  },
  {
    path: '/self-service/go-tasks',
    name: '::Tasks',
    parentName: selfServiceRoute,
    iconClass: 'task',
  },
];
