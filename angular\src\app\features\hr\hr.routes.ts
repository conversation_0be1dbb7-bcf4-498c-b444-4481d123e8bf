import { Routes } from '@angular/router';
import { type ABP } from '@abp/ng.core';
import { DepartmentsComponent } from './departments/departments.component';
import { routes as employeesRoutes } from './employees/employees.routes';
import { routes as contractRoutes } from './contracts/contracts.routes';
import { routes as workingSchedulesRoutes } from './working-schedules/working-schedules.routes';
import { AttendanceComponent } from './attendance/attendance.component';
import { TerminalsComponent } from './terminals/terminals.component';
import { EmployeePinsComponent } from './employee-pins/employee-pins.component';
import { routes as leaveTypesRoutes } from './leave-types/leave-types.routes';
import { routes as leaveAllocationsRoutes } from './leave-allocations/leave-allocations.routes';
import { JobTitlesComponent } from './job-titles/job-titles.component';
import { HolidaysComponent } from './holidays/holidays.component';
import { JobExecutionRecordComponent } from './job-execution-record/job-execution-record.component';
import { PenaltiesTypesComponent } from './penalties-types/penalties-types.component';
import { routes as penaltiesRoutes } from './penalties/penalties.routes';
import { routes as rewardsRoutes } from './rewards/rewards.routes';
import { routes as employeeCustodyManagementRoutes } from './employee-custody-management/employeeCustodyManagement.routes';
import { RewardsTypesComponent } from './rewards-types/rewards-types.component';

export const routes: Routes = [
  {
    path: 'departments',
    component: DepartmentsComponent,
  },
  {
    path: 'job-titles',
    component: JobTitlesComponent,
  },
  {
    path: 'employees',
    children: employeesRoutes,
  },
  {
    path: 'contracts',
    children: contractRoutes,
  },
  {
    path: 'working-schedules',
    children: workingSchedulesRoutes,
  },
  {
    path: 'attendance',
    component: AttendanceComponent,
  },
  {
    path: 'holidays',
    component: HolidaysComponent,
  },
  {
    path: 'job-execution-record',
    component: JobExecutionRecordComponent,
  },
  {
    path: 'fingerprint/terminals',
    component: TerminalsComponent,
  },
  {
    path: 'fingerprint/employee-pins',
    component: EmployeePinsComponent,
  },
  {
    path: 'time-off/leave-types',
    children: leaveTypesRoutes,
  },
  {
    path: 'time-off/leave-allocations',
    children: leaveAllocationsRoutes,
  },
  {
    path: 'penalties-management/penalties-types',
    component: PenaltiesTypesComponent,
  },
  {
    path: 'penalties-management/penalties',
    children: penaltiesRoutes,
  },
  {
    path: 'rewards-management/rewards-types',
    component: RewardsTypesComponent,
  },
  {
    path: 'rewards-management/rewards',
    children: rewardsRoutes,
  },
  {
    path: 'employee-custody-management/custody',
    children: employeeCustodyManagementRoutes,
  },
];

const hrRoute = '::Menu:HR';
const fingerPrintTerminalsRoute = '::Menu:FingerPrintTerminals';
const timeOffRoute = '::GoldenOwl:TimeOff';
const penaltiesRoute = '::PenaltiesManagement';
const rewardsRoute = '::RewaredsManagement';
const employeeCustodyManagementRoute = '::EmployeeCustodyManagement';

export const abpRoutes: ABP.Route[] = [
  {
    group: 'HR',
    name: hrRoute,
    iconClass: 'groups',
    order: 4,
  },
  {
    path: '/hr/departments',
    name: '::Menu:Department',
    iconClass: 'account_tree',
    parentName: hrRoute,
    requiredPolicy: 'HR.DepartmentsAndJobTitles.Index',
  },
  {
    path: '/hr/job-titles',
    name: '::Menu:JobTitles',
    iconClass: 'groups',
    parentName: hrRoute,
    requiredPolicy: 'HR.DepartmentsAndJobTitles.Index',
  },
  {
    path: '/hr/employees',
    name: '::Menu:Employee',
    iconClass: 'badge',
    parentName: hrRoute,
    requiredPolicy: 'HR.Employees.Index',
  },
  {
    path: '/hr/contracts',
    name: '::GoldenOwl:Contract',
    iconClass: 'description',
    parentName: hrRoute,
    requiredPolicy: 'HR.Contract.Index',
  },
  {
    path: '/hr/working-schedules',
    name: '::Menu:WorkingSchedules',
    iconClass: 'today',
    parentName: hrRoute,
    requiredPolicy: 'HR.WorkingScheduleManagement',
  },
  {
    path: '/hr/attendance',
    name: '::Menu:AttendanceRecords',
    iconClass: 'perm_contact_calendar',
    parentName: hrRoute,
  },
  {
    path: '/hr/holidays',
    name: '::Menu:Holiday',
    iconClass: 'event',
    parentName: hrRoute,
    requiredPolicy: 'HR.Holiday.Index',
  },
  {
    path: '/hr/job-execution-record',
    name: '::JobExecutionRecord',
    iconClass: 'dns',
    parentName: hrRoute,
    requiredPolicy: 'HR.JobExecutionRecord.Index',
  },
  {
    name: fingerPrintTerminalsRoute,
    iconClass: 'fingerprint',
    parentName: hrRoute,
  },
  {
    path: '/hr/fingerprint/terminals',
    name: '::Menu:FingerPrintTerminalDevices',
    iconClass: 'monitor_heart',
    parentName: fingerPrintTerminalsRoute,
    requiredPolicy: 'HR.Terminals.Index',
  },
  {
    path: '/hr/fingerprint/employee-pins',
    name: '::Menu:EmployeeFingerPrintPin',
    iconClass: 'recent_actors',
    parentName: fingerPrintTerminalsRoute,
    requiredPolicy: 'HR.EmployeePin.Index',
  },
  {
    name: timeOffRoute,
    iconClass: 'calendar_month',
    parentName: hrRoute,
  },
  {
    path: '/hr/time-off/leave-types',
    name: '::GoldenOwl:LeaveTypes',
    iconClass: 'list_alt',
    parentName: timeOffRoute,
    requiredPolicy: 'HR.LeaveTypeManagement',
  },
  {
    path: '/hr/time-off/leave-allocations',
    name: '::LeaveAllocations',
    iconClass: 'feed',
    parentName: timeOffRoute,
    requiredPolicy: 'HR.LeaveAllocations.Index',
  },
  {
    name: penaltiesRoute,
    iconClass: 'person_remove',
    parentName: hrRoute,
  },
  {
    path: '/hr/penalties-management/penalties-types',
    name: '::PenaltiesTypes',
    iconClass: 'format_list_bulleted',
    parentName: penaltiesRoute,
    requiredPolicy: 'HR.PenaltyType.Index',
  },
  {
    path: '/hr/penalties-management/penalties',
    name: '::Penalties',
    iconClass: 'block',
    parentName: penaltiesRoute,
    requiredPolicy: 'HR.Penalty.Index',
  },
  {
    name: rewardsRoute,
    iconClass: 'person_add',
    parentName: hrRoute,
  },
  {
    path: '/hr/rewards-management/rewards-types',
    name: '::RewardsTypes',
    iconClass: 'format_list_bulleted',
    parentName: rewardsRoute,
    requiredPolicy: 'HR.RewardType.Index',
  },
  {
    path: '/hr/rewards-management/rewards',
    name: '::Rewards',
    iconClass: 'stars',
    parentName: rewardsRoute,
    requiredPolicy: 'HR.Reward.Index',
  },
  {
    name: employeeCustodyManagementRoute,
    iconClass: 'widgets',
    parentName: hrRoute,
  },
  {
    path: '/hr/employee-custody-management/custody',
    name: '::EmployeeCustodyManagement:Custodies',
    iconClass: 'assignment_turned_in',
    parentName: employeeCustodyManagementRoute,
    requiredPolicy: 'HR.EmployeeCustody.Index',
  },
];
