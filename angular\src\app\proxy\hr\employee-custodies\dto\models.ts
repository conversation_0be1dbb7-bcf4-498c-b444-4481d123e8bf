import type { FullAuditedEntityDto, PagedResultRequestDto } from '@abp/ng.core';

export interface CreateEmployeeCustodyDto {
  employeeId?: string;
  description?: string;
  receiptDate?: string;
}

export interface EmployeeCustodyDto extends FullAuditedEntityDto<string> {
  employeeId?: string;
  employeeName?: string;
  description?: string;
  receiptDate?: string;
  releaseDate?: string;
  releaseReason?: string;
}

export interface GetEmployeeCustodiesRequestDto extends PagedResultRequestDto {
  employeeId?: string;
}

export interface ReleaseEmployeeCustodyDto {
  releaseDate?: string;
  releaseReason?: string;
}
