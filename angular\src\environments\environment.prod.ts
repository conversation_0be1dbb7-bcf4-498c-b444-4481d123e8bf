import { Environment } from '@abp/ng.core';

const baseUrl = 'http://localhost:4200';

export const environment = {
  production: false,
  application: {
    baseUrl,
    name: 'Golden Owl',
    logoUrl: '',
  },
  oAuthConfig: {
    issuer: 'https://localhost:44375/',
    redirectUri: baseUrl,
    clientId: 'GoldenOwl_App',
    responseType: 'code',
    scope: 'offline_access GoldenOwl',
    requireHttps: true,
  },
  apis: {
    default: {
      url: 'https://localhost:44375',
      rootNamespace: 'GoldenOwl',
    },
  },
  remoteEnv: {
    url: 'assets/config/config.json',
    mergeStrategy: mergeFunction,
  },
} as Environment;

interface RemoteEnvironment {
  baseURL: string;
  production: boolean;
  appName: string;
  logoUrl: string;
  issuer: string;
  clientId: string;
  responseType: string;
  scope: string;
  requireHttps: boolean;
  apiURL: string;
  rootNamespace: string;
}

export function mergeFunction(_: any, remoteEnv: RemoteEnvironment): Environment {
  return {
    production: remoteEnv.production,
    application: {
      baseUrl: remoteEnv.baseURL,
      name: remoteEnv.appName,
      logoUrl: remoteEnv.logoUrl,
    },
    oAuthConfig: {
      issuer: remoteEnv.issuer,
      redirectUri: remoteEnv.baseURL,
      clientId: remoteEnv.clientId,
      responseType: remoteEnv.responseType,
      scope: remoteEnv.scope,
      requireHttps: remoteEnv.requireHttps,
    },
    apis: {
      default: {
        url: remoteEnv.apiURL,
        rootNamespace: remoteEnv.rootNamespace,
      }
    }
  }
}
