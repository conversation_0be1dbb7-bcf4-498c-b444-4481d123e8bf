{"version": 3, "sources": ["../../../../../../node_modules/@fullcalendar/angular/fesm2020/fullcalendar-angular.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, Input, ViewChild, ContentChild, NgModule } from '@angular/core';\nimport { Calendar } from '@fullcalendar/core';\nimport { CustomRenderingStore } from '@fullcalendar/core/internal';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nconst _c0 = [\"*\"];\nconst _c1 = [\"rootEl\"];\nconst _c2 = a0 => ({\n  $implicit: a0\n});\nfunction TransportContainerComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2, 0);\n    i0.ɵɵelementContainer(2, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.elClasses || \"\")(\"ngStyle\", ctx_r0.elStyle || null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c2, ctx_r0.renderProps));\n  }\n}\nfunction TransportContainerComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 2, 0);\n    i0.ɵɵelementContainer(2, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.elClasses || \"\")(\"ngStyle\", ctx_r0.elStyle || null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c2, ctx_r0.renderProps));\n  }\n}\nfunction TransportContainerComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 2, 0);\n    i0.ɵɵelementContainer(2, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.elClasses || \"\")(\"ngStyle\", ctx_r0.elStyle || null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c2, ctx_r0.renderProps));\n  }\n}\nfunction TransportContainerComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 2, 0);\n    i0.ɵɵelementContainer(2, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.elClasses || \"\")(\"ngStyle\", ctx_r0.elStyle || null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c2, ctx_r0.renderProps));\n  }\n}\nfunction TransportContainerComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 2, 0);\n    i0.ɵɵelementContainer(2, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.elClasses || \"\")(\"ngStyle\", ctx_r0.elStyle || null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c2, ctx_r0.renderProps));\n  }\n}\nfunction TransportContainerComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 2, 0);\n    i0.ɵɵelementContainer(2, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.elClasses || \"\")(\"ngStyle\", ctx_r0.elStyle || null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c2, ctx_r0.renderProps));\n  }\n}\nconst _c3 = [\"dayHeaderContent\"];\nconst _c4 = [\"dayCellContent\"];\nconst _c5 = [\"weekNumberContent\"];\nconst _c6 = [\"nowIndicatorContent\"];\nconst _c7 = [\"eventContent\"];\nconst _c8 = [\"slotLaneContent\"];\nconst _c9 = [\"slotLabelContent\"];\nconst _c10 = [\"allDayContent\"];\nconst _c11 = [\"moreLinkContent\"];\nconst _c12 = [\"noEventsContent\"];\nconst _c13 = [\"resourceAreaHeaderContent\"];\nconst _c14 = [\"resourceGroupLabelContent\"];\nconst _c15 = [\"resourceLabelContent\"];\nconst _c16 = [\"resourceLaneContent\"];\nconst _c17 = [\"resourceGroupLaneContent\"];\nfunction FullCalendarComponent_transport_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"transport-container\", 1);\n  }\n  if (rf & 2) {\n    const customRendering_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"inPlaceOf\", customRendering_r1.containerEl)(\"reportEl\", customRendering_r1.reportNewContainerEl)(\"elTag\", customRendering_r1.elTag)(\"elClasses\", customRendering_r1.elClasses)(\"elStyle\", customRendering_r1.elStyle)(\"elAttrs\", customRendering_r1.elAttrs)(\"template\", ctx_r1.templateMap[customRendering_r1.generatorName])(\"renderProps\", customRendering_r1.renderProps);\n  }\n}\nconst OPTION_IS_DEEP = {\n  headerToolbar: true,\n  footerToolbar: true,\n  events: true,\n  eventSources: true,\n  resources: true\n};\n/*\nNOTE: keep synced with component\n*/\nconst OPTION_INPUT_NAMES = ['events', 'eventSources', 'resources'];\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\n/*\nReally simple clone utility. Only copies plain arrays, objects, and Dates. Transfers everything else as-is.\nWanted to use a third-party lib, but none did exactly this.\n*/\nfunction deepCopy(input) {\n  if (Array.isArray(input)) {\n    return input.map(deepCopy);\n  } else if (input instanceof Date) {\n    return new Date(input.valueOf());\n  } else if (typeof input === 'object' && input) {\n    // non-null object\n    return mapHash(input, deepCopy);\n  } else {\n    // everything else (null, function, etc)\n    return input;\n  }\n}\nfunction mapHash(input, func) {\n  const output = {};\n  for (const key in input) {\n    if (hasOwnProperty.call(input, key)) {\n      output[key] = func(input[key], key);\n    }\n  }\n  return output;\n}\n\n/*\nForked from https://github.com/epoberezkin/fast-deep-equal (also has MIT license)\nNeeded ESM support or else Angular complains about treeshaking\n(https://github.com/fullcalendar/fullcalendar-angular/issues/421)\n*/\nfunction deepEqual(a, b) {\n  if (a === b) return true;\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;) if (!deepEqual(a[i], b[i])) return false;\n      return true;\n    }\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n    for (i = length; i-- !== 0;) if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n      if (!deepEqual(a[key], b[key])) return false;\n    }\n    return true;\n  }\n  // true if both NaN, false otherwise\n  return a !== a && b !== b;\n}\nconst dummyContainer$1 = typeof document !== 'undefined' ? document.createDocumentFragment() : null;\nclass OffscreenFragmentComponent {\n  constructor(element) {\n    this.element = element;\n  }\n  ngAfterViewInit() {\n    if (dummyContainer$1) {\n      dummyContainer$1.appendChild(this.element.nativeElement);\n    }\n  }\n  // invoked BEFORE component removed from DOM\n  ngOnDestroy() {\n    if (dummyContainer$1) {\n      dummyContainer$1.removeChild(this.element.nativeElement);\n    }\n  }\n}\nOffscreenFragmentComponent.ɵfac = function OffscreenFragmentComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || OffscreenFragmentComponent)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\nOffscreenFragmentComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: OffscreenFragmentComponent,\n  selectors: [[\"offscreen-fragment\"]],\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function OffscreenFragmentComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OffscreenFragmentComponent, [{\n    type: Component,\n    args: [{\n      selector: 'offscreen-fragment',\n      template: '<ng-content></ng-content>',\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\nconst dummyContainer = typeof document !== 'undefined' ? document.createDocumentFragment() : null;\nclass TransportContainerComponent {\n  ngAfterViewInit() {\n    const rootEl = this.rootElRef?.nativeElement; // assumed defined\n    replaceEl(rootEl, this.inPlaceOf);\n    applyElAttrs(rootEl, undefined, this.elAttrs);\n    // insurance for if Preact recreates and reroots inPlaceOf element\n    this.inPlaceOf.style.display = 'none';\n    this.reportEl(rootEl);\n  }\n  ngOnChanges(changes) {\n    const rootEl = this.rootElRef?.nativeElement;\n    // ngOnChanges is called before ngAfterViewInit (and before DOM initializes)\n    // so make sure rootEl is defined before doing anything\n    if (rootEl) {\n      // If the ContentContainer's tagName changed, it will create a new DOM element in its\n      // original place. Detect this and re-replace.\n      if (this.inPlaceOf.parentNode !== dummyContainer) {\n        replaceEl(rootEl, this.inPlaceOf);\n        applyElAttrs(rootEl, undefined, this.elAttrs);\n        this.reportEl(rootEl);\n      } else {\n        const elAttrsChange = changes['elAttrs'];\n        if (elAttrsChange) {\n          applyElAttrs(rootEl, elAttrsChange.previousValue, elAttrsChange.currentValue);\n        }\n      }\n    }\n  }\n  // invoked BEFORE component removed from DOM\n  ngOnDestroy() {\n    if (\n    // protect against Preact recreating and rerooting inPlaceOf element\n    this.inPlaceOf.parentNode === dummyContainer && dummyContainer) {\n      dummyContainer.removeChild(this.inPlaceOf);\n    }\n    this.reportEl(null);\n  }\n}\nTransportContainerComponent.ɵfac = function TransportContainerComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || TransportContainerComponent)();\n};\nTransportContainerComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TransportContainerComponent,\n  selectors: [[\"transport-container\"]],\n  viewQuery: function TransportContainerComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c1, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rootElRef = _t.first);\n    }\n  },\n  inputs: {\n    inPlaceOf: \"inPlaceOf\",\n    reportEl: \"reportEl\",\n    elTag: \"elTag\",\n    elClasses: \"elClasses\",\n    elStyle: \"elStyle\",\n    elAttrs: \"elAttrs\",\n    template: \"template\",\n    renderProps: \"renderProps\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 6,\n  vars: 6,\n  consts: [[\"rootEl\", \"\"], [3, \"ngIf\"], [3, \"ngClass\", \"ngStyle\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n  template: function TransportContainerComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, TransportContainerComponent_ng_template_0_Template, 3, 6, \"ng-template\", 1)(1, TransportContainerComponent_ng_template_1_Template, 3, 6, \"ng-template\", 1)(2, TransportContainerComponent_ng_template_2_Template, 3, 6, \"ng-template\", 1)(3, TransportContainerComponent_ng_template_3_Template, 3, 6, \"ng-template\", 1)(4, TransportContainerComponent_ng_template_4_Template, 3, 6, \"ng-template\", 1)(5, TransportContainerComponent_ng_template_5_Template, 3, 6, \"ng-template\", 1);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.elTag == \"div\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.elTag == \"span\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.elTag == \"a\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.elTag == \"tr\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.elTag == \"th\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.elTag == \"td\");\n    }\n  },\n  dependencies: [i1.NgIf, i1.NgClass, i1.NgStyle, i1.NgTemplateOutlet],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TransportContainerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'transport-container',\n      encapsulation: ViewEncapsulation.None,\n      template: \"<ng-template [ngIf]=\\\"elTag == 'div'\\\">\\n  <div #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </div>\\n</ng-template>\\n<ng-template [ngIf]=\\\"elTag == 'span'\\\">\\n  <span #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </span>\\n</ng-template>\\n<ng-template [ngIf]=\\\"elTag == 'a'\\\">\\n  <a #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </a>\\n</ng-template>\\n<ng-template [ngIf]=\\\"elTag == 'tr'\\\">\\n  <tr #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </tr>\\n</ng-template>\\n<ng-template [ngIf]=\\\"elTag == 'th'\\\">\\n  <th #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </th>\\n</ng-template>\\n<ng-template [ngIf]=\\\"elTag == 'td'\\\">\\n  <td #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </td>\\n</ng-template>\\n\"\n    }]\n  }], null, {\n    inPlaceOf: [{\n      type: Input\n    }],\n    reportEl: [{\n      type: Input\n    }],\n    elTag: [{\n      type: Input\n    }],\n    elClasses: [{\n      type: Input\n    }],\n    elStyle: [{\n      type: Input\n    }],\n    elAttrs: [{\n      type: Input\n    }],\n    template: [{\n      type: Input\n    }],\n    renderProps: [{\n      type: Input\n    }],\n    rootElRef: [{\n      type: ViewChild,\n      args: ['rootEl']\n    }]\n  });\n})();\nfunction replaceEl(subject, inPlaceOf) {\n  inPlaceOf.parentNode?.insertBefore(subject, inPlaceOf.nextSibling);\n  if (dummyContainer) {\n    dummyContainer.appendChild(inPlaceOf);\n  }\n}\nfunction applyElAttrs(el, previousAttrs = {}, currentAttrs = {}) {\n  // these are called \"attributes\" but they manipulate DOM node *properties*\n  for (const attrName in previousAttrs) {\n    if (!(attrName in currentAttrs)) {\n      el[attrName] = null;\n    }\n  }\n  for (const attrName in currentAttrs) {\n    el[attrName] = currentAttrs[attrName];\n  }\n}\nclass FullCalendarComponent {\n  constructor(element, changeDetector) {\n    this.element = element;\n    this.calendar = null;\n    this.optionSnapshot = {}; // for diffing\n    this.customRenderingMap = new Map();\n    this.templateMap = {};\n    const customRenderingStore = new CustomRenderingStore();\n    customRenderingStore.subscribe(customRenderingMap => {\n      this.customRenderingMap = customRenderingMap;\n      this.customRenderingArray = undefined; // clear cache\n      changeDetector.detectChanges();\n    });\n    this.handleCustomRendering = customRenderingStore.handle.bind(customRenderingStore);\n    this.templateMap = this; // alias to this\n  }\n  ngAfterViewInit() {\n    const {\n      deepChangeDetection\n    } = this;\n    const options = {\n      ...this.options,\n      ...this.buildInputOptions()\n    };\n    // initialize snapshot\n    this.optionSnapshot = mapHash(options, (optionVal, optionName) => deepChangeDetection && OPTION_IS_DEEP[optionName] ? deepCopy(optionVal) : optionVal);\n    const calendarEl = this.element.nativeElement;\n    const calendar = this.calendar = new Calendar(calendarEl, {\n      ...options,\n      ...this.buildExtraOptions()\n    });\n    // Ionic dimensions hack\n    // https://github.com/fullcalendar/fullcalendar/issues/4976\n    const ionContent = calendarEl.closest('ion-content');\n    if (ionContent && ionContent.componentOnReady) {\n      ionContent.componentOnReady().then(() => {\n        window.requestAnimationFrame(() => {\n          calendar.render();\n        });\n      });\n    } else {\n      calendar.render();\n    }\n    // Angular v19, whether because of new Vite dev environment or not,\n    // loads outer elements' styles late, so dimensions might not be final here.\n    // Force a size-update after a delay.\n    setTimeout(() => calendar.updateSize());\n  }\n  /*\n  allows us to manually detect complex input changes, internal mutations to certain options.\n  called before ngOnChanges. called much more often than ngOnChanges.\n  */\n  ngDoCheck() {\n    if (this.calendar) {\n      // not the initial render\n      const {\n        deepChangeDetection,\n        optionSnapshot\n      } = this;\n      const newOptions = {\n        ...this.options,\n        ...this.buildInputOptions()\n      };\n      const newProcessedOptions = {};\n      const changedOptionNames = [];\n      // detect adds and updates (and update snapshot)\n      for (const optionName in newOptions) {\n        if (newOptions.hasOwnProperty(optionName)) {\n          let optionVal = newOptions[optionName];\n          if (deepChangeDetection && OPTION_IS_DEEP[optionName]) {\n            if (!deepEqual(optionSnapshot[optionName], optionVal)) {\n              optionSnapshot[optionName] = deepCopy(optionVal);\n              changedOptionNames.push(optionName);\n            }\n          } else {\n            if (optionSnapshot[optionName] !== optionVal) {\n              optionSnapshot[optionName] = optionVal;\n              changedOptionNames.push(optionName);\n            }\n          }\n          newProcessedOptions[optionName] = optionVal;\n        }\n      }\n      const oldOptionNames = Object.keys(optionSnapshot);\n      // detect removals (and update snapshot)\n      for (const optionName of oldOptionNames) {\n        if (!(optionName in newOptions)) {\n          // doesn't exist in new options?\n          delete optionSnapshot[optionName];\n          changedOptionNames.push(optionName);\n        }\n      }\n      if (changedOptionNames.length) {\n        this.calendar.pauseRendering();\n        this.calendar.resetOptions({\n          ...newProcessedOptions,\n          ...this.buildExtraOptions()\n        }, changedOptionNames);\n      }\n    }\n  }\n  ngAfterContentChecked() {\n    if (this.calendar) {\n      // too defensive?\n      this.calendar.resumeRendering();\n    }\n  }\n  ngOnDestroy() {\n    if (this.calendar) {\n      // too defensive?\n      this.calendar.destroy();\n      this.calendar = null;\n    }\n  }\n  get customRenderings() {\n    return this.customRenderingArray || (this.customRenderingArray = [...this.customRenderingMap.values()]);\n  }\n  getApi() {\n    return this.calendar;\n  }\n  buildInputOptions() {\n    const options = {};\n    for (const inputName of OPTION_INPUT_NAMES) {\n      const inputValue = this[inputName];\n      if (inputValue != null) {\n        // exclude both null and undefined\n        options[inputName] = inputValue;\n      }\n    }\n    return options;\n  }\n  buildExtraOptions() {\n    return {\n      handleCustomRendering: this.handleCustomRendering,\n      customRenderingMetaMap: this.templateMap,\n      customRenderingReplaces: true\n    };\n  }\n  // for `trackBy` in loop\n  trackCustomRendering(index, customRendering) {\n    return customRendering.id;\n  }\n}\nFullCalendarComponent.ɵfac = function FullCalendarComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || FullCalendarComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\nFullCalendarComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: FullCalendarComponent,\n  selectors: [[\"full-calendar\"]],\n  contentQueries: function FullCalendarComponent_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, _c3, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c4, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c5, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c6, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c7, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c8, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c9, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c10, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c11, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c12, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c13, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c14, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c15, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c16, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c17, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dayHeaderContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dayCellContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.weekNumberContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nowIndicatorContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.eventContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.slotLaneContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.slotLabelContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.allDayContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.moreLinkContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.noEventsContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.resourceAreaHeaderContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.resourceGroupLabelContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.resourceLabelContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.resourceLaneContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.resourceGroupLaneContent = _t.first);\n    }\n  },\n  inputs: {\n    options: \"options\",\n    deepChangeDetection: \"deepChangeDetection\",\n    events: \"events\",\n    eventSources: \"eventSources\",\n    resources: \"resources\"\n  },\n  decls: 2,\n  vars: 2,\n  consts: [[3, \"inPlaceOf\", \"reportEl\", \"elTag\", \"elClasses\", \"elStyle\", \"elAttrs\", \"template\", \"renderProps\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [3, \"inPlaceOf\", \"reportEl\", \"elTag\", \"elClasses\", \"elStyle\", \"elAttrs\", \"template\", \"renderProps\"]],\n  template: function FullCalendarComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"offscreen-fragment\");\n      i0.ɵɵtemplate(1, FullCalendarComponent_transport_container_1_Template, 1, 8, \"transport-container\", 0);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngForOf\", ctx.customRenderings)(\"ngForTrackBy\", ctx.trackCustomRendering);\n    }\n  },\n  dependencies: [OffscreenFragmentComponent, TransportContainerComponent, i1.NgForOf],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FullCalendarComponent, [{\n    type: Component,\n    args: [{\n      selector: 'full-calendar',\n      encapsulation: ViewEncapsulation.None // the styles are root-level, not scoped within the component\n      ,\n      template: \"<offscreen-fragment>\\n  <transport-container *ngFor=\\\"let customRendering of customRenderings; trackBy:trackCustomRendering\\\"\\n    [inPlaceOf]=\\\"customRendering.containerEl\\\"\\n    [reportEl]=\\\"customRendering.reportNewContainerEl\\\"\\n    [elTag]=\\\"customRendering.elTag\\\"\\n    [elClasses]=\\\"customRendering.elClasses\\\"\\n    [elStyle]=\\\"customRendering.elStyle\\\"\\n    [elAttrs]=\\\"customRendering.elAttrs\\\"\\n    [template]=\\\"templateMap[customRendering.generatorName]!\\\"\\n    [renderProps]=\\\"customRendering.renderProps\\\"\\n  ></transport-container>\\n</offscreen-fragment>\\n\"\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    options: [{\n      type: Input\n    }],\n    deepChangeDetection: [{\n      type: Input\n    }],\n    events: [{\n      type: Input\n    }],\n    eventSources: [{\n      type: Input\n    }],\n    resources: [{\n      type: Input\n    }],\n    dayHeaderContent: [{\n      type: ContentChild,\n      args: ['dayHeaderContent', {\n        static: true\n      }]\n    }],\n    dayCellContent: [{\n      type: ContentChild,\n      args: ['dayCellContent', {\n        static: true\n      }]\n    }],\n    weekNumberContent: [{\n      type: ContentChild,\n      args: ['weekNumberContent', {\n        static: true\n      }]\n    }],\n    nowIndicatorContent: [{\n      type: ContentChild,\n      args: ['nowIndicatorContent', {\n        static: true\n      }]\n    }],\n    eventContent: [{\n      type: ContentChild,\n      args: ['eventContent', {\n        static: true\n      }]\n    }],\n    slotLaneContent: [{\n      type: ContentChild,\n      args: ['slotLaneContent', {\n        static: true\n      }]\n    }],\n    slotLabelContent: [{\n      type: ContentChild,\n      args: ['slotLabelContent', {\n        static: true\n      }]\n    }],\n    allDayContent: [{\n      type: ContentChild,\n      args: ['allDayContent', {\n        static: true\n      }]\n    }],\n    moreLinkContent: [{\n      type: ContentChild,\n      args: ['moreLinkContent', {\n        static: true\n      }]\n    }],\n    noEventsContent: [{\n      type: ContentChild,\n      args: ['noEventsContent', {\n        static: true\n      }]\n    }],\n    resourceAreaHeaderContent: [{\n      type: ContentChild,\n      args: ['resourceAreaHeaderContent', {\n        static: true\n      }]\n    }],\n    resourceGroupLabelContent: [{\n      type: ContentChild,\n      args: ['resourceGroupLabelContent', {\n        static: true\n      }]\n    }],\n    resourceLabelContent: [{\n      type: ContentChild,\n      args: ['resourceLabelContent', {\n        static: true\n      }]\n    }],\n    resourceLaneContent: [{\n      type: ContentChild,\n      args: ['resourceLaneContent', {\n        static: true\n      }]\n    }],\n    resourceGroupLaneContent: [{\n      type: ContentChild,\n      args: ['resourceGroupLaneContent', {\n        static: true\n      }]\n    }]\n  });\n})();\nclass FullCalendarModule {}\nFullCalendarModule.ɵfac = function FullCalendarModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || FullCalendarModule)();\n};\nFullCalendarModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: FullCalendarModule,\n  declarations: [FullCalendarComponent, OffscreenFragmentComponent, TransportContainerComponent],\n  imports: [CommonModule],\n  exports: [FullCalendarComponent]\n});\nFullCalendarModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule]]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FullCalendarModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [FullCalendarComponent, OffscreenFragmentComponent, TransportContainerComponent],\n      imports: [CommonModule],\n      exports: [FullCalendarComponent]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of lib\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FullCalendarComponent, FullCalendarModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,QAAQ;AACrB,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,IAAG,mBAAmB,GAAG,CAAC;AAC1B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,OAAO,aAAa,EAAE,EAAE,WAAW,OAAO,WAAW,IAAI;AAClF,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,QAAQ,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,WAAW,CAAC;AAAA,EAC9H;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,GAAG,CAAC;AACjC,IAAG,mBAAmB,GAAG,CAAC;AAC1B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,OAAO,aAAa,EAAE,EAAE,WAAW,OAAO,WAAW,IAAI;AAClF,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,QAAQ,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,WAAW,CAAC;AAAA,EAC9H;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,GAAG,CAAC;AAC9B,IAAG,mBAAmB,GAAG,CAAC;AAC1B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,OAAO,aAAa,EAAE,EAAE,WAAW,OAAO,WAAW,IAAI;AAClF,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,QAAQ,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,WAAW,CAAC;AAAA,EAC9H;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,GAAG,CAAC;AAC/B,IAAG,mBAAmB,GAAG,CAAC;AAC1B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,OAAO,aAAa,EAAE,EAAE,WAAW,OAAO,WAAW,IAAI;AAClF,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,QAAQ,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,WAAW,CAAC;AAAA,EAC9H;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,GAAG,CAAC;AAC/B,IAAG,mBAAmB,GAAG,CAAC;AAC1B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,OAAO,aAAa,EAAE,EAAE,WAAW,OAAO,WAAW,IAAI;AAClF,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,QAAQ,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,WAAW,CAAC;AAAA,EAC9H;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,GAAG,CAAC;AAC/B,IAAG,mBAAmB,GAAG,CAAC;AAC1B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,OAAO,aAAa,EAAE,EAAE,WAAW,OAAO,WAAW,IAAI;AAClF,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,QAAQ,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,WAAW,CAAC;AAAA,EAC9H;AACF;AACA,IAAM,MAAM,CAAC,kBAAkB;AAC/B,IAAM,MAAM,CAAC,gBAAgB;AAC7B,IAAM,MAAM,CAAC,mBAAmB;AAChC,IAAM,MAAM,CAAC,qBAAqB;AAClC,IAAM,MAAM,CAAC,cAAc;AAC3B,IAAM,MAAM,CAAC,iBAAiB;AAC9B,IAAM,MAAM,CAAC,kBAAkB;AAC/B,IAAM,OAAO,CAAC,eAAe;AAC7B,IAAM,OAAO,CAAC,iBAAiB;AAC/B,IAAM,OAAO,CAAC,iBAAiB;AAC/B,IAAM,OAAO,CAAC,2BAA2B;AACzC,IAAM,OAAO,CAAC,2BAA2B;AACzC,IAAM,OAAO,CAAC,sBAAsB;AACpC,IAAM,OAAO,CAAC,qBAAqB;AACnC,IAAM,OAAO,CAAC,0BAA0B;AACxC,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,uBAAuB,CAAC;AAAA,EAC1C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,qBAAqB,IAAI;AAC/B,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,aAAa,mBAAmB,WAAW,EAAE,YAAY,mBAAmB,oBAAoB,EAAE,SAAS,mBAAmB,KAAK,EAAE,aAAa,mBAAmB,SAAS,EAAE,WAAW,mBAAmB,OAAO,EAAE,WAAW,mBAAmB,OAAO,EAAE,YAAY,OAAO,YAAY,mBAAmB,aAAa,CAAC,EAAE,eAAe,mBAAmB,WAAW;AAAA,EAC7X;AACF;AACA,IAAM,iBAAiB;AAAA,EACrB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,WAAW;AACb;AAIA,IAAM,qBAAqB,CAAC,UAAU,gBAAgB,WAAW;AACjE,IAAM,iBAAiB,OAAO,UAAU;AAKxC,SAAS,SAAS,OAAO;AACvB,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,MAAM,IAAI,QAAQ;AAAA,EAC3B,WAAW,iBAAiB,MAAM;AAChC,WAAO,IAAI,KAAK,MAAM,QAAQ,CAAC;AAAA,EACjC,WAAW,OAAO,UAAU,YAAY,OAAO;AAE7C,WAAO,QAAQ,OAAO,QAAQ;AAAA,EAChC,OAAO;AAEL,WAAO;AAAA,EACT;AACF;AACA,SAAS,QAAQ,OAAO,MAAM;AAC5B,QAAM,SAAS,CAAC;AAChB,aAAW,OAAO,OAAO;AACvB,QAAI,eAAe,KAAK,OAAO,GAAG,GAAG;AACnC,aAAO,GAAG,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG;AAAA,IACpC;AAAA,EACF;AACA,SAAO;AACT;AAOA,SAAS,UAAU,GAAG,GAAG;AACvB,MAAI,MAAM,EAAG,QAAO;AACpB,MAAI,KAAK,KAAK,OAAO,KAAK,YAAY,OAAO,KAAK,UAAU;AAC1D,QAAI,EAAE,gBAAgB,EAAE,YAAa,QAAO;AAC5C,QAAI,QAAQ,GAAG;AACf,QAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,eAAS,EAAE;AACX,UAAI,UAAU,EAAE,OAAQ,QAAO;AAC/B,WAAK,IAAI,QAAQ,QAAQ,IAAI,KAAI,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAG,QAAO;AAChE,aAAO;AAAA,IACT;AACA,QAAI,EAAE,gBAAgB,OAAQ,QAAO,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE;AAC5E,QAAI,EAAE,YAAY,OAAO,UAAU,QAAS,QAAO,EAAE,QAAQ,MAAM,EAAE,QAAQ;AAC7E,QAAI,EAAE,aAAa,OAAO,UAAU,SAAU,QAAO,EAAE,SAAS,MAAM,EAAE,SAAS;AACjF,WAAO,OAAO,KAAK,CAAC;AACpB,aAAS,KAAK;AACd,QAAI,WAAW,OAAO,KAAK,CAAC,EAAE,OAAQ,QAAO;AAC7C,SAAK,IAAI,QAAQ,QAAQ,IAAI,KAAI,CAAC,OAAO,UAAU,eAAe,KAAK,GAAG,KAAK,CAAC,CAAC,EAAG,QAAO;AAC3F,SAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,UAAI,MAAM,KAAK,CAAC;AAChB,UAAI,CAAC,UAAU,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,EAAG,QAAO;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAEA,SAAO,MAAM,KAAK,MAAM;AAC1B;AACA,IAAM,mBAAmB,OAAO,aAAa,cAAc,SAAS,uBAAuB,IAAI;AAC/F,IAAM,6BAAN,MAAiC;AAAA,EAC/B,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,kBAAkB;AAChB,QAAI,kBAAkB;AACpB,uBAAiB,YAAY,KAAK,QAAQ,aAAa;AAAA,IACzD;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,QAAI,kBAAkB;AACpB,uBAAiB,YAAY,KAAK,QAAQ,aAAa;AAAA,IACzD;AAAA,EACF;AACF;AACA,2BAA2B,OAAO,SAAS,mCAAmC,mBAAmB;AAC/F,SAAO,KAAK,qBAAqB,4BAA+B,kBAAqB,UAAU,CAAC;AAClG;AACA,2BAA2B,OAAyB,kBAAkB;AAAA,EACpE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,EAClC,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,QAAI,KAAK,GAAG;AACV,MAAG,gBAAgB;AACnB,MAAG,aAAa,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EACA,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,iBAAiB,OAAO,aAAa,cAAc,SAAS,uBAAuB,IAAI;AAC7F,IAAM,8BAAN,MAAkC;AAAA,EAChC,kBAAkB;AAChB,UAAM,SAAS,KAAK,WAAW;AAC/B,cAAU,QAAQ,KAAK,SAAS;AAChC,iBAAa,QAAQ,QAAW,KAAK,OAAO;AAE5C,SAAK,UAAU,MAAM,UAAU;AAC/B,SAAK,SAAS,MAAM;AAAA,EACtB;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,SAAS,KAAK,WAAW;AAG/B,QAAI,QAAQ;AAGV,UAAI,KAAK,UAAU,eAAe,gBAAgB;AAChD,kBAAU,QAAQ,KAAK,SAAS;AAChC,qBAAa,QAAQ,QAAW,KAAK,OAAO;AAC5C,aAAK,SAAS,MAAM;AAAA,MACtB,OAAO;AACL,cAAM,gBAAgB,QAAQ,SAAS;AACvC,YAAI,eAAe;AACjB,uBAAa,QAAQ,cAAc,eAAe,cAAc,YAAY;AAAA,QAC9E;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ;AAAA;AAAA,MAEA,KAAK,UAAU,eAAe,kBAAkB;AAAA,MAAgB;AAC9D,qBAAe,YAAY,KAAK,SAAS;AAAA,IAC3C;AACA,SAAK,SAAS,IAAI;AAAA,EACpB;AACF;AACA,4BAA4B,OAAO,SAAS,oCAAoC,mBAAmB;AACjG,SAAO,KAAK,qBAAqB,6BAA6B;AAChE;AACA,4BAA4B,OAAyB,kBAAkB;AAAA,EACrE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,EACnC,WAAW,SAAS,kCAAkC,IAAI,KAAK;AAC7D,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,KAAK,CAAC;AAAA,IACvB;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY,GAAG;AAAA,IAClE;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,UAAU;AAAA,IACV,OAAO;AAAA,IACP,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,EACf;AAAA,EACA,UAAU,CAAI,oBAAoB;AAAA,EAClC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,CAAC;AAAA,EACnH,UAAU,SAAS,qCAAqC,IAAI,KAAK;AAC/D,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,eAAe,CAAC,EAAE,GAAG,oDAAoD,GAAG,GAAG,eAAe,CAAC,EAAE,GAAG,oDAAoD,GAAG,GAAG,eAAe,CAAC,EAAE,GAAG,oDAAoD,GAAG,GAAG,eAAe,CAAC,EAAE,GAAG,oDAAoD,GAAG,GAAG,eAAe,CAAC,EAAE,GAAG,oDAAoD,GAAG,GAAG,eAAe,CAAC;AAAA,IACxe;AACA,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,QAAQ,IAAI,SAAS,KAAK;AACxC,MAAG,UAAU;AACb,MAAG,WAAW,QAAQ,IAAI,SAAS,MAAM;AACzC,MAAG,UAAU;AACb,MAAG,WAAW,QAAQ,IAAI,SAAS,GAAG;AACtC,MAAG,UAAU;AACb,MAAG,WAAW,QAAQ,IAAI,SAAS,IAAI;AACvC,MAAG,UAAU;AACb,MAAG,WAAW,QAAQ,IAAI,SAAS,IAAI;AACvC,MAAG,UAAU;AACb,MAAG,WAAW,QAAQ,IAAI,SAAS,IAAI;AAAA,IACzC;AAAA,EACF;AAAA,EACA,cAAc,CAAI,MAAS,SAAY,SAAY,gBAAgB;AAAA,EACnE,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,UAAU,SAAS,WAAW;AACrC,YAAU,YAAY,aAAa,SAAS,UAAU,WAAW;AACjE,MAAI,gBAAgB;AAClB,mBAAe,YAAY,SAAS;AAAA,EACtC;AACF;AACA,SAAS,aAAa,IAAI,gBAAgB,CAAC,GAAG,eAAe,CAAC,GAAG;AAE/D,aAAW,YAAY,eAAe;AACpC,QAAI,EAAE,YAAY,eAAe;AAC/B,SAAG,QAAQ,IAAI;AAAA,IACjB;AAAA,EACF;AACA,aAAW,YAAY,cAAc;AACnC,OAAG,QAAQ,IAAI,aAAa,QAAQ;AAAA,EACtC;AACF;AACA,IAAM,wBAAN,MAA4B;AAAA,EAC1B,YAAY,SAAS,gBAAgB;AACnC,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,iBAAiB,CAAC;AACvB,SAAK,qBAAqB,oBAAI,IAAI;AAClC,SAAK,cAAc,CAAC;AACpB,UAAM,uBAAuB,IAAI,qBAAqB;AACtD,yBAAqB,UAAU,wBAAsB;AACnD,WAAK,qBAAqB;AAC1B,WAAK,uBAAuB;AAC5B,qBAAe,cAAc;AAAA,IAC/B,CAAC;AACD,SAAK,wBAAwB,qBAAqB,OAAO,KAAK,oBAAoB;AAClF,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,kBAAkB;AAChB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,UAAU,kCACX,KAAK,UACL,KAAK,kBAAkB;AAG5B,SAAK,iBAAiB,QAAQ,SAAS,CAAC,WAAW,eAAe,uBAAuB,eAAe,UAAU,IAAI,SAAS,SAAS,IAAI,SAAS;AACrJ,UAAM,aAAa,KAAK,QAAQ;AAChC,UAAM,WAAW,KAAK,WAAW,IAAI,SAAS,YAAY,kCACrD,UACA,KAAK,kBAAkB,EAC3B;AAGD,UAAM,aAAa,WAAW,QAAQ,aAAa;AACnD,QAAI,cAAc,WAAW,kBAAkB;AAC7C,iBAAW,iBAAiB,EAAE,KAAK,MAAM;AACvC,eAAO,sBAAsB,MAAM;AACjC,mBAAS,OAAO;AAAA,QAClB,CAAC;AAAA,MACH,CAAC;AAAA,IACH,OAAO;AACL,eAAS,OAAO;AAAA,IAClB;AAIA,eAAW,MAAM,SAAS,WAAW,CAAC;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,QAAI,KAAK,UAAU;AAEjB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,aAAa,kCACd,KAAK,UACL,KAAK,kBAAkB;AAE5B,YAAM,sBAAsB,CAAC;AAC7B,YAAM,qBAAqB,CAAC;AAE5B,iBAAW,cAAc,YAAY;AACnC,YAAI,WAAW,eAAe,UAAU,GAAG;AACzC,cAAI,YAAY,WAAW,UAAU;AACrC,cAAI,uBAAuB,eAAe,UAAU,GAAG;AACrD,gBAAI,CAAC,UAAU,eAAe,UAAU,GAAG,SAAS,GAAG;AACrD,6BAAe,UAAU,IAAI,SAAS,SAAS;AAC/C,iCAAmB,KAAK,UAAU;AAAA,YACpC;AAAA,UACF,OAAO;AACL,gBAAI,eAAe,UAAU,MAAM,WAAW;AAC5C,6BAAe,UAAU,IAAI;AAC7B,iCAAmB,KAAK,UAAU;AAAA,YACpC;AAAA,UACF;AACA,8BAAoB,UAAU,IAAI;AAAA,QACpC;AAAA,MACF;AACA,YAAM,iBAAiB,OAAO,KAAK,cAAc;AAEjD,iBAAW,cAAc,gBAAgB;AACvC,YAAI,EAAE,cAAc,aAAa;AAE/B,iBAAO,eAAe,UAAU;AAChC,6BAAmB,KAAK,UAAU;AAAA,QACpC;AAAA,MACF;AACA,UAAI,mBAAmB,QAAQ;AAC7B,aAAK,SAAS,eAAe;AAC7B,aAAK,SAAS,aAAa,kCACtB,sBACA,KAAK,kBAAkB,IACzB,kBAAkB;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAAA,EACA,wBAAwB;AACtB,QAAI,KAAK,UAAU;AAEjB,WAAK,SAAS,gBAAgB;AAAA,IAChC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,UAAU;AAEjB,WAAK,SAAS,QAAQ;AACtB,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,IAAI,mBAAmB;AACrB,WAAO,KAAK,yBAAyB,KAAK,uBAAuB,CAAC,GAAG,KAAK,mBAAmB,OAAO,CAAC;AAAA,EACvG;AAAA,EACA,SAAS;AACP,WAAO,KAAK;AAAA,EACd;AAAA,EACA,oBAAoB;AAClB,UAAM,UAAU,CAAC;AACjB,eAAW,aAAa,oBAAoB;AAC1C,YAAM,aAAa,KAAK,SAAS;AACjC,UAAI,cAAc,MAAM;AAEtB,gBAAQ,SAAS,IAAI;AAAA,MACvB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB;AAClB,WAAO;AAAA,MACL,uBAAuB,KAAK;AAAA,MAC5B,wBAAwB,KAAK;AAAA,MAC7B,yBAAyB;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA,EAEA,qBAAqB,OAAO,iBAAiB;AAC3C,WAAO,gBAAgB;AAAA,EACzB;AACF;AACA,sBAAsB,OAAO,SAAS,8BAA8B,mBAAmB;AACrF,SAAO,KAAK,qBAAqB,uBAA0B,kBAAqB,UAAU,GAAM,kBAAqB,iBAAiB,CAAC;AACzI;AACA,sBAAsB,OAAyB,kBAAkB;AAAA,EAC/D,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,EAC7B,gBAAgB,SAAS,qCAAqC,IAAI,KAAK,UAAU;AAC/E,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,UAAU,KAAK,CAAC;AAClC,MAAG,eAAe,UAAU,KAAK,CAAC;AAClC,MAAG,eAAe,UAAU,KAAK,CAAC;AAClC,MAAG,eAAe,UAAU,KAAK,CAAC;AAClC,MAAG,eAAe,UAAU,KAAK,CAAC;AAClC,MAAG,eAAe,UAAU,KAAK,CAAC;AAClC,MAAG,eAAe,UAAU,KAAK,CAAC;AAClC,MAAG,eAAe,UAAU,MAAM,CAAC;AACnC,MAAG,eAAe,UAAU,MAAM,CAAC;AACnC,MAAG,eAAe,UAAU,MAAM,CAAC;AACnC,MAAG,eAAe,UAAU,MAAM,CAAC;AACnC,MAAG,eAAe,UAAU,MAAM,CAAC;AACnC,MAAG,eAAe,UAAU,MAAM,CAAC;AACnC,MAAG,eAAe,UAAU,MAAM,CAAC;AACnC,MAAG,eAAe,UAAU,MAAM,CAAC;AAAA,IACrC;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AACvE,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,oBAAoB,GAAG;AACxE,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,sBAAsB,GAAG;AAC1E,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AACnE,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB,GAAG;AACtE,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AACvE,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB,GAAG;AACtE,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB,GAAG;AACtE,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,4BAA4B,GAAG;AAChF,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,4BAA4B,GAAG;AAChF,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,uBAAuB,GAAG;AAC3E,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,sBAAsB,GAAG;AAC1E,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B,GAAG;AAAA,IACjF;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,qBAAqB;AAAA,IACrB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,aAAa,YAAY,SAAS,aAAa,WAAW,WAAW,YAAY,eAAe,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,GAAG,aAAa,YAAY,SAAS,aAAa,WAAW,WAAW,YAAY,aAAa,CAAC;AAAA,EACxP,UAAU,SAAS,+BAA+B,IAAI,KAAK;AACzD,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,GAAG,oBAAoB;AACzC,MAAG,WAAW,GAAG,sDAAsD,GAAG,GAAG,uBAAuB,CAAC;AACrG,MAAG,aAAa;AAAA,IAClB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,UAAU;AACb,MAAG,WAAW,WAAW,IAAI,gBAAgB,EAAE,gBAAgB,IAAI,oBAAoB;AAAA,IACzF;AAAA,EACF;AAAA,EACA,cAAc,CAAC,4BAA4B,6BAAgC,OAAO;AAAA,EAClF,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MAEjC,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,QACzB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,QACvB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,QAC1B,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,QAC5B,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,QACrB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,QACxB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,QACzB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,QACxB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,QACxB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,QAClC,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,QAClC,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,QAC7B,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,QAC5B,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,QACjC,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAyB;AAAC;AAC1B,mBAAmB,OAAO,SAAS,2BAA2B,mBAAmB;AAC/E,SAAO,KAAK,qBAAqB,oBAAoB;AACvD;AACA,mBAAmB,OAAyB,iBAAiB;AAAA,EAC3D,MAAM;AAAA,EACN,cAAc,CAAC,uBAAuB,4BAA4B,2BAA2B;AAAA,EAC7F,SAAS,CAAC,YAAY;AAAA,EACtB,SAAS,CAAC,qBAAqB;AACjC,CAAC;AACD,mBAAmB,OAAyB,iBAAiB;AAAA,EAC3D,SAAS,CAAC,CAAC,YAAY,CAAC;AAC1B,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,uBAAuB,4BAA4B,2BAA2B;AAAA,MAC7F,SAAS,CAAC,YAAY;AAAA,MACtB,SAAS,CAAC,qBAAqB;AAAA,IACjC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}