import { Component, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';
import { RewardService } from '@proxy/hr/rewards';
import {
  AlertService,
  LOADING,
  TtwrGridComponent,
} from '@ttwr-framework/ngx-main-visuals';
import { map, Subject } from 'rxjs';
import { rewards } from '../../rewards.model';

@Component({
  selector: 'app-rewards-index',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `<ttwr-grid [config]="config" />`,
})
export class RewardsIndexComponent {
  private rewardService = inject(RewardService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private alert = inject(AlertService);
  private destroyRef = inject(DestroyRef);
  private loading = inject(LOADING);

  private refreshSubject = new Subject<void>();

  protected config = rewards()
    .exclude({
      rewardTypeId: true,
      summary: true,
    })
    .grid({
      title: '::Rewards',
      refreshSubject: this.refreshSubject,
      dataFunc: (pagination, _, filters) => {
        return this.rewardService
          .getList({
            maxResultCount: +pagination.pageSize,
            skipCount: +(pagination.pageSize * pagination.pageIndex),
            employeeId: filters.find((f) => f.attribute === 'employeeId')
              ?.value,
          })
          .pipe(map((res) => res as any));
      },
      fields: {
        employeeId: {
          nonSearchable: false,
          columnName: '::GoldenOwl:Employee',
        },
        rewardTypeName: {
          columnName: '::Reward:RewardType',
        },
        declarationDate: {
          columnName: '::RewardType:DeclarationDate',
        },
        implementationDate: {
          columnName: '::RewardType:ImplementationDate',
        },
        expirationDate: {
          columnName: '::Reward:ExpirationDate',
        },
      },
      actions: [
        {
          matIcon: 'add',
          tooltip: 'Add',
          delegateFunc: () =>
            this.router.navigate(['create'], { relativeTo: this.route.parent }),
        },
      ],
      fieldActions: [
        {
          matIcon: 'edit',
          tooltip: 'Edit',
          delegateFunc: (obj) =>
            this.router.navigate(['update', obj.id], {
              relativeTo: this.route.parent,
            }),
        },
        {
          matIcon: 'delete',
          tooltip: 'Delete',
          color: 'warn',
          confirmation: {
            title: '::Warning',
            message: '::AreYouSureYouWantToDelete?',
          },
          delegateFunc: (obj) => {
            this.loading.set(true);
            this.rewardService
              .delete(obj.id)
              .pipe(takeUntilDestroyed(this.destroyRef))
              .subscribe(() => {
                this.refreshSubject.next();
                this.alert.success('::SuccessDelete');
              });
          },
        },
      ],
    });
}
