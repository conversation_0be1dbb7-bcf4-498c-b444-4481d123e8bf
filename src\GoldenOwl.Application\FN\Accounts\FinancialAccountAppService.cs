using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoldenOwl.FN.Accounts.DTO;
using GoldenOwl.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace GoldenOwl.FN.Accounts;

public class FinancialAccountAppService : GoldenOwlAppService, IFinancialAccountAppService
{
    private readonly IAccountAndBalancesManager _accountManager;
    private readonly IRepository<Account, Guid> _accountRepository;

    public FinancialAccountAppService(IAccountAndBalancesManager accountManager,
        IRepository<Account, Guid> accountRepository)
    {
        _accountManager = accountManager;
        _accountRepository = accountRepository;
    }

    [Authorize(GoldenOwlPermissions.AccountIndex)]
    public async Task<PagedResultDto<AccountDto>> GetListAsync(PagedResultRequestDto input, string? codePrefix = null,
        string? name = null)
    {
        IQueryable<Account> query = await _accountRepository.GetQueryableAsync();
        query = query.OrderBy(c => c.Code);

        if (!string.IsNullOrWhiteSpace(codePrefix))
        {
            query = query.Where(c => c.Code.StartsWith(codePrefix));
        }

        if (!string.IsNullOrWhiteSpace(name))
        {
            query = query.Where(c => c.Name.Contains(name));
        }

        int totalCount = await AsyncExecuter.CountAsync(query);
        List<Account> accounts = await AsyncExecuter.ToListAsync(query.PageBy(input));
        List<AccountDto> accountsDto =
            ObjectMapper.Map<List<Account>, List<AccountDto>>(accounts);

        return new PagedResultDto<AccountDto>(totalCount, accountsDto);
    }

    [Authorize(GoldenOwlPermissions.AccountIndex)]
    public async Task<AccountDto> GetAsync(Guid id)
    {
        Account account = await _accountRepository.GetAsync(id);
        return ObjectMapper.Map<Account, AccountDto>(account);
    }

    [Authorize(GoldenOwlPermissions.AccountManagement)]
    public virtual Task<Guid> CreateAsync(CreateAccountDto accountDto)
    {
        return _accountManager.CreateAccountAsync(accountDto.Code, accountDto.Name, accountDto.CurrencyCode,
            accountDto.TypeName, accountDto.CashFlowType);
    }

    [Authorize(GoldenOwlPermissions.AccountManagement)]
    public virtual async Task UpdateAsync(UpdateAccountDto accountDto, Guid id)
    {
        await _accountManager.UpdateAccountAsync(id, accountDto.Code, accountDto.Name, accountDto.TypeName,
            accountDto.CashFlowType);
    }

    [Authorize(GoldenOwlPermissions.AccountManagement)]
    public async Task ChangeDeprecationAsync(Guid id)
    {
        await _accountManager.ChangeDeprecation(id);
    }
}