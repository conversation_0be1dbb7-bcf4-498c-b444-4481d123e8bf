﻿using GoldenOwl.HR.Departments.DTO;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoldenOwl.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace GoldenOwl.HR.Departments
{
    public class DepartmentAppService : GoldenOwlAppService, IDepartmentAppService
    {
        private readonly IDepartmentManager _departmentManager;
        private readonly IRepository<Department, Guid> _departmentRepository;

        public DepartmentAppService(IDepartmentManager departmentManager,
            IRepository<Department, Guid> departmentRepository)
        {
            _departmentManager = departmentManager;
            _departmentRepository = departmentRepository;
        }

        public async Task<DepartmentDto> GetGetDepartmentById(Guid id)
        {
            var department = await _departmentRepository.GetAsync(id);
            var result = ObjectMapper.Map<Department, DepartmentDto>(department);
            var parentDepartment = await _departmentManager.GetParentDepartmentAsync(department.Id);
            if (parentDepartment is not null)
            {
                result.ParentDepartmentName = parentDepartment.Name;
                result.ParentDepartmentId = parentDepartment.Id;
            }

            return result;
        }

        public virtual async Task<Guid> PostCreateDepartment(CreateUpdateDepartmentDto dto)
        {
            var department = await _departmentManager.CreateDepartmentAsync(dto.Name, dto.ParentDepartmentId);
            return department.Id;
        }

        public async Task<PagedResultDto<DepartmentDto>> GetGetAllDepartments(PagedResultRequestDto input)
        {
            var query = await _departmentRepository.GetQueryableAsync();
            var totalCount = query.Count();
            query = query
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount);
            var departments = await AsyncExecuter.ToListAsync(query);

            var result = new PagedResultDto<DepartmentDto>(totalCount,
                ObjectMapper.Map<List<Department>, List<DepartmentDto>>(departments));
            foreach (var department in result.Items)
            {
                var dep = departments.FirstOrDefault(a => a.Id == department.Id);
                var parentDepartment = await _departmentManager.GetParentDepartmentAsync(dep.Id);
                if (parentDepartment is not null)
                {
                    department.ParentDepartmentName = parentDepartment.Name;
                    department.ParentDepartmentId = parentDepartment.Id;
                }
            }

            return result;
        }

        public async Task<List<DepartmentDto>> GetGetAllRootDepartments()
        {
            var departments = await _departmentManager.GetRootDepartmentsAsync();

            return ObjectMapper.Map<List<Department>, List<DepartmentDto>>(departments);
        }

        public async Task<IEnumerable<DepartmentDto>> GetGetDepartmentChildren(Guid id)
        {
            var departments = await _departmentManager.GetDepartmentChildrenAsync(id);
            List<DepartmentDto> departmentDtos = new List<DepartmentDto>();
            foreach (var department in departments)
            {
                DepartmentDto departmentDto = ObjectMapper.Map<Department, DepartmentDto>(department);
                departmentDto.ParentDepartmentId = id;
                departmentDtos.Add(departmentDto);
            }

            return departmentDtos;
        }

        public async Task<bool> GetDepartmentHasChild(Guid id)
        {
            return (await _departmentManager.GetDepartmentChildrenAsync(id)).Any();
        }


        [Authorize(GoldenOwlPermissions.DepartmentsAndJobTitlesDelete)]
        public async Task DeleteAsync(Guid id)
        {
            await _departmentRepository.GetAsync(id);
            await _departmentManager.DeleteAsync(id);
        }


        public virtual async Task<Guid> PutUpdateDepartment(Guid id, CreateUpdateDepartmentDto dto)
        {
            var department = await _departmentManager.UpdateDepartmentAsync(id, dto.Name, dto.ParentDepartmentId);
            return department.Id;
        }
    }
}