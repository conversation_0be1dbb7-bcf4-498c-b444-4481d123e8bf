﻿using Elsa.EntityFrameworkCore.Modules.Management;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.DependencyInjection;

namespace ElsaWorkflows.EntityFrameworkCore.DbContext
{
    public class ElsaWorkflowsManagementDbContext : ManagementElsaDbContext, ITransientDependency
    {
        public ElsaWorkflowsManagementDbContext(DbContextOptions<ManagementElsaDbContext> options,
            IServiceProvider serviceProvider) : base(options, serviceProvider)
        {
        }
    }
}