import type { AccountTypeDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class AccountTypeService {
  apiName = 'Default';
  

  getCategoryList = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, AccountTypeDto[]>({
      method: 'GET',
      url: '/api/app/account-type/category-list',
    },
    { apiName: this.apiName,...config });
  

  getHasAccountTypes = (categoryCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, boolean>({
      method: 'GET',
      url: '/api/app/account-type/has-account-types',
      params: { categoryCode },
    },
    { apiName: this.apiName,...config });
  

  getList = (categoryCode?: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AccountTypeDto[]>({
      method: 'GET',
      url: '/api/app/account-type',
      params: { categoryCode },
    },
    { apiName: this.apiName,...config });
  

  getView = (name: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AccountTypeDto>({
      method: 'GET',
      url: '/api/app/account-type/view',
      params: { name },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
