import type { AuditedEntityDto } from '@abp/ng.core';

export interface CreatePayslipDto {
  employeeId?: string;
  startDate?: string;
  endDate?: string;
}

export interface PayslipDetailsDto extends AuditedEntityDto<string> {
  employeeId?: string;
  employeeName?: string;
  startDate?: string;
  endDate?: string;
  payslipItems: PayslipItemDto[];
}

export interface PayslipDto extends AuditedEntityDto<string> {
  employeeId?: string;
  employeeFullName?: string;
  startDate?: string;
  endDate?: string;
}

export interface PayslipItemDto {
  structureRuleId?: string;
  name?: string;
  categoryName?: string;
  code?: string;
  sequence: number;
  amount: number;
}
