import { Component, DestroyRef, inject, signal } from '@angular/core';
import { AlertService, LOADING, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { extraFields } from './extra-fields.model';
import { ExtraFieldDefinitionService, FieldType } from '@proxy/extra-field-definitions';
import { requireAllOperator } from '@shared';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { finalize, Subject } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { ExtraFieldsCreateDialogComponent } from './components/extra-fields-create-dialog/extra-fields-create-dialog.component';
import { ExtraFieldsUpdateDialogComponent } from './components/extra-fields-update-dialog/extra-fields-update-dialog.component';
import { formatDate } from '@angular/common';

@Component({
  selector: 'app-extra-fields',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `<ttwr-grid [config]="config" />`,
})
export class ExtraFieldsComponent {
  private extraFieldDefinition = inject(ExtraFieldDefinitionService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private dialog = inject(MatDialog);
  private loading = inject(LOADING);

  private refreshSubject = new Subject<void>();

  protected config = extraFields.grid({
    title: '::ExtraFieldDefinitions',
    refreshSubject: this.refreshSubject,
    dataFunc: (pagination, _, filters) => this.extraFieldDefinition.getList({
      maxResultCount: pagination.pageSize,
      skipCount: pagination.pageSize * pagination.pageIndex,
    }, filters[0]?.value).pipe(
      requireAllOperator(),
    ),
    fields: {
      fieldName: {
        columnName: '::FieldName',
      },
      fieldType: {
        columnName: '::FieldType',
      },
      entityType: {
        columnName: '::EntityType',
        nonSearchable: false,
      },
      code: {
        columnName: '::Code',
        hiddenSignal: signal(true),
      },
      defaultValue: {
        columnName: '::DefaultValue',
        displayCell: (cell, obj) => {
          const newCell = cell.replace(/^"|"$/g, '');

          return obj.fieldType === FieldType.DateTime
            ? formatDate(newCell, 'yyyy-MM-dd hh:mm a', 'en-Us')
            : newCell;
        },
      },
    },
    actions: [
      {
        matIcon: 'add',
        tooltip: 'Add',
        delegateFunc: () => {
          const ref = this.dialog.open(ExtraFieldsCreateDialogComponent, {
            width: '100%',
            maxWidth: '700px',
          })

          ref.afterClosed().pipe(
            takeUntilDestroyed(this.destroyRef)
          ).subscribe(res => {
            if (res) {
              this.refreshSubject.next();
            }
          })
        },
      }
    ],
    fieldActions: [
      {
        matIcon: 'edit',
        tooltip: 'Edit',
        delegateFunc: (obj) => {
          const ref = this.dialog.open(ExtraFieldsUpdateDialogComponent, {
            width: '100%',
            maxWidth: '700px',
            data: {
              ...obj,
              defaultValue: obj.defaultValue.replace(/^"|"$/g, ''),
            },
          })

          ref.afterClosed().pipe(
            takeUntilDestroyed(this.destroyRef)
          ).subscribe(res => {
            if (res) {
              this.refreshSubject.next();
            }
          })
        }
      },
      {
        matIcon: 'delete',
        tooltip: 'Delete',
        color: 'warn',
        confirmation: {
          title: '::Warning',
          message: '::AreYouSureYouWantToDelete?',
        },
        delegateFunc: (obj) => {
          this.loading.set(true);

          this.extraFieldDefinition.delete(obj.id).pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          ).subscribe(() => {
            this.refreshSubject.next();
            this.alert.success('::SuccessDelete');
          })
        }
      }
    ],
  });
}
