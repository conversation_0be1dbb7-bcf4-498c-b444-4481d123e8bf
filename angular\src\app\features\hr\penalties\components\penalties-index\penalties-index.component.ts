import { Component, DestroyRef, inject } from '@angular/core';
import { AlertService, LOADING, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { penalties } from '../../penalties.model';
import { PenaltyService } from '@proxy/hr/penalties';
import { requireAllOperator } from '@shared';
import { Subject } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-penalties-index',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `<ttwr-grid [config]="config" />`,
})
export class PenaltiesIndexComponent {
  private penalty = inject(PenaltyService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private alert = inject(AlertService);
  private destroyRef = inject(DestroyRef);
  private loading = inject(LOADING);

  private refreshSubject = new Subject<void>();

  protected config = penalties().exclude({
    penaltyTypeId: true,
    summary: true,
  }).grid({
    title: '::Penalties',
    refreshSubject: this.refreshSubject,
    dataFunc: (pagination, _, filters) => this.penalty.getGetAllPenaltiesByDto({
      maxResultCount: pagination.pageSize,
      skipCount: pagination.pageSize * pagination.pageIndex,
      employeeId: filters.find(f => f.attribute === 'employeeId')?.value,
    }).pipe(
      requireAllOperator(),
    ),
    fields: {
      employeeId: {
        nonSearchable: false,
        columnName: '::GoldenOwl:Employee',
      },
      penaltyTypeName: {
        columnName: '::Penalty:PenaltyType',
      },
      declarationDate: {
        columnName: '::PenaltyType:DeclarationDate',
      },
      implementationDate: {
        columnName: '::PenaltyType:ImplementationDate',
      },
      expirationDate: {
        columnName: '::Penalty:ExpirationDate',
      },
    },
    actions: [
      {
        matIcon: 'add',
        tooltip: 'Add',
        delegateFunc: () => this.router.navigate(['create'], { relativeTo: this.route.parent })
      }
    ],
    fieldActions: [
      {
        matIcon: 'edit',
        tooltip: 'Edit',
        delegateFunc: (obj) =>
          this.router.navigate(['update', obj.id], {
            relativeTo: this.route.parent,
          }),
      },
      {
        matIcon: 'delete',
        tooltip: 'Delete',
        color: 'warn',
        confirmation: {
          title: '::Warning',
          message: '::AreYouSureYouWantToDelete?',
        },
        delegateFunc: obj => {
          this.loading.set(true);

          this.penalty.deleteDeletePenaltyById(obj.id).pipe(
            takeUntilDestroyed(this.destroyRef)
          ).subscribe(() => {
            this.refreshSubject.next();
            this.alert.success('::SuccessDelete');
          })
        }
      }
    ],
  })
}
