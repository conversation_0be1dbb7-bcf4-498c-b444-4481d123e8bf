import type { AttendanceDto, CreateUpdateAttendanceDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class AttendanceService {
  apiName = 'Default';
  

  create = (createUpdateAttendanceDto: CreateUpdateAttendanceDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/attendance',
      body: createUpdateAttendanceDto,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AttendanceDto>({
      method: 'GET',
      url: `/api/app/attendance/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (dto: PagedResultRequestDto, employeeId: string, fromDate: string, toDate: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<AttendanceDto>>({
      method: 'GET',
      url: '/api/app/attendance',
      params: { skipCount: dto.skipCount, maxResultCount: dto.maxResultCount, employeeId, fromDate, toDate },
    },
    { apiName: this.apiName,...config });
  

  update = (id: string, createUpdateAttendanceDto: CreateUpdateAttendanceDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: `/api/app/attendance/${id}`,
      body: createUpdateAttendanceDto,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
