<h1>
  {{ 'GoldenOwl:ViewTaskDetails' | i18n }}
</h1>
<mat-card class="common-task-card">
  <mat-card-content>
    <ttwr-view [config]="viewConfig" />
    <h2>
      {{ 'GoldenOwl:CorrectionRequestInformation' | i18n }}
    </h2>
    <mat-card class="sub-card">
      <mat-card-content>
        <ttwr-view [config]="viewRequestConfig" />
      </mat-card-content>
    </mat-card>
    <mat-divider />
    <div class="buttons-container">
      <div>
        @if ((taskSubject | async)?.state === GoTaskState.Open) {
        <button mat-raised-button color="primary" (click)="approveTask()">
          {{ '::Approve' | i18n }}
        </button>
        <button mat-raised-button color="warn" (click)="rejectTask()">
          {{ '::Reject' | i18n }}
        </button>
        }
      </div>
      <button mat-raised-button color="accent" (click)="goBack()">
        {{ '::Back' | i18n }}
      </button>
    </div>
  </mat-card-content>
</mat-card>
