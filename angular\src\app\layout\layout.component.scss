mat-sidenav-container {
  height: 100%;
  padding-top: var(--mat-toolbar-standard-height);
}

mat-sidenav {
  --mat-expansion-header-collapsed-state-height: 56px;
  --mat-list-list-item-leading-icon-end-space: 1rem;
  --mdc-list-list-item-label-text-size: 0.875rem;

  --_text-color: #808691;

  --mdc-list-list-item-label-text-color: var(--_text-color);
  --mdc-list-list-item-leading-icon-color: var(--_text-color);
  --mat-expansion-header-text-color: var(--_text-color);
  --mdc-list-list-item-hover-state-layer-color: white;
  --mdc-list-list-item-hover-leading-icon-color: var(--_text-color);
  --mdc-list-list-item-hover-label-text-color: white;
  --mdc-list-list-item-focus-leading-icon-color: var(--_text-color);
  --mdc-list-list-item-focus-label-text-color: white;
  --mat-expansion-header-hover-state-layer-color: rgba(255, 255, 255, 0.08);

  background-color: #161616;

  width: 320px;
  padding-top: calc(var(--mat-toolbar-standard-height) + 0.5rem);

  mat-expansion-panel {
    box-shadow: none;
    background-color: transparent;

    mat-expansion-panel-header {
      padding: 0 1rem;
      border-radius: var(--mat-list-active-indicator-shape);

      &:hover {
        --mat-expansion-header-text-color: white;
      }
    }
  }

  mat-panel-title {
    display: flex;
    justify-content: start;
    gap: var(--mat-list-list-item-leading-icon-end-space);
    font-size: 0.875rem;

    mat-icon {
      color: var(--mdc-list-list-item-leading-icon-color);
    }
  }
}

mat-toolbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 2;

  .logo {
    background-image: var(--lpx-logo);
    background-repeat: no-repeat;
    background-size: auto 70px;
    background-position: left 0;
    display: block;
    height: 70px;
    width: 70px;
  }

  h1 {
    margin: 0 1rem;
  }

  > * {
    color: inherit;
  }
}

main {
  margin: 0 1.5rem 1.5rem;
  min-height: calc(100vh - 2 * var(--mat-toolbar-standard-height));

  app-breadcrumb {
    display: block;
    margin: 1rem 0 1.5rem;
  }
}
