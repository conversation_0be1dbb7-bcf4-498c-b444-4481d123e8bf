/* These tokens are generated using https://themes.angular-material.dev/ */
/* Preview: https://themes.angular-material.dev/?bold-font-weight=700&brand-font-family=Roboto&medium-font-weight=500&plain-font-family=Roboto&regular-font-weight=400&seed-error=%23ffb4ab&seed-neutral=%2374796a&seed-neutral-variant=%23727a64&seed-primary=%237ab800&seed-secondary=%23afd27d&seed-tertiary=%2345e08e */
/* Seed Colors: primary: #7ab800, secondary: #afd27d, tertiary: #45e08e, error: #ffb4ab, neutral: #74796a, neutral-variant: #727a64 */
/* Seed Typography: plain-font-family: Roboto, brand-font-family: Roboto, bold-font-weight: 700, medium-font-weight: 500, regular-font-weight: 400 */

/* Make sure to import fonts in `<head>` of html */
/*
<style>
  @import url('https://fonts.googleapis.com/css2?family=Roboto&family=Roboto&display=swap');
</style>

OR

<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto&family=Roboto&display=swap">
*/

@use "@angular/material" as mat;

/* Light Theme */
:root,
:host {
  @include mat.theme-overrides(
    (
      primary: #446900,
      on-primary: #ffffff,
      primary-container: #b3f64e,
      on-primary-container: #111f00,
      inverse-primary: #98d932,
      primary-fixed: #b3f64e,
      primary-fixed-dim: #98d932,
      on-primary-fixed: #111f00,
      on-primary-fixed-variant: #324f00,
      secondary: #4a671f,
      on-secondary: #ffffff,
      secondary-container: #caef96,
      on-secondary-container: #111f00,
      secondary-fixed: #caef96,
      secondary-fixed-dim: #afd27d,
      on-secondary-fixed: #111f00,
      on-secondary-fixed-variant: #334f06,
      tertiary: #006d3e,
      on-tertiary: #ffffff,
      tertiary-container: #67fea8,
      on-tertiary-container: #00210f,
      tertiary-fixed: #67fea8,
      tertiary-fixed-dim: #45e08e,
      on-tertiary-fixed: #00210f,
      on-tertiary-fixed-variant: #00522d,
      background: #fcf9f6,
      on-background: #1c1c1a,
      surface: #fcf9f6,
      surface-dim: #dcd9d7,
      surface-bright: #fcf9f6,
      surface-container-lowest: #ffffff,
      surface-container-low: #f6f3f0,
      surface-container: #f0edeb,
      surface-container-high: #ebe8e5,
      surface-container-highest: #e5e2df,
      on-surface: #1c1c1a,
      shadow: #000000,
      scrim: #000000,
      surface-tint: #5b6052,
      inverse-surface: #31302f,
      inverse-on-surface: #f3f0ee,
      outline: #767870,
      outline-variant: #c6c7bd,
      neutral: #787775,
      neutral10: #1c1c1a,
      error: #ba1a1a,
      error-container: #ffdad6,
      on-error: #ffffff,
      on-error-container: #410002,
      surface-variant: #e2e3d9,
      on-surface-variant: #454840,
      neutral-variant: #76786f,
      neutral-variant20: #2f312a,
      inverse-secondary: #afd27d,
      inverse-tertiary: #45e08e,
    )
  );
}

/* Dark Theme */
.dark {
  @include mat.theme-overrides(
    (
      primary: #98d932,
      on-primary: #213600,
      primary-container: #324f00,
      on-primary-container: #b3f64e,
      inverse-primary: #446900,
      primary-fixed: #b3f64e,
      primary-fixed-dim: #98d932,
      on-primary-fixed: #111f00,
      on-primary-fixed-variant: #324f00,
      secondary: #afd27d,
      on-secondary: #213600,
      secondary-container: #334f06,
      on-secondary-container: #caef96,
      secondary-fixed: #caef96,
      secondary-fixed-dim: #afd27d,
      on-secondary-fixed: #111f00,
      on-secondary-fixed-variant: #334f06,
      tertiary: #45e08e,
      on-tertiary: #00391e,
      tertiary-container: #00522d,
      on-tertiary-container: #67fea8,
      tertiary-fixed: #67fea8,
      tertiary-fixed-dim: #45e08e,
      on-tertiary-fixed: #00210f,
      on-tertiary-fixed-variant: #00522d,
      background: #131312,
      on-background: #e5e2df,
      surface: #131312,
      surface-dim: #131312,
      surface-bright: #3a3938,
      surface-container-lowest: #0e0e0d,
      surface-container-low: #1c1c1a,
      surface-container: #20201e,
      surface-container-high: #2a2a29,
      surface-container-highest: #353533,
      on-surface: #e5e2df,
      shadow: #000000,
      scrim: #000000,
      surface-tint: #c4c9b7,
      inverse-surface: #e5e2df,
      inverse-on-surface: #31302f,
      outline: #909189,
      outline-variant: #454840,
      neutral: #787775,
      neutral10: #1c1c1a,
      error: #ffb4ab,
      error-container: #93000a,
      on-error: #690005,
      on-error-container: #ffdad6,
      surface-variant: #454840,
      on-surface-variant: #c6c7bd,
      neutral-variant: #76786f,
      neutral-variant20: #2f312a,
      inverse-secondary: #4a671f,
      inverse-tertiary: #006d3e,
    )
  );
}

/* Typography */
:root,
:host {
  /* core typography tokens are not generating css variables, hence below */
  --mat-sys-brand-font-family: Roboto;
  --mat-sys-plain-font-family: Roboto;
  --mat-sys-bold-font-weight: 700;
  --mat-sys-medium-font-weight: 500;
  --mat-sys-regular-font-weight: 400;

  @include mat.theme-overrides(
    (
      brand-family: Roboto,
      plain-family: Roboto,
      bold-weight: 700,
      medium-weight: 500,
      regular-weight: 400,
    )
  );
}
