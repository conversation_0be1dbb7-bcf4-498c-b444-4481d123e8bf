﻿using Elsa.Extensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;


namespace ElsaWorkflows.HttpApi.Host
{
    public class Program
    {
        public async static Task<int> Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            builder.Host
                .AddAppSettingsSecretsJson()
                .UseAutofac();

            await builder.Services.AddApplicationAsync<ElsaWorkflowsHttpApiHostModule>();

            builder.Services.AddCors(cors => cors
                .AddDefaultPolicy(policy => policy
                    .AllowAnyOrigin() // For demo purposes only. Use a specific origin instead.
                    .AllowAnyHeader()
                    .AllowAnyMethod()
                    .WithExposedHeaders("x-elsa-workflow-instance-id")));
            var app = builder.Build();

            app.UseWorkflowsApi();
            app.UseCors();
            app.Run();
            return 0;
        }
    }
}