import { arrayMap, fields, model } from '@ttwr-framework/ngx-main-visuals';
import { inject } from '@angular/core';
import { AttachmentTypeService, EntityType } from '@proxy/attachment-types';
import { Observable, switchMap, tap } from 'rxjs';

export const attachment = (
  entityType: Observable<EntityType>,
  idToAllowedExtensionsMap?: Map<string, string>
) => {
  const attachmentType = inject(AttachmentTypeService);

  return model({
    id: fields.text(),
    fileName: fields.text(),
    file: fields.file(),
    attachmentTypeId: fields.selectFetch('single', () => entityType.pipe(
      switchMap(entityType => attachmentType.getListByEntityTypeAsynByEntityType(entityType)),
      tap(types => {
        if (idToAllowedExtensionsMap) {
          types.forEach(type => {
            idToAllowedExtensionsMap.set(
              type.id!,
              type.allowedExtensions.map(({ value }) => `.${value}`).join(','),
            );
          })
        }
      }),
      arrayMap(type => ({
        label: type.name!,
        value: type.id!,
      })),
    )),
    fileSize: fields.number(),
    creationTime: fields.date(),
    expiryDate: fields.date(),
  })
}
