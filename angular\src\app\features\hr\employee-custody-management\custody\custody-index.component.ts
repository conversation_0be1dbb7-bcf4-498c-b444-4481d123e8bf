import { Component, DestroyRef, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatDialog } from '@angular/material/dialog';
import { pagedMap, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { map, Subject } from 'rxjs';
import { custody } from './custody.model';
import { EmployeeCustodyService } from '@proxy/hr/employee-custodies';
import { CustodyCreateDialogComponent } from './custody-create-dialog/custody-create-dialog.component';
import { CustodyUpdateDialogComponent } from './custody-update-dialog/custody-update-dialog.component';

@Component({
  selector: 'app-custody-types',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `<ttwr-grid [config]="config" />`,
})
export class CustodyComponent {
  private custody = inject(EmployeeCustodyService);
  private dialog = inject(MatDialog);
  private destroyRef = inject(DestroyRef);
  private refreshSubject = new Subject<void>();

  protected config = custody.grid({
    title: '::EmployeeCustodyIndex',
    refreshSubject: this.refreshSubject,

    dataFunc: (pagination, _, filters) => {
      return this.custody
        .getGetAllEmployeeCustodiesByDto({
          maxResultCount: +pagination.pageSize,
          skipCount: +(pagination.pageSize * pagination.pageIndex),
          employeeId: filters.find((f) => f.attribute === 'employeeId')?.value,
        })
        .pipe(map((res) => res as any));
    },

    fields: {
      employeeId: {
        hiddenSignal: signal(true),
      },
      employeeName: {
        columnName: '::GoldenOwl:Employee',
      },
      description: {
        columnName: '::EmployeeCustody:Description',
      },
      receiptDate: {
        columnName: '::EmployeeCustody:ReceiptDate',
      },
      releaseDate: {
        columnName: '::EmployeeCustody:ReleaseDate',
      },
      releaseReason: {
        columnName: '::EmployeeCustody:ReleaseReason',
      },
    },

    actions: [
      {
        matIcon: 'add',
        tooltip: 'Add',
        delegateFunc: () => {
          const ref = this.dialog.open(CustodyCreateDialogComponent, {
            width: '100%',
            maxWidth: '700px',
          });

          ref
            .afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe((res) => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        },
      },
    ],
    fieldActions: [
      {
        matIcon: 'edit',
        tooltip: 'Edit',
        delegateFunc: (obj) => {
          const ref = this.dialog.open(CustodyUpdateDialogComponent, {
            width: '100%',
            maxWidth: '700px',
            data: obj,
          });

          ref
            .afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe((res) => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        },
      },
    ],
  });
}
