import { InjectionToken, Type } from '@angular/core';

/**
 * Injection token used to define extra features
 * in the feature management, where the key is the
 * `name` of `valueType` attribute in the feature
 * that is defined at backend and the value is the
 * component rendered.
 *
 * The used component must have an input of name
 * `feature` which is of type `FeatureWithTypeAndControl`.
 *
 * @example
 * ```ts
 * import { ApplicationConfig } from '@angular/core';
 * import { DatetimeExtraFeatureComponent } from '@shared/components';
 *
 * export const config: ApplicationConfig = {
 *  providers: [
 *    ...,
 *    {
 *      provide: EXTRA_FEATURES,
 *      useValue: {
 *        'DatetimeAttribute': DatetimeExtraFeatureComponent,
 *      },
 *    },
 *  ],
 * }
 * ```
 */
export const EXTRA_FEATURES = new InjectionToken<
  Record<string, Type<any>>
>('ABP extra feature management types', {
  providedIn: 'root',
  factory: () => ({}),
})
