import type { FinancialClientType } from '../financial-client-type.enum';
import type { CashFlowType } from '../../accounts/cash-flow-type.enum';
import type { FullAuditedEntityDto } from '@abp/ng.core';

export interface CreateFinancialClientDto {
  financialClientType: FinancialClientType;
  name?: string;
  address?: string;
  phoneNumber?: string;
  accountId?: string;
  accountCode?: string;
  accountCurrencyCode?: string;
  accountCashFlowType?: CashFlowType;
}

export interface FinancialClientDto extends FullAuditedEntityDto<string> {
  name?: string;
  address?: string;
  phoneNumber?: string;
  accountId?: string;
  accountCode?: string;
}

export interface UpdateFinancialClientDto {
  financialClientType: FinancialClientType;
  name?: string;
  address?: string;
  phoneNumber?: string;
  accountId?: string;
  accountCode?: string;
  accountCurrencyCode?: string;
  accountCashFlowType?: CashFlowType;
}
