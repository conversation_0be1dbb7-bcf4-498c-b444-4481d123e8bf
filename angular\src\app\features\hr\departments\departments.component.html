<h1>
  {{ '::GoldenOwl:Department' | i18n }}
</h1>
<mat-card>
  <mat-card-content>
    <div class="top-card-row">
      <mat-button-toggle-group>
        <mat-button-toggle
          (click)="activeSection.set('table')"
          [checked]="activeSection() === 'table'"
        >
          {{ 'Table' | i18n }}
        </mat-button-toggle>
        <mat-button-toggle
          (click)="activeSection.set('tree')"
          [checked]="activeSection() === 'tree'"
        >
          {{ 'Tree' | i18n }}
        </mat-button-toggle>
      </mat-button-toggle-group>
      <button (click)="openCreateDialog()" mat-flat-button>
        {{ 'Add' | i18n }}
      </button>
    </div>
    <ttwr-grid
      [style.display]="activeSection() === 'tree' ? 'none' : 'block'"
      [config]="config"
    />
    @if (treeData(); as data) {
      <app-org-tree
        [style.display]="activeSection() === 'table' ? 'none' : 'block'"
        [data]="data"
      />
    }
  </mat-card-content>
</mat-card>
