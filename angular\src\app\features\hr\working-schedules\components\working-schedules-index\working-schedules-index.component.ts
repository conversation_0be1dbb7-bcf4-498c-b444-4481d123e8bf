import { Component, inject, signal } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { workingSchedules } from '../../working-schedules.model';
import { requireAllOperator, timeToIso } from '@shared';
import { WorkingScheduleService } from '@proxy/hr/working-schedules';
import { WorkingHoursDialogComponent } from './working-hours-dialog/working-hours-dialog.component';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-working-schedules-index',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `<ttwr-grid class="actions-end" [config]="config"/>`,
})
export class WorkingSchedulesIndexComponent {
  private workingSchedule = inject(WorkingScheduleService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private dialog = inject(MatDialog);

  protected config = workingSchedules.grid({
    title: '::WorkingSchedules',
    dataFunc: (pagination) => this.workingSchedule.getList({
      skipCount: pagination.pageSize * pagination.pageIndex,
      maxResultCount: pagination.pageSize,
    }).pipe(requireAllOperator()),
    actions: [
      {
        tooltip: 'Add',
        matIcon: 'add',
        delegateFunc: () =>
          this.router.navigate(['create'], { relativeTo: this.route.parent }),
      },
    ],
    fields: {
      workingHours: {
        hiddenSignal: signal(true),
      }
    },
    fieldActions: [
      {
        label: 'Edit',
        color: 'primary',
        delegateFunc: (obj) =>
          this.router.navigate(['update', obj.id], {
            relativeTo: this.route.parent,
          }),
      },
      {
        label: '::GoldenOwl:WorkingHours',
        color: 'accent',
        showFunc: obj => !!obj.workingHours && !!obj.workingHours.length,
        delegateFunc: (obj) => {
          this.dialog.open(WorkingHoursDialogComponent, {
            data: obj.workingHours.map(workingHour => ({
              ...workingHour,
              from: timeToIso(workingHour.from),
              to: timeToIso(workingHour.to),
              expectedAttendance: timeToIso(workingHour.expectedAttendance),
            })),
            width: '700px'
          });
        },
      }
    ],
  });
}
