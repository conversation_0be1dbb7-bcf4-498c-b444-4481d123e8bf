import {inject, Injectable} from '@angular/core';
import {AuthService} from '@abp/ng.core';
import {jwtDecode} from 'jwt-decode';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  private authService = inject(AuthService);

  isEmployee(): boolean {
    const token = this.authService.getAccessToken();
    if(token) {
      const decodedToken: any = jwtDecode(token);
      return decodedToken.EmployeeId;
    }
    return false;
  }

}
