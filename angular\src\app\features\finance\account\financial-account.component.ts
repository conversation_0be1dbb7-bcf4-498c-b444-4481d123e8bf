import { Component, DestroyRef, inject } from '@angular/core';
import { LanguageService, LOADING, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { financialAccount } from './financial-account.model';
import { FinancialAccountService } from '@proxy/fn/accounts';
import { requireAllOperator } from '@shared';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Subject } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { FinancialAccountCreateDialogComponent } from './components/financial-account-create-dialog/financial-account-create-dialog.component';
import { FinancialAccountUpdateDialogComponent } from './components/financial-account-update-dialog/financial-account-update-dialog.component';

@Component({
  selector: 'app-financial-account',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `<ttwr-grid [config]="config" />`,
})
export class FinancialAccountComponent {
  private financialAccount = inject(FinancialAccountService);
  private language = inject(LanguageService);
  private destroyRef = inject(DestroyRef);
  private dialog = inject(MatDialog);
  private loading = inject(LOADING);

  private refreshSubject = new Subject<void>();

  protected config = financialAccount('codePrefix').exclude({
    codePrefix: true,
  }).grid({
    refreshSubject: this.refreshSubject,
    dataFunc: (pagination, _, filters) => {
      const prefix = filters.find(f => f.attribute === 'typeName')?.value;
      const name = filters.find(f => f.attribute === 'name')?.value

      return this.financialAccount.getList({
        maxResultCount: pagination.pageSize,
        skipCount: pagination.pageSize * pagination.pageIndex,
      }, prefix, name).pipe(
        requireAllOperator() as any,
      )
    },
    fields: {
      name: {
        nonSearchable: false,
      },
      typeName: {
        columnName: '::Account:TypeName',
        nonSearchable: false,
        displayCell: cell => this.language.translate(cell),
      },
      code: {
        columnName: '::Account:Code',
      },
      currencyCode: {
        columnName: '::Account:Currency'
      },
      cashFlowType: {
        columnName: '::Account:CashFlowType'
      },
      isDeprecated: {
        columnName: '::Account:IsDeprecated',
      },
    },
    actions: [
      {
        matIcon: 'add',
        tooltip: 'Add',
        delegateFunc: () => {
          const ref = this.dialog.open(FinancialAccountCreateDialogComponent, {
            width: '100%',
            maxWidth: '700px'
          });

          ref.afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        }
      }
    ],
    fieldActions: [
      {
        label: 'Enable',
        showFunc: ({ isDeprecated }) => !isDeprecated,
        delegateFunc: ({ id }) => this.changeActivation(id),
      },
      {
        label: 'Disable',
        showFunc: ({ isDeprecated }) => isDeprecated,
        delegateFunc: ({ id }) => this.changeActivation(id),
      },
      {
        label: 'Edit',
        matButtonType: 'flat',
        delegateFunc: ({ id }) => {
          const ref = this.dialog.open(FinancialAccountUpdateDialogComponent, {
            width: '100%',
            maxWidth: '700px',
            data: {
              id,
            },
          });

          ref.afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        },
      }
    ],
  });

  changeActivation(id: string) {
    this.loading.set(true);

    this.financialAccount.changeDeprecation(id).pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(() => {
      this.loading.set(false);
      this.refreshSubject.next();
    });
  }
}
