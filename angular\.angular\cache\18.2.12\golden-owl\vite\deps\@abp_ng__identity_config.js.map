{"version": 3, "sources": ["../../../../../../node_modules/@abp/ng.identity/fesm2022/abp-ng.identity-config.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { APP_INITIALIZER, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport { RoutesService } from '@abp/ng.core';\nconst IDENTITY_ROUTE_PROVIDERS = [{\n  provide: APP_INITIALIZER,\n  useFactory: configureRoutes,\n  deps: [RoutesService],\n  multi: true\n}];\nfunction configureRoutes(routesService) {\n  return () => {\n    routesService.add([{\n      path: undefined,\n      name: \"AbpIdentity::Menu:IdentityManagement\" /* eIdentityRouteNames.IdentityManagement */,\n      parentName: \"AbpUiNavigation::Menu:Administration\" /* eThemeSharedRouteNames.Administration */,\n      requiredPolicy: \"AbpIdentity.Roles || AbpIdentity.Users\" /* eIdentityPolicyNames.IdentityManagement */,\n      iconClass: 'fa fa-id-card-o',\n      layout: \"application\" /* eLayoutType.application */,\n      order: 1\n    }, {\n      path: '/identity/roles',\n      name: \"AbpIdentity::Roles\" /* eIdentityRouteNames.Roles */,\n      parentName: \"AbpIdentity::Menu:IdentityManagement\" /* eIdentityRouteNames.IdentityManagement */,\n      requiredPolicy: \"AbpIdentity.Roles\" /* eIdentityPolicyNames.Roles */,\n      order: 1\n    }, {\n      path: '/identity/users',\n      name: \"AbpIdentity::Users\" /* eIdentityRouteNames.Users */,\n      parentName: \"AbpIdentity::Menu:IdentityManagement\" /* eIdentityRouteNames.IdentityManagement */,\n      requiredPolicy: \"AbpIdentity.Users\" /* eIdentityPolicyNames.Users */,\n      order: 2\n    }]);\n  };\n}\nfunction provideIdentityConfig() {\n  return makeEnvironmentProviders([IDENTITY_ROUTE_PROVIDERS]);\n}\n\n/**\n * @deprecated IdentityConfigModule is deprecated use `provideIdentityConfig` *function* instead.\n */\nclass IdentityConfigModule {\n  static forRoot() {\n    return {\n      ngModule: IdentityConfigModule,\n      providers: [provideIdentityConfig()]\n    };\n  }\n  static {\n    this.ɵfac = function IdentityConfigModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || IdentityConfigModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: IdentityConfigModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IdentityConfigModule, [{\n    type: NgModule\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { IDENTITY_ROUTE_PROVIDERS, IdentityConfigModule, configureRoutes, provideIdentityConfig };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAGA,IAAM,2BAA2B,CAAC;AAAA,EAChC,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM,CAAC,aAAa;AAAA,EACpB,OAAO;AACT,CAAC;AACD,SAAS,gBAAgB,eAAe;AACtC,SAAO,MAAM;AACX,kBAAc,IAAI,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,OAAO;AAAA,IACT,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AACF;AACA,SAAS,wBAAwB;AAC/B,SAAO,yBAAyB,CAAC,wBAAwB,CAAC;AAC5D;AAKA,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,OAAO,UAAU;AACf,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,sBAAsB,CAAC;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAsB;AAAA,IACzD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}