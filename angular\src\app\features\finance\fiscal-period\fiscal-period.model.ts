import { FinancialPeriodState } from "@proxy/fn/financial-periods";
import { model, fields, takeOptions } from "@ttwr-framework/ngx-main-visuals";

export const fiscalPeriod = model({
  id: fields.text(),
  startDate: fields.date(),
  endDate: fields.date(),
  lastAccountingAuditing: fields.date(),
  financialPeriodState: fields.select('single', takeOptions(FinancialPeriodState)),
  code: fields.text(),
});

export const addAccountingAuditing = model({
  id: fields.text(),
  accountingAuditingDate: fields.date(),
});
