import { arrayMap, fields, model, takeOptions } from '@ttwr-framework/ngx-main-visuals';
import { InOutMode } from '@proxy/hr/attendances';
import { requireAllOperator } from '@shared';
import { map } from 'rxjs';
import { inject } from '@angular/core';
import { EmployeeService } from '@proxy/hr/employees';

export const attendance = () => {
  const employees = inject(EmployeeService);

  return model({
    id: fields.text(),
    employeeId: fields.selectFetch('single', () => employees.getList(
      { maxResultCount: 999 }, '00000000-0000-0000-0000-000000000000', undefined as any
    ).pipe(
      requireAllOperator(),
      map(res => res.items),
      arrayMap(employee => ({
        label: `${employee.name} ${employee.surname}`,
        value: employee.id,
      }))
    )),
    checkIn: fields.datetime(),
    checkOut: fields.datetime(),
    inMode: fields.select('single', takeOptions(InOutMode)),
    outMode: fields.select('single', takeOptions(InOutMode)),
  })
}
