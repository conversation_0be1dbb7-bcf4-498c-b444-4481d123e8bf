import { Component, computed, inject, signal } from '@angular/core';
import { MatBadge } from '@angular/material/badge';
import { MatIconButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { map } from 'rxjs';
import { NotificationService } from '@proxy/notifications';
import { toSignal } from '@angular/core/rxjs-interop';
import { CdkMenu, CdkMenuItem, CdkMenuTrigger } from '@angular/cdk/menu';
import { MatListItem, MatListItemIcon, MatNavList } from '@angular/material/list';
import { MatCard } from '@angular/material/card';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { RouterLink } from '@angular/router';
import { MatDivider } from '@angular/material/divider';

@Component({
  selector: 'app-notification-button',
  standalone: true,
  imports: [
    <PERSON><PERSON>adge,
    MatIconButton,
    MatIcon,
    CdkMenuTrigger,
    CdkMenu,
    CdkMenuItem,
    MatListItem,
    MatCard,
    LanguagePipe,
    MatListItemIcon,
    MatNavList,
    RouterLink,
    MatDivider
  ],
  templateUrl: './notification-button.component.html',
  styleUrl: './notification-button.component.scss',
  animations: [
    trigger('menuAnimation', [
      state('void', style({
        opacity: 0,
        transform: 'scale(0.8)',
      })),
      state('*', style({
        opacity: 1,
        transform: 'scale(1)',
      })),
      transition(':enter', [
        animate('150ms cubic-bezier(0.4, 0.0, 0.2, 1)')
      ]),
      transition(':leave', [
        animate('75ms cubic-bezier(0.4, 0.0, 0.2, 1)', style({
          opacity: 0,
          transform: 'scale(0.8)',
        }))
      ])
    ])
  ],
})
export class NotificationButtonComponent {
  private notification = inject(NotificationService);

  protected clickedNotifications = signal<string[]>([]);

  protected notifications = toSignal(
    this.notification.getList({
      maxResultCount: 5,
    }).pipe(
      map(res => res.items!),
    )
  );

  protected newNotificationsCount = computed(() => {
    return (this.notifications() ?? []).filter(notification => !notification.isSeen).length - this.clickedNotifications().length
  })

  markAsSeen(id: string) {
    this.notification.markAsSeen(id).subscribe();
    this.clickedNotifications.update(pre => [...pre, id]);
  }
}
