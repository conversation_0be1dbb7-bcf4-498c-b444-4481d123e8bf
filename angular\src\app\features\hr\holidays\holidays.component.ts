import { Component, DestroyRef, inject, signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { MatDialog } from '@angular/material/dialog';
import { map, shareReplay, startWith, Subject, switchMap } from 'rxjs';
import { AlertService, LanguagePipe, LOADING, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { HolidaysCreateDialogComponent } from './holidays-create-dialog/holidays-create-dialog.component';
import { HolidaysUpdateDialogComponent } from './holidays-update-dialog/holidays-update-dialog.component';
import { CalendarComponent, combineTwoDates, requireAllOperator } from '@shared';
import { MatButtonToggle, MatButtonToggleGroup } from '@angular/material/button-toggle';
import { MatCard, MatCardContent } from '@angular/material/card';
import { MatButton } from '@angular/material/button';
import { HolidayService } from '@proxy/hr/holidays';
import { holidays } from './holidays.model';

@Component({
  selector: 'app-holidays-index',
  standalone: true,
  imports: [TtwrGridComponent, MatButtonToggleGroup, MatButtonToggle, LanguagePipe, MatCard, MatCardContent, MatButton, CalendarComponent],
  templateUrl: './holidays.component.html',
  styleUrl: './holidays.component.scss',
})
export class HolidaysComponent {
  private holiday = inject(HolidayService);
  private dialog = inject(MatDialog);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);

  private refreshSubject = new Subject<void>();
  protected activeSection = signal<'table' | 'calendar'>('table');

  private allHolidays$ = this.refreshSubject.pipe(
    startWith(null),
    switchMap(() => this.holiday.getList({
      maxResultCount: 1000,
    }).pipe(
      requireAllOperator(),
      shareReplay({
        bufferSize: 1,
        refCount: true,
      })
    ))
  );

  protected holidays = toSignal(this.allHolidays$.pipe(
    map(res => res.items),
  ));

  protected config = holidays.grid({
    hiddenPagination: true,
    elevationClass: 'mat-elevation-z0',
    dataFunc: () => this.allHolidays$,
    fieldActions: [
      {
        matIcon: 'edit',
        tooltip: 'Edit',
        color: 'primary',
        delegateFunc: (obj) => {
          const ref = this.dialog.open(HolidaysUpdateDialogComponent, {
            width: '500px',
            data: {
              ...obj,
              dateRange: combineTwoDates(obj.from, obj.to),
            },
          });

          ref.afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        }
      },
      {
        matIcon: 'delete',
        tooltip: 'Delete',
        color: 'warn',
        confirmation: {
          title: '::Warning',
          message: '::AreYouSureYouWantToDelete?',
        },
        delegateFunc: (obj) => {
          this.loading.set(true);

          this.holiday.delete(obj.id).pipe(
            takeUntilDestroyed(this.destroyRef)
          ).subscribe(() => {
            this.refreshSubject.next();
            this.alert.success('::SuccessDelete');
          })
        }
      }
    ],
    fields: {
      isReOccurring: {
        columnName: '::GoldenOwl:IsReOccurring',
      },
      from: {
        columnName: '::GoldenOwl:From',
      },
      to: {
        columnName: '::GoldenOwl:To',
      },
    },
  });

  protected openCreateDialog() {
    const ref = this.dialog.open(HolidaysCreateDialogComponent, {
      width: '500px'
    });

    ref.afterClosed()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(res => {
        if (res) {
          this.refreshSubject.next();
        }
      });
  }
}
