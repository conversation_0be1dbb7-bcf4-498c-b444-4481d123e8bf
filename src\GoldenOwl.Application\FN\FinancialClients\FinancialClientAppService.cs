using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoldenOwl.FN.FinancialClients.DTO;
using GoldenOwl.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace GoldenOwl.FN.FinancialClients;

public class FinancialClientAppService : GoldenOwlAppService, IFinancialClientAppService
{
    private readonly IFinancialClientManager _financialClientManager;
    private readonly IRepository<FinancialClient, Guid> _financialClientRepository;

    public FinancialClientAppService(IFinancialClientManager financialClientManager,
        IRepository<FinancialClient, Guid> financialClientRepository)
    {
        _financialClientManager = financialClientManager;
        _financialClientRepository = financialClientRepository;
    }

    [Authorize(GoldenOwlPermissions.FinancialClientIndex)]
    public async Task<PagedResultDto<FinancialClientDto>> GetListAsync(PagedResultRequestDto input, string? name = null)
    {
        IQueryable<FinancialClient> query = await _financialClientRepository.GetQueryableAsync();

        if (!string.IsNullOrWhiteSpace(name))
        {
            query = query.Where(c => c.Name.Contains(name));
        }

        var totalCount = await AsyncExecuter.CountAsync(query);
        query = query.PageBy(input);
        var financialClients = await AsyncExecuter.ToListAsync(query);
        var financialClientsDto = ObjectMapper.Map<List<FinancialClient>, List<FinancialClientDto>>(financialClients);

        return new PagedResultDto<FinancialClientDto>(totalCount, financialClientsDto);
    }

    [Authorize(GoldenOwlPermissions.FinancialClientIndex)]
    public async Task<FinancialClientDto> GetAsync(Guid id)
    {
        var account = await _financialClientRepository.GetAsync(id);
        return ObjectMapper.Map<FinancialClient, FinancialClientDto>(account);
    }

    [Authorize(GoldenOwlPermissions.FinancialClientManagement)]
    public virtual async Task<Guid> CreateAsync(CreateFinancialClientDto financialClientDto)
    {
        financialClientDto.Name = financialClientDto.Name.Trim();
        financialClientDto.Address = financialClientDto.Address.Trim();
        financialClientDto.PhoneNumber = financialClientDto.PhoneNumber.Trim();

        return await _financialClientManager.CreateFinancialClientAsync(
            financialClientDto.FinancialClientType,
            financialClientDto.Name,
            financialClientDto.Address,
            financialClientDto.PhoneNumber,
            financialClientDto.AccountId,
            financialClientDto.AccountCode,
            financialClientDto.AccountCurrencyCode,
            financialClientDto.AccountCashFlowType);
    }

    [Authorize(GoldenOwlPermissions.FinancialClientManagement)]
    public virtual async Task UpdateAsync(Guid id, UpdateFinancialClientDto financialClientDto)
    {
        financialClientDto.Name = financialClientDto.Name.Trim();
        financialClientDto.Address = financialClientDto.Address.Trim();
        financialClientDto.PhoneNumber = financialClientDto.PhoneNumber.Trim();

        await _financialClientManager.UpdateFinancialClientAsync(
            id,
            financialClientDto.FinancialClientType,
            financialClientDto.Name,
            financialClientDto.Address,
            financialClientDto.PhoneNumber,
            financialClientDto.AccountId,
            financialClientDto.AccountCode,
            financialClientDto.AccountCurrencyCode,
            financialClientDto.AccountCashFlowType);
    }

    [Authorize(GoldenOwlPermissions.FinancialClientManagement)]
    public async Task DeleteAsync(Guid id)
    {
        await _financialClientManager.DeleteFinancialClientAsync(id);
    }
}