import { fields, model, Option, takeOptions } from '@ttwr-framework/ngx-main-visuals';
import { of } from 'rxjs';

export const ownAllocation = model({
  leaveTypeName: fields.text(),
  balance: fields.text(),
  totalDays: fields.number(),
  totalHours: fields.number(),
});

export const allocationDetails = model({
  validityFrom: fields.datetime(),
  validityTo: fields.datetime(),
  balance: fields.text(),
  numberOfDays: fields.number(),
  numberOfHours: fields.number(),
});
