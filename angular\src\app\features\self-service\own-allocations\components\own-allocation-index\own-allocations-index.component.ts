import { Component, DestroyRef, inject } from '@angular/core';
import {
  extraGridFilter,
  GridFilterOperation,
  LOADING,
  pagedMap, PagedResult,
  takeOptions,
  TtwrGridComponent
} from '@ttwr-framework/ngx-main-visuals';
import { ActivatedRoute } from '@angular/router';
import {finalize, of, pipe, Subject} from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {LeaveAllocationService} from '@proxy/hr/leave-allocations';
import {allocationDetails, ownAllocation} from '../../own-allocations.model';
import {requireAllOperator} from '@shared/functions';
import {LocalizationService, PagedResultDto} from '@abp/ng.core';
import {EmployeeLeaveAllocationDto} from '@proxy/hr/leave-allocations/dtos';

@Component({
  selector: 'app-own-allocations-index',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `
    <ttwr-grid [config]="config" />
    @if(viewDetails){
    <ttwr-grid [config]="detailsConfig" />
    }
  `,
})
export class OwnAllocationsIndexComponent {
  private ownAllocationService = inject(LeaveAllocationService); // Service for own allocations
  private localizationService = inject(LocalizationService);
  private selectedRow: any;

  private refreshSubject = new Subject<void>();

  public viewDetails = false;

  protected config = ownAllocation.exclude({
    totalHours: true,
    totalDays: true
  }).grid({
    title: 'Menu:MyAllocations', // Title for the grid
    refreshSubject: this.refreshSubject,
    onRowClick: (obj) => {
      this.selectedRow = obj;
      this.viewDetails = true;
      this.detailsConfig.refreshSubject?.next();
    },
    dataFunc: (pagination, _, filters) => {
      // Optional: Use filters here if needed, like leave type or date range

      return this.ownAllocationService.getOwnAllocations({
        skipCount: pagination.pageSize * pagination.pageIndex,
        maxResultCount: pagination.pageSize,
      }).pipe(
        requireAllOperator(),
        pagedMap(item => ({
          ...item,
          balance: item.totalDays +" " + this.localizationService.instant('::Day') +" " + item.totalHours +" " + this.localizationService.instant('::Hour'),
        }))
      )
      ;
    },
    fields: {
      leaveTypeName: {
        columnName: '::LeaveTypeName',
      },
      balance: {
        columnName: '::Balance',
      },
    },
  });

  protected detailsConfig = allocationDetails.exclude({
    numberOfDays: true,
    numberOfHours: true
  }).grid({
    title: '::AllocationsDetails', // Title for the grid
    refreshSubject: this.refreshSubject,
    hiddenPagination: true,
    dataFunc: (pagination, _, filters) => {

      // Optional: Use filters here if needed, like leave type or date range
      const result = {
        totalCount: this.selectedRow ? this.selectedRow.allocations.length : 0,
        items: this.selectedRow ? this.selectedRow.allocations : [],
      };

      return of(result).pipe(
        requireAllOperator(),
        pagedMap((item:any) => ({
          ...item,
          balance: item.numberOfDays +" " + this.localizationService.instant('::Day') +" " + item.numberOfHours +" " + this.localizationService.instant('::Hour'),
        }))
      )
        ;;

    },
    fields: {
      validityFrom: {
        columnName: '::GoldenOwl:ValidityFrom',
      },
      validityTo: {
        columnName: '::GoldenOwl:ValidityTo',
      },
      balance: {
        columnName: '::Balance',
      },
    },
  });

}
