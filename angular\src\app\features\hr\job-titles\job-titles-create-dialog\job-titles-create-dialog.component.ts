import { Component, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { AlertService, LanguagePipe, LOADING, TtwrFormComponent } from '@ttwr-framework/ngx-main-visuals';
import { finalize } from 'rxjs';
import { JobTitleService } from '@proxy/hr/job-titles';
import { jobTitles } from '../job-titles.model';

@Component({
  selector: 'app-job-titles-create',
  standalone: true,
  imports: [TtwrFormComponent, MatDialogTitle, MatDialogContent, LanguagePipe],
  template: `
    <h1 mat-dialog-title>{{ '::CreateJobTitle' | i18n }}</h1>
    <mat-dialog-content>
      <ttwr-form [config]="config"/>
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class JobTitlesCreateDialogComponent {
  private jobTitle = inject(JobTitleService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);
  private dialogRef = inject(MatDialogRef);

  protected config = jobTitles().select({
    name: true,
    departmentId: true,
    parentJobTitleId: true,
    baseSalary: true,
  }).form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        this.jobTitle
          .create(body)
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::CreatedSuccessfully');
            this.dialogRef.close(true);
          })
      },
    },
    fields: {
      parentJobTitleId: {
        validators: [],
        search: true,
        label: '::GoldenOwl:ParentJobTitle'
      },
      departmentId: {
        search: true,
        label: '::Department:Name',
      },
      baseSalary: {
        label: '::GoldenOwl:BaseSalary',
      }
    },
  });
}
