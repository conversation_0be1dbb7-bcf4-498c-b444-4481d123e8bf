using Elsa.Extensions;
using Elsa.Workflows;
using Elsa.Workflows.Activities.Flowchart.Attributes;
using Elsa.Workflows.Attributes;
using Elsa.Workflows.Models;
using ElsaWorkflows.GoldenOwl.Common.GoTasks;
using ElsaWorkflows.GoldenOwl.Common.Requests;

namespace ElsaWorkflows.Domain.Activities.GoTasks;

[Activity("GoTasks",
    "Assigns a new GoTask with the request to the previous user based on the userId, workflow input name: \"RequestId\" - input: Guid UserId")]
[FlowNode("Approved", "Rejected")]
public class AssignGoTaskToUser : CodeActivity
{
    private IGoTaskElsaHelper _goTaskElsaHelper;

    public Input<string> TaskName { get; set; } = default!;

    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        _goTaskElsaHelper = context.GetRequiredService<IGoTaskElsaHelper>();

        var input = context.GetLastResult();
        if (input?.ToString() is null)
        {
            throw new InvalidOperationException("The last result is null or invalid");
        }

        var userId = Guid.Parse(input.ToString());

        var requestId = Guid.Parse(context.GetWorkflowInput<string>("RequestId"));
        Enum.TryParse(context.GetWorkflowInput<string>("RequestType"), false, out RequestTypes requestType);

        var goTaskName = TaskName.GetOrDefault(context);
        if (string.IsNullOrWhiteSpace(goTaskName))
        {
            goTaskName = "go-task";
        }

        var bookmark = context.CreateBookmark(new CreateBookmarkArgs()
        {
            BookmarkName = "UserGoTask",
            Callback = OnBookmarkReceived,
        });

        await _goTaskElsaHelper.CreateAssignableGoTaskAsync(requestType, goTaskName, userId, requestId, bookmark.Id);
    }


    private async ValueTask OnBookmarkReceived(ActivityExecutionContext context)
    {
        var result = context.GetWorkflowInput<string>("Result");
        if (result is not ("Approved" or "Rejected"))
        {
            throw new NotImplementedException(
                $"the result: {result} is not supported by activity {nameof(AssignGoTaskToUser)}");
        }

        await context.CompleteActivityWithOutcomesAsync(result);
    }
}