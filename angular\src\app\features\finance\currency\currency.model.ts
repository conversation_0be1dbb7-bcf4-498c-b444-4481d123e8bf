import { fields, model } from '@ttwr-framework/ngx-main-visuals';

export const currency = model({
  code: fields.text(),
  fullName: fields.text(),
  symbol: fields.text(),
  isActive: fields.boolean(),
  decimalPlaces: fields.number(),
  unitLabel: fields.text(),
  subUnitLabel: fields.text(),
});

export const exchangeRate = model({
  id: fields.text(),
  date: fields.date(),
  rate: fields.number(),
})
