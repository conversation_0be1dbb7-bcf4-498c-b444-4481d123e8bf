import type { CancelContractDto, ContractDto, CreateUpdateContractDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ContractService {
  apiName = 'Default';
  

  create = (dto: CreateUpdateContractDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/contract',
      body: dto,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ContractDto>({
      method: 'GET',
      url: `/api/app/contract/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (dto: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<ContractDto>>({
      method: 'GET',
      url: '/api/app/contract',
      params: { skipCount: dto.skipCount, maxResultCount: dto.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  putActivateContract = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: `/api/app/contract/${id}/activate-contract`,
    },
    { apiName: this.apiName,...config });
  

  putCancelContract = (id: string, cancelContractDto: CancelContractDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: `/api/app/contract/${id}/cancel-contract`,
      body: cancelContractDto,
    },
    { apiName: this.apiName,...config });
  

  putExpireContract = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: `/api/app/contract/${id}/expire-contract`,
    },
    { apiName: this.apiName,...config });
  

  update = (id: string, dto: CreateUpdateContractDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'PUT',
      responseType: 'text',
      url: `/api/app/contract/${id}`,
      body: dto,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
