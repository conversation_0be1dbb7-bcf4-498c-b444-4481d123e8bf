using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoldenOwl.FN.ExchangeRates.DTO;
using GoldenOwl.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace GoldenOwl.FN.ExchangeRates;

public class ExchangeRateAppService : GoldenOwlAppService, IExchangeRateAppService
{
    private readonly IExchangeRateManager _exchangeRateManager;
    private readonly IRepository<ExchangeRate, Guid> _exchangeRateRepository;

    public ExchangeRateAppService(IExchangeRateManager exchangeRateManager,
        IRepository<ExchangeRate, Guid> exchangeRateRepository)
    {
        _exchangeRateManager = exchangeRateManager;
        _exchangeRateRepository = exchangeRateRepository;
    }

    [Authorize(GoldenOwlPermissions.CurrencyAndExchangeRateIndex)]
    public async Task<ExchangeRateDto> GetAsync(Guid id)
    {
        ExchangeRate exchangeRate = await _exchangeRateRepository.GetAsync(id);
        return ObjectMapper.Map<ExchangeRate, ExchangeRateDto>(exchangeRate);
    }

    [Authorize(GoldenOwlPermissions.CurrencyAndExchangeRateManagement)]
    public virtual async Task CreateAsync(CreateOrUpdateExchangeRateDto exchangeRate)
    {
        await _exchangeRateManager.AddExchangeRateAsync(exchangeRate.Date, exchangeRate.Rate,
            exchangeRate.CurrencyCode);
    }

    [Authorize(GoldenOwlPermissions.CurrencyAndExchangeRateManagement)]
    public virtual async Task UpdateAsync(CreateOrUpdateExchangeRateDto exchangeRate, Guid id)
    {
        await _exchangeRateManager.UpdateExchangeRateAsync(id, exchangeRate.Date, exchangeRate.Rate);
    }

    [Authorize(GoldenOwlPermissions.CurrencyAndExchangeRateIndex)]
    public async Task<PagedResultDto<ExchangeRateDto>> GetListAsync(PagedResultRequestDto input, string currencyCode)
    {
        IQueryable<ExchangeRate> query = await _exchangeRateRepository.GetQueryableAsync();
        query = query.Where(e => e.CurrencyCode == currencyCode)
            .OrderByDescending(c => c.Date);

        int totalCount = await AsyncExecuter.CountAsync(query);
        List<ExchangeRate> exchangeRates = await AsyncExecuter.ToListAsync(query.PageBy(input));
        List<ExchangeRateDto> exchangeRatesDto =
            ObjectMapper.Map<List<ExchangeRate>, List<ExchangeRateDto>>(exchangeRates);

        return new PagedResultDto<ExchangeRateDto>(totalCount, exchangeRatesDto);
    }

    [Authorize(GoldenOwlPermissions.CurrencyAndExchangeRateManagement)]
    public async Task DeleteAsync(Guid id)
    {
        ExchangeRate exchangeRate = await _exchangeRateRepository.GetAsync(id);
        await _exchangeRateRepository.DeleteAsync(exchangeRate);
    }
}