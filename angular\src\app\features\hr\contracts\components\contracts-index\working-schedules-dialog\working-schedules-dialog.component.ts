import { Component, inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogTitle } from '@angular/material/dialog';
import { ContractWorkingScheduleDto } from '@proxy/hr/contracts/dto';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-working-schedules-dialog',
  standalone: true,
  imports: [
    LanguagePipe,
    MatDialogTitle,
    MatDialogContent
  ],
  template: `
    <h1 mat-dialog-title>
      {{ '::WorkingSchedules' | i18n }}
    </h1>
    <mat-dialog-content>
      <table class="ttwr-basic-table">
        <thead>
        <tr>
          <th>{{ 'name' | i18n }}</th>
          <th>{{ 'GoldenOwl:From' | i18n }}</th>
          <th>{{ 'GoldenOwl:To' | i18n }}</th>
        </tr>
        </thead>
        <tbody>
        @for (schedule of data; track schedule.id) {
          <tr>
            <td>{{ schedule.workingScheduleName }}</td>
            <td>{{ schedule.from }}</td>
            <td>{{ schedule.to }}</td>
          </tr>
        }
        </tbody>
      </table>
    </mat-dialog-content>
  `,
})
export class WorkingSchedulesDialogComponent {
  protected data = inject<ContractWorkingScheduleDto[]>(MAT_DIALOG_DATA);
}

