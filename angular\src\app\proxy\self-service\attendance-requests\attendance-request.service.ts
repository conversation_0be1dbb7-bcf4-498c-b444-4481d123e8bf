import type { AttendanceRequestState } from './attendance-request-state.enum';
import type { AttendanceRequestDto, CreateUpdateAttendanceRequestDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class AttendanceRequestService {
  apiName = 'Default';
  

  cancelSelfRequest = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/attendance-request/${id}/cancel-self-request`,
    },
    { apiName: this.apiName,...config });
  

  createSelfRequest = (createUpdateAttendanceRequestDto: CreateUpdateAttendanceRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AttendanceRequestDto>({
      method: 'POST',
      url: '/api/app/attendance-request/self-request',
      body: createUpdateAttendanceRequestDto,
    },
    { apiName: this.apiName,...config });
  

  getRequest = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AttendanceRequestDto>({
      method: 'GET',
      url: `/api/app/attendance-request/${id}/request`,
    },
    { apiName: this.apiName,...config });
  

  getSelfRequests = (dto: PagedResultRequestDto, fromDate: string, toDate: string, includedStates: AttendanceRequestState[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<AttendanceRequestDto>>({
      method: 'GET',
      url: '/api/app/attendance-request/self-requests',
      params: { skipCount: dto.skipCount, maxResultCount: dto.maxResultCount, fromDate, toDate, includedStates },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
