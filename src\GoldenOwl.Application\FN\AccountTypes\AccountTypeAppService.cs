using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoldenOwl.FN.AccountTypes.DTO;
using GoldenOwl.Permissions;
using Microsoft.AspNetCore.Authorization;

namespace GoldenOwl.FN.AccountTypes;

public class AccountTypeAppService : GoldenOwlAppService, IAccountTypeAppService
{
    private readonly IAccountTypeService _accountTypeService;

    public AccountTypeAppService(IAccountTypeService accountTypeService)
    {
        _accountTypeService = accountTypeService;
    }

    [Authorize(GoldenOwlPermissions.AccountIndex)]
    public Task<IEnumerable<AccountTypeDto>> GetListAsync(string? categoryCode = null)
    {
        List<AccountType> accountTypes;
        if (!string.IsNullOrWhiteSpace(categoryCode))
        {
            accountTypes = _accountTypeService.GetAccountTypesByCategory(categoryCode);
        }
        else
        {
            accountTypes = _accountTypeService.GetAccountTypes();
        }

        List<AccountTypeDto> accountTypeDtos =
            ObjectMapper.Map<List<AccountType>, List<AccountTypeDto>>(accountTypes);
        return Task.FromResult(accountTypeDtos.AsEnumerable());
    }

    [Authorize(GoldenOwlPermissions.AccountIndex)]
    public Task<List<AccountTypeDto>> GetCategoryListAsync()
    {
        List<AccountTypeCategory> accountTypeCategories = _accountTypeService.GetAccountTypeCategories();
        List<AccountTypeDto> accountTypeDtos =
            ObjectMapper.Map<List<AccountTypeCategory>, List<AccountTypeDto>>(accountTypeCategories);

        return Task.FromResult(accountTypeDtos);
    }

    [Authorize(GoldenOwlPermissions.AccountIndex)]
    public Task<bool> GetHasAccountTypesAsync(string categoryCode)
    {
        return Task.FromResult(_accountTypeService.HasAccountTypes(categoryCode));
    }

    [Authorize(GoldenOwlPermissions.AccountIndex)]
    public Task<AccountTypeDto> GetViewAsync(string name)
    {
        AccountType accountType = _accountTypeService.GetAccountTypeByName(name);
        return Task.FromResult(ObjectMapper.Map<AccountType, AccountTypeDto>(accountType));
    }
}