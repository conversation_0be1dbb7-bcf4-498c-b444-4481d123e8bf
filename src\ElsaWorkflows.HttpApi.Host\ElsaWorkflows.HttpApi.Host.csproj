﻿  <Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <OutputType>Exe</OutputType>
    </PropertyGroup>

    <ItemGroup>
      <Content Include="appsettings.json">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Elsa.Identity" Version="3.3.1" />
      <PackageReference Include="Elsa.Workflows.Api" Version="3.3.1" />
      <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.7" />
      <PackageReference Include="Microsoft.IdentityModel.JsonWebTokens" Version="8.3.0" />
      <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
      <PackageReference Include="Volo.Abp.Autofac" Version="9.0.2" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\ElsaWorkflows.Domain\ElsaWorkflows.Domain.csproj" />
      <ProjectReference Include="..\ElsaWorkflows.EntityFrameworkCore\ElsaWorkflows.EntityFrameworkCore.csproj" />
    </ItemGroup>
</Project>
