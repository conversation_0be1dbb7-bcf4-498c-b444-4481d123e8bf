import { Component, DestroyRef, inject } from '@angular/core';
import { AlertService, fields, LOADING, pagedMap, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { attachmentTypes } from './attachment-types.model';
import { AttachmentTypeService } from '@proxy/attachment-types';
import { bytesToFormattedSize, requireAllOperator } from '@shared';
import { MatDialog } from '@angular/material/dialog';
import {
  AttachmentTypesCreateDialogComponent
} from './attachment-types-create-dialog/attachment-types-create-dialog.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Subject } from 'rxjs';
import {
  AttachmentTypesUpdateDialogComponent
} from './attachment-types-update-dialog/attachment-types-update-dialog.component';

@Component({
  selector: 'app-attachment-types',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `
    <ttwr-grid class="actions-end" [config]="config"/>`,
})
export class AttachmentTypesComponent {
  private attachmentType = inject(AttachmentTypeService);
  private dialog = inject(MatDialog);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);

  private refreshSubject = new Subject<void>();

  protected config = attachmentTypes().exclude({
    allowedExtensions: true,
  }).extend({
    allowedExtensions: fields.text(),
  }).grid({
    title: '::AttachmentTypes',
    refreshSubject: this.refreshSubject,
    dataFunc: pagination => this.attachmentType.getList({
      skipCount: pagination.pageSize * pagination.pageIndex,
      maxResultCount: pagination.pageSize,
    }).pipe(
      requireAllOperator(),
      pagedMap(type => ({
        ...type,
        allowedExtensions: type.allowedExtensions.map(ext => ext.value).join(', '),
      })),
    ),
    actions: [
      {
        matIcon: 'add',
        tooltip: 'Add',
        delegateFunc: () => {
          const ref = this.dialog.open(AttachmentTypesCreateDialogComponent, {
            width: '100%',
            maxWidth: '700px',
          })

          ref.afterClosed().pipe(
            takeUntilDestroyed(this.destroyRef)
          ).subscribe(res => {
            if (res) {
              this.refreshSubject.next();
            }
          })
        },
      }
    ],
    fieldActions: [
      {
        matIcon: 'edit',
        tooltip: 'Edit',
        delegateFunc: (obj) => {
          const ref = this.dialog.open(AttachmentTypesUpdateDialogComponent, {
            width: '100%',
            maxWidth: '700px',
            data: {
              ...obj,
              allowedExtensions: obj.allowedExtensions.split(',').map(ext => ext.trim()),
            },
          })

          ref.afterClosed().pipe(
            takeUntilDestroyed(this.destroyRef)
          ).subscribe(res => {
            if (res) {
              this.refreshSubject.next();
            }
          })
        }
      },
      {
        matIcon: 'delete',
        tooltip: 'Delete',
        color: 'warn',
        confirmation: {
          title: '::Warning',
          message: '::AreYouSureYouWantToDelete?',
        },
        delegateFunc: (obj) => {
          this.loading.set(true);

          this.attachmentType.delete(obj.id).pipe(
            takeUntilDestroyed(this.destroyRef)
          ).subscribe(() => {
            this.refreshSubject.next();
            this.alert.success('::SuccessDelete');
          })
        }
      }
    ],
    fields: {
      entityType: {
        columnName: '::EntityType',
      },
      allowedSize: {
        columnName: '::AllowedSize',
        displayCell: cell => {
          const { size, unit } = bytesToFormattedSize(cell);

          return `${size.toFixed(2)} ${unit}`;
        }
      },
      allowedExtensions: {
        columnName: '::AllowedExtensions'
      },
    },
  })
}
