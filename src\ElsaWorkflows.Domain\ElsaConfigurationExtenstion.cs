﻿using Elsa.Features.Services;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Concurrent;
using Elsa.Extensions;

namespace ElsaWorkflows.Domain
{
    public static class ElsaConfigurationExtenstion
    {
        private static readonly IDictionary<IServiceCollection, IModule> Modules = new ConcurrentDictionary<IServiceCollection, IModule>();

        private static Action<IModule>? _configuration;
        public static IModule GoAddElsa(this IServiceCollection services, Action<IModule>? configure = null)
        {
            if (configure is not null)
            {
                services.GoAddElsaConfiguration(configure);
            } 
            return services.AddElsa(_configuration);
        }

        public static void GoAddElsaConfiguration(this IServiceCollection services, Action<IModule> config)
        {
            _configuration = (Action<IModule>)Delegate.Combine(_configuration, config);
        }

    }
}
