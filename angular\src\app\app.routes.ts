import { Routes } from '@angular/router';
import { routes as abpOverridesRoutes } from './abp-overrides';
import { HomeComponent } from './common/home.component';
import { NotFoundComponent } from './common/not-found.component';
import { NotificationsComponent } from './features/notifications/notifications.component';
import { routes as selfServiceRoutes } from './features/self-service/self-service.routes';
import { routes as hrRoutes } from './features/hr/hr.routes';
import { routes as settingsRoutes } from './features/settings/settings.routes';
import { routes as payrollManagementRoutes } from './features/payroll-management/payroll-management.routes';
import { routes as financeRoutes } from './features/finance/finance.routes';


export const routes: Routes = [
  {
    path: '',
    component: HomeComponent,
  },

  // features urls
  {
    path: 'notifications',
    component: NotificationsComponent,
  },
  {
    path: 'self-service',
    children: selfServiceRoutes,
  },
  {
    path: 'hr',
    children: hrRoutes,
  },
  {
    path: 'settings',
    children: settingsRoutes,
  },
  {
    path: 'payroll-management',
    children: payrollManagementRoutes,
  },
  {
    path: 'finance',
    children: financeRoutes,
  },

  ...abpOverridesRoutes,
  // ==== catch rest urls ====
  {
    path: '**',
    component: NotFoundComponent,
  },
];
