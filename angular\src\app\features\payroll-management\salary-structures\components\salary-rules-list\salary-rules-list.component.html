<h3>
  {{ '::PayrollSalaryRules' | i18n }}
</h3>
<div cdkDropList (cdkDropListDropped)="drop($event)">
  @for (rule of selectedRules(); track $index) {
    <div cdkDrag>
      <div cdkDragHandle>
        <mat-icon>menu</mat-icon>
      </div>
      <mat-form-field subscriptSizing="dynamic">
        <mat-label>{{ '::GoldenOwl:SalaryRule' | i18n }}</mat-label>
        <mat-select (valueChange)="changeValue($index, $event)" [value]="rule">
          @for (rule of rules(); track rule.value) {
            <mat-option [value]="rule.value">
              {{ rule.label }}
            </mat-option>
          }
        </mat-select>
        <mat-error>
          {{ 'This field is required' | i18n }}
        </mat-error>
      </mat-form-field>
      @if (!$first || !$last) {
        <button
          [matTooltip]="'Delete' | i18n"
          (click)="removeRule($index)"
          color="warn"
          mat-mini-fab
          type="button"
        >
          <mat-icon>close</mat-icon>
        </button>
      }
    </div>
  }
  <div class="buttons-row">
    <button (click)="addItem()" type="button" mat-flat-button>
      {{ 'Add' | i18n }}
      {{ ' ' }}
      {{ '::Item' | i18n }}
    </button>
    <button (click)="addNewSalaryRule()" type="button" color="success" mat-flat-button>
      {{ 'Add' | i18n }}
      {{ ' ' }}
      {{ '::NewSalaryRule' | i18n }}
    </button>
  </div>
</div>
