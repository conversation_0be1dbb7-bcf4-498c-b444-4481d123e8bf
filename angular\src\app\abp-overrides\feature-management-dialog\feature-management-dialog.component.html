<h1 mat-dialog-title>
  {{ 'AbpFeatureManagement::Features' | abpLocalization }}
</h1>
<mat-dialog-content>
  @if (featuresGroups(); as groups) {
    @if (groups.length > 0) {
      <mat-nav-list>
        @for (group of groups; track group.name) {
          <button
            (click)="activeGroup.set(group.name)"
            role="tab"
            mat-list-item
            [activated]="activeGroup() === group.name"
          >
            {{ group.displayName }}
          </button>
        }
      </mat-nav-list>
      @if (featuresMap().get(activeGroup() ?? ''); as features) {
        <div>
          <h4>{{ activeGroupDisplayName() }}</h4>
          <mat-divider />
          @for (feature of features; track feature.name) {
            <div class="feature-field">
              @switch (feature.featureType) {
                @case ('boolean') {
                  <mat-checkbox [formControl]="feature.control">
                    {{ feature.displayName }}
                  </mat-checkbox>
                }
                @case ('select') {
                  <mat-form-field subscriptSizing="dynamic">
                    <mat-label>{{ feature.displayName }}</mat-label>
                    <mat-select [formControl]="feature.control">
                      @for (option of $any($any(feature.valueType).itemSource.items); track option.value) {
                        <mat-option [value]="option.value">
                          {{ option.value }}
                        </mat-option>
                      }
                    </mat-select>
                  </mat-form-field>
                }
                @case ('number') {
                  <mat-form-field subscriptSizing="dynamic">
                    <mat-label>{{ feature.displayName }}</mat-label>
                    <input [formControl]="feature.control" matInput type="number">
                  </mat-form-field>
                }
                @case ('text') {
                  <mat-form-field subscriptSizing="dynamic">
                    <mat-label>{{ feature.displayName }}</mat-label>
                    <input [formControl]="feature.control" matInput>
                  </mat-form-field>
                }
                @case ('custom') {
                  @if (feature.valueType.name && extraFeatures[feature.valueType.name]; as component) {
                    <ng-container
                      *ngComponentOutlet="component; inputs: { feature: feature }"
                    />
                  } @else {
                    <span class="error-text">
                      No attribute defined in EXTRA_FEATURES token for '{{ feature.valueType.name }}' type
                    </span>
                  }
                }
              }
              <small>
                {{ feature.description }}
              </small>
            </div>
          }
        </div>
      }
    } @else {
      <p>
        {{ 'AbpFeatureManagement::NoFeatureFoundMessage' | abpLocalization }}
      </p>
      <!-- for proper justify -->
      <span style="flex-grow: 1"></span>
    }
  } @else {
    <mat-progress-spinner mode="indeterminate" />
  }
</mat-dialog-content>
<mat-dialog-actions>
  <button mat-dialog-close mat-raised-button>
    {{ 'Cancel' | i18n }}
  </button>
  <button (click)="save()" [disabled]="activeGroup() === null" mat-raised-button>
    <mat-icon>check</mat-icon>
    {{ 'Save' | i18n }}
  </button>
</mat-dialog-actions>
