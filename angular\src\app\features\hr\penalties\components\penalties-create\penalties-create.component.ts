import { Component, DestroyRef, inject } from '@angular/core';
import {
  AlertService,
  LanguagePipe,
  LOADING,
  requiredValidator,
  TtwrFormComponent
} from '@ttwr-framework/ngx-main-visuals';
import { Mat<PERSON>ard, MatCardContent } from '@angular/material/card';
import { penalties } from '../../penalties.model';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize } from 'rxjs';
import { PenaltyService } from '@proxy/hr/penalties';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { clearDate } from '@shared';

@Component({
  selector: 'app-penalties-create',
  standalone: true,
  imports: [
    LanguagePipe,
    MatCard,
    MatCardContent,
    TtwrFormComponent
  ],
  templateUrl: './penalties-create.component.html',
  styles: `
    :host {
      --ttwr-form-gap: 1rem;
      --ttwr-form-grid-template-columns: 1fr 1fr;
    }
  `,
})
export class PenaltiesCreateComponent {
  private penalty = inject(PenaltyService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);

  protected config = penalties().exclude({
    penaltyTypeName: true,
  }).form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        this.penalty.postCreatePenaltyByDto({
          ...body,
          declarationDate: clearDate(body.declarationDate),
          implementationDate: clearDate(body.implementationDate),
          expirationDate: clearDate(body.expirationDate),
        }).pipe(
          finalize(() => this.loading.set(false)),
          takeUntilDestroyed(this.destroyRef),
        ).subscribe(() => {
          this.alert.success('::CreatedSuccessfully');
          this.router.navigate(['.'], { relativeTo: this.route.parent });
        })
      }
    },
    fields: {
      summary: {
        label: '::PenaltyType:Summary',
        textInputType: 'textarea',
        inputSize: 'span 2',
      },
      employeeId: {
        label: '::GoldenOwl:Employee',
        search: true,
      },
      penaltyTypeId: {
        label: '::Penalty:PenaltyType',
        search: true,
      },
      declarationDate: {
        label: '::PenaltyType:DeclarationDate',
        onChange: () => {
          this.config.fields.implementationDate.control.updateValueAndValidity({
            emitEvent: false,
          });

          this.config.fields.expirationDate.control.updateValueAndValidity({
            emitEvent: false,
          });
        },
        validators: [
          requiredValidator,
          {
            name: 'invalidDeclarationDate',
            message: '::InvalidDeclarationDate',
            validator: control => {
              if (!control.value) return null;

              const implementationDateValue = (control.parent?.controls as any)['implementationDate']?.value;

              if (!implementationDateValue) return null;

              if (implementationDateValue < control.value) return {
                invalidDeclarationDate: true,
              };

              return null;
            },
          }
        ],
      },
      implementationDate: {
        label: '::PenaltyType:ImplementationDate',
        onChange: () => {
          this.config.fields.declarationDate.control.updateValueAndValidity({
            emitEvent: false,
          });

          this.config.fields.expirationDate.control.updateValueAndValidity({
            emitEvent: false,
          });
        },
        validators: [
          requiredValidator,
          {
            name: 'invalidImplementationDate',
            message: '::InvalidImplementationDate',
            validator: control => {
              if (!control.value) return null;

              const expirationDateValue = (control.parent?.controls as any)['expirationDate']?.value;

              if (!expirationDateValue) return null;

              if (expirationDateValue < control.value) return {
                invalidImplementationDate: true,
              };

              return null;
            },
          }
        ],
      },
      expirationDate: {
        label: '::Penalty:ExpirationDate',
        onChange: () => {
          this.config.fields.declarationDate.control.updateValueAndValidity({
            emitEvent: false,
          });

          this.config.fields.implementationDate.control.updateValueAndValidity({
            emitEvent: false,
          });
        },
        validators: [
          requiredValidator,
          {
            name: 'invalidExpirationDate',
            message: '::InvalidExpirationDate',
            validator: control => {
              if (!control.value) return null;

              const implementationDateValue = (control.parent?.controls as any)['implementationDate']?.value;

              if (!implementationDateValue) return null;

              if (control.value < implementationDateValue) return {
                invalidExpirationDate: true,
              };

              return null;
            },
          }
        ],
      },
    },
  })
}
