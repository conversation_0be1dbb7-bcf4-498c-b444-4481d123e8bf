import type { JobExecutionLogDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class JobExecutionLogService {
  apiName = 'Default';
  

  getExecutionLogs = (executionRecordId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, JobExecutionLogDto[]>({
      method: 'GET',
      url: `/api/app/job-execution-log/execution-logs/${executionRecordId}`,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
