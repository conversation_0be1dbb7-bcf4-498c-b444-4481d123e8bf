import {
  RestService
} from "./chunk-VITQ7ATO.js";
import "./chunk-JS23NXQZ.js";
import "./chunk-F7YCCNPX.js";
import "./chunk-AABMUNXW.js";
import "./chunk-K46JBGQH.js";
import "./chunk-B4FFJ7GE.js";
import "./chunk-VTW5CIPD.js";
import {
  Injectable,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-7YC2NMXI.js";
import "./chunk-ILOBLWYU.js";
import "./chunk-ZTELYOIP.js";

// node_modules/@abp/ng.permission-management/fesm2022/abp-ng.permission-management-proxy.mjs
var PermissionsService = class _PermissionsService {
  constructor(restService) {
    this.restService = restService;
    this.apiName = "AbpPermissionManagement";
    this.get = (providerName, providerKey) => this.restService.request({
      method: "GET",
      url: "/api/permission-management/permissions",
      params: {
        providerName,
        providerKey
      }
    }, {
      apiName: this.apiName
    });
    this.update = (providerName, providerKey, input) => this.restService.request({
      method: "PUT",
      url: "/api/permission-management/permissions",
      params: {
        providerName,
        providerKey
      },
      body: input
    }, {
      apiName: this.apiName
    });
  }
  static {
    this.ɵfac = function PermissionsService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _PermissionsService)(ɵɵinject(RestService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _PermissionsService,
      factory: _PermissionsService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PermissionsService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RestService
  }], null);
})();
export {
  PermissionsService
};
//# sourceMappingURL=@abp_ng__permission-management_proxy.js.map
