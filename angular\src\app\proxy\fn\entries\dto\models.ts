import type { EntryType } from '../entry-type.enum';
import type { FullAuditedEntityDto } from '@abp/ng.core';

export interface CreateEntryDto {
  entryType: EntryType;
  entryDate?: string;
  description?: string;
  entryItems: CreateUpdateEntryItemDto[];
}

export interface CreateUpdateEntryItemDto {
  serialNumber: number;
  accountId?: string;
  description?: string;
  debit: number;
  credit: number;
  exchangeRate: number;
  currencyDebit: number;
  currencyCredit: number;
}

export interface EntryDto extends FullAuditedEntityDto<string> {
  entryType: EntryType;
  entryNumber?: string;
  entryDate?: string;
  description?: string;
  isPosted: boolean;
  postedDate?: string;
  totalAmount: number;
  financialPeriodId?: string;
  entryItems: EntryItemDto[];
}

export interface EntryItemDto {
  serialNumber: number;
  accountId?: string;
  accountCode?: string;
  accountName?: string;
  currencyCode?: string;
  description?: string;
  debit: number;
  credit: number;
  exchangeRate: number;
  currencyDebit: number;
  currencyCredit: number;
}

export interface UpdateEntryDto {
  entryDate?: string;
  description?: string;
  entryItems: CreateUpdateEntryItemDto[];
}
