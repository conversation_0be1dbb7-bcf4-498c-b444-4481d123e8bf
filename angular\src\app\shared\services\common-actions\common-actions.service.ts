import { inject, Injectable } from '@angular/core';
import { CONFIRM_MESSAGE, CONFIRM_TITLE } from '@shared';
import { Observable } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { CommonActionsConfirmComponent } from './common-actions-confirm/common-actions-confirm.component';

@Injectable({
  providedIn: 'root',
})
export class CommonActionsService {
  private dialog = inject(MatDialog);

  public confirmationAction(
    title: string = CONFIRM_TITLE,
    message: string = CONFIRM_MESSAGE
  ): Observable<boolean> {
    const dialogRef = this.dialog.open(CommonActionsConfirmComponent, {
      data: { title, message },
    });
    return dialogRef.afterClosed();
  }
}
