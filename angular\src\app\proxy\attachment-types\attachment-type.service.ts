import type { AttachmentTypeDto } from './dto/models';
import type { EntityType } from './entity-type.enum';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class AttachmentTypeService {
  apiName = 'Default';
  

  create = (dto: AttachmentTypeDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/attachment-type',
      body: dto,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/attachment-type/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getAvailableExtension = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, string[]>({
      method: 'GET',
      url: '/api/app/attachment-type/available-extension',
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<AttachmentTypeDto>>({
      method: 'GET',
      url: '/api/app/attachment-type',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getListByEntityTypeAsynByEntityType = (entityType: EntityType, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AttachmentTypeDto[]>({
      method: 'GET',
      url: '/api/app/attachment-type/by-entity-type-asyn',
      params: { entityType },
    },
    { apiName: this.apiName,...config });
  

  update = (dto: AttachmentTypeDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: '/api/app/attachment-type',
      body: dto,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
