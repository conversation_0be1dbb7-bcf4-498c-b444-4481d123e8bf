import {
  RoutesService
} from "./chunk-VITQ7ATO.js";
import "./chunk-JS23NXQZ.js";
import "./chunk-F7YCCNPX.js";
import "./chunk-AABMUNXW.js";
import "./chunk-K46JBGQH.js";
import "./chunk-B4FFJ7GE.js";
import "./chunk-VTW5CIPD.js";
import {
  APP_INITIALIZER,
  NgModule,
  makeEnvironmentProviders,
  setClassMetadata,
  ɵɵdefineInjector,
  ɵɵdefineNgModule
} from "./chunk-7YC2NMXI.js";
import "./chunk-ILOBLWYU.js";
import "./chunk-ZTELYOIP.js";

// node_modules/@abp/ng.identity/fesm2022/abp-ng.identity-config.mjs
var IDENTITY_ROUTE_PROVIDERS = [{
  provide: APP_INITIALIZER,
  useFactory: configureRoutes,
  deps: [RoutesService],
  multi: true
}];
function configureRoutes(routesService) {
  return () => {
    routesService.add([{
      path: void 0,
      name: "AbpIdentity::Menu:IdentityManagement",
      parentName: "AbpUiNavigation::Menu:Administration",
      requiredPolicy: "AbpIdentity.Roles || AbpIdentity.Users",
      iconClass: "fa fa-id-card-o",
      layout: "application",
      order: 1
    }, {
      path: "/identity/roles",
      name: "AbpIdentity::Roles",
      parentName: "AbpIdentity::Menu:IdentityManagement",
      requiredPolicy: "AbpIdentity.Roles",
      order: 1
    }, {
      path: "/identity/users",
      name: "AbpIdentity::Users",
      parentName: "AbpIdentity::Menu:IdentityManagement",
      requiredPolicy: "AbpIdentity.Users",
      order: 2
    }]);
  };
}
function provideIdentityConfig() {
  return makeEnvironmentProviders([IDENTITY_ROUTE_PROVIDERS]);
}
var IdentityConfigModule = class _IdentityConfigModule {
  static forRoot() {
    return {
      ngModule: _IdentityConfigModule,
      providers: [provideIdentityConfig()]
    };
  }
  static {
    this.ɵfac = function IdentityConfigModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _IdentityConfigModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _IdentityConfigModule
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({});
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(IdentityConfigModule, [{
    type: NgModule
  }], null, null);
})();
export {
  IDENTITY_ROUTE_PROVIDERS,
  IdentityConfigModule,
  configureRoutes,
  provideIdentityConfig
};
//# sourceMappingURL=@abp_ng__identity_config.js.map
