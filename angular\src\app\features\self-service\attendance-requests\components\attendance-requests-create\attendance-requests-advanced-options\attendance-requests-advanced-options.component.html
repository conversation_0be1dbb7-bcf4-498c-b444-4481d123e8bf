<mat-radio-group [formControl]="modeControl" (change)="onSelectionChange($event)">
  <mat-radio-button value="remove-all">
    {{ '::RemoveOtherAttendancesAtTheSameDay' | i18n }}
  </mat-radio-button>
  <mat-radio-button value="remove-overlapping-only">
    {{ '::RemoveOverlappingOnly' | i18n }}
  </mat-radio-button>
  <mat-radio-button value="fix">
    {{ '::FixSpecificAttendance' | i18n }}
  </mat-radio-button>
</mat-radio-group>
<div class="inputs-column">
  <div class="fix-inputs-row">
    <mat-form-field subscriptSizing="dynamic">
      <mat-label>{{ '::GoldenOwl:From' | i18n }}</mat-label>
      <input [value]="fromValue() ?? ''" readonly matInput />
    </mat-form-field>
    <mat-form-field subscriptSizing="dynamic">
      <mat-label>{{ '::GoldenOwl:To' | i18n }}</mat-label>
      <input [value]="toValue() ?? ''" readonly matInput />
    </mat-form-field>
    @if (affectedDays(); as days) {
      <span>
        {{ '::DaysAffected' | i18n }} {{ days }}
      </span>
    }
  </div>
  <mat-form-field>
    <mat-label>{{ '::SelectAttendance' | i18n }}</mat-label>
    <mat-select
      [formControl]="attendanceIdControl"
    >
    </mat-select>
  </mat-form-field>
</div>
