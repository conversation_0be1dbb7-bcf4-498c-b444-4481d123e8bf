import { Component, input } from '@angular/core';
import {
  DateTimeFieldType,
  FormFieldWithControlAndName,
  ICustomInputComponent, LanguagePipe,
  provideNativeDatetimeA<PERSON>pter,
  TtwrDatetimepicker,
  TtwrDatetimepickerInput,
  TtwrDatetimepickerToggle
} from '@ttwr-framework/ngx-main-visuals';
import { Mat<PERSON>rror, MatFormField, MatLabel, MatSuffix } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-time-period-custom-input',
  standalone: true,
  imports: [
    MatFormField,
    MatLabel,
    MatError,
    MatSuffix,
    TtwrDatetimepicker,
    MatInput,
    ReactiveFormsModule,
    TtwrDatetimepickerInput,
    TtwrDatetimepickerToggle,
    LanguagePipe,
  ],
  template: `
    <mat-form-field>
      <mat-label>{{ (field().label ?? field().name) | i18n }}</mat-label>
      <ttwr-datetimepicker
        [timeInput]="true"
        mode="portrait"
        type="time"
        #datetimePicker
      />
      <input
        matInput
        [ttwrDatetimepicker]="datetimePicker"
        [formControl]="field().control"
        [readonly]="field().readonlySignal?.()"
      >
      <ttwr-datetimepicker-toggle
        [disabled]="field().disabledSignal?.() || field().readonlySignal?.()"
        [for]="datetimePicker"
        matSuffix
      />
      <mat-error>
        @for (validator of field().validators ?? []; track validator.name) {
          <span [hidden]="!field().control.hasError(validator.name)">
            {{ validator.message | i18n }}
          </span>
        }
      </mat-error>
    </mat-form-field>
  `,
  providers: [
    provideNativeDatetimeAdapter({
      parse: {},
      display: {
        dateInput: { year: 'numeric', month: '2-digit', day: '2-digit' },
        monthInput: { month: 'long' },
        datetimeInput: {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
        },
        timeInput: {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false,
        },
        monthYearLabel: { year: 'numeric', month: 'short' },
        dateA11yLabel: { year: 'numeric', month: 'long', day: 'numeric' },
        monthYearA11yLabel: { year: 'numeric', month: 'long' },
        popupHeaderDateLabel: { weekday: 'short', month: 'short', day: '2-digit' },
      },
    })
  ],
  styles: `
    mat-form-field {
      width: 100%;
    }
  `,
})
export class TimePeriodCustomInputComponent implements ICustomInputComponent<DateTimeFieldType> {
  public field = input.required<FormFieldWithControlAndName<DateTimeFieldType>>();
}
