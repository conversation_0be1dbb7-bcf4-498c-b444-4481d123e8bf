import type { AuditedEntityDto } from '@abp/ng.core';

export interface CreateStructureRuleDto {
  ruleId?: string;
  sequence: number;
}

export interface CreateUpdateSalaryStructureDto {
  name?: string;
  structureRules: CreateStructureRuleDto[];
}

export interface SalaryStructureDetailsDto extends AuditedEntityDto<string> {
  name?: string;
  structureRules: StructureRuleDto[];
}

export interface SalaryStructureDto extends AuditedEntityDto<string> {
  name?: string;
}

export interface StructureRuleDto {
  ruleId?: string;
  sequence: number;
  name?: string;
  categoryName?: string;
  code?: string;
  isActive: boolean;
}
