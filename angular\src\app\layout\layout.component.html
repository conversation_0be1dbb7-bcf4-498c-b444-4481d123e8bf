<mat-toolbar class="mat-elevation-z2">
  @if (isSmall()) {
  <button (click)="sidenav.toggle()" mat-icon-button>
    <mat-icon>menu</mat-icon>
  </button>
  } @else {
  <div class="logo"></div>
  }
  <div style="flex-grow: 1"></div>
  @if (isAuth()) {
  <app-notification-button />
  }

  <button mat-icon-button [matMenuTriggerFor]="menu">
    <mat-icon>language</mat-icon>
  </button>
  <mat-menu #menu>
    <button mat-menu-item (click)="changeLang('ar')">العربية</button>
    <button mat-menu-item (click)="changeLang('en')">English</button>
  </mat-menu>

  <mat-menu #accountMenu>
    <button mat-menu-item (click)="navigateToManageProfile()">
      {{ "AbpAccount::MyAccount" | abpLocalization }}
    </button>
    <button mat-menu-item (click)="logout()">
      {{ "AbpUi::Logout" | abpLocalization }}
    </button>
  </mat-menu>
  @switch (isAuth()) { @case (true) {
  <button mat-stroked-button [matMenuTriggerFor]="accountMenu">
    <mat-icon>person</mat-icon>
    {{ username() }}
  </button>
  } @case (false) {
  <button (click)="navigateToLogin()" mat-button>
    {{ "AbpUi::Login" | abpLocalization }}
  </button>
  } @default { Authorizing... } }
</mat-toolbar>
<mat-sidenav-container>
  <mat-sidenav
    fixedInViewport
    [mode]="isSmall() ? 'over' : 'side'"
    [opened]="!isSmall()"
    #sidenav
  >
    <mat-nav-list>
      @for (route of routes$ | async; track route.name) { @if
      (!isDropdown(route)) {
      <a
        mat-list-item
        (click)="isSmall() && sidenav.close()"
        [routerLink]="[route.path]"
        *abpPermission="route.requiredPolicy"
      >
        @if (route.iconClass) {
        <mat-icon matListItemIcon>
          {{ defaultIconsMap.get(route.iconClass) ?? route.iconClass }}
        </mat-icon>
        }
        {{ route.name | abpLocalization }}
      </a>
      } @else {
      <ng-container
        [ngTemplateOutlet]="dropdownLink"
        [ngTemplateOutletContext]="{ $implicit: route }"
      >
      </ng-container>
      } }
    </mat-nav-list>
  </mat-sidenav>
  <mat-sidenav-content>
    <main>
      <app-breadcrumb />
      <router-outlet />
    </main>
  </mat-sidenav-content>
</mat-sidenav-container>

<ng-template #dropdownLink let-route>
  @if (route.children?.length) {
  <mat-expansion-panel *abpPermission="route.requiredPolicy">
    <mat-expansion-panel-header>
      <mat-panel-title>
        <mat-icon>
          {{ defaultIconsMap.get(route.iconClass) ?? route.iconClass }}
        </mat-icon>
        {{ route.name | abpLocalization }}
      </mat-panel-title>
    </mat-expansion-panel-header>
    <ng-container
      *ngTemplateOutlet="forTemplate; context: { $implicit: route }"
    />
  </mat-expansion-panel>
  }
</ng-template>

<ng-template #forTemplate let-route>
  @for (child of route.children; track $index) {
  <ng-template
    [ngTemplateOutlet]="child.children?.length ? dropdownChild : defaultChild"
    [ngTemplateOutletContext]="{ $implicit: child }"
  ></ng-template>
  }
</ng-template>

<ng-template #defaultChild let-child>
  @if (child.path) {
  <a
    mat-list-item
    class="mat-mdc-list-item-interactive"
    matRipple
    [routerLink]="[child.path]"
    (click)="isSmall() && sidenav.close()"
    *abpPermission="child.requiredPolicy"
  >
    @if (child.iconClass) {
    <mat-icon matListItemIcon>
      {{ defaultIconsMap.get(child.iconClass) ?? child.iconClass }}
    </mat-icon>
    }
    {{ child.name | abpLocalization }}
  </a>
  }
</ng-template>

<ng-template #dropdownChild let-child>
  <mat-expansion-panel *abpPermission="child.requiredPolicy">
    <mat-expansion-panel-header>
      <mat-panel-title>
        <mat-icon>
          {{ defaultIconsMap.get(child.iconClass) ?? child.iconClass }}
        </mat-icon>
        {{ child.name | abpLocalization }}
      </mat-panel-title>
    </mat-expansion-panel-header>
    <ng-container
      *ngTemplateOutlet="forTemplate; context: { $implicit: child }"
    />
  </mat-expansion-panel>
</ng-template>
