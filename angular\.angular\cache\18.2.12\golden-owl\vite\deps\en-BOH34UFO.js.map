{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/en.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    v = val.toString().replace(/^[^.]*\\.?/, '').length;\n  if (i === 1 && v === 0) return 1;\n  return 5;\n}\nexport default [\"en\", [[\"a\", \"p\"], [\"AM\", \"PM\"], u], [[\"AM\", \"PM\"], u, u], [[\"S\", \"M\", \"T\", \"W\", \"T\", \"F\", \"S\"], [\"Sun\", \"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"], [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\"], [\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"Th\", \"Fr\", \"Sa\"]], u, [[\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"], [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"]], u, [[\"B\", \"A\"], [\"BC\", \"AD\"], [\"Before Christ\", \"Anno Domini\"]], 0, [6, 0], [\"M/d/yy\", \"MMM d, y\", \"MMMM d, y\", \"EEEE, MMMM d, y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1}, {0}\", u, \"{1} 'at' {0}\", u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"USD\", \"$\", \"US Dollar\", {}, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACnB,QAAM,IAAI,KACR,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,GAC5B,IAAI,IAAI,SAAS,EAAE,QAAQ,aAAa,EAAE,EAAE;AAC9C,MAAI,MAAM,KAAK,MAAM,EAAG,QAAO;AAC/B,SAAO;AACT;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,CAAC,UAAU,UAAU,WAAW,aAAa,YAAY,UAAU,UAAU,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,CAAC,WAAW,YAAY,SAAS,SAAS,OAAO,QAAQ,QAAQ,UAAU,aAAa,WAAW,YAAY,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,iBAAiB,aAAa,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,YAAY,aAAa,iBAAiB,GAAG,CAAC,UAAU,aAAa,eAAe,gBAAgB,GAAG,CAAC,YAAY,GAAG,gBAAgB,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,UAAU,aAAa,KAAK,GAAG,OAAO,KAAK,aAAa,CAAC,GAAG,OAAO,MAAM;", "names": []}