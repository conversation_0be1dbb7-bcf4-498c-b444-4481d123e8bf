## Merge Request Template for Main Feature Branches

### Description

Please provide a detailed description of the changes in this merge request. Include the purpose and any relevant context.

### Checklist

Before submitting your merge request, ensure the following tasks are completed:

- [ ] **Domain Layer:**
    - Finish and verify all changes in the domain layer.
- [ ] **Infrastructure Layer:**
    - Complete and verify the infrastructure layer implementation.
- [ ] **Application Layer:**
    - Finish all changes in the application layer and ensure they are functioning correctly.
- [ ] **Localization:**
    - Complete localization for all relevant languages and verify translations.
- [ ] **Data Seed:**
    - Complete localization for all relevant languages and verify translations.
- [ ] **Required Permissions:**
    - Ensure you have created the required permissions for this feature.

### Testing

- [ ] **Unit Tests:**
    - Ensure all unit tests are written and passing.
- [ ] **Integration Tests:**
    - Verify that integration tests are written and passing.
- [ ] **Manual Testing:**
    - Perform manual testing to ensure the feature works as expected.

### Additional Information

- [ ] **Documentation:**
    - Update any relevant documentation related to this feature.
- [ ] **Dependencies:**
    - List any dependencies that need to be resolved before merging.
- [ ] **Reviewers:**
    - Add reviewers and ensure their feedback is addressed.

### Review and Merge

- [ ] **Code Review:**
    - Ensure the code has been reviewed and approved by the required reviewers.
- [ ] **Merge:**
    - Confirm all checks are passed and merge the feature branch into the main branch.
