import { Component, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { AlertService, LanguagePipe, LOADING, TtwrFormComponent } from '@ttwr-framework/ngx-main-visuals';
import { finalize } from 'rxjs';
import { jobTitles } from '../job-titles.model';
import { JobTitleService } from '@proxy/hr/job-titles';

@Component({
  selector: 'app-job-titles-update',
  standalone: true,

  imports: [TtwrFormComponent, MatDialogTitle, MatDialogContent, LanguagePipe],
  template: `
    <h1 mat-dialog-title>{{ '::UpdateJobTitle' | i18n }}</h1>
    <mat-dialog-content>
      <ttwr-form [config]="config"/>
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class JobTitlesUpdateDialogComponent {
  private jobTitle = inject(JobTitleService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);
  private dialogRef = inject(MatDialogRef);
  private data = inject<{ id: string }>(MAT_DIALOG_DATA);

  protected config = jobTitles().select({
    name: true,
    departmentId: true,
    parentJobTitleId: true,
    baseSalary: true,
  }).form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        this.jobTitle
          .update(this.data.id, body)
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::UpdatedSuccessfully');
            this.dialogRef.close(true);
          })
      },
    },
    viewFunc: () => this.jobTitle.get(this.data.id),
    fields: {
      parentJobTitleId: {
        validators: [],
        search: true,
        label: '::GoldenOwl:ParentJobTitle'
      },
      departmentId: {
        search: true,
        label: '::Department:Name',
      },
      baseSalary: {
        label: '::GoldenOwl:BaseSalary',
      },
    },
  });
}
