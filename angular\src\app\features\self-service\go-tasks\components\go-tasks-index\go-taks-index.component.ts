import { Component, DestroyRef, inject } from '@angular/core';
import {
  AlertService,
  LOADING,
  pagedMap,
  TtwrGridComponent,
} from '@ttwr-framework/ngx-main-visuals';
import { requireAllOperator } from '@shared';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, map, of, Subject } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { LocalizationService } from '@abp/ng.core';
import { GoTaskService, GoTaskState } from '@proxy/go-tasks';
import { tasks } from '../../go-tasks.model';

@Component({
  selector: 'app-go-tasks-index',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `<ttwr-grid [config]="config" />`,
})
export class GoTasksIndexComponent {
  private task = inject(GoTaskService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);
  private localizationService = inject(LocalizationService);
  private refreshSubject = new Subject<void>();

  constructor() {}

  protected config = tasks
    .exclude({
      creatorId: true,
      bindingModelId: true,
    })
    .grid({
      title: '::Tasks',
      refreshSubject: this.refreshSubject,

      dataFunc: (pagination, _, filters) => {
        return this.task
          .getGoTasksAssignedForCurrentUser({
            skipCount: pagination.pageSize * pagination.pageIndex,
            maxResultCount: pagination.pageSize,
          })
          .pipe(
            requireAllOperator(),

            pagedMap((item) => ({
              ...item,
              creationTime: item.creationTime as any,
            }))
          );
      },
      fields: {
        creationTime: {
          columnName: '::IssuedOn',
        },
        state: {
          columnName: '::State',
          displayCell: (state: GoTaskState) => {
            return this.getLocalizedState(state);
          },
        },
      },
      fieldActions: [
        {
          color: 'accent',
          label: 'View',
          matButtonType: 'flat',
          delegateFunc: (obj) =>
            this.router.navigate([obj.id], { relativeTo: this.route }),
        },
        {
          color: 'primary',
          label: 'Approve',
          showFunc: (obj) => obj.state === GoTaskState.Open,
          delegateFunc: (obj) => {
            this.loading.set(true);
            this.task
              .approveGoTaskByGoTaskId(obj.id)
              .pipe(
                finalize(() => this.loading.set(false)),
                takeUntilDestroyed(this.destroyRef)
              )
              .subscribe(() => {
                this.refreshSubject.next();
                this.alert.success('::');
              });
          },
        },
        {
          color: 'warn',
          label: 'Reject',
          showFunc: (obj) => obj.state === GoTaskState.Open,
          confirmation: {
            title: '::Warning',
            message: '::ConfirmRejectTask',
          },
          delegateFunc: (obj) => {
            this.loading.set(true);
            this.task
              .rejectGoTaskByGoTaskId(obj.id)
              .pipe(
                finalize(() => this.loading.set(false)),
                takeUntilDestroyed(this.destroyRef)
              )
              .subscribe(() => {
                this.refreshSubject.next();
                this.alert.success('::SuccessReject');
              });
          },
        },
      ],
    });

  private getLocalizedState(state: GoTaskState): string {
    switch (state) {
      case GoTaskState.Open:
        return this.localizationService.instant('::unfinished');
      case GoTaskState.Closed:
        return this.localizationService.instant('::finished');
      default:
        return 'Unknown';
    }
  }
}
