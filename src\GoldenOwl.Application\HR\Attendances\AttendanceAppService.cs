﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoldenOwl.HR.Attendances.DTO;
using GoldenOwl.HR.Employees;
using GoldenOwl.JobExecutionRecords;
using Quartz;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;

namespace GoldenOwl.HR.Attendances;

public class AttendanceAppService : GoldenOwlAppService, IAttendanceAppService
{
    private readonly IEmployeeRepository _employeeRepository;
    private readonly IRepository<Attendance, Guid> _attendanceRepository;
    
    public AttendanceAppService(IEmployeeRepository employeeRepository,
        IRepository<Attendance, Guid> attendanceRepository)
    {
        _employeeRepository = employeeRepository;
        _attendanceRepository = attendanceRepository;
    }

    public async Task<Guid> CreateAsync(CreateUpdateAttendanceDto createUpdateAttendanceDto)
    {
        if (await _attendanceRepository.AnyAsync(a =>
                a.EmployeeId == createUpdateAttendanceDto.EmployeeId &&
                createUpdateAttendanceDto.CheckIn != null
                    ? a.CheckIn.Value.Date == createUpdateAttendanceDto.CheckIn.Value.Date
                    : a.CheckOut.Value.Date == createUpdateAttendanceDto.CheckOut.Value.Date))
            throw new UserFriendlyException(L[GoldenOwlApplicationErrorCodes.AttendanceAlreadyExists]);

        Employee employee = await _employeeRepository.GetAsync(createUpdateAttendanceDto.EmployeeId);

        Attendance attendance = new Attendance(GuidGenerator.Create(),createUpdateAttendanceDto.EmployeeId, createUpdateAttendanceDto.CheckIn,
            createUpdateAttendanceDto.CheckOut, InOutMode.Manual, InOutMode.Manual);
        await _attendanceRepository.InsertAsync(attendance);

        return attendance.Id;
    }

    public async Task UpdateAsync(Guid id, CreateUpdateAttendanceDto createUpdateAttendanceDto)
    {
        Employee employee = await _employeeRepository.GetAsync(createUpdateAttendanceDto.EmployeeId);
        Attendance attendance = await _attendanceRepository.GetAsync(id);
        attendance.Update(createUpdateAttendanceDto.EmployeeId, createUpdateAttendanceDto.CheckIn,
            InOutMode.Manual, createUpdateAttendanceDto.CheckOut, InOutMode.Manual);
    }

    public async Task<PagedResultDto<AttendanceDto>> GetListAsync(PagedResultRequestDto dto, Guid employeeId,
        DateTime? fromDate, DateTime? toDate)
    {
        IQueryable<Attendance> query = await _attendanceRepository.GetQueryableAsync();

        if (employeeId != Guid.Empty)
        {
            query = query.Where(a => a.EmployeeId == employeeId);
        }

        if (fromDate != null && toDate != null)
        {
            query = query.Where(a => a.CheckIn >= fromDate && a.CheckOut <= toDate);
        }

        int totalCount = await AsyncExecuter.CountAsync(query);
        List<Attendance> attendances = await AsyncExecuter.ToListAsync(query.PageBy(dto));
        List<AttendanceDto> attendanceDtos = ObjectMapper.Map<List<Attendance>, List<AttendanceDto>>(attendances);

        return new PagedResultDto<AttendanceDto>(totalCount, attendanceDtos);
    }

    public async Task<AttendanceDto> GetAsync(Guid id)
    {
        Attendance attendance = await _attendanceRepository.GetAsync(id);
        return ObjectMapper.Map<Attendance, AttendanceDto>(attendance);
    }
}