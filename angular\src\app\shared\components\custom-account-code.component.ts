import { Component, input, effect, computed } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatFormField, MatInput } from '@angular/material/input';
import { MatLabel } from '@angular/material/form-field';
import {
  FormFieldWithControlAndName,
  ICustomInputComponent,
  TextFieldType,
  LanguagePipe,
} from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-custom-account-code',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormField,
    MatInput,
    MatLabel,
    LanguagePipe,
  ],
  template: `
    <div
      [ngStyle]="{ 'flex-direction': flexDirection }"
      class="account-code-container"
    >
      <mat-form-field appearance="outline" class="account-code-main">
        <mat-label>{{ field().label ?? field().name | i18n }}</mat-label>
        <input matInput [formControl]="field().control" />
      </mat-form-field>

      <mat-form-field appearance="outline" class="account-code-prefix">
        <input matInput [formControl]="prefixControl" readonly style="text-align: center;" />
      </mat-form-field>
    </div>
  `,
  styles: [`
    .account-code-container {
      display: flex;
      gap: 2px;
      align-items: flex-start;
      width: 100%;
    }
    .account-code-main {
      width: 80%;
    }
    .account-code-prefix {
      width: 20%;
    }
  `],
})
export class CustomAccountCodeComponent implements ICustomInputComponent<TextFieldType> {
  field = input.required<FormFieldWithControlAndName<TextFieldType>>();
  prefix = input<any>();

  private prefixValue = computed(() => this.prefix());
  prefixControl = new FormControl({ value: '', disabled: false });

  flexDirection = document.dir === 'rtl' ? 'row' : 'row-reverse';

  constructor() {
    effect(() => {
      let val = this.prefixValue() ?? '';
      if (typeof val === 'function') val = val();

      this.prefixControl.setValue(val);

      const fullCode = this.field().control.value ?? '';
      if (fullCode.startsWith(val)) {
        const suffix = fullCode.slice(val.length);
        this.field().control.setValue(suffix, { emitEvent: false });
      }
    });
  }
}
