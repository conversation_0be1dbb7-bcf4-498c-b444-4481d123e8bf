<ttwr-grid class="empty-grid" [config]="config">
  <div class="container">
    <mat-form-field>
      <mat-label>
        {{ 'NotificationStatus' | i18n }}
      </mat-label>
      <mat-select [formControl]="notificationStatusFormControl">
        <mat-option value="all">
          {{ 'All' | i18n }}
        </mat-option>
        <mat-option value="seen">
          {{ 'Seen' | i18n }}
        </mat-option>
        <mat-option value="not-seen">
          {{ 'NotSeen' | i18n }}
        </mat-option>
      </mat-select>
    </mat-form-field>
    <div class="cards-container">
      @for (notification of currentNotifications(); track notification.id) {
        <mat-card
          matRipple
          [matRippleDisabled]="notification.isSeen"
          (click)="!notification.isSeen && markAsSeen(notification.id!)"
          [class.not-seen]="!notification.isSeen"
          appearance="outlined"
        >
          <mat-card-header>
            <div mat-card-avatar>
              <mat-icon>notifications</mat-icon>
            </div>
            <mat-card-title>
              {{ notification.title }}
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            {{ notification.message }}
          </mat-card-content>
          <mat-card-actions align="end">
            {{ 'Since' | i18n }}
            {{ notification.creationTime }}
          </mat-card-actions>
        </mat-card>
      } @empty {
        <p class="no-data">{{ 'no-data' | i18n }}</p>
      }
    </div>
  </div>
</ttwr-grid>
