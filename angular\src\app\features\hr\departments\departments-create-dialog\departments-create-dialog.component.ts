import { Component, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import {
  AlertService,
  LanguagePipe,
  LOADING,
  requiredValidator,
  TtwrFormComponent
} from '@ttwr-framework/ngx-main-visuals';
import { finalize } from 'rxjs';
import { departments } from '../departments.model';
import { DepartmentService } from '@proxy/hr/departments';

@Component({
  selector: 'app-departments-create',
  standalone: true,
  imports: [TtwrFormComponent, MatDialogTitle, MatDialogContent, LanguagePipe],
  template: `
    <h1 mat-dialog-title>{{ '::CreateDepartment' | i18n }}</h1>
    <mat-dialog-content>
      <ttwr-form [config]="config"/>
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class DepartmentsCreateDialogComponent {
  private departments = inject(DepartmentService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);
  private dialogRef = inject(MatDialogRef);

  protected config = departments().exclude({
    parentDepartmentName: true,
  }).form({
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        this.departments
          .postCreateDepartmentByDto(body)
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::CreatedSuccessfully');
            this.dialogRef.close(true);
          })
      },
    },
    fields: {
      name: {
        validators: [requiredValidator],
      },
      parentDepartmentId: {
        label: '::ParentDepartment',
        search: true,
      }
    },
  });
}
