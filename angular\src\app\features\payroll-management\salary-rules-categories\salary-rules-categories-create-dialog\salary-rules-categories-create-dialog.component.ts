import { Component, DestroyRef, inject } from '@angular/core';
import { AlertService, LanguagePipe, LOADING, TtwrFormComponent } from '@ttwr-framework/ngx-main-visuals';
import { MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { finalize } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SalaryRuleCategoryService } from '@proxy/payroll/salary-rule-categories';
import { salaryRulesCategories } from '../salary-rules-categories.model';

@Component({
  selector: 'app-salary-rules-categories-create-dialog',
  standalone: true,
  imports: [
    LanguagePipe,
    MatDialogContent,
    MatDialogTitle,
    TtwrFormComponent
  ],
  template: `
    <h2 mat-dialog-title>{{ '::CreateSalaryRuleCategory' | i18n }}</h2>
    <mat-dialog-content>
      <ttwr-form [config]="config"/>
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class SalaryRulesCategoriesCreateDialogComponent {
  private salaryRuleCategory = inject(SalaryRuleCategoryService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);
  private dialogRef = inject(MatDialogRef);

  protected config = salaryRulesCategories.form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        this.salaryRuleCategory.create(body)
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::CreatedSuccessfully');
            this.dialogRef.close(true);
          })
      },
    },
    fields: {
      code: {
        label: '::GoldenOwl:Code',
      }
    },
  })
}
