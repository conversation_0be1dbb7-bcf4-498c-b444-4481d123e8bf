import { Component, DestroyRef, inject } from '@angular/core';
import { AlertService, LanguagePipe, LOADING, TtwrFormComponent } from '@ttwr-framework/ngx-main-visuals';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { finalize, of } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { terminals } from '../terminals.model';
import { TerminalService } from '@proxy/hr/terminals';
import { TerminalDto } from '@proxy/hr/terminals/dto';

@Component({
  selector: 'app-terminals-update-dialog',
  standalone: true,
  imports: [
    LanguagePipe,
    MatDialogContent,
    MatDialogTitle,
    TtwrFormComponent
  ],
  template: `
    <h2 mat-dialog-title>{{ '::UpdateTerminal' | i18n }}</h2>
    <mat-dialog-content>
      <ttwr-form [config]="config"/>
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-gap: 1rem;
      --ttwr-form-grid-template-columns: 1fr 1fr;
    }
  `,
})
export class TerminalsUpdateDialogComponent {
  private terminal = inject(TerminalService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);
  private data = inject<TerminalDto>(MAT_DIALOG_DATA);
  private dialogRef = inject(MatDialogRef);

  protected config = terminals.form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        this.terminal.update(this.data.id!, body)
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::UpdatedSuccessfully');
            this.dialogRef.close(true);
          })
      },
    },
    actions: [
      {
        label: '::TestTerminal',
        causeValidate: true,
        delegateFunc: body => {
          this.loading.set(true);

          this.terminal.testConnection(body).pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          ).subscribe(({ isSuccess, retrievedTime }) => {
            if (isSuccess) {
              this.alert.success('::ConnectionSuccess', {
                translateArguments: [String(retrievedTime)],
              });
            } else {
              this.alert.error('::UnexpectedError')
            }
          });
        },
      }
    ],
    viewFunc: () => of(this.data),
    fields: {
      ipAddress: {
        label: '::Terminal:IpAddress',
      },
      comKey: {
        label: '::Terminal:ComKey',
      },
      port: {
        label: '::Terminal:Port',
      },
    },
  })
}
