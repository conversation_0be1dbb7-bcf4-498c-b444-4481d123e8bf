// This file was generated by running 'ng generate @angular/material:theme-color'.
// Proceed with caution if making changes to this file.

@use "sass:map";
@use "@angular/material" as mat;

// Note: Color palettes are generated from primary: #6750a4
$_palettes: (
  primary: (
    0: #000000,
    10: #22005d,
    20: #381e72,
    25: #432b7e,
    30: #4f378a,
    35: #5b4397,
    40: #6750a4,
    50: #8069bf,
    60: #9a83db,
    70: #b69df7,
    80: #cfbcff,
    90: #e9ddff,
    95: #f6eeff,
    98: #fdf7ff,
    99: #fffbff,
    100: #ffffff,
  ),
  secondary: (
    0: #000000,
    10: #1f1635,
    20: #342b4b,
    25: #3f3657,
    30: #4b4263,
    35: #574d6f,
    40: #63597c,
    50: #7c7296,
    60: #968bb1,
    70: #b1a5cc,
    80: #cdc0e9,
    90: #e9ddff,
    95: #f6eeff,
    98: #fdf7ff,
    99: #fffbff,
    100: #ffffff,
  ),
  tertiary: (
    0: #000000,
    10: #3c002b,
    20: #5a1243,
    25: #681e4f,
    30: #752a5b,
    35: #843667,
    40: #924274,
    50: #af5a8e,
    60: #cd73a8,
    70: #eb8dc4,
    80: #ffaedb,
    90: #ffd8ea,
    95: #ffecf3,
    98: #fff8f8,
    99: #fffbff,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    10: #1d1b20,
    20: #322f35,
    25: #3d3a41,
    30: #48464c,
    35: #545158,
    40: #605d64,
    50: #79767d,
    60: #938f96,
    70: #aea9b1,
    80: #cac5cc,
    90: #e6e0e9,
    95: #f5eff7,
    98: #fdf7ff,
    99: #fffbff,
    100: #ffffff,
    4: #0f0d13,
    6: #141218,
    12: #211f24,
    17: #2b292f,
    22: #36343a,
    24: #3b383e,
    87: #ded8e0,
    92: #ece6ee,
    94: #f2ecf4,
    96: #f8f2fa,
  ),
  neutral-variant: (
    0: #000000,
    10: #1d1a24,
    20: #322f3a,
    25: #3d3a45,
    30: #494551,
    35: #55505c,
    40: #615c69,
    50: #7a7582,
    60: #948e9c,
    70: #afa9b7,
    80: #cbc4d2,
    90: #e7e0ee,
    95: #f5eefd,
    98: #fdf7ff,
    99: #fffbff,
    100: #ffffff,
  ),
  error: (
    0: #000000,
    10: #410002,
    20: #690005,
    25: #7e0007,
    30: #93000a,
    35: #a80710,
    40: #ba1a1a,
    50: #de3730,
    60: #ff5449,
    70: #ff897d,
    80: #ffb4ab,
    90: #ffdad6,
    95: #ffedea,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
);

$_rest: (
  secondary: map.get($_palettes, secondary),
  neutral: map.get($_palettes, neutral),
  neutral-variant: map.get($_palettes, neutral-variant),
  error: map.get($_palettes, error),
);

$primary-palette: map.merge(map.get($_palettes, primary), $_rest);
$tertiary-palette: map.merge(map.get($_palettes, tertiary), $_rest);
