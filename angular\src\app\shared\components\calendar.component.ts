import { Component, computed, inject, input } from '@angular/core';
import { HolidayDto } from '@proxy/hr/holidays/dtos';
import { CalendarOptions } from '@fullcalendar/core';
import { FullCalendarModule } from '@fullcalendar/angular';
import dayGridPlugin from '@fullcalendar/daygrid';
import { LanguageService } from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-calendar',
  standalone: true,
  imports: [FullCalendarModule],
  template: `<full-calendar [options]="options()" />`,
  styles: `
    :host {
      display: block;
      margin-top: 1rem;
    }
  `
})
export class CalendarComponent {
  private language = inject(LanguageService);

  public holidays = input<HolidayDto[]>([]);

  protected options = computed<CalendarOptions>(() => ({
    initialView: 'dayGridMonth',
    plugins: [dayGridPlugin],
    events: this.holidays().map(holiday => ({
      title: holiday.name,
      start: holiday.from,
      end: holiday.to,
    })),
    locale: this.language.selectedLanguage(),
  }));
}
