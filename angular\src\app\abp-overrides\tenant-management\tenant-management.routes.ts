import { Routes } from '@angular/router';
import { authGuard, permissionGuard } from '@abp/ng.core';
import { TenantManagementComponent } from './tenant-management.component';

export const routes: Routes = [
  {
    path: 'tenants',
    canActivate: [authGuard, permissionGuard],
    component: TenantManagementComponent,
    title: 'AbpTenantManagement::Tenants',
    data: {
      requiredPolicy: 'AbpTenantManagement.Tenants',
    },
  }
];
