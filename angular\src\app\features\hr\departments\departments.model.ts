import { model, fields, arrayMap } from '@ttwr-framework/ngx-main-visuals';
import { inject } from '@angular/core';
import { DepartmentService } from '@proxy/hr/departments';
import { requireAllOperator } from '@shared';
import { map } from 'rxjs';

export const departments = () => {
  const departments = inject(DepartmentService);

  return model({
    id: fields.text(),
    name: fields.text(),
    parentDepartmentName: fields.text(),
    parentDepartmentId: fields.selectFetch('single', () => departments.getGetAllDepartmentsByInput({
      maxResultCount: 999.
    }).pipe(
      requireAllOperator(),
      map(res => res.items),
      arrayMap(item => ({
        label: item.name,
        value: item.id,
      })),
    )),
  });
}

