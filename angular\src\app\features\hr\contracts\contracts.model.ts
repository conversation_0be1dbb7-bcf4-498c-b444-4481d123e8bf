import { arrayMap, fields, model, takeOptions } from '@ttwr-framework/ngx-main-visuals';
import { inject } from '@angular/core';
import { requireAllOperator } from '@shared';
import { map } from 'rxjs';
import { EmployeeService } from '@proxy/hr/employees';
import { ContractState, ContractType, WorkEntrySource } from '@proxy/hr/contracts';
import { WorkingScheduleService } from '@proxy/hr/working-schedules';
import { JobTitleService } from '@proxy/hr/job-titles';
import { SalaryStructureService } from '@proxy/payroll/salary-structures';

export const contracts = () => {
  const employees = inject(EmployeeService);
  const workingSchedules = inject(WorkingScheduleService);
  const jobTitle = inject(JobTitleService);
  const salaryStructure = inject(SalaryStructureService)

  return model({
    id: fields.text(),
    name: fields.text(),
    type: fields.select('single', takeOptions(ContractType)),
    // has custom input implementation
    dateRange: fields.text(),
    employeeId: fields.selectFetch('single', () => employees.getList(
      { maxResultCount: 999 }, '00000000-0000-0000-0000-000000000000', undefined as any,
    ).pipe(
      requireAllOperator(),
      map(res => res.items),
      arrayMap(employee => ({
        label: `${employee.name} ${employee.surname}`,
        value: employee.id,
      }))
    )),
    jobTitleId: fields.selectFetch('single', () => jobTitle.getList({
        maxResultCount: 999,
      }, '00000000-0000-0000-0000-000000000000').pipe(
        requireAllOperator(),
        map(res => res.items),
        arrayMap(jobTitle => ({
          label: jobTitle.name,
          value: jobTitle.id,
        }))
      )
    ),
    state: fields.select('single', takeOptions(ContractState)),
    workEntrySource: fields.select('single', takeOptions(WorkEntrySource)),
    salaryStructureId: fields.selectFetch('single', () => salaryStructure.getList({
      maxResultCount: 999,
    }).pipe(
      requireAllOperator(),
      map(res => res.items),
      arrayMap(structure => ({
        label: structure.name,
        value: structure.id,
      }))
    )),
    contractWorkingSchedules: fields.list({
      id: fields.text(),
      workingScheduleId: fields.selectFetch('single', () => workingSchedules.getList({
        maxResultCount: 999
      }).pipe(
        requireAllOperator(),
        map(res => res.items),
        arrayMap(workingSchedule => ({
          label: workingSchedule.name,
          value: workingSchedule.id,
        }))
      )),
      // has custom input implementation
      dateRange: fields.text(),
    }),
  })
}

