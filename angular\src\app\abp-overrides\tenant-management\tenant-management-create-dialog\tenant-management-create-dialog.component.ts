import { Component, DestroyRef, inject } from '@angular/core';
import { MatD<PERSON>ogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { LocalizationModule, LocalizationService } from '@abp/ng.core';
import {
  AlertService,
  fields,
  LOADING,
  model,
  requiredValidator,
  TtwrFormComponent
} from '@ttwr-framework/ngx-main-visuals';
import { Validators } from '@angular/forms';
import { TenantService } from '@abp/ng.tenant-management/proxy';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-tenant-management-create-dialog',
  standalone: true,
  imports: [
    MatDialogTitle,
    LocalizationModule,
    MatDialogContent,
    TtwrFormComponent
  ],
  templateUrl: './tenant-management-create-dialog.component.html',
  styleUrl: './tenant-management-create-dialog.component.scss'
})
export class TenantManagementCreateDialogComponent {
  private localization = inject(LocalizationService);
  private destroyRef = inject(DestroyRef);
  private tenants = inject(TenantService);
  private ref = inject(MatDialogRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);

  protected config = model({
    name: fields.text(),
    adminEmailAddress: fields.text(),
    adminPassword: fields.text(),
  }).form({
    submitAction: {
      onSubmit: (value) => {
        this.loading.set(true);

        this.tenants.create(value)
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe({
            next: () => {
              this.loading.set(false);
              this.ref.close(true);
              this.alert.success(
                this.localization.instant('AbpSettingManagement::SuccessfullySaved')
              );
            },
            error: err => {
              this.loading.set(false);
              const message = this.localization.instant(err.error?.error?.message || 'AbpAccount::DefaultErrorMessage');
              this.alert.error(message);
            }
          })
      },
      submitLabel: 'Save',
      submitIcon: 'check',
    },
    initialRequired: true,
    fields: {
      name: {
        label: this.localization.instant('AbpTenantManagement::DisplayName:TenantName')
      },
      adminEmailAddress: {
        validators: [
          requiredValidator,
          {
            name: 'email',
            validator: Validators.email,
            message: this.localization.instant('AbpValidation::ThisFieldIsNotAValidEmailAddress.'),
          }
        ],
        label: this.localization.instant('AbpTenantManagement::DisplayName:AdminEmailAddress')
      },
      adminPassword: {
        textInputType: 'password',
        label: this.localization.instant('AbpTenantManagement::DisplayName:AdminPassword')
      },
    },
    actions: [
      {
        label: 'Cancel',
        delegateFunc: () => {
          this.ref.close();
        }
      }
    ]
  })
}
