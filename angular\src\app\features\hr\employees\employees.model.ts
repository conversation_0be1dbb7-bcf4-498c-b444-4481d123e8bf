import { arrayMap, fields, model, takeOptions } from '@ttwr-framework/ngx-main-visuals';
import { inject } from '@angular/core';
import { requireAllOperator, takeOptionsWithUndefinedOption } from '@shared';
import { map } from 'rxjs';
import { EmployeeService, EmployeeStatus, Gender, MaritalStatus, MilitaryServiceStatus } from '@proxy/hr/employees';
import { IdentityUserService } from '@abp/ng.identity/proxy';
import { JobTitleService } from '@proxy/hr/job-titles';

export const employees = () => {
  const users = inject(IdentityUserService);
  const employee = inject(EmployeeService);
  const jobTitle = inject(JobTitleService);

  return model({
    id: fields.text(),
    name: fields.text(),
    surname: fields.text(),
    fatherName: fields.text(),
    motherName: fields.text(),
    nationalNumber: fields.text(),
    gender: fields.select('single', takeOptionsWithUndefinedOption(Gender)),
    employeeStatus: fields.select('single', takeOptions(EmployeeStatus)),
    dateOfBirth: fields.date(),
    maritalStatus: fields.select('single', takeOptionsWithUndefinedOption(MaritalStatus)),
    militaryServiceStatus: fields.select('single', takeOptionsWithUndefinedOption(MilitaryServiceStatus)),
    managerId: fields.selectFetch('single', () => employee.getManagers('00000000-0000-0000-0000-000000000000').pipe(
      arrayMap(employee => ({
        label: `${employee.name} ${employee.surname}`,
        value: employee.id!,
      }))
    )),
    userId: fields.selectFetch('single', () => users.getList({
      maxResultCount: 999,
    }).pipe(
      requireAllOperator(),
      map(res => res.items),
      arrayMap(user => ({
        label: `${user.name} ${user.surname}`,
        value: user.id,
      })),
    )),
    jobTitleName: fields.text(),
    jobTitleId: fields.selectFetch('single', () => jobTitle.getList({
      maxResultCount: 999,
    }, '00000000-0000-0000-0000-000000000000').pipe(
      requireAllOperator(),
      map(res => res.items),
      arrayMap(user => ({
        label: user.name,
        value: user.id,
      })),
    ))
  })
}

export const contactInformationModel = model({
  email: fields.text(),
  workEmail: fields.text(),
  phoneNumber: fields.text(),
  workPhoneNumber: fields.text(),
})

export const emergencyContactInformationModel = model({
  emergencyPhone: fields.text(),
  contactName: fields.text(),
  relationShip: fields.text(),
})
