import { Component, inject, input } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { NonNullableFormBuilder, ReactiveFormsModule } from '@angular/forms';
import {
  Mat<PERSON>rror,
  MatFormField,
  MatLabel,
  MatOption,
  MatSelect,
} from '@angular/material/select';
import {
  FormFieldWithControlAndName,
  ICustomInputComponent,
  LanguagePipe,
  Option,
  TextFieldType,
} from '@ttwr-framework/ngx-main-visuals';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import {
  Observable,
  debounceTime,
  distinctUntilChanged,
  startWith,
  switchMap,
} from 'rxjs';

@Component({
  selector: 'app-custom-select-search',
  standalone: true,
  imports: [
    MatSelect,
    MatOption,
    MatFormField,
    MatLabel,
    MatError,
    NgxMatSelectSearchModule,
    ReactiveFormsModule,
    LanguagePipe,
  ],
  template: `
    <mat-form-field>
      <mat-label>{{ field().label ?? field().name | i18n }}</mat-label>
      <mat-select [formControl]="field().control">
        <mat-option>
          <ngx-mat-select-search
            [formControl]="filterControl"
            [placeholderLabel]="field().placeholder ?? '' | i18n"
          />
        </mat-option>
        @for (option of options(); track option.value) {
          <mat-option [value]="option.value">
            {{ option.label }}
          </mat-option>
        }
      </mat-select>
      <mat-error>
        @for (validator of field().validators ?? []; track validator.name) {
          <span [hidden]="!field().control.hasError(validator.name)">
            {{ validator.message | i18n }}
          </span>
        }
      </mat-error>
    </mat-form-field>
  `,
  styles: [
    `
      mat-form-field {
        width: 100%;
      }
    `,
  ],
})
export class CustomSelectSearchComponent implements ICustomInputComponent<TextFieldType> {
  private fb = inject(NonNullableFormBuilder);

  field = input.required<FormFieldWithControlAndName<TextFieldType>>();
  dataFunc = input.required<(searchTerm: string) => Observable<Option<any>[]>>();

  filterControl = this.fb.control('');

  options = toSignal(
    this.filterControl.valueChanges.pipe(
      startWith(''),
      debounceTime(300),
      distinctUntilChanged(),
      switchMap((searchTerm) => this.dataFunc()(searchTerm))
    ),
    {
      initialValue: [],
    }
  );

}
