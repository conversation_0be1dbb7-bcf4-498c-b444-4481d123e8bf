import type { ExtraFieldDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { EntityType } from '../extra-field-definitions/entity-type.enum';

@Injectable({
  providedIn: 'root',
})
export class ExtraFieldValueService {
  apiName = 'Default';
  

  getList = (entityId: string, entityType: EntityType, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ExtraFieldDto[]>({
      method: 'GET',
      url: '/api/app/extra-field-value',
      params: { entityId, entityType },
    },
    { apiName: this.apiName,...config });
  

  set = (extraFieldDtos: ExtraFieldDto[], bindEntityId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/extra-field-value/set/${bindEntityId}`,
      body: extraFieldDtos,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
