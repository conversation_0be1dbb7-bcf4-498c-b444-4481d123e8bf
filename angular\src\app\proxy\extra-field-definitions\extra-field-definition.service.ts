import type { ExtraFieldDefinitionDto } from './dto/models';
import type { EntityType } from './entity-type.enum';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ExtraFieldDefinitionService {
  apiName = 'Default';
  

  create = (definitionDto: ExtraFieldDefinitionDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/extra-field-definition',
      body: definitionDto,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/extra-field-definition/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, entityType?: EntityType, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<ExtraFieldDefinitionDto>>({
      method: 'GET',
      url: '/api/app/extra-field-definition',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount, entityType },
    },
    { apiName: this.apiName,...config });
  

  getListByEntityType = (entityType: EntityType, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ExtraFieldDefinitionDto[]>({
      method: 'GET',
      url: '/api/app/extra-field-definition/by-entity-type',
      params: { entityType },
    },
    { apiName: this.apiName,...config });
  

  hasAssociatedValues = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, boolean>({
      method: 'POST',
      url: `/api/app/extra-field-definition/${id}/has-associated-values`,
    },
    { apiName: this.apiName,...config });
  

  isKeyUnique = (key: string, id?: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, boolean>({
      method: 'POST',
      url: `/api/app/extra-field-definition/${id}/is-key-unique`,
      params: { key },
    },
    { apiName: this.apiName,...config });
  

  update = (definitionDto: ExtraFieldDefinitionDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: '/api/app/extra-field-definition',
      body: definitionDto,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
