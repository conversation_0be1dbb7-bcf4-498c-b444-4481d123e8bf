import { Routes } from '@angular/router';
import { UsersComponent } from './user/users.component';
import { RolesComponent } from './roles/roles.component';

export const routes: Routes = [
  {
    path: 'users',
    component: UsersComponent,
    title: 'AbpIdentity::Users',
    data: {
      requiredPolicy: 'AbpIdentity.Users',
    },
  },
  {
    path: 'roles',
    component: RolesComponent,
    title: 'AbpIdentity::Roles',
    data: {
      requiredPolicy: 'AbpIdentity.Roles',
    },
  }
];
