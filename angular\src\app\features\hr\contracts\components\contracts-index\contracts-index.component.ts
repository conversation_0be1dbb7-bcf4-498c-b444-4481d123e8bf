import { Component, DestroyRef, inject, signal } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { fields, LOADING, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { contracts } from '../../contracts.model';
import { requireAllOperator } from '@shared';
import { ContractService, ContractState } from '@proxy/hr/contracts';
import { MatDialog } from '@angular/material/dialog';
import { WorkingSchedulesDialogComponent } from './working-schedules-dialog/working-schedules-dialog.component';
import { Subject } from 'rxjs';
import { ContractsCancelDialogComponent } from './contracts-cancel-dialog/contracts-cancel-dialog.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-contracts-index',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `<ttwr-grid class="actions-end" [config]="config"/>`,
})
export class ContractsIndexComponent {
  private contracts = inject(ContractService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private dialog = inject(MatDialog);
  private destroyRef = inject(DestroyRef);
  private loading = inject(LOADING);

  private refreshSubject = new Subject<void>();

  protected config = contracts().exclude({
    employeeId: true,
    dateRange: true,
    contractWorkingSchedules: true,
    jobTitleId: true,
    salaryStructureId: true,
  }).extend({
    jobTitleName: fields.text(),
    start: fields.date(),
    end: fields.date(),
    contractWorkingSchedules: fields.list({
      id: fields.text(),
      workingScheduleId: fields.text(),
      from: fields.date(),
      to: fields.date(),
    })
  }).grid({
    title: '::GoldenOwl:Contract',
    refreshSubject: this.refreshSubject,
    showActionsAsMenu: true,
    dataFunc: (pagination) => this.contracts.getList({
      skipCount: pagination.pageSize * pagination.pageIndex,
      maxResultCount: pagination.pageSize,
    }).pipe(
      requireAllOperator(),
    ),
    fields: {
      type: {
        columnName: '::Type',
      },
      state: {
        columnName: '::State',
      },
      jobTitleName: {
        columnName: '::GoldenOwl:JobTitle'
      },
      start: {
        columnName: '::Contract:Start',
      },
      end: {
        columnName: '::Contract:End',
      },
      workEntrySource: {
        columnName: '::Contract:WorkEntrySource',
      },
      contractWorkingSchedules: {
        hiddenSignal: signal(true),
      },
    },
    actions: [
      {
        tooltip: 'Add',
        matIcon: 'add',
        delegateFunc: () =>
          this.router.navigate(['create'], { relativeTo: this.route.parent }),
      },
    ],
    fieldActions: [
      {
        label: 'Edit',
        color: 'primary',
        delegateFunc: (obj) =>
          this.router.navigate(['update', obj.id], {
            relativeTo: this.route.parent,
          }),
      },
      {
        label: '::WorkingSchedules',
        color: 'accent',
        showFunc: obj => !!obj.contractWorkingSchedules && !!obj.contractWorkingSchedules.length,
        delegateFunc: (obj) => {
          this.dialog.open(WorkingSchedulesDialogComponent, {
            data: obj.contractWorkingSchedules,
            width: '700px'
          });
        },
      },
      {
        label: '::Cancel',
        color: 'warn',
        showFunc: obj => obj.state === ContractState.Draft || obj.state === ContractState.Active,
        delegateFunc: (obj) => {
          const ref = this.dialog.open(ContractsCancelDialogComponent, {
            data: obj.id,
            width: '500px',
          })

          ref.afterClosed().pipe(
            takeUntilDestroyed(this.destroyRef)
          ).subscribe(result => {
            if (result) {
              this.refreshSubject.next();
            }
          })
        }
      },
      {
        label: '::Activate',
        showFunc: obj => obj.state === ContractState.Draft,
        delegateFunc: obj => {
          this.loading.set(true);

          this.contracts.putActivateContract(obj.id).pipe(
            takeUntilDestroyed(this.destroyRef)
          ).subscribe(() => this.refreshSubject.next())
        },
      },
      {
        label: '::Expire',
        showFunc: obj => obj.state === ContractState.Draft || obj.state === ContractState.Active,
        delegateFunc: obj => {
          this.loading.set(true);

          this.contracts.putExpireContract(obj.id).pipe(
            takeUntilDestroyed(this.destroyRef)
          ).subscribe(() => this.refreshSubject.next())
        },
      }
    ],
  });
}
