import type { AddAccountingAuditingDto, CloseFinancialPeriodDto, CreateFinancialPeriodDto, FinancialPeriodDto, UpdateFinancialPeriodDto } from './dto/models';
import type { FinancialPeriodState } from './financial-period-state.enum';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class FinancialPeriodService {
  apiName = 'Default';
  

  create = (input: CreateFinancialPeriodDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/financial-period',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, financialPeriodState: FinancialPeriodState, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<FinancialPeriodDto>>({
      method: 'GET',
      url: '/api/app/financial-period',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount, financialPeriodState },
    },
    { apiName: this.apiName,...config });
  

  putAddAuditing = (input: AddAccountingAuditingDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: '/api/app/financial-period/add-auditing',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  putClose = (input: CloseFinancialPeriodDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: '/api/app/financial-period/close',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  putUpdate = (input: UpdateFinancialPeriodDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: '/api/app/financial-period/update',
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
