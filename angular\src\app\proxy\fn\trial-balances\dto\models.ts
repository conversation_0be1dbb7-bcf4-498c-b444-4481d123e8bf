
export interface GetTrialBalanceRequestDto {
  accountId?: string;
  fromDate?: string;
  toDate?: string;
  currencyCode?: string;
}

export interface TrialBalanceDto {
  currencyCode?: string;
  fromDate?: string;
  toDate?: string;
  totalDebitBalance: number;
  totalCreditBalance: number;
  balanceItems: TrialBalanceItemDto[];
}

export interface TrialBalanceItemDto {
  accountId?: string;
  accountCode?: string;
  accountName?: string;
  currencyCode?: string;
  totalDebit: number;
  totalCredit: number;
  balance: number;
}
