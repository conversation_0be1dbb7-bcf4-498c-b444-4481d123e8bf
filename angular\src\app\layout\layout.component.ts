import { Component, inject, signal } from '@angular/core';
import {
  ABP,
  AuthService,
  LocalizationModule,
  NAVIGATE_TO_MANAGE_PROFILE,
  PermissionDirective,
  RoutesService,
  SessionStateService,
  TreeNode
} from '@abp/ng.core';
import { AsyncPipe, NgTemplateOutlet } from '@angular/common';
import { MatSidenavModule } from '@angular/material/sidenav';
import { RouterLink, RouterOutlet } from '@angular/router';
import { MatIcon } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatRipple } from '@angular/material/core';
import { MatButton, MatIconButton } from '@angular/material/button';
import { MatToolbar } from '@angular/material/toolbar';
import { MatMenuModule } from '@angular/material/menu';
import { BreadcrumbComponent } from './breadcrumb/breadcrumb.component';
import { environment } from '../../environments/environment';
import { toSignal } from '@angular/core/rxjs-interop';
import { catchError, map, of, shareReplay, tap } from 'rxjs';
import { BreakpointObserver } from '@angular/cdk/layout';
import { ProfileService } from '@abp/ng.account.core/proxy';
import { NotificationButtonComponent } from './notification-button/notification-button.component';

@Component({
  selector: 'app-layout',
  standalone: true,
  imports: [
    MatSidenavModule,
    MatListModule,
    MatExpansionModule,
    MatIcon,
    MatRipple,
    MatIconButton,
    MatToolbar,
    MatMenuModule,
    AsyncPipe,
    RouterOutlet,
    RouterLink,
    LocalizationModule,
    PermissionDirective,
    NgTemplateOutlet,
    BreadcrumbComponent,
    MatButton,
    NotificationButtonComponent,
  ],
  templateUrl: './layout.component.html',
  styleUrl: './layout.component.scss',
})
export class LayoutComponent {
  private routes = inject(RoutesService);
  private session = inject(SessionStateService);
  private auth = inject(AuthService);
  private observer = inject(BreakpointObserver);
  private profile = inject(ProfileService);
  protected navigateToManageProfile = inject(NAVIGATE_TO_MANAGE_PROFILE);

  protected username = signal('');

  protected isAuth = toSignal(
    this.profile.get().pipe(
      tap(res => res && this.username.set(res.userName ?? '')),
      map(res => !!res),
      catchError(() => {
        this.auth.navigateToLogin();
        return of(false);
      }),
    )
  )

  protected appName = environment.application.name;
  protected routes$ = this.routes.visible$;
  protected defaultIconsMap = new Map([
    ['fa fa-wrench', 'build'],
    ['fa fa-users', 'people'],
    ['fa fa-cog', 'settings'],
    ['fa fa-id-card-o', 'contact_mail']
  ])

  protected isSmall = toSignal(
    this.observer
      .observe('(max-width: 700px)')
      .pipe(
        map((result) => result.matches),
        shareReplay()
      )
  )

  isDropdown(node: TreeNode<ABP.Route>) {
    return !node?.isLeaf || this.routes.hasChildren(node.name);
  }

  changeLang(language: 'ar' | 'en') {
    this.session.setLanguage(language);
  }

  logout() {
    this.auth.logout().subscribe(() => {
      this.auth.navigateToLogin();
    });
  }

  navigateToLogin() {
    this.auth.navigateToLogin();
  }
}
