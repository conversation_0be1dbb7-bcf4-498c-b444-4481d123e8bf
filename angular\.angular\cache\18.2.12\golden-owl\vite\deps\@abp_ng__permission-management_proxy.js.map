{"version": 3, "sources": ["../../../../../../node_modules/@abp/ng.permission-management/fesm2022/abp-ng.permission-management-proxy.mjs"], "sourcesContent": ["import * as i1 from '@abp/ng.core';\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\nclass PermissionsService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'AbpPermissionManagement';\n    this.get = (providerName, providerKey) => this.restService.request({\n      method: 'GET',\n      url: '/api/permission-management/permissions',\n      params: {\n        providerName,\n        providerKey\n      }\n    }, {\n      apiName: this.apiName\n    });\n    this.update = (providerName, providerKey, input) => this.restService.request({\n      method: 'PUT',\n      url: '/api/permission-management/permissions',\n      params: {\n        providerName,\n        providerKey\n      },\n      body: input\n    }, {\n      apiName: this.apiName\n    });\n  }\n  static {\n    this.ɵfac = function PermissionsService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PermissionsService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: PermissionsService,\n      factory: PermissionsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PermissionsService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.RestService\n  }], null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { PermissionsService };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAGA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,MAAM,CAAC,cAAc,gBAAgB,KAAK,YAAY,QAAQ;AAAA,MACjE,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,IACF,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,SAAS,CAAC,cAAc,aAAa,UAAU,KAAK,YAAY,QAAQ;AAAA,MAC3E,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAM;AAAA,IACR,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAuB,SAAY,WAAW,CAAC;AAAA,IAClF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,oBAAmB;AAAA,MAC5B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;", "names": []}