<mat-card>
  <mat-card-content>
    <mat-nav-list>
      <button
        (click)="activeSection.set('password')"
        role="tab"
        mat-list-item
        [activated]="activeSection() === 'password'"
      >
        {{ 'AbpUi::ChangePassword' | abpLocalization }}
      </button>
      <button
        (click)="activeSection.set('settings')"
        role="tab"
        mat-list-item
        [activated]="activeSection() === 'settings'"
      >
        {{ 'AbpAccount::PersonalSettings' | abpLocalization }}
      </button>
    </mat-nav-list>
    <div>
      @if (activeSection() === 'password') {
        <h3>
          {{ 'AbpIdentity::ChangePassword' | abpLocalization }}
        </h3>
        <mat-divider />
        <ttwr-form [config]="passwordForm" />
      } @else {
        <h3>
          {{ 'AbpIdentity::PersonalSettings' | abpLocalization }}
        </h3>
        <mat-divider />
        <ttwr-form id="settings-form" [config]="settingsForm" />
      }
    </div>
  </mat-card-content>
</mat-card>
