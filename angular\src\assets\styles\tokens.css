/* These tokens are generated using https://themes.angular-material.dev/ */
/* Preview: https://themes.angular-material.dev/?seed-error=%23ffb4ab&seed-neutral=%2375777c&seed-neutral-variant=%23737781&seed-primary=%232c5f9f&seed-secondary=%23b7c7e6&seed-tertiary=%23f0b0fc */
/* Seed Colors: primary: #2c5f9f, secondary: #b7c7e6, tertiary: #f0b0fc, error: #ffb4ab, neutral: #75777c, neutral-variant: #737781 */

/* Light Theme */
:root,
:host {
    --mat-sys-primary: #2c5f9f;
    --mat-sys-on-primary: #ffffff;
    --mat-sys-primary-container: #d5e3ff;
    --mat-sys-on-primary-container: #001c3b;
    --mat-sys-inverse-primary: #a6c8ff;
    --mat-sys-primary-fixed: #d5e3ff;
    --mat-sys-primary-fixed-dim: #a6c8ff;
    --mat-sys-on-primary-fixed: #001c3b;
    --mat-sys-on-primary-fixed-variant: #054786;
    --mat-sys-secondary: #4f5f7a;
    --mat-sys-on-secondary: #ffffff;
    --mat-sys-secondary-container: #d5e3ff;
    --mat-sys-on-secondary-container: #0a1c33;
    --mat-sys-secondary-fixed: #d5e3ff;
    --mat-sys-secondary-fixed-dim: #b7c7e6;
    --mat-sys-on-secondary-fixed: #0a1c33;
    --mat-sys-on-secondary-fixed-variant: #384761;
    --mat-sys-tertiary: #7f498d;
    --mat-sys-on-tertiary: #ffffff;
    --mat-sys-tertiary-container: #fcd7ff;
    --mat-sys-on-tertiary-container: #340043;
    --mat-sys-tertiary-fixed: #fcd7ff;
    --mat-sys-tertiary-fixed-dim: #f0b0fc;
    --mat-sys-on-tertiary-fixed: #340043;
    --mat-sys-on-tertiary-fixed-variant: #653173;
    --mat-sys-background: #fcf8f8;
    --mat-sys-on-background: #1c1b1c;
    --mat-sys-surface: #fcf8f8;
    --mat-sys-surface-dim: #dcd9d9;
    --mat-sys-surface-bright: #fcf8f8;
    --mat-sys-surface-container-lowest: #ffffff;
    --mat-sys-surface-container-low: #f6f3f3;
    --mat-sys-surface-container: #f1eded;
    --mat-sys-surface-container-high: #ebe7e7;
    --mat-sys-surface-container-highest: #e5e2e1;
    --mat-sys-on-surface: #1c1b1c;
    --mat-sys-shadow: #000000;
    --mat-sys-scrim: #000000;
    --mat-sys-surface-tint: #5c5e63;
    --mat-sys-inverse-surface: #313030;
    --mat-sys-inverse-on-surface: #f3f0f0;
    --mat-sys-outline: #76777b;
    --mat-sys-outline-variant: #c6c6cb;
    --mat-sys-neutral: #797777;
    --mat-sys-neutral10: #1c1b1c;
    --mat-sys-error: #ba1a1a;
    --mat-sys-error-container: #ffdad6;
    --mat-sys-on-error: #ffffff;
    --mat-sys-on-error-container: #410002;
    --mat-sys-surface-variant: #e2e2e7;
    --mat-sys-on-surface-variant: #45474b;
    --mat-sys-neutral-variant: #76777c;
    --mat-sys-neutral-variant20: #2f3035;
    --mat-sys-inverse-secondary: #b7c7e6;
    --mat-sys-inverse-tertiary: #f0b0fc;
}

/* Dark Theme */
.dark {
    --mat-sys-primary: #a6c8ff;
    --mat-sys-on-primary: #003060;
    --mat-sys-primary-container: #054786;
    --mat-sys-on-primary-container: #d5e3ff;
    --mat-sys-inverse-primary: #2c5f9f;
    --mat-sys-primary-fixed: #d5e3ff;
    --mat-sys-primary-fixed-dim: #a6c8ff;
    --mat-sys-on-primary-fixed: #001c3b;
    --mat-sys-on-primary-fixed-variant: #054786;
    --mat-sys-secondary: #b7c7e6;
    --mat-sys-on-secondary: #213149;
    --mat-sys-secondary-container: #384761;
    --mat-sys-on-secondary-container: #d5e3ff;
    --mat-sys-secondary-fixed: #d5e3ff;
    --mat-sys-secondary-fixed-dim: #b7c7e6;
    --mat-sys-on-secondary-fixed: #0a1c33;
    --mat-sys-on-secondary-fixed-variant: #384761;
    --mat-sys-tertiary: #f0b0fc;
    --mat-sys-on-tertiary: #4c195b;
    --mat-sys-tertiary-container: #653173;
    --mat-sys-on-tertiary-container: #fcd7ff;
    --mat-sys-tertiary-fixed: #fcd7ff;
    --mat-sys-tertiary-fixed-dim: #f0b0fc;
    --mat-sys-on-tertiary-fixed: #340043;
    --mat-sys-on-tertiary-fixed-variant: #653173;
    --mat-sys-background: #131314;
    --mat-sys-on-background: #e5e2e1;
    --mat-sys-surface: #131314;
    --mat-sys-surface-dim: #131314;
    --mat-sys-surface-bright: #3a3939;
    --mat-sys-surface-container-lowest: #0e0e0e;
    --mat-sys-surface-container-low: #1c1b1c;
    --mat-sys-surface-container: #201f20;
    --mat-sys-surface-container-high: #2a2a2a;
    --mat-sys-surface-container-highest: #353435;
    --mat-sys-on-surface: #e5e2e1;
    --mat-sys-shadow: #000000;
    --mat-sys-scrim: #000000;
    --mat-sys-surface-tint: #c5c6cc;
    --mat-sys-inverse-surface: #e5e2e1;
    --mat-sys-inverse-on-surface: #313030;
    --mat-sys-outline: #8f9095;
    --mat-sys-outline-variant: #45474b;
    --mat-sys-neutral: #797777;
    --mat-sys-neutral10: #1c1b1c;
    --mat-sys-error: #ffb4ab;
    --mat-sys-error-container: #93000a;
    --mat-sys-on-error: #690005;
    --mat-sys-on-error-container: #ffdad6;
    --mat-sys-surface-variant: #45474b;
    --mat-sys-on-surface-variant: #c6c6cb;
    --mat-sys-neutral-variant: #76777c;
    --mat-sys-neutral-variant20: #2f3035;
    --mat-sys-inverse-secondary: #4f5f7a;
    --mat-sys-inverse-tertiary: #7f498d;
}