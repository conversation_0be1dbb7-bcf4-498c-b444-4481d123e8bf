@let _type = type();
@let label = name();

@if (
  _type === fieldType.String ||
  _type === fieldType.Int ||
  _type === fieldType.Decimal
  ) {
  <mat-form-field>
    <mat-label>{{ label }}</mat-label>
    <input [type]="_type === fieldType.String ? 'text': 'number'" matInput [formControl]="control()"/>
    <mat-error>{{ 'this field is required' | i18n }}</mat-error>
  </mat-form-field>
} @else if (_type === fieldType.Bool) {
  <mat-checkbox [formControl]="control()">
    {{ label }}
  </mat-checkbox>
} @else if (_type === fieldType.DateOnly) {
  <mat-form-field>
    <mat-label>{{ label }}</mat-label>
    <input
      matInput
      [matDatepicker]="picker"
      [formControl]="control()"
    >
    <mat-datepicker-toggle
      matIconSuffix
      [for]="picker"
    />
    <mat-datepicker #picker />
    <mat-error>{{ 'this field is required' | i18n }}</mat-error>
  </mat-form-field>

} @else if (_type === fieldType.DateTime) {
  <mat-form-field>
    <mat-label>{{ label }}</mat-label>
    <input
      matInput
      [ttwrDatetimepicker]="picker"
      [formControl]="control()"
    >
    <ttwr-datetimepicker-toggle
      matIconSuffix
      [for]="picker"
    />
    <ttwr-datetimepicker #picker />
    <mat-error>{{ 'this field is required' | i18n }}</mat-error>
  </mat-form-field>
} @else {
  <mat-form-field>
    <mat-label>{{ label }}</mat-label>
    <mat-select [formControl]="control()">
      @for (option of options(); track option) {
        <mat-option [value]="option">
          {{ option }}
        </mat-option>
      }
    </mat-select>
    <mat-error>{{ 'this field is required' | i18n }}</mat-error>
  </mat-form-field>
}
