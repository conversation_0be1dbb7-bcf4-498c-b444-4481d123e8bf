{"version": 3, "sources": ["../../../../../../node_modules/@abp/ng.setting-management/fesm2022/abp-ng.setting-management-proxy.mjs", "../../../../../../node_modules/@abp/ng.setting-management/fesm2022/abp-ng.setting-management-config.mjs"], "sourcesContent": ["import * as i1 from '@abp/ng.core';\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\nvar index$1 = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\nvar index = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Abp: index$1\n});\nclass EmailSettingsService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'SettingManagement';\n    this.get = config => this.restService.request({\n      method: 'GET',\n      url: '/api/setting-management/emailing'\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.sendTestEmail = (input, config) => this.restService.request({\n      method: 'POST',\n      url: '/api/setting-management/emailing/send-test-email',\n      body: input\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.update = (input, config) => this.restService.request({\n      method: 'POST',\n      url: '/api/setting-management/emailing',\n      body: input\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n  }\n  static {\n    this.ɵfac = function EmailSettingsService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EmailSettingsService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: EmailSettingsService,\n      factory: EmailSettingsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EmailSettingsService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.RestService\n  }], null);\n})();\nclass TimeZoneSettingsService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'SettingManagement';\n    this.get = config => this.restService.request({\n      method: 'GET',\n      responseType: 'text',\n      url: '/api/setting-management/timezone'\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.getTimezones = config => this.restService.request({\n      method: 'GET',\n      url: '/api/setting-management/timezone/timezones'\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.update = (timezone, config) => this.restService.request({\n      method: 'POST',\n      url: '/api/setting-management/timezone',\n      params: {\n        timezone\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n  }\n  static {\n    this.ɵfac = function TimeZoneSettingsService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TimeZoneSettingsService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TimeZoneSettingsService,\n      factory: TimeZoneSettingsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TimeZoneSettingsService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.RestService\n  }], null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { EmailSettingsService, TimeZoneSettingsService, index as Volo };\n", "import * as i3 from '@abp/ng.theme.shared';\nimport { collapse, ThemeSharedModule } from '@abp/ng.theme.shared';\nimport * as i0 from '@angular/core';\nimport { inject, Component, Injectable, InjectionToken, APP_INITIALIZER, Injector, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport * as i2 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport { finalize, debounceTime, map } from 'rxjs/operators';\nimport * as i1 from '@abp/ng.setting-management/proxy';\nimport * as i4 from '@abp/ng.core';\nimport { LocalizationService, ConfigStateService, AbstractNavTreeService, RoutesService, noop, featuresFactory, CoreModule } from '@abp/ng.core';\nimport * as i5 from '@ngx-validate/core';\nimport { NgxValidateCoreModule } from '@ngx-validate/core';\nimport { combineLatest } from 'rxjs';\nconst _c0 = () => ({\n  time: \"200ms\",\n  easing: \"linear\"\n});\nconst _c1 = (a0, a1) => ({\n  value: a0,\n  params: a1\n});\nfunction EmailSettingGroupComponent_Conditional_0_button_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function EmailSettingGroupComponent_Conditional_0_button_57_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.openSendEmailModal());\n    });\n    i0.ɵɵelement(1, \"i\", 25);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"AbpSettingManagement::SendTestEmail\"), \" \");\n  }\n}\nfunction EmailSettingGroupComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 5);\n    i0.ɵɵlistener(\"ngSubmit\", function EmailSettingGroupComponent_Conditional_0_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.submit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 6)(2, \"label\", 7);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"abpLocalization\");\n    i0.ɵɵelementStart(5, \"span\", 8);\n    i0.ɵɵtext(6, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(7, \"input\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 6)(9, \"label\", 7);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"abpLocalization\");\n    i0.ɵɵelementStart(12, \"span\", 8);\n    i0.ɵɵtext(13, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(14, \"input\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 6)(16, \"label\", 7);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"input\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 6)(21, \"label\", 7);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"abpLocalization\");\n    i0.ɵɵelementStart(24, \"span\", 8);\n    i0.ɵɵtext(25, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(26, \"input\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 13);\n    i0.ɵɵelement(28, \"input\", 14);\n    i0.ɵɵelementStart(29, \"label\", 15);\n    i0.ɵɵtext(30);\n    i0.ɵɵpipe(31, \"abpLocalization\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 13);\n    i0.ɵɵelement(33, \"input\", 16);\n    i0.ɵɵelementStart(34, \"label\", 17);\n    i0.ɵɵtext(35);\n    i0.ɵɵpipe(36, \"abpLocalization\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\")(38, \"div\", 6)(39, \"label\", 7);\n    i0.ɵɵtext(40);\n    i0.ɵɵpipe(41, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(42, \"input\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 6)(44, \"label\", 7);\n    i0.ɵɵtext(45);\n    i0.ɵɵpipe(46, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(47, \"input\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"div\", 20)(49, \"label\", 7);\n    i0.ɵɵtext(50);\n    i0.ɵɵpipe(51, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(52, \"input\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(53, \"hr\");\n    i0.ɵɵelementStart(54, \"abp-button\", 22);\n    i0.ɵɵtext(55);\n    i0.ɵɵpipe(56, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(57, EmailSettingGroupComponent_Conditional_0_button_57_Template, 4, 3, \"button\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_12_0;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.form)(\"validateOnSubmit\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 14, \"AbpSettingManagement::DefaultFromDisplayName\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(11, 16, \"AbpSettingManagement::DefaultFromAddress\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 18, \"AbpSettingManagement::SmtpHost\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(23, 20, \"AbpSettingManagement::SmtpPort\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(31, 22, \"AbpSettingManagement::SmtpEnableSsl\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(36, 24, \"AbpSettingManagement::SmtpUseDefaultCredentials\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"@collapse\", i0.ɵɵpureFunction2(35, _c1, ((tmp_12_0 = ctx_r2.form.get(\"smtpUseDefaultCredentials\")) == null ? null : tmp_12_0.value) ? \"collapsed\" : \"expanded\", i0.ɵɵpureFunction0(34, _c0)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(41, 26, \"AbpSettingManagement::SmtpDomain\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(46, 28, \"AbpSettingManagement::SmtpUserName\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(51, 30, \"AbpSettingManagement::SmtpPassword\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(56, 32, \"AbpSettingManagement::Save\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"abpPermission\", ctx_r2.emailingPolicy);\n  }\n}\nfunction EmailSettingGroupComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"SendTestEmail\"));\n  }\n}\nfunction EmailSettingGroupComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 26);\n    i0.ɵɵlistener(\"ngSubmit\", function EmailSettingGroupComponent_ng_template_4_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.emailTestFormSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 6)(2, \"label\", 7);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 6)(7, \"label\", 7);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 6)(12, \"label\", 7);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 6)(17, \"label\", 7);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"textarea\", 30);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.emailTestForm);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 5, \"AbpSettingManagement::SenderEmailAddress\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 7, \"AbpSettingManagement::TargetEmailAddress\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 9, \"AbpSettingManagement::Subject\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 11, \"AbpSettingManagement::Body\"));\n  }\n}\nfunction EmailSettingGroupComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"abp-button\", 31)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"abpLocalization\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"button\", 32);\n    i0.ɵɵtext(5, \"Close\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.emailTestForm.invalid && ctx_r2.emailTestForm.dirty);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"AbpTenantManagement::Send\"));\n  }\n}\nconst {\n  required,\n  email\n} = Validators;\nclass EmailSettingGroupComponent {\n  constructor(emailSettingsService, fb, toasterService) {\n    this.emailSettingsService = emailSettingsService;\n    this.fb = fb;\n    this.toasterService = toasterService;\n    this.localizationService = inject(LocalizationService);\n    this.configStateSevice = inject(ConfigStateService);\n    this.currentUserEmail = toSignal(this.configStateSevice.getDeep$(['currentUser', 'email']));\n    this.saving = false;\n    this.emailingPolicy = \"SettingManagement.Emailing\" /* SettingManagementPolicyNames.Emailing */;\n    this.isEmailTestModalOpen = false;\n    this.modalSize = {\n      size: 'lg'\n    };\n  }\n  ngOnInit() {\n    this.getData();\n  }\n  getData() {\n    this.emailSettingsService.get().subscribe(res => {\n      this.buildForm(res);\n    });\n  }\n  buildForm(emailSettings) {\n    this.form = this.fb.group({\n      defaultFromDisplayName: [emailSettings.defaultFromDisplayName, [Validators.required]],\n      defaultFromAddress: [emailSettings.defaultFromAddress, [Validators.required]],\n      smtpHost: [emailSettings.smtpHost],\n      smtpPort: [emailSettings.smtpPort, [Validators.required]],\n      smtpEnableSsl: [emailSettings.smtpEnableSsl],\n      smtpUseDefaultCredentials: [emailSettings.smtpUseDefaultCredentials],\n      smtpDomain: [emailSettings.smtpDomain],\n      smtpUserName: [emailSettings.smtpUserName],\n      smtpPassword: [emailSettings.smtpPassword]\n    });\n  }\n  submit() {\n    if (this.saving || this.form.invalid) return;\n    this.saving = true;\n    this.emailSettingsService.update(this.form.value).pipe(finalize(() => this.saving = false)).subscribe(() => {\n      this.toasterService.success('AbpSettingManagement::SavedSuccessfully');\n      this.getData();\n    });\n  }\n  openSendEmailModal() {\n    this.buildEmailTestForm();\n    this.isEmailTestModalOpen = true;\n  }\n  buildEmailTestForm() {\n    const {\n      defaultFromAddress\n    } = this.form.value || {};\n    const defaultSubject = this.localizationService.instant('AbpSettingManagement::TestEmailSubject', ...[Math.floor(Math.random() * 9999).toString()]);\n    const defaultBody = this.localizationService.instant('AbpSettingManagement::TestEmailBody');\n    this.emailTestForm = this.fb.group({\n      senderEmailAddress: [defaultFromAddress || '', [required, email]],\n      targetEmailAddress: [this.currentUserEmail(), [required, email]],\n      subject: [defaultSubject, [required]],\n      body: [defaultBody]\n    });\n  }\n  emailTestFormSubmit() {\n    if (this.emailTestForm.invalid) {\n      return;\n    }\n    this.emailSettingsService.sendTestEmail(this.emailTestForm.value).subscribe(res => {\n      this.toasterService.success('AbpSettingManagement::SuccessfullySent');\n      this.isEmailTestModalOpen = false;\n    });\n  }\n  static {\n    this.ɵfac = function EmailSettingGroupComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EmailSettingGroupComponent)(i0.ɵɵdirectiveInject(i1.EmailSettingsService), i0.ɵɵdirectiveInject(i2.UntypedFormBuilder), i0.ɵɵdirectiveInject(i3.ToasterService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: EmailSettingGroupComponent,\n      selectors: [[\"abp-email-setting-group\"]],\n      decls: 8,\n      vars: 3,\n      consts: [[\"abpHeader\", \"\"], [\"abpBody\", \"\"], [\"abpFooter\", \"\"], [1, \"py-2\", \"abp-md-form\", 3, \"formGroup\", \"validateOnSubmit\"], [3, \"visibleChange\", \"visible\", \"options\"], [1, \"py-2\", \"abp-md-form\", 3, \"ngSubmit\", \"formGroup\", \"validateOnSubmit\"], [1, \"mb-3\", \"form-group\"], [1, \"form-label\"], [1, \"ms-1\"], [\"type\", \"text\", \"formControlName\", \"defaultFromDisplayName\", 1, \"form-control\"], [\"type\", \"text\", \"formControlName\", \"defaultFromAddress\", 1, \"form-control\"], [\"type\", \"text\", \"formControlName\", \"smtpHost\", 1, \"form-control\"], [\"type\", \"number\", \"formControlName\", \"smtpPort\", 1, \"form-control\"], [1, \"form-check\", \"mb-2\"], [\"type\", \"checkbox\", \"id\", \"smtp-enable-ssl\", \"formControlName\", \"smtpEnableSsl\", 1, \"form-check-input\"], [\"for\", \"smtp-enable-ssl\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"smtp-use-default-credentials\", \"formControlName\", \"smtpUseDefaultCredentials\", 1, \"form-check-input\"], [\"for\", \"smtp-use-default-credentials\", 1, \"form-check-label\"], [\"type\", \"text\", \"formControlName\", \"smtpDomain\", 1, \"form-control\"], [\"type\", \"text\", \"formControlName\", \"smtpUserName\", 1, \"form-control\"], [1, \"form-group\"], [\"type\", \"password\", \"formControlName\", \"smtpPassword\", 1, \"form-control\"], [\"buttonType\", \"submit\", \"iconClass\", \"fa fa-save\"], [\"type\", \"button\", \"class\", \"btn btn-primary mx-2\", 3, \"click\", 4, \"abpPermission\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"mx-2\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"f-send\"], [\"id\", \"emailTestForm\", 3, \"ngSubmit\", \"formGroup\"], [\"formControlName\", \"senderEmailAddress\", 1, \"form-control\"], [\"formControlName\", \"targetEmailAddress\", 1, \"form-control\"], [\"formControlName\", \"subject\", 1, \"form-control\"], [\"formControlName\", \"body\", 1, \"form-control\"], [\"buttonType\", \"submit\", \"iconClass\", \"fa fa-send\", \"formName\", \"emailTestForm\", 3, \"disabled\"], [\"type\", \"button\", \"abpClose\", \"\", 1, \"btn\", \"btn-secondary\"]],\n      template: function EmailSettingGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, EmailSettingGroupComponent_Conditional_0_Template, 58, 38, \"form\", 3);\n          i0.ɵɵelementStart(1, \"abp-modal\", 4);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function EmailSettingGroupComponent_Template_abp_modal_visibleChange_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.isEmailTestModalOpen, $event) || (ctx.isEmailTestModalOpen = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(2, EmailSettingGroupComponent_ng_template_2_Template, 3, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(4, EmailSettingGroupComponent_ng_template_4_Template, 21, 13, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(6, EmailSettingGroupComponent_ng_template_6_Template, 6, 4, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.form ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.isEmailTestModalOpen);\n          i0.ɵɵproperty(\"options\", ctx.modalSize);\n        }\n      },\n      dependencies: [i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NumberValueAccessor, i2.CheckboxControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i4.FormSubmitDirective, i4.PermissionDirective, i5.ValidationGroupDirective, i5.ValidationDirective, i3.ButtonComponent, i3.ModalComponent, i3.ModalCloseDirective, i4.LocalizationPipe],\n      encapsulation: 2,\n      data: {\n        animation: [collapse]\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EmailSettingGroupComponent, [{\n    type: Component,\n    args: [{\n      selector: 'abp-email-setting-group',\n      animations: [collapse],\n      template: \"@if (form) {\\r\\n  <form [formGroup]=\\\"form\\\" class=\\\"py-2 abp-md-form\\\" (ngSubmit)=\\\"submit()\\\" [validateOnSubmit]=\\\"true\\\">\\r\\n    <div class=\\\"mb-3 form-group\\\">\\r\\n      <label class=\\\"form-label\\\"\\r\\n        >{{ 'AbpSettingManagement::DefaultFromDisplayName' | abpLocalization\\r\\n        }}<span class=\\\"ms-1\\\">*</span></label\\r\\n      >\\r\\n      <input type=\\\"text\\\" class=\\\"form-control\\\" formControlName=\\\"defaultFromDisplayName\\\" />\\r\\n    </div>\\r\\n    <div class=\\\"mb-3 form-group\\\">\\r\\n      <label class=\\\"form-label\\\"\\r\\n        >{{ 'AbpSettingManagement::DefaultFromAddress' | abpLocalization\\r\\n        }}<span class=\\\"ms-1\\\">*</span></label\\r\\n      >\\r\\n      <input type=\\\"text\\\" class=\\\"form-control\\\" formControlName=\\\"defaultFromAddress\\\" />\\r\\n    </div>\\r\\n    <div class=\\\"mb-3 form-group\\\">\\r\\n      <label class=\\\"form-label\\\">{{ 'AbpSettingManagement::SmtpHost' | abpLocalization }}</label>\\r\\n      <input type=\\\"text\\\" class=\\\"form-control\\\" formControlName=\\\"smtpHost\\\" />\\r\\n    </div>\\r\\n    <div class=\\\"mb-3 form-group\\\">\\r\\n      <label class=\\\"form-label\\\"\\r\\n        >{{ 'AbpSettingManagement::SmtpPort' | abpLocalization }}<span class=\\\"ms-1\\\">*</span></label\\r\\n      >\\r\\n      <input type=\\\"number\\\" class=\\\"form-control\\\" formControlName=\\\"smtpPort\\\" />\\r\\n    </div>\\r\\n\\r\\n    <div class=\\\"form-check mb-2\\\">\\r\\n      <input\\r\\n        type=\\\"checkbox\\\"\\r\\n        id=\\\"smtp-enable-ssl\\\"\\r\\n        class=\\\"form-check-input\\\"\\r\\n        formControlName=\\\"smtpEnableSsl\\\"\\r\\n      />\\r\\n      <label class=\\\"form-check-label\\\" for=\\\"smtp-enable-ssl\\\">{{\\r\\n        'AbpSettingManagement::SmtpEnableSsl' | abpLocalization\\r\\n      }}</label>\\r\\n    </div>\\r\\n    <div class=\\\"form-check mb-2\\\">\\r\\n      <input\\r\\n        type=\\\"checkbox\\\"\\r\\n        id=\\\"smtp-use-default-credentials\\\"\\r\\n        class=\\\"form-check-input\\\"\\r\\n        formControlName=\\\"smtpUseDefaultCredentials\\\"\\r\\n      />\\r\\n      <label class=\\\"form-check-label\\\" for=\\\"smtp-use-default-credentials\\\">{{\\r\\n        'AbpSettingManagement::SmtpUseDefaultCredentials' | abpLocalization\\r\\n      }}</label>\\r\\n    </div>\\r\\n\\r\\n    <div\\r\\n      [@collapse]=\\\"{\\r\\n        value: form.get('smtpUseDefaultCredentials')?.value ? 'collapsed' : 'expanded',\\r\\n        params: { time: '200ms', easing: 'linear' }\\r\\n      }\\\"\\r\\n    >\\r\\n      <div class=\\\"mb-3 form-group\\\">\\r\\n        <label class=\\\"form-label\\\">{{ 'AbpSettingManagement::SmtpDomain' | abpLocalization }}</label>\\r\\n        <input type=\\\"text\\\" class=\\\"form-control\\\" formControlName=\\\"smtpDomain\\\" />\\r\\n      </div>\\r\\n\\r\\n      <div class=\\\"mb-3 form-group\\\">\\r\\n        <label class=\\\"form-label\\\">{{\\r\\n          'AbpSettingManagement::SmtpUserName' | abpLocalization\\r\\n        }}</label>\\r\\n        <input type=\\\"text\\\" class=\\\"form-control\\\" formControlName=\\\"smtpUserName\\\" />\\r\\n      </div>\\r\\n\\r\\n      <div class=\\\"form-group\\\">\\r\\n        <label class=\\\"form-label\\\">{{\\r\\n          'AbpSettingManagement::SmtpPassword' | abpLocalization\\r\\n        }}</label>\\r\\n        <input type=\\\"password\\\" class=\\\"form-control\\\" formControlName=\\\"smtpPassword\\\" />\\r\\n      </div>\\r\\n    </div>\\r\\n\\r\\n    <hr />\\r\\n\\r\\n    <abp-button buttonType=\\\"submit\\\" iconClass=\\\"fa fa-save\\\">\\r\\n      {{ 'AbpSettingManagement::Save' | abpLocalization }}\\r\\n    </abp-button>\\r\\n    <button\\r\\n      *abpPermission=\\\"emailingPolicy\\\"\\r\\n      type=\\\"button\\\"\\r\\n      (click)=\\\"openSendEmailModal()\\\"\\r\\n      class=\\\"btn btn-primary mx-2\\\"\\r\\n    >\\r\\n      <i class=\\\"fa f-send\\\" aria-hidden=\\\"true\\\"></i>\\r\\n      {{ 'AbpSettingManagement::SendTestEmail' | abpLocalization }}\\r\\n    </button>\\r\\n  </form>\\r\\n}\\r\\n\\r\\n<abp-modal [(visible)]=\\\"isEmailTestModalOpen\\\" [options]=\\\"modalSize\\\">\\r\\n  <ng-template #abpHeader>\\r\\n    <h3>{{ 'SendTestEmail' | abpLocalization }}</h3>\\r\\n  </ng-template>\\r\\n\\r\\n  <ng-template #abpBody>\\r\\n    <form [formGroup]=\\\"emailTestForm\\\" id=\\\"emailTestForm\\\" (ngSubmit)=\\\"emailTestFormSubmit()\\\">\\r\\n      <div class=\\\"mb-3 form-group\\\">\\r\\n        <label class=\\\"form-label\\\">{{\\r\\n          'AbpSettingManagement::SenderEmailAddress' | abpLocalization\\r\\n        }}</label>\\r\\n        <input class=\\\"form-control\\\" formControlName=\\\"senderEmailAddress\\\" />\\r\\n      </div>\\r\\n\\r\\n      <div class=\\\"mb-3 form-group\\\">\\r\\n        <label class=\\\"form-label\\\">{{\\r\\n          'AbpSettingManagement::TargetEmailAddress' | abpLocalization\\r\\n        }}</label>\\r\\n        <input class=\\\"form-control\\\" formControlName=\\\"targetEmailAddress\\\" />\\r\\n      </div>\\r\\n\\r\\n      <div class=\\\"mb-3 form-group\\\">\\r\\n        <label class=\\\"form-label\\\">{{ 'AbpSettingManagement::Subject' | abpLocalization }}</label>\\r\\n        <input class=\\\"form-control\\\" formControlName=\\\"subject\\\" />\\r\\n      </div>\\r\\n\\r\\n      <div class=\\\"mb-3 form-group\\\">\\r\\n        <label class=\\\"form-label\\\">{{ 'AbpSettingManagement::Body' | abpLocalization }}</label>\\r\\n        <textarea class=\\\"form-control\\\" formControlName=\\\"body\\\"></textarea>\\r\\n      </div>\\r\\n    </form>\\r\\n  </ng-template>\\r\\n\\r\\n  <ng-template #abpFooter>\\r\\n    <abp-button\\r\\n      buttonType=\\\"submit\\\"\\r\\n      iconClass=\\\"fa fa-send\\\"\\r\\n      formName=\\\"emailTestForm\\\"\\r\\n      [disabled]=\\\"emailTestForm.invalid && emailTestForm.dirty\\\"\\r\\n    >\\r\\n      <span>{{ 'AbpTenantManagement::Send' | abpLocalization }}</span>\\r\\n    </abp-button>\\r\\n\\r\\n    <button type=\\\"button\\\" class=\\\"btn btn-secondary\\\" abpClose>Close</button>\\r\\n  </ng-template>\\r\\n</abp-modal>\\r\\n\"\n    }]\n  }], () => [{\n    type: i1.EmailSettingsService\n  }, {\n    type: i2.UntypedFormBuilder\n  }, {\n    type: i3.ToasterService\n  }], null);\n})();\nclass SettingTabsService extends AbstractNavTreeService {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵSettingTabsService_BaseFactory;\n      return function SettingTabsService_Factory(__ngFactoryType__) {\n        return (ɵSettingTabsService_BaseFactory || (ɵSettingTabsService_BaseFactory = i0.ɵɵgetInheritedFactory(SettingTabsService)))(__ngFactoryType__ || SettingTabsService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SettingTabsService,\n      factory: SettingTabsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SettingTabsService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nfunction configureRoutes(routesService) {\n  return () => {\n    routesService.add([{\n      name: \"AbpSettingManagement::Settings\" /* eSettingManagementRouteNames.Settings */,\n      path: '/setting-management',\n      parentName: \"AbpUiNavigation::Menu:Administration\" /* eThemeSharedRouteNames.Administration */,\n      layout: \"application\" /* eLayoutType.application */,\n      order: 100,\n      iconClass: 'fa fa-cog'\n    }]);\n  };\n}\nconst SETTING_MANAGEMENT_HAS_SETTING = new InjectionToken('SETTING_MANAGEMENT_HAS_SETTING', {\n  factory: () => {\n    const settingTabsService = inject(SettingTabsService);\n    return settingTabsService.visible$.pipe(debounceTime(0), map(nodes => !!nodes.length));\n  }\n});\nconst SETTING_MANAGEMENT_ROUTE_PROVIDERS = [{\n  provide: APP_INITIALIZER,\n  useFactory: configureRoutes,\n  deps: [RoutesService],\n  multi: true\n}, {\n  provide: APP_INITIALIZER,\n  useFactory: noop,\n  deps: [SETTING_MANAGEMENT_HAS_SETTING],\n  multi: true\n}];\nconst SETTING_MANAGEMENT_SETTING_TAB_PROVIDERS = [{\n  provide: APP_INITIALIZER,\n  useFactory: configureSettingTabs,\n  deps: [SettingTabsService],\n  multi: true\n}];\nfunction configureSettingTabs(settingTabs) {\n  return () => {\n    settingTabs.add([{\n      name: \"AbpSettingManagement::Menu:Emailing\" /* eSettingManamagementSettingTabNames.EmailSettingGroup */,\n      order: 100,\n      requiredPolicy: 'SettingManagement.Emailing',\n      component: EmailSettingGroupComponent\n    }]);\n  };\n}\nconst SETTING_MANAGEMENT_FEATURES = new InjectionToken('SETTING_MANAGEMENT_FEATURES', {\n  providedIn: 'root',\n  factory: () => {\n    const configState = inject(ConfigStateService);\n    const featureKey = 'SettingManagement.Enable';\n    const mapFn = features => ({\n      enable: features[featureKey].toLowerCase() !== 'false'\n    });\n    return featuresFactory(configState, [featureKey], mapFn);\n  }\n});\nconst SETTING_MANAGEMENT_ROUTE_VISIBILITY = new InjectionToken('SETTING_MANAGEMENT_ROUTE_VISIBILITY', {\n  providedIn: 'root',\n  factory: () => {\n    const stream = inject(SETTING_MANAGEMENT_FEATURES);\n    return stream.pipe(map(features => features.enable));\n  }\n});\nconst SETTING_MANAGEMENT_FEATURES_PROVIDERS = [{\n  provide: APP_INITIALIZER,\n  useFactory: noop,\n  deps: [SETTING_MANAGEMENT_ROUTE_VISIBILITY],\n  multi: true\n}];\nconst SETTING_MANAGEMENT_VISIBLE_PROVIDERS = [{\n  provide: APP_INITIALIZER,\n  useFactory: setSettingManagementVisibility,\n  deps: [Injector],\n  multi: true\n}];\nfunction setSettingManagementVisibility(injector) {\n  return () => {\n    const settingManagementHasSetting$ = injector.get(SETTING_MANAGEMENT_HAS_SETTING);\n    const isSettingManagementFeatureEnable$ = injector.get(SETTING_MANAGEMENT_ROUTE_VISIBILITY);\n    const routes = injector.get(RoutesService);\n    combineLatest([settingManagementHasSetting$, isSettingManagementFeatureEnable$]).subscribe(([settingManagementHasSetting, isSettingManagementFeatureEnable]) => {\n      routes.patch(\"AbpSettingManagement::Settings\" /* eSettingManagementRouteNames.Settings */, {\n        invisible: !(settingManagementHasSetting && isSettingManagementFeatureEnable)\n      });\n    });\n  };\n}\nfunction provideSettingManagementConfig() {\n  return makeEnvironmentProviders([SETTING_MANAGEMENT_ROUTE_PROVIDERS, SETTING_MANAGEMENT_SETTING_TAB_PROVIDERS, SETTING_MANAGEMENT_FEATURES_PROVIDERS, SETTING_MANAGEMENT_VISIBLE_PROVIDERS]);\n}\n\n/**\n@deprecated This method is deprecated, use it from @abp/ng.setting-management/proxy\n*/\nclass EmailSettingsService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'SettingManagement';\n    this.get = () => this.restService.request({\n      method: 'GET',\n      url: '/api/setting-management/emailing'\n    }, {\n      apiName: this.apiName\n    });\n    this.sendTestEmail = input => this.restService.request({\n      method: 'POST',\n      url: '/api/setting-management/emailing/send-test-email',\n      body: input\n    }, {\n      apiName: this.apiName\n    });\n    this.update = input => this.restService.request({\n      method: 'POST',\n      url: '/api/setting-management/emailing',\n      body: input\n    }, {\n      apiName: this.apiName\n    });\n  }\n  static {\n    this.ɵfac = function EmailSettingsService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EmailSettingsService)(i0.ɵɵinject(i4.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: EmailSettingsService,\n      factory: EmailSettingsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EmailSettingsService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i4.RestService\n  }], null);\n})();\nclass SettingManagementConfigModule {\n  /**\n   * @deprecated forRoot method is deprecated, use `provideSettingManagementConfig` *function* for config settings.\n   */\n  static forRoot() {\n    return {\n      ngModule: SettingManagementConfigModule,\n      providers: [provideSettingManagementConfig()]\n    };\n  }\n  static {\n    this.ɵfac = function SettingManagementConfigModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SettingManagementConfigModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: SettingManagementConfigModule,\n      declarations: [EmailSettingGroupComponent],\n      imports: [CoreModule, ThemeSharedModule, NgxValidateCoreModule],\n      exports: [EmailSettingGroupComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CoreModule, ThemeSharedModule, NgxValidateCoreModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SettingManagementConfigModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CoreModule, ThemeSharedModule, NgxValidateCoreModule],\n      declarations: [EmailSettingGroupComponent],\n      exports: [EmailSettingGroupComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { EmailSettingGroupComponent, EmailSettingsService, SETTING_MANAGEMENT_FEATURES, SETTING_MANAGEMENT_FEATURES_PROVIDERS, SETTING_MANAGEMENT_HAS_SETTING, SETTING_MANAGEMENT_ROUTE_PROVIDERS, SETTING_MANAGEMENT_ROUTE_VISIBILITY, SETTING_MANAGEMENT_SETTING_TAB_PROVIDERS, SETTING_MANAGEMENT_VISIBLE_PROVIDERS, SettingManagementConfigModule, SettingTabsService, configureRoutes, configureSettingTabs, provideSettingManagementConfig, setSettingManagementVisibility };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAI,UAAuB,OAAO,OAAO;AAAA,EACvC,WAAW;AACb,CAAC;AACD,IAAI,QAAqB,OAAO,OAAO;AAAA,EACrC,WAAW;AAAA,EACX,KAAK;AACP,CAAC;AACD,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,MAAM,YAAU,KAAK,YAAY,QAAQ;AAAA,MAC5C,QAAQ;AAAA,MACR,KAAK;AAAA,IACP,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,OACX,OACJ;AACD,SAAK,gBAAgB,CAAC,OAAO,WAAW,KAAK,YAAY,QAAQ;AAAA,MAC/D,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,MAAM;AAAA,IACR,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,OACX,OACJ;AACD,SAAK,SAAS,CAAC,OAAO,WAAW,KAAK,YAAY,QAAQ;AAAA,MACxD,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,MAAM;AAAA,IACR,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,OACX,OACJ;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAyB,SAAY,WAAW,CAAC;AAAA,IACpF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,sBAAqB;AAAA,MAC9B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,MAAM,YAAU,KAAK,YAAY,QAAQ;AAAA,MAC5C,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,KAAK;AAAA,IACP,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,OACX,OACJ;AACD,SAAK,eAAe,YAAU,KAAK,YAAY,QAAQ;AAAA,MACrD,QAAQ;AAAA,MACR,KAAK;AAAA,IACP,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,OACX,OACJ;AACD,SAAK,SAAS,CAAC,UAAU,WAAW,KAAK,YAAY,QAAQ;AAAA,MAC3D,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,QACN;AAAA,MACF;AAAA,IACF,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,OACX,OACJ;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAA4B,SAAY,WAAW,CAAC;AAAA,IACvF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,yBAAwB;AAAA,MACjC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;;;ACnGH,IAAM,MAAM,OAAO;AAAA,EACjB,MAAM;AAAA,EACN,QAAQ;AACV;AACA,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,OAAO;AAAA,EACP,QAAQ;AACV;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,sFAAsF;AACpH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,mBAAmB,CAAC;AAAA,IACnD,CAAC;AACD,IAAG,UAAU,GAAG,KAAK,EAAE;AACvB,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,qCAAqC,GAAG,GAAG;AAAA,EAC7F;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,YAAY,SAAS,6EAA6E;AAC9G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,CAAC;AAAA,IACvC,CAAC;AACD,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,SAAS,CAAC;AAC5C,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,GAAG,GAAG;AAChB,IAAG,aAAa,EAAE;AAClB,IAAG,UAAU,GAAG,SAAS,CAAC;AAC1B,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,SAAS,CAAC;AAC5C,IAAG,OAAO,EAAE;AACZ,IAAG,OAAO,IAAI,iBAAiB;AAC/B,IAAG,eAAe,IAAI,QAAQ,CAAC;AAC/B,IAAG,OAAO,IAAI,GAAG;AACjB,IAAG,aAAa,EAAE;AAClB,IAAG,UAAU,IAAI,SAAS,EAAE;AAC5B,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,OAAO,CAAC,EAAE,IAAI,SAAS,CAAC;AAC9C,IAAG,OAAO,EAAE;AACZ,IAAG,OAAO,IAAI,iBAAiB;AAC/B,IAAG,aAAa;AAChB,IAAG,UAAU,IAAI,SAAS,EAAE;AAC5B,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,OAAO,CAAC,EAAE,IAAI,SAAS,CAAC;AAC9C,IAAG,OAAO,EAAE;AACZ,IAAG,OAAO,IAAI,iBAAiB;AAC/B,IAAG,eAAe,IAAI,QAAQ,CAAC;AAC/B,IAAG,OAAO,IAAI,GAAG;AACjB,IAAG,aAAa,EAAE;AAClB,IAAG,UAAU,IAAI,SAAS,EAAE;AAC5B,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,OAAO,EAAE;AAC/B,IAAG,UAAU,IAAI,SAAS,EAAE;AAC5B,IAAG,eAAe,IAAI,SAAS,EAAE;AACjC,IAAG,OAAO,EAAE;AACZ,IAAG,OAAO,IAAI,iBAAiB;AAC/B,IAAG,aAAa,EAAE;AAClB,IAAG,eAAe,IAAI,OAAO,EAAE;AAC/B,IAAG,UAAU,IAAI,SAAS,EAAE;AAC5B,IAAG,eAAe,IAAI,SAAS,EAAE;AACjC,IAAG,OAAO,EAAE;AACZ,IAAG,OAAO,IAAI,iBAAiB;AAC/B,IAAG,aAAa,EAAE;AAClB,IAAG,eAAe,IAAI,KAAK,EAAE,IAAI,OAAO,CAAC,EAAE,IAAI,SAAS,CAAC;AACzD,IAAG,OAAO,EAAE;AACZ,IAAG,OAAO,IAAI,iBAAiB;AAC/B,IAAG,aAAa;AAChB,IAAG,UAAU,IAAI,SAAS,EAAE;AAC5B,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,OAAO,CAAC,EAAE,IAAI,SAAS,CAAC;AAC9C,IAAG,OAAO,EAAE;AACZ,IAAG,OAAO,IAAI,iBAAiB;AAC/B,IAAG,aAAa;AAChB,IAAG,UAAU,IAAI,SAAS,EAAE;AAC5B,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,OAAO,EAAE,EAAE,IAAI,SAAS,CAAC;AAC/C,IAAG,OAAO,EAAE;AACZ,IAAG,OAAO,IAAI,iBAAiB;AAC/B,IAAG,aAAa;AAChB,IAAG,UAAU,IAAI,SAAS,EAAE;AAC5B,IAAG,aAAa,EAAE;AAClB,IAAG,UAAU,IAAI,IAAI;AACrB,IAAG,eAAe,IAAI,cAAc,EAAE;AACtC,IAAG,OAAO,EAAE;AACZ,IAAG,OAAO,IAAI,iBAAiB;AAC/B,IAAG,aAAa;AAChB,IAAG,WAAW,IAAI,6DAA6D,GAAG,GAAG,UAAU,EAAE;AACjG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,QAAI;AACJ,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,aAAa,OAAO,IAAI,EAAE,oBAAoB,IAAI;AAChE,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,GAAG,IAAI,8CAA8C,CAAC;AAC1F,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,IAAI,IAAI,0CAA0C,CAAC;AACvF,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,IAAI,IAAI,gCAAgC,CAAC;AAC7E,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,IAAI,IAAI,gCAAgC,CAAC;AAC7E,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,IAAI,IAAI,qCAAqC,CAAC;AAClF,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,IAAI,IAAI,iDAAiD,CAAC;AAC9F,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,aAAgB,gBAAgB,IAAI,OAAO,WAAW,OAAO,KAAK,IAAI,2BAA2B,MAAM,OAAO,OAAO,SAAS,SAAS,cAAc,YAAe,gBAAgB,IAAI,GAAG,CAAC,CAAC;AAC3M,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,IAAI,IAAI,kCAAkC,CAAC;AAC/E,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,IAAI,IAAI,oCAAoC,CAAC;AACjF,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,IAAI,IAAI,oCAAoC,CAAC;AACjF,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,IAAI,IAAI,4BAA4B,GAAG,GAAG;AACpF,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,iBAAiB,OAAO,cAAc;AAAA,EACtD;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,IAAI;AACzB,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,kBAAqB,YAAY,GAAG,GAAG,eAAe,CAAC;AAAA,EAC5D;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,WAAW,YAAY,SAAS,6EAA6E;AAC9G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,oBAAoB,CAAC;AAAA,IACpD,CAAC;AACD,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,SAAS,CAAC;AAC5C,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa;AAChB,IAAG,UAAU,GAAG,SAAS,EAAE;AAC3B,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,SAAS,CAAC;AAC5C,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa;AAChB,IAAG,UAAU,IAAI,SAAS,EAAE;AAC5B,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,OAAO,CAAC,EAAE,IAAI,SAAS,CAAC;AAC9C,IAAG,OAAO,EAAE;AACZ,IAAG,OAAO,IAAI,iBAAiB;AAC/B,IAAG,aAAa;AAChB,IAAG,UAAU,IAAI,SAAS,EAAE;AAC5B,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,OAAO,CAAC,EAAE,IAAI,SAAS,CAAC;AAC9C,IAAG,OAAO,EAAE;AACZ,IAAG,OAAO,IAAI,iBAAiB;AAC/B,IAAG,aAAa;AAChB,IAAG,UAAU,IAAI,YAAY,EAAE;AAC/B,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,aAAa,OAAO,aAAa;AAC/C,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,GAAG,GAAG,0CAA0C,CAAC;AACrF,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,GAAG,GAAG,0CAA0C,CAAC;AACrF,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,IAAI,GAAG,+BAA+B,CAAC;AAC3E,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,IAAI,IAAI,4BAA4B,CAAC;AAAA,EAC3E;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,cAAc,EAAE,EAAE,GAAG,MAAM;AAChD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa,EAAE;AAClB,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,OAAO,GAAG,OAAO;AACpB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,YAAY,OAAO,cAAc,WAAW,OAAO,cAAc,KAAK;AACpF,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,GAAG,GAAG,2BAA2B,CAAC;AAAA,EACxE;AACF;AACA,IAAM;AAAA,EACJ;AAAA,EACA;AACF,IAAI;AACJ,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,YAAY,sBAAsB,IAAI,gBAAgB;AACpD,SAAK,uBAAuB;AAC5B,SAAK,KAAK;AACV,SAAK,iBAAiB;AACtB,SAAK,sBAAsB,OAAO,mBAAmB;AACrD,SAAK,oBAAoB,OAAO,kBAAkB;AAClD,SAAK,mBAAmB,SAAS,KAAK,kBAAkB,SAAS,CAAC,eAAe,OAAO,CAAC,CAAC;AAC1F,SAAK,SAAS;AACd,SAAK,iBAAiB;AACtB,SAAK,uBAAuB;AAC5B,SAAK,YAAY;AAAA,MACf,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,UAAU;AACR,SAAK,qBAAqB,IAAI,EAAE,UAAU,SAAO;AAC/C,WAAK,UAAU,GAAG;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EACA,UAAU,eAAe;AACvB,SAAK,OAAO,KAAK,GAAG,MAAM;AAAA,MACxB,wBAAwB,CAAC,cAAc,wBAAwB,CAAC,WAAW,QAAQ,CAAC;AAAA,MACpF,oBAAoB,CAAC,cAAc,oBAAoB,CAAC,WAAW,QAAQ,CAAC;AAAA,MAC5E,UAAU,CAAC,cAAc,QAAQ;AAAA,MACjC,UAAU,CAAC,cAAc,UAAU,CAAC,WAAW,QAAQ,CAAC;AAAA,MACxD,eAAe,CAAC,cAAc,aAAa;AAAA,MAC3C,2BAA2B,CAAC,cAAc,yBAAyB;AAAA,MACnE,YAAY,CAAC,cAAc,UAAU;AAAA,MACrC,cAAc,CAAC,cAAc,YAAY;AAAA,MACzC,cAAc,CAAC,cAAc,YAAY;AAAA,IAC3C,CAAC;AAAA,EACH;AAAA,EACA,SAAS;AACP,QAAI,KAAK,UAAU,KAAK,KAAK,QAAS;AACtC,SAAK,SAAS;AACd,SAAK,qBAAqB,OAAO,KAAK,KAAK,KAAK,EAAE,KAAK,SAAS,MAAM,KAAK,SAAS,KAAK,CAAC,EAAE,UAAU,MAAM;AAC1G,WAAK,eAAe,QAAQ,yCAAyC;AACrE,WAAK,QAAQ;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,mBAAmB;AACxB,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,qBAAqB;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,KAAK,KAAK,SAAS,CAAC;AACxB,UAAM,iBAAiB,KAAK,oBAAoB,QAAQ,0CAA0C,GAAG,CAAC,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI,EAAE,SAAS,CAAC,CAAC;AAClJ,UAAM,cAAc,KAAK,oBAAoB,QAAQ,qCAAqC;AAC1F,SAAK,gBAAgB,KAAK,GAAG,MAAM;AAAA,MACjC,oBAAoB,CAAC,sBAAsB,IAAI,CAAC,UAAU,KAAK,CAAC;AAAA,MAChE,oBAAoB,CAAC,KAAK,iBAAiB,GAAG,CAAC,UAAU,KAAK,CAAC;AAAA,MAC/D,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC;AAAA,MACpC,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB;AACpB,QAAI,KAAK,cAAc,SAAS;AAC9B;AAAA,IACF;AACA,SAAK,qBAAqB,cAAc,KAAK,cAAc,KAAK,EAAE,UAAU,SAAO;AACjF,WAAK,eAAe,QAAQ,wCAAwC;AACpE,WAAK,uBAAuB;AAAA,IAC9B,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA+B,kBAAqB,oBAAoB,GAAM,kBAAqB,kBAAkB,GAAM,kBAAqB,cAAc,CAAC;AAAA,IAClM;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,yBAAyB,CAAC;AAAA,MACvC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,QAAQ,eAAe,GAAG,aAAa,kBAAkB,GAAG,CAAC,GAAG,iBAAiB,WAAW,SAAS,GAAG,CAAC,GAAG,QAAQ,eAAe,GAAG,YAAY,aAAa,kBAAkB,GAAG,CAAC,GAAG,QAAQ,YAAY,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,QAAQ,QAAQ,mBAAmB,0BAA0B,GAAG,cAAc,GAAG,CAAC,QAAQ,QAAQ,mBAAmB,sBAAsB,GAAG,cAAc,GAAG,CAAC,QAAQ,QAAQ,mBAAmB,YAAY,GAAG,cAAc,GAAG,CAAC,QAAQ,UAAU,mBAAmB,YAAY,GAAG,cAAc,GAAG,CAAC,GAAG,cAAc,MAAM,GAAG,CAAC,QAAQ,YAAY,MAAM,mBAAmB,mBAAmB,iBAAiB,GAAG,kBAAkB,GAAG,CAAC,OAAO,mBAAmB,GAAG,kBAAkB,GAAG,CAAC,QAAQ,YAAY,MAAM,gCAAgC,mBAAmB,6BAA6B,GAAG,kBAAkB,GAAG,CAAC,OAAO,gCAAgC,GAAG,kBAAkB,GAAG,CAAC,QAAQ,QAAQ,mBAAmB,cAAc,GAAG,cAAc,GAAG,CAAC,QAAQ,QAAQ,mBAAmB,gBAAgB,GAAG,cAAc,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,QAAQ,YAAY,mBAAmB,gBAAgB,GAAG,cAAc,GAAG,CAAC,cAAc,UAAU,aAAa,YAAY,GAAG,CAAC,QAAQ,UAAU,SAAS,wBAAwB,GAAG,SAAS,GAAG,eAAe,GAAG,CAAC,QAAQ,UAAU,GAAG,OAAO,eAAe,QAAQ,GAAG,OAAO,GAAG,CAAC,eAAe,QAAQ,GAAG,MAAM,QAAQ,GAAG,CAAC,MAAM,iBAAiB,GAAG,YAAY,WAAW,GAAG,CAAC,mBAAmB,sBAAsB,GAAG,cAAc,GAAG,CAAC,mBAAmB,sBAAsB,GAAG,cAAc,GAAG,CAAC,mBAAmB,WAAW,GAAG,cAAc,GAAG,CAAC,mBAAmB,QAAQ,GAAG,cAAc,GAAG,CAAC,cAAc,UAAU,aAAa,cAAc,YAAY,iBAAiB,GAAG,UAAU,GAAG,CAAC,QAAQ,UAAU,YAAY,IAAI,GAAG,OAAO,eAAe,CAAC;AAAA,MAC92D,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,WAAW,GAAG,mDAAmD,IAAI,IAAI,QAAQ,CAAC;AACrF,UAAG,eAAe,GAAG,aAAa,CAAC;AACnC,UAAG,iBAAiB,iBAAiB,SAAS,uEAAuE,QAAQ;AAC3H,YAAG,cAAc,GAAG;AACpB,YAAG,mBAAmB,IAAI,sBAAsB,MAAM,MAAM,IAAI,uBAAuB;AACvF,mBAAU,YAAY,MAAM;AAAA,UAC9B,CAAC;AACD,UAAG,WAAW,GAAG,mDAAmD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,mDAAmD,IAAI,IAAI,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,mDAAmD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAC3V,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,IAAI,OAAO,IAAI,EAAE;AAClC,UAAG,UAAU;AACb,UAAG,iBAAiB,WAAW,IAAI,oBAAoB;AACvD,UAAG,WAAW,WAAW,IAAI,SAAS;AAAA,QACxC;AAAA,MACF;AAAA,MACA,cAAc,CAAI,eAAkB,sBAAyB,qBAAwB,8BAAiC,iBAAoB,sBAAyB,oBAAuB,iBAAoB,qBAAwB,qBAAwB,0BAA6B,qBAAwB,iBAAoB,gBAAmB,qBAAwB,gBAAgB;AAAA,MAClY,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,QAAQ;AAAA,MACtB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY,CAAC,QAAQ;AAAA,MACrB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,qBAAN,MAAM,4BAA2B,uBAAuB;AAAA,EACtD,OAAO;AACL,SAAK,OAAuB,uBAAM;AAChC,UAAI;AACJ,aAAO,SAAS,2BAA2B,mBAAmB;AAC5D,gBAAQ,oCAAoC,kCAAqC,sBAAsB,mBAAkB,IAAI,qBAAqB,mBAAkB;AAAA,MACtK;AAAA,IACF,GAAG;AAAA,EACL;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,oBAAmB;AAAA,MAC5B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,SAAS,gBAAgB,eAAe;AACtC,SAAO,MAAM;AACX,kBAAc,IAAI,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,WAAW;AAAA,IACb,CAAC,CAAC;AAAA,EACJ;AACF;AACA,IAAM,iCAAiC,IAAI,eAAe,kCAAkC;AAAA,EAC1F,SAAS,MAAM;AACb,UAAM,qBAAqB,OAAO,kBAAkB;AACpD,WAAO,mBAAmB,SAAS,KAAK,aAAa,CAAC,GAAG,IAAI,WAAS,CAAC,CAAC,MAAM,MAAM,CAAC;AAAA,EACvF;AACF,CAAC;AACD,IAAM,qCAAqC,CAAC;AAAA,EAC1C,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM,CAAC,aAAa;AAAA,EACpB,OAAO;AACT,GAAG;AAAA,EACD,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM,CAAC,8BAA8B;AAAA,EACrC,OAAO;AACT,CAAC;AACD,IAAM,2CAA2C,CAAC;AAAA,EAChD,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM,CAAC,kBAAkB;AAAA,EACzB,OAAO;AACT,CAAC;AACD,SAAS,qBAAqB,aAAa;AACzC,SAAO,MAAM;AACX,gBAAY,IAAI,CAAC;AAAA,MACf,MAAM;AAAA,MACN,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,WAAW;AAAA,IACb,CAAC,CAAC;AAAA,EACJ;AACF;AACA,IAAM,8BAA8B,IAAI,eAAe,+BAA+B;AAAA,EACpF,YAAY;AAAA,EACZ,SAAS,MAAM;AACb,UAAM,cAAc,OAAO,kBAAkB;AAC7C,UAAM,aAAa;AACnB,UAAM,QAAQ,eAAa;AAAA,MACzB,QAAQ,SAAS,UAAU,EAAE,YAAY,MAAM;AAAA,IACjD;AACA,WAAO,gBAAgB,aAAa,CAAC,UAAU,GAAG,KAAK;AAAA,EACzD;AACF,CAAC;AACD,IAAM,sCAAsC,IAAI,eAAe,uCAAuC;AAAA,EACpG,YAAY;AAAA,EACZ,SAAS,MAAM;AACb,UAAM,SAAS,OAAO,2BAA2B;AACjD,WAAO,OAAO,KAAK,IAAI,cAAY,SAAS,MAAM,CAAC;AAAA,EACrD;AACF,CAAC;AACD,IAAM,wCAAwC,CAAC;AAAA,EAC7C,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM,CAAC,mCAAmC;AAAA,EAC1C,OAAO;AACT,CAAC;AACD,IAAM,uCAAuC,CAAC;AAAA,EAC5C,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM,CAAC,QAAQ;AAAA,EACf,OAAO;AACT,CAAC;AACD,SAAS,+BAA+B,UAAU;AAChD,SAAO,MAAM;AACX,UAAM,+BAA+B,SAAS,IAAI,8BAA8B;AAChF,UAAM,oCAAoC,SAAS,IAAI,mCAAmC;AAC1F,UAAM,SAAS,SAAS,IAAI,aAAa;AACzC,kBAAc,CAAC,8BAA8B,iCAAiC,CAAC,EAAE,UAAU,CAAC,CAAC,6BAA6B,gCAAgC,MAAM;AAC9J,aAAO,MAAM,kCAA8E;AAAA,QACzF,WAAW,EAAE,+BAA+B;AAAA,MAC9C,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,SAAS,iCAAiC;AACxC,SAAO,yBAAyB,CAAC,oCAAoC,0CAA0C,uCAAuC,oCAAoC,CAAC;AAC7L;AAKA,IAAMA,wBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,MAAM,MAAM,KAAK,YAAY,QAAQ;AAAA,MACxC,QAAQ;AAAA,MACR,KAAK;AAAA,IACP,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,gBAAgB,WAAS,KAAK,YAAY,QAAQ;AAAA,MACrD,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,MAAM;AAAA,IACR,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,SAAS,WAAS,KAAK,YAAY,QAAQ;AAAA,MAC9C,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,MAAM;AAAA,IACR,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAyB,SAAY,WAAW,CAAC;AAAA,IACpF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,sBAAqB;AAAA,MAC9B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkBA,uBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,gCAAN,MAAM,+BAA8B;AAAA;AAAA;AAAA;AAAA,EAIlC,OAAO,UAAU;AACf,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,+BAA+B,CAAC;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sCAAsC,mBAAmB;AAC5E,aAAO,KAAK,qBAAqB,gCAA+B;AAAA,IAClE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,0BAA0B;AAAA,MACzC,SAAS,CAAC,YAAY,mBAAmB,qBAAqB;AAAA,MAC9D,SAAS,CAAC,0BAA0B;AAAA,IACtC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,YAAY,mBAAmB,qBAAqB;AAAA,IAChE,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY,mBAAmB,qBAAqB;AAAA,MAC9D,cAAc,CAAC,0BAA0B;AAAA,MACzC,SAAS,CAAC,0BAA0B;AAAA,IACtC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["EmailSettingsService"]}