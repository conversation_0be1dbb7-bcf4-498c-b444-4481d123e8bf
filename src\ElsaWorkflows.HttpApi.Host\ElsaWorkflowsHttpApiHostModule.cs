using Elsa.Extensions;
using ElsaWorkflows.Domain;
using ElsaWorkflows.EntityFrameworkCore.EntityFrameworkCore;
using Volo.Abp.Autofac;
using Volo.Abp.Modularity;


namespace ElsaWorkflows.HttpApi.Host;

[DependsOn([
    typeof(AbpAutofacModule),
    typeof(ElsaWorkflowsEntityFrameworkCoreModule),
    typeof(ElsaWorkflowsDomainModule)
])]
public class ElsaWorkflowsHttpApiHostModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.GoAddElsa(elsa =>
        {
            elsa.UseIdentity(identity =>
            {
                identity.TokenOptions =
                    options => options.SigningKey =
                        "sufficiently-large-secret-signing-key"; // This key needs to be at least 256 bits long.
                identity.UseAdminUserProvider();
            });

            elsa.UseDefaultAuthentication(auth => auth.UseAdminApiKey());

            elsa.UseWorkflowsApi();
        });
    }
}