import { fields, model, takeOptions } from '@ttwr-framework/ngx-main-visuals';
import { JobExecutionStatus } from '@proxy/job-execution-records';

export const jobExecutionRecord = model({
  id: fields.text(),
  name: fields.text(),
  groupName: fields.text(),
  status: fields.select('single', takeOptions(JobExecutionStatus)),
  details: fields.text(),
  creationTime: fields.datetime(),
  lastModificationTime: fields.datetime(),
})
