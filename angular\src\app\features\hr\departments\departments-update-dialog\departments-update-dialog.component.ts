import { Component, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import {
  AlertService,
  LanguagePipe,
  LOADING,
  requiredValidator,
  TtwrFormComponent
} from '@ttwr-framework/ngx-main-visuals';
import { finalize } from 'rxjs';
import { departments } from '../departments.model';
import { DepartmentService } from '@proxy/hr/departments';

@Component({
  selector: 'app-departments-update',
  standalone: true,

  imports: [TtwrFormComponent, MatDialogTitle, MatDialogContent, LanguagePipe],
  template: `
    <h1 mat-dialog-title>{{ '::UpdateDepartment' | i18n }}</h1>
    <mat-dialog-content>
      <ttwr-form [config]="config"/>
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class DepartmentsUpdateDialogComponent {
  private departments = inject(DepartmentService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);
  private dialogRef = inject(MatDialogRef);
  private data = inject<{ id: string }>(MAT_DIALOG_DATA);

  protected config = departments().exclude({
    parentDepartmentName: true,
  }).form({
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        this.departments
          .putUpdateDepartmentByIdAndDto(this.data.id, body)
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::UpdatedSuccessfully');
            this.dialogRef.close(true);
          })
      },
    },
    fields: {
      name: {
        validators: [requiredValidator],
      },
      parentDepartmentId: {
        label: '::ParentDepartment',
        search: true,
      }
    },
    viewFunc: () => this.departments.getGetDepartmentByIdById(this.data.id),
  });
}
