{"version": 3, "sources": ["../../../../../../node_modules/@abp/ng.core/fesm2022/abp-ng.core-locale.mjs"], "sourcesContent": ["import { differentLocales } from '@abp/ng.core';\nimport { isDevMode } from '@angular/core';\nlet localeMap = {};\nfunction loadLocale(locale) {\n  // hard coded list works with esbuild. Source https://github.com/angular/angular-cli/issues/26904#issuecomment-1903596563\n  const list = {\n    'ar': () => import('@angular/common/locales/ar'),\n    'cs': () => import('@angular/common/locales/cs'),\n    'en': () => import('@angular/common/locales/en'),\n    'en-GB': () => import('@angular/common/locales/en-GB'),\n    'es': () => import('@angular/common/locales/es'),\n    'de': () => import('@angular/common/locales/de'),\n    'fi': () => import('@angular/common/locales/fi'),\n    'fr': () => import('@angular/common/locales/fr'),\n    'hi': () => import('@angular/common/locales/hi'),\n    'hu': () => import('@angular/common/locales/hu'),\n    'is': () => import('@angular/common/locales/is'),\n    'it': () => import('@angular/common/locales/it'),\n    'pt': () => import('@angular/common/locales/pt'),\n    'tr': () => import('@angular/common/locales/tr'),\n    'ru': () => import('@angular/common/locales/ru'),\n    'ro': () => import('@angular/common/locales/ro'),\n    'sk': () => import('@angular/common/locales/sk'),\n    'sl': () => import('@angular/common/locales/sl'),\n    'zh-Hans': () => import('@angular/common/locales/zh-Hans'),\n    'zh-Hant': () => import('@angular/common/locales/zh-Hant')\n  };\n  return list[locale]();\n}\nfunction registerLocaleForEsBuild({\n  cultureNameLocaleFileMap = {},\n  errorHandlerFn = defaultLocalErrorHandlerFn\n} = {}) {\n  return locale => {\n    localeMap = {\n      ...differentLocales,\n      ...cultureNameLocaleFileMap\n    };\n    const l = localeMap[locale] || locale;\n    const localeSupportList = \"ar|cs|en|en-GB|es|de|fi|fr|hi|hu|is|it|pt|tr|ru|ro|sk|sl|zh-Hans|zh-Hant\".split(\"|\");\n    if (localeSupportList.indexOf(locale) == -1) {\n      return;\n    }\n    return new Promise((resolve, reject) => {\n      return loadLocale(l).then(val => {\n        let module = val;\n        while (module.default) {\n          module = module.default;\n        }\n        resolve({\n          default: module\n        });\n      }).catch(error => {\n        errorHandlerFn({\n          resolve,\n          reject,\n          error,\n          locale\n        });\n      });\n    });\n  };\n}\nfunction registerLocale({\n  cultureNameLocaleFileMap = {},\n  errorHandlerFn = defaultLocalErrorHandlerFn\n} = {}) {\n  return locale => {\n    localeMap = {\n      ...differentLocales,\n      ...cultureNameLocaleFileMap\n    };\n    const localePath = `/locales/${localeMap[locale] || locale}`;\n    return new Promise((resolve, reject) => {\n      return import(/* webpackMode: \"lazy-once\" */\n      /* webpackChunkName: \"locales\"*/\n      /* webpackInclude: /[/\\\\](ar|cs|en|en-GB|es|de|fi|fr|hi|hu|is|it|pt|tr|ru|ro|sk|sl|zh-Hans|zh-Hant)\\.(mjs|js)$/ */\n      /* webpackExclude: /[/\\\\]global|extra/ */\n      `@angular/common${localePath}`).then(val => {\n        let module = val;\n        while (module.default) {\n          module = module.default;\n        }\n        resolve({\n          default: module\n        });\n      }).catch(error => {\n        errorHandlerFn({\n          resolve,\n          reject,\n          error,\n          locale\n        });\n      });\n    });\n  };\n}\nconst extraLocales = {};\nfunction storeLocaleData(data, localeId) {\n  extraLocales[localeId] = data;\n}\nasync function defaultLocalErrorHandlerFn({\n  locale,\n  resolve\n}) {\n  if (extraLocales[locale]) {\n    resolve({\n      default: extraLocales[localeMap[locale] || locale]\n    });\n    return;\n  }\n  if (isDevMode()) {\n    console.error(`Cannot find the ${locale} locale file. You can check how can add new culture at https://abp.io/docs/latest/framework/ui/angular/localization#adding-a-new-culture`);\n  }\n  resolve();\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { defaultLocalErrorHandlerFn, registerLocale, registerLocaleForEsBuild, storeLocaleData };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAEA,IAAI,YAAY,CAAC;AACjB,SAAS,WAAW,QAAQ;AAE1B,QAAM,OAAO;AAAA,IACX,MAAM,MAAM,OAAO,iCAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,SAAS,MAAM,OAAO,qBAA+B;AAAA,IACrD,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,MAAM,MAAM,OAAO,kBAA4B;AAAA,IAC/C,WAAW,MAAM,OAAO,uBAAiC;AAAA,IACzD,WAAW,MAAM,OAAO,uBAAiC;AAAA,EAC3D;AACA,SAAO,KAAK,MAAM,EAAE;AACtB;AACA,SAAS,yBAAyB;AAAA,EAChC,2BAA2B,CAAC;AAAA,EAC5B,iBAAiB;AACnB,IAAI,CAAC,GAAG;AACN,SAAO,YAAU;AACf,gBAAY,kCACP,mBACA;AAEL,UAAM,IAAI,UAAU,MAAM,KAAK;AAC/B,UAAM,oBAAoB,2EAA2E,MAAM,GAAG;AAC9G,QAAI,kBAAkB,QAAQ,MAAM,KAAK,IAAI;AAC3C;AAAA,IACF;AACA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,aAAO,WAAW,CAAC,EAAE,KAAK,SAAO;AAC/B,YAAI,SAAS;AACb,eAAO,OAAO,SAAS;AACrB,mBAAS,OAAO;AAAA,QAClB;AACA,gBAAQ;AAAA,UACN,SAAS;AAAA,QACX,CAAC;AAAA,MACH,CAAC,EAAE,MAAM,WAAS;AAChB,uBAAe;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,SAAS,eAAe;AAAA,EACtB,2BAA2B,CAAC;AAAA,EAC5B,iBAAiB;AACnB,IAAI,CAAC,GAAG;AACN,SAAO,YAAU;AACf,gBAAY,kCACP,mBACA;AAEL,UAAM,aAAa,YAAY,UAAU,MAAM,KAAK,MAAM;AAC1D,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,aAAO;AAAA;AAAA;AAAA;AAAA;AAAA,QAIP,kBAAkB,UAAU;AAAA,QAAI,KAAK,SAAO;AAC1C,YAAI,SAAS;AACb,eAAO,OAAO,SAAS;AACrB,mBAAS,OAAO;AAAA,QAClB;AACA,gBAAQ;AAAA,UACN,SAAS;AAAA,QACX,CAAC;AAAA,MACH,CAAC,EAAE,MAAM,WAAS;AAChB,uBAAe;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,IAAM,eAAe,CAAC;AACtB,SAAS,gBAAgB,MAAM,UAAU;AACvC,eAAa,QAAQ,IAAI;AAC3B;AACA,SAAe,2BAA2B,IAGvC;AAAA,6CAHuC;AAAA,IACxC;AAAA,IACA;AAAA,EACF,GAAG;AACD,QAAI,aAAa,MAAM,GAAG;AACxB,cAAQ;AAAA,QACN,SAAS,aAAa,UAAU,MAAM,KAAK,MAAM;AAAA,MACnD,CAAC;AACD;AAAA,IACF;AACA,QAAI,UAAU,GAAG;AACf,cAAQ,MAAM,mBAAmB,MAAM,0IAA0I;AAAA,IACnL;AACA,YAAQ;AAAA,EACV;AAAA;", "names": []}