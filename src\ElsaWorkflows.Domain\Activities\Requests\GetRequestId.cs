using Elsa.Extensions;
using Elsa.Workflows;
using Elsa.Workflows.Attributes;

namespace ElsaWorkflows.Domain.Activities.Requests;


[Activity("Requests",
    "Gets the request id from workflow inputs, workflow input name: \"RequestId\" - output: requestId")]
public class GetRequestId : CodeActivity<Guid>
{
    protected override void Execute(ActivityExecutionContext context)
    {
        if (!context.WorkflowInput.TryGetValue("RequestId", out var input))
        {
            throw new InvalidOperationException(
                "The Input \"RequestId\" is not set, please set it in the designer");
        }

        if (!Guid.TryParse(input.ToString(), out var requestId) || requestId == Guid.Empty)
        {
            throw new InvalidOperationException(
                "The Input \"RequestId\" is empty or invalid when you try to access it");
        }

        context.SetResult(requestId);
    }
}