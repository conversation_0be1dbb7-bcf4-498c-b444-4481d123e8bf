import type { CreateUpdateEmployeePinDto, EmployeePinDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class EmployeePinService {
  apiName = 'Default';
  

  deleteDelete = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/employee-pin/${id}/delete`,
    },
    { apiName: this.apiName,...config });
  

  getGetAllByInput = (input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<EmployeePinDto>>({
      method: 'GET',
      url: '/api/app/employee-pin/get-all',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getGetByIdById = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, EmployeePinDto>({
      method: 'GET',
      url: `/api/app/employee-pin/${id}/get-by-id`,
    },
    { apiName: this.apiName,...config });
  

  postCreateEmployeePinByDto = (dto: CreateUpdateEmployeePinDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/employee-pin/create-employee-pin',
      body: dto,
    },
    { apiName: this.apiName,...config });
  

  putUpdateEmployeePinByIdAndDto = (id: string, dto: CreateUpdateEmployeePinDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: `/api/app/employee-pin/${id}/update-employee-pin`,
      body: dto,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
