import { ChangeDetectionStrategy, Component, inject, input } from '@angular/core';
import {
  FormFieldWithControlAndName,
  ICustomInputComponent,
  LanguagePipe,
  provideNativeDatetimeAdapter,
  TextFieldType,
  TtwrDatetimepicker,
  TtwrDatetimepickerInput,
  TtwrDatetimepickerToggle,
} from '@ttwr-framework/ngx-main-visuals';
import { FormGroup, NonNullableFormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { map, skip, startWith, switchMap, take } from 'rxjs';
import { FieldType } from '@proxy/extra-field-definitions';
import { AsyncPipe } from '@angular/common';
import { MatError, MatFormField, MatHint, MatLabel, MatSuffix } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { MatCheckbox } from '@angular/material/checkbox';
import { Mat<PERSON><PERSON>picker, MatDatepickerInput, MatDatepickerToggle } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatIconButton, MatMiniFabButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { CURRENT_SELECT_OPTIONS } from '../extra-fields-update-dialog/extra-fields-update-dialog.component';

@Component({
  selector: 'app-default-value-custom-input',
  standalone: true,
  imports: [
    AsyncPipe,
    MatFormField,
    MatLabel,
    LanguagePipe,
    ReactiveFormsModule,
    MatInput,
    MatError,
    MatCheckbox,
    MatDatepickerInput,
    MatDatepickerToggle,
    MatDatepicker,
    MatSuffix,
    TtwrDatetimepicker,
    TtwrDatetimepickerInput,
    TtwrDatetimepickerToggle,
    MatIconButton,
    MatIcon,
    MatMiniFabButton,
    MatHint,
  ],
  providers: [
    provideNativeDateAdapter(),
    provideNativeDatetimeAdapter(),
  ],
  templateUrl: './default-value-custom-input.component.html',
  styles: `
    mat-form-field {
      width: 100%;
    }

    .array-field {
      margin-bottom: 1rem;
    }

    :host ::ng-deep {
      .form-array {
        display: flex;
        gap: 1rem;
      }

      .array-form-fields {
        display: block !important;
        flex-grow: 1;
      }

      mat-form-field {
        width: 100%;
      }
    }
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DefaultValueCustomInputComponent implements ICustomInputComponent<TextFieldType> {
  private fb = inject(NonNullableFormBuilder);
  private currentSelectOptions = inject(CURRENT_SELECT_OPTIONS, { optional: true });

  public field = input.required<FormFieldWithControlAndName<TextFieldType>>();

  protected fieldType = FieldType;

  protected type$ = toObservable(this.field).pipe(
    switchMap(field => {
      const fieldType = (field.control.parent as FormGroup).controls['fieldType'];

      return fieldType.valueChanges.pipe(
        startWith(fieldType.value),
      )
    }),
    map(value => value as FieldType),
  )

  protected selectionForm = this.fb.group({
    selection: this.fb.array<string>([]),
  })

  constructor() {
    // selection first value handling
    this.type$.pipe(
      take(1),
      map(type => type === FieldType.Selection),
      takeUntilDestroyed(),
    ).subscribe(isSelect => {
      if (isSelect) {
        (this.currentSelectOptions?.() ?? []).forEach(
          v => this.addForm(v)
        )
      }
    });

    // selection change handling
    this.selectionForm.controls.selection.valueChanges.pipe(
      skip(1),
      takeUntilDestroyed(),
    ).subscribe(value => {
      this.field().control.setValue(
        value.length === 0
          ? ''
          : value as any // we didn't change it to string because we will parse in future
      );
    });

    // boolean type first value
    this.type$.pipe(
      take(1),
      takeUntilDestroyed()
    ).subscribe(type => {
      if (type === FieldType.Bool) {
        const stringValue = this.field().control.value;
        this.field().control.setValue((stringValue === 'true') as any)
      }
    });

    // reset handling && boolean value change handling
    this.type$.pipe(
      skip(1),
      takeUntilDestroyed()
    ).subscribe(type => {
      this.field().control.reset(type === FieldType.Bool ? false as any : undefined);
      this.selectionForm.controls.selection.clear();
    });
  }

  addForm(initial: string | null = null) {
    this.selectionForm.controls.selection.push(
      this.fb.control(initial as any, [Validators.required])
    );
  }

  removeForm(index: number) {
    this.selectionForm.controls.selection.removeAt(index);
  }
}
