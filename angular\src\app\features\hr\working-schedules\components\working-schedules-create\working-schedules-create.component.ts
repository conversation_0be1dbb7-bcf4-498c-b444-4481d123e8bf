import { Component, DestroyRef, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';
import { AlertService, LanguagePipe, LOADING } from '@ttwr-framework/ngx-main-visuals';
import { finalize } from 'rxjs';
import { MatCard, MatCardActions, MatCardContent } from '@angular/material/card';
import { isoToTime } from '@shared';
import { WorkingScheduleService } from '@proxy/hr/working-schedules';
import { MatError, MatFormField, MatLabel } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { NonNullableFormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { WorkingHoursDto } from '@proxy/hr/working-schedules/dto';
import { WorkingHoursGridFormComponent } from '../working-hours-grid-form/working-hours-grid-form.component';
import { MatButton } from '@angular/material/button';

@Component({
  selector: 'app-working-schedules-create',
  standalone: true,
  imports: [MatCardContent, MatCard, LanguagePipe, MatFormField, MatLabel, MatError, MatInput, ReactiveFormsModule, WorkingHoursGridFormComponent, MatCardActions, MatButton],
  templateUrl: './working-schedules-create.component.html',
  styles: `
    mat-form-field {
      min-width: 400px;
    }
  `,
})
export class WorkingSchedulesCreateComponent {
  private workingSchedule = inject(WorkingScheduleService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private fb = inject(NonNullableFormBuilder);
  private loading = inject(LOADING);

  protected nameControl = this.fb.control('', [Validators.required]);

  protected lastId = signal(0);
  protected workingHours = signal<(WorkingHoursDto & { id: number })[]>([]);


  submit() {
    if (this.nameControl.invalid) {
      this.nameControl.markAsTouched();
      return;
    }

    if (this.workingHours().length === 0) {
      this.alert.error('::GoldenOwl:MustHaveAtLeastOneWorkingHour')
      return;
    }

    this.loading.set(true);

    this.workingSchedule
      .create({
        name: this.nameControl.value,
        workingHours: this.workingHours().map(workingHour => ({
          day: workingHour.day,
          from: isoToTime(workingHour.from!),
          to: isoToTime(workingHour.to!),
          expectedAttendance: isoToTime(workingHour.expectedAttendance!),
        })),
      })
      .pipe(
        finalize(() => this.loading.set(false)),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(() => {
        this.alert.success('::CreatedSuccessfully');
        this.router.navigate(['.'], { relativeTo: this.route.parent });
      })
  }
}
