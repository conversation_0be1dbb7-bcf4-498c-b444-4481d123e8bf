using Elsa.Extensions;
using Elsa.Workflows;
using Elsa.Workflows.Activities.Flowchart.Attributes;
using Elsa.Workflows.Attributes;
using ElsaWorkflows.GoldenOwl.Common.HR.Employee;

namespace ElsaWorkflows.Domain.Activities.HR.Employee;

[Activity("Employee", "Gets the userId for the previous employee, input: employeeId, output: userId")]
[FlowNode("Pass", "NoUser")]
public class GetEmployeeUserId : CodeActivity<Guid>
{
    protected override async ValueTask ExecuteAsync(ActivityExecutionContext context)
    {
        var employeeElsaHelper = context.GetRequiredService<IEmployeeElsaHelper>();

        var input = context.GetLastResult();
        
        if (input?.ToString() is null)
        {
            throw new InvalidOperationException("The last result is null or invalid");
        }

        var employeeId = Guid.Parse(input.ToString());
        var userId = await employeeElsaHelper.GetEmployeeUserIdAsync(employeeId);
        context.SetResult(userId);
        
        if (userId.HasValue)
        {
            await context.CompleteActivityWithOutcomesAsync("Pass");
        }
        else
        {
            await context.CompleteActivityWithOutcomesAsync("NoUser");
        }
    }
}