import {
  DateFieldType,
  FormFieldWithControlAndName,
  ICustomInputComponent,
  LanguagePipe
} from '@ttwr-framework/ngx-main-visuals';
import { Component, input } from '@angular/core';
import { <PERSON><PERSON>rror, MatFormField, <PERSON><PERSON>abel, MatSuffix } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { DateFilterFn, MatDatepicker, MatDatepickerInput, MatDatepickerToggle } from '@angular/material/datepicker';
import { ReactiveFormsModule } from '@angular/forms';
import { provideNativeDateAdapter } from '@angular/material/core';

@Component({
  selector: 'app-date-with-disabled-cells-custom-input',
  standalone: true,
  imports: [
    MatError,
    MatLabel,
    MatFormField,
    LanguagePipe,
    MatInput,
    MatDatepickerInput,
    ReactiveFormsModule,
    MatDatepickerToggle,
    MatDatepicker,
    MatSuffix,
  ],
  providers: [provideNativeDateAdapter()],
  template: `
    <mat-form-field>
      <mat-label>{{ (field().label ?? field().name) | i18n }}</mat-label>
      <input
        matInput
        [matDatepicker]="picker"
        [matDatepickerFilter]="dateFilter()"
        [formControl]="field().control"
      >
      <mat-datepicker-toggle
        matIconSuffix
        [for]="picker"
        [disabled]="field().readonlySignal?.()"
      />
      <mat-datepicker #picker />
      <mat-error>
        @for (validator of field().validators ?? []; track validator.name) {
          <span [hidden]="!field().control.hasError(validator.name)">
          {{ validator.message | i18n }}
        </span>
        }
      </mat-error>
    </mat-form-field>
  `,
  styles: `
    mat-form-field {
      width: 100%;
    }
  `,
})
export class DateWithDisabledCellsCustomInputComponent implements ICustomInputComponent<DateFieldType> {
  public field = input.required<FormFieldWithControlAndName<DateFieldType>>();
  public dateFilter = input.required<DateFilterFn<Date | null>>();
}
