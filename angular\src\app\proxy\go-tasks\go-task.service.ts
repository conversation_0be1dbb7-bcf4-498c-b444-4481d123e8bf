import type { GoTaskDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class GoTaskService {
  apiName = 'Default';
  

  approveGoTaskByGoTaskId = (goTaskId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/go-task/approve-go-task/${goTaskId}`,
    },
    { apiName: this.apiName,...config });
  

  getGoTask = (goTaskId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, GoTaskDto>({
      method: 'GET',
      url: `/api/app/go-task/go-task/${goTaskId}`,
    },
    { apiName: this.apiName,...config });
  

  getGoTasksAssignedForCurrentUser = (dto: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<GoTaskDto>>({
      method: 'GET',
      url: '/api/app/go-task/go-tasks-assigned-for-current-user',
      params: { skipCount: dto.skipCount, maxResultCount: dto.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  rejectGoTaskByGoTaskId = (goTaskId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/go-task/reject-go-task/${goTaskId}`,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
