import { Component, DestroyRef, inject } from '@angular/core';
import { extraGridFilter, GridFilterOperation, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { attendance } from './attendance.model';
import { AttendanceService } from '@proxy/hr/attendances';
import { DateRangeCustomInputComponent, extractTwoDates, requireAllOperator } from '@shared';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AttendanceUpdateDialogComponent } from './attendance-update-dialog/attendance-update-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { AttendanceCreateDialogComponent } from './attendance-create-dialog/attendance-create-dialog.component';

@Component({
  selector: 'app-attendance',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `
    <ttwr-grid [config]="config"/>`,
})
export class AttendanceComponent {
  private attendance = inject(AttendanceService);
  private dialog = inject(MatDialog);
  private destroyRef = inject(DestroyRef);

  private refreshSubject = new Subject<void>();

  protected config = attendance().grid({
    title: '::AttendanceRecords',
    refreshSubject: this.refreshSubject,
    dataFunc: (pagination, _, filters) => {
      const employeeId = filters.find(f => f.attribute === 'employeeId');
      const dateRange = filters.find(f => f.attribute === 'duration');

      const dateRangeResult = dateRange ? extractTwoDates(dateRange.value) : undefined;

      return this.attendance.getList({
          skipCount: pagination.pageSize * pagination.pageIndex,
          maxResultCount: pagination.pageSize,
        },
        employeeId?.value ?? '00000000-0000-0000-0000-000000000000',
        dateRangeResult ? dateRangeResult.start : '',
        dateRangeResult ? dateRangeResult.end : '',
      ).pipe(requireAllOperator());
    },
    fields: {
      employeeId: {
        nonSearchable: false,
        columnName: '::GoldenOwl:Employee',
      },
      checkIn: {
        columnName: '::GoldenOwl:CheckIn',
      },
      checkOut: {
        columnName: '::GoldenOwl:CheckOut',
      },
      inMode: {
        columnName: '::GoldenOwl:CheckInMode',
      },
      outMode: {
        columnName: '::GoldenOwl:CheckOutMode',
      },
    },
    actions: [
      {
        matIcon: 'add',
        tooltip: 'Add',
        delegateFunc: () => {
          const ref = this.dialog.open(AttendanceCreateDialogComponent, {
            width: '500px'
          });

          ref.afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        }
      }
    ],
    fieldActions: [
      {
        matIcon: 'edit',
        tooltip: 'Edit',
        delegateFunc: (obj) => {
          const ref = this.dialog.open(AttendanceUpdateDialogComponent, {
            width: '500px',
            data: obj,
          });

          ref.afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        }
      },
    ],
    extraFilters: [
      extraGridFilter({
        type: 'text',
        name: 'duration',
        label: '::GoldenOwl:Duration',
        searchBarOperation: GridFilterOperation.In,
        customInputComponent: DateRangeCustomInputComponent,
        customInputComponentExtraInputs: {
          disableDateFilter: true,
        },
      })
    ],
  });
}
