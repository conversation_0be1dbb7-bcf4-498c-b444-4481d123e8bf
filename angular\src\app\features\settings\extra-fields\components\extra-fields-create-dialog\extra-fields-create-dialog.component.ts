import { Component, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { AlertService, LanguagePipe, LOADING, TtwrFormComponent } from '@ttwr-framework/ngx-main-visuals';
import { finalize } from 'rxjs';
import { extraFields } from '../../extra-fields.model';
import { ExtraFieldDefinitionService, FieldType } from '@proxy/extra-field-definitions';
import { DefaultValueCustomInputComponent } from '../default-value-custom-input/default-value-custom-input.component';
import { clearDate } from '@shared';
import { UniqueCodeCustomInputComponent } from '../unique-code-custom-input/unique-code-custom-input.component';

@Component({
  selector: 'app-extra-fields-create-dialog',
  standalone: true,
  imports: [Ttwr<PERSON><PERSON><PERSON>omponent, MatDialogTitle, MatD<PERSON>og<PERSON>ontent, LanguagePipe],
  template: `
    <h1 mat-dialog-title>{{ '::AddExtraDefinition' | i18n }}</h1>
    <mat-dialog-content>
      <ttwr-form [config]="config"/>
    </mat-dialog-content>
  `,
  styles: `
    :host {
      --ttwr-form-grid-template-columns: 1fr 1fr;
      --ttwr-form-gap: 1rem;
    }
  `,
})
export class ExtraFieldsCreateDialogComponent {
  private extraFieldDefinition = inject(ExtraFieldDefinitionService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);
  private dialogRef = inject(MatDialogRef);

  protected config = extraFields.form({
    initialRequired: true,
    submitAction: {
      onSubmit: (body) => {
        this.loading.set(true);

        let defaultValue: string;
        if (body.fieldType === FieldType.Int || body.fieldType === FieldType.Decimal) {
          defaultValue = JSON.stringify(body.defaultValue).replace(/"/g, '');
        } else if (body.fieldType === FieldType.DateOnly) {
          defaultValue = JSON.stringify(clearDate(body.defaultValue));
        } else if (body.fieldType === FieldType.Bool) {
          defaultValue = JSON.stringify(Boolean(body.defaultValue));
        } else if (body.fieldType === FieldType.Selection) {
          defaultValue = JSON.stringify(body.defaultValue[0]);
        } else {
          defaultValue = JSON.stringify(body.defaultValue);
        }

        this.extraFieldDefinition
          .create({
            ...body,
            defaultValue,
            selectListValues: body.fieldType === FieldType.Selection ? body.defaultValue as any : [],
          })
          .pipe(
            finalize(() => this.loading.set(false)),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => {
            this.alert.success('::CreatedSuccessfully');
            this.dialogRef.close(true);
          })
      },
    },
    fields: {
      code: {
        customInputComponent: UniqueCodeCustomInputComponent,
        label: '::Code',
      },
      fieldType: {
        defaultValue: FieldType.Int,
        label: '::FieldType',
      },
      defaultValue: {
        customInputComponent: DefaultValueCustomInputComponent,
        label: '::DefaultValue',
      },
      fieldName: {
        label: '::FieldName',
      },
      entityType: {
        label: '::EntityType',
      },
    },
  });
}
