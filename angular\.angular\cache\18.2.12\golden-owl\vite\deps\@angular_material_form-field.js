import {
  MAT_ERROR,
  MAT_FORM_FIELD,
  MAT_FORM_FIELD_DEFAULT_OPTIONS,
  MAT_PREFIX,
  MAT_SUFFIX,
  Mat<PERSON>rror,
  MatFormField,
  MatFormFieldControl,
  MatFormFieldModule,
  MatHint,
  Mat<PERSON><PERSON><PERSON>,
  MatPrefix,
  MatSuffix,
  getMatFormFieldDuplicatedHintError,
  getMatFormFieldMissingControlError,
  getMatFormFieldPlaceholderConflictError,
  matFormFieldAnimations
} from "./chunk-DTMWAZLO.js";
import "./chunk-KW6MAD7U.js";
import "./chunk-2MXHA5U4.js";
import "./chunk-DNW4SGAD.js";
import "./chunk-HL4RP4FA.js";
import "./chunk-VTW5CIPD.js";
import "./chunk-7YC2NMXI.js";
import "./chunk-ILOBLWYU.js";
import "./chunk-ZTELYOIP.js";
export {
  MAT_ERROR,
  MAT_FORM_FIELD,
  MAT_FORM_FIELD_DEFAULT_OPTIONS,
  MAT_PREFIX,
  MAT_SUFFIX,
  MatError,
  MatFormField,
  MatFormFieldControl,
  MatFormFieldModule,
  MatHint,
  MatLabel,
  MatPrefix,
  MatSuffix,
  getMatFormFieldDuplicatedHintError,
  getMatFormFieldMissingControlError,
  getMatFormFieldPlaceholderConflictError,
  matFormFieldAnimations
};
//# sourceMappingURL=@angular_material_form-field.js.map
