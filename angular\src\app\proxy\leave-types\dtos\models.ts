import type { RequestUnit } from '../../hr/leave-types/request-unit.enum';
import type { KindOfTimeOff } from '../../hr/leave-types/kind-of-time-off.enum';
import type { EntityDto } from '@abp/ng.core';

export interface LeaveTypeCreateUpdateDto {
  name?: string;
  requestUnit: RequestUnit;
  kindOfTimeOff: KindOfTimeOff;
  requiresAllocation: boolean;
  code?: string;
}

export interface LeaveTypeDetailDto {
  name?: string;
  requestUnit: RequestUnit;
  kindOfTimeOff: KindOfTimeOff;
  requiresAllocation: boolean;
  code?: string;
}

export interface LeaveTypeDto {
  id?: string;
  name?: string;
  requestUnit: RequestUnit;
  code?: string;
}

export interface LeaveTypeIdAndNameDto extends EntityDto<string> {
  name?: string;
}
