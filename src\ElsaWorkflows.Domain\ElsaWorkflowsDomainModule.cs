﻿using Volo.Abp.Modularity;
using Elsa.Extensions;
using ElsaWorkflows.Domain.Activities;
using ElsaWorkflows.Domain.Activities.GoTasks;
using ElsaWorkflows.Domain.Activities.HR.Employee;
using ElsaWorkflows.Domain.Activities.Requests;
using ElsaWorkflows.Domain.Activities.Requests.LeaveRequests;
using ElsaWorkflows.GoldenOwl.Common;

namespace ElsaWorkflows.Domain;

[DependsOn([
    typeof(ElsaWorkflowsGoldenOwlCommonModule)
])]
public class ElsaWorkflowsDomainModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.GoAddElsaConfiguration(elsa =>
        {
            elsa.AddActivity<GetEmployeeUpperId>();
            elsa.AddActivity<GetRequesterEmployeeId>();
            elsa.AddActivity<GetEmployeeByPosition>();
            elsa.AddActivity<GetEmployeeUserId>();
            
            elsa.AddActivity<GetRequestId>();
            elsa.AddActivity<ApproveRequest>();
            elsa.AddActivity<VerifyRequest>();
            elsa.AddActivity<RejectRequest>();
            
            elsa.AddActivity<ValidateLeaveBalanceForEmployee>();
            
            elsa.AddActivity<AssignGoTaskToUser>();
            
            elsa.AddActivity<GetFirstFromList>();
        });
    }
}