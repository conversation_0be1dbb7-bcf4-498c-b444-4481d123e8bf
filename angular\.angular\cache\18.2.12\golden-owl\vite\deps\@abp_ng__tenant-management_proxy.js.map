{"version": 3, "sources": ["../../../../../../node_modules/@abp/ng.tenant-management/fesm2022/abp-ng.tenant-management-proxy.mjs"], "sourcesContent": ["import * as i1 from '@abp/ng.core';\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\nclass TenantService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'AbpTenantManagement';\n    this.create = input => this.restService.request({\n      method: 'POST',\n      url: '/api/multi-tenancy/tenants',\n      body: input\n    }, {\n      apiName: this.apiName\n    });\n    this.delete = id => this.restService.request({\n      method: 'DELETE',\n      url: `/api/multi-tenancy/tenants/${id}`\n    }, {\n      apiName: this.apiName\n    });\n    this.deleteDefaultConnectionString = id => this.restService.request({\n      method: 'DELETE',\n      url: `/api/multi-tenancy/tenants/${id}/default-connection-string`\n    }, {\n      apiName: this.apiName\n    });\n    this.get = id => this.restService.request({\n      method: 'GET',\n      url: `/api/multi-tenancy/tenants/${id}`\n    }, {\n      apiName: this.apiName\n    });\n    this.getDefaultConnectionString = id => this.restService.request({\n      method: 'GET',\n      responseType: 'text',\n      url: `/api/multi-tenancy/tenants/${id}/default-connection-string`\n    }, {\n      apiName: this.apiName\n    });\n    this.getList = input => this.restService.request({\n      method: 'GET',\n      url: '/api/multi-tenancy/tenants',\n      params: {\n        filter: input.filter,\n        sorting: input.sorting,\n        skipCount: input.skipCount,\n        maxResultCount: input.maxResultCount\n      }\n    }, {\n      apiName: this.apiName\n    });\n    this.update = (id, input) => this.restService.request({\n      method: 'PUT',\n      url: `/api/multi-tenancy/tenants/${id}`,\n      body: input\n    }, {\n      apiName: this.apiName\n    });\n    this.updateDefaultConnectionString = (id, defaultConnectionString) => this.restService.request({\n      method: 'PUT',\n      url: `/api/multi-tenancy/tenants/${id}/default-connection-string`,\n      params: {\n        defaultConnectionString\n      }\n    }, {\n      apiName: this.apiName\n    });\n  }\n  static {\n    this.ɵfac = function TenantService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TenantService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TenantService,\n      factory: TenantService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TenantService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.RestService\n  }], null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TenantService };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAGA,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,SAAS,WAAS,KAAK,YAAY,QAAQ;AAAA,MAC9C,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,MAAM;AAAA,IACR,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,SAAS,QAAM,KAAK,YAAY,QAAQ;AAAA,MAC3C,QAAQ;AAAA,MACR,KAAK,8BAA8B,EAAE;AAAA,IACvC,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,gCAAgC,QAAM,KAAK,YAAY,QAAQ;AAAA,MAClE,QAAQ;AAAA,MACR,KAAK,8BAA8B,EAAE;AAAA,IACvC,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,MAAM,QAAM,KAAK,YAAY,QAAQ;AAAA,MACxC,QAAQ;AAAA,MACR,KAAK,8BAA8B,EAAE;AAAA,IACvC,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,6BAA6B,QAAM,KAAK,YAAY,QAAQ;AAAA,MAC/D,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,KAAK,8BAA8B,EAAE;AAAA,IACvC,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,UAAU,WAAS,KAAK,YAAY,QAAQ;AAAA,MAC/C,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,QACN,QAAQ,MAAM;AAAA,QACd,SAAS,MAAM;AAAA,QACf,WAAW,MAAM;AAAA,QACjB,gBAAgB,MAAM;AAAA,MACxB;AAAA,IACF,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,SAAS,CAAC,IAAI,UAAU,KAAK,YAAY,QAAQ;AAAA,MACpD,QAAQ;AAAA,MACR,KAAK,8BAA8B,EAAE;AAAA,MACrC,MAAM;AAAA,IACR,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,gCAAgC,CAAC,IAAI,4BAA4B,KAAK,YAAY,QAAQ;AAAA,MAC7F,QAAQ;AAAA,MACR,KAAK,8BAA8B,EAAE;AAAA,MACrC,QAAQ;AAAA,QACN;AAAA,MACF;AAAA,IACF,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAkB,SAAY,WAAW,CAAC;AAAA,IAC7E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,eAAc;AAAA,MACvB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;", "names": []}