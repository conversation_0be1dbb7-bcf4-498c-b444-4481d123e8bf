import { Component, DestroyRef, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatDialog } from '@angular/material/dialog';
import { SalaryEffectValueType } from '@proxy/hr/penalty-types';
import { RewardTypeService } from '@proxy/hr/reward-types';
import { requireAllOperator } from '@shared';
import { pagedMap, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { map, Subject } from 'rxjs';
import { rewardsTypes } from './rewards-type.model';
import { RewardsTypesUpdateDialogComponent } from './rewards-types-update-dialog/rewards-types-update-dialog.component';
import { RewardsTypesCreateDialogComponent } from './rewards-types-create-dialog/rewards-types-create-dialog.component';

@Component({
  selector: 'app-rewards-types',
  standalone: true,
  imports: [TtwrGridComponent],
  template: `<ttwr-grid class="actions-end" [config]="config" />`,
})
export class RewardsTypesComponent {
  private rewardTypeService = inject(RewardTypeService);
  private dialog = inject(MatDialog);
  private destroyRef = inject(DestroyRef);

  private refreshSubject = new Subject<void>();

  protected config = rewardsTypes.grid({
    refreshSubject: this.refreshSubject,
    title: '::RewardsTypes',
    dataFunc: (pagination) =>
      this.rewardTypeService
        .getList({
          skipCount: pagination.pageSize * pagination.pageIndex,
          maxResultCount: pagination.pageSize,
        })
        .pipe(
          requireAllOperator(),
          map((r) => r as any),
          pagedMap((type: any) => ({
            ...type,
            salaryEffectValueType:
              type.rewardSalaryEffect?.salaryEffectValueType,
            salaryEffectValue: type.rewardSalaryEffect?.salaryEffectValue,
          }))
        ),
    fields: {
      salaryEffectValueType: {
        hiddenSignal: signal(true),
      },
      salaryEffectValue: {
        columnName: '::RewardsType:SalaryEffectValue',
        displayCell: (cell, field) =>
          !cell
            ? '-'
            : field.salaryEffectValueType === SalaryEffectValueType.FixedAmount
            ? cell.toString()
            : `${cell}%`,
      },
      code: {
        columnName: '::RewardsType:Code',
      },
    },
    actions: [
      {
        matIcon: 'add',
        tooltip: 'Add',
        delegateFunc: () => {
          const ref = this.dialog.open(RewardsTypesCreateDialogComponent, {
            width: '100%',
            maxWidth: '700px',
          });

          ref
            .afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe((res) => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        },
      },
    ],
    fieldActions: [
      {
        matIcon: 'edit',
        tooltip: 'Edit',
        delegateFunc: (obj) => {
          const ref = this.dialog.open(RewardsTypesUpdateDialogComponent, {
            width: '100%',
            maxWidth: '700px',
            data: obj,
          });

          ref
            .afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe((res) => {
              if (res) {
                this.refreshSubject.next();
              }
            });
        },
      },
    ],
  });
}
