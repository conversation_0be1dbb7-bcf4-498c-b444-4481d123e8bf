import { model, fields, takeOptions } from '@ttwr-framework/ngx-main-visuals';
import { EntityType, FieldType } from '@proxy/extra-field-definitions';

export const extraFields = model({
  id: fields.text(),
  fieldName: fields.text(),
  code: fields.text(),
  entityType: fields.select('single', takeOptions(EntityType)),
  fieldType: fields.select('single', takeOptions(FieldType)),
  defaultValue: fields.text(),
});

export const extraFieldDefaultValue = model({
  number: fields.number(),
  text: fields.text(),
  date: fields.date(),
  datetime: fields.datetime(),
  boolean: fields.boolean(),
  selection: fields.list({
    value: fields.text(),
  }),
})
