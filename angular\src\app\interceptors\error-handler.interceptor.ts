import { HttpErrorResponse, HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { AlertService, LOADING } from '@ttwr-framework/ngx-main-visuals';
import { catchError, throwError } from 'rxjs';

export const errorHandlerInterceptor: HttpInterceptorFn = (req, next) => {
  const alert = inject(AlertService);
  const loading = inject(LOADING);

  return next(req).pipe(
    catchError(err => {
      if (err instanceof HttpErrorResponse) {

        let abpError: { error: { message: string } } = err.error;

        if (typeof err.error === 'string') {
          abpError = JSON.parse(err.error);
        }

        loading.set(false);

        if (!err.url?.endsWith('account/my-profile') && abpError?.error) {
          alert.error(abpError.error.message);
        }
      }

      return throwError(() => err);
    })
  );
}
